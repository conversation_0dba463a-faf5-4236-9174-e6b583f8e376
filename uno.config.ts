// import remToPx from '@unocss/preset-rem-to-px'
import { defineConfig, presetIcons, presetUno } from 'unocss'

// function presetRemToPx(RemToPxOptions): any {
//   return remToPx(RemToPxOptions)
// }
export default defineConfig({
  presets: [
    presetUno(),
    // presetRemToPx({
    //   baseFontSize: 4,
    // }),
    presetIcons({
      prefix: 'i-',
      extraProperties: {
        display: 'inline-block',
      },
    }),
  ],
  theme: {
    colors: {
      primary: '#399e96',
      labelText: '#424242',
    },
  },
})
