{"name": "caguuu-erp-frontend", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "build:dev": "run-p type-check \"build-only:dev {@}\" --", "build:prod": "run-p type-check \"build-only:prod {@}\" --", "build:staging": "run-p type-check \"build-only:staging {@}\" --", "gene": "node ./build/swagger.generate.js", "openapi:gene": "node ./build/openapi.generate.js", "openapi:gene:dev": "NODE_ENV=development node ./build/openapi.generate.js", "openapi:gene:prod": "NODE_ENV=production node ./build/openapi.generate.js", "preview": "vite preview", "build-only": "vite build", "build-only:dev": "vite build --mode development", "build-only:prod": "vite build --mode production", "build-only:staging": "vite build --mode staging", "type-check": "vue-tsc --build", "lint": "eslint .", "lint:fix": "eslint . --fix", "test": "vitest run"}, "dependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-stencil": "^2.1.5", "@antv/x6-plugin-transform": "^2.1.8", "@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@popperjs/core": "^2.11.8", "@tinymce/tinymce-vue": "^6.1.0", "@unocss/reset": "66.1.0-beta.3", "@vueuse/core": "^12.7.0", "axios": "^1.7.9", "dayjs": "^1.11.13", "element-plus": "^2.9.4", "image-size": "^2.0.1", "jsencrypt": "^3.3.2", "mockjs": "^1.1.0", "nanoid": "^5.1.5", "pinia": "^2.3.1", "qs": "^6.14.0", "tinymce": "^7.6.1", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0", "vxe-pc-ui": "^4.4.4", "vxe-table": "4.12.5", "xe-utils": "^3.7.0"}, "devDependencies": {"@antfu/eslint-config": "^4.2.0", "@iconify-json/ep": "^1.2.2", "@iconify-json/svg-spinners": "^1.2.2", "@openapitools/openapi-generator-cli": "^2.18.4", "@tsconfig/node22": "^22.0.0", "@types/lodash-es": "^4.17.12", "@types/mockjs": "^1.0.10", "@types/node": "^22.13.1", "@types/qs": "^6.9.18", "@unocss/eslint-plugin": "^65.4.3", "@unocss/preset-rem-to-px": "^66.0.0", "@vitejs/plugin-basic-ssl": "^2.0.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vue/eslint-config-typescript": "^14.3.0", "@vue/runtime-core": "^3.5.13", "@vue/tsconfig": "^0.7.0", "dotenv": "^16.5.0", "eslint": "^9.18.0", "eslint-plugin-vue": "^9.32.0", "jiti": "^2.4.2", "jsdom": "^26.1.0", "lodash-es": "^4.17.21", "npm-run-all2": "^7.0.2", "sass": "^1.84.0", "swagger-axios-codegen": "^0.17.2", "typescript": "~5.7.3", "unocss": "^65.4.3", "unplugin-auto-import": "^19.0.0", "unplugin-icons": "^22.0.0", "unplugin-vue-components": "^28.1.0", "vite": "^6.0.11", "vite-plugin-lazy-import": "^1.0.7", "vite-plugin-mock": "^3.0.2", "vite-plugin-vue-devtools": "^7.7.1", "vitest": "^3.1.1", "vue-tsc": "^2.2.0"}}