import type { MockMethod } from 'vite-plugin-mock'
import Mock from 'mockjs'

// 工单列表查询接口
const ticketQueryMock: MockMethod = {
  url: '/dev-proxy/gaia-workflow/api/ticket-instances/query',
  method: 'post',
  response: ({ body }) => {
    console.warn('Mock API Hit: /api/ticket-instances/query', 'Request Body:', body)

    const { pageNum = 1, pageSize = 15 } = body

    const totalItems = 55

    // 使用 Mock.js 生成工单列表数据项，严格按照 TicketInstListItemVO 接口定义
    const ticketListTemplate = {
      'id|+1': (pageNum - 1) * pageSize + 1,
      'relevantId': () => `SPU-${Mock.Random.integer(10000, 99999)}`,
      'ticketDefId': () => String(Mock.Random.integer(1, 4)), // 转换为string类型以符合API定义
      'wfInstId': () => Mock.Random.integer(10000, 99999),
      'formInstId': () => Mock.Random.integer(10000, 99999),
      // priority 应该是数字类型，值越大优先级越高
      // 3: 加急, 2: 一般, 1: 暂缓
      'priority': () => Mock.Random.pick([3, 2, 1]),
      'creator': () => ({ // 严格按照 UserSimpleInfoVO 结构
        userId: Mock.Random.integer(100, 500),
        userName: Mock.Random.word(5, 10),
        name: Mock.Random.cname(),
      }),
      'status': () => Mock.Random.pick(['PROCESSING', 'COMPLETED']),
      'createTime': () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      'updateTime': () => Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
    }

    // 生成指定数量的记录
    const records = Mock.mock({
      [`records|${pageSize}`]: [ticketListTemplate],
    }).records

    // 模拟分页响应结构 PageVOTicketInstListItemVO
    const pageData = {
      pageNum,
      pageSize,
      total: totalItems,
      pages: Math.ceil(totalItems / pageSize),
      records, // 直接使用原始记录，不进行后处理
    }

    // 模拟最终响应结构 ResultVOPageVOTicketInstListItemVO
    return {
      code: 200, // 成功状态码
      message: 'Success (Mocked)',
      data: pageData,
      success: true,
    }
  },
}

// 工单详情查询接口
const ticketDetailMock: MockMethod = {
  url: '/dev-proxy/gaia-workflow/api/ticket-instances/:id',
  method: 'get',
  response: ({ query }) => {
    console.warn('Mock API Hit: /api/ticket-instances/:id', 'Params:', query)

    const id = query.id || '1'

    // 生成工单详情数据，严格按照 TaskInstDetailVO 接口定义
    const detail = {
      taskInstId: Number(id),
      ticketDefId: Mock.Random.integer(1, 4),
      relevantId: `SPU-${Mock.Random.integer(10000, 99999)}`,
      wfInstId: Mock.Random.integer(10000, 99999),
      formInstId: Mock.Random.integer(10000, 99999),
      priority: Mock.Random.pick([3, 2, 1]),
      ticketStatus: Mock.Random.pick(['PROCESSING', 'COMPLETED']),
      currentNodeId: `node_${Mock.Random.string('lower', 5)}`,
      currentNodeName: Mock.Random.pick(['信息录入', '初审', '复审', '定价审核']),
      currentNodeStatus: 'ACTIVE', // 添加节点状态字段
      currentNodeProcessor: [
        {
          userId: Mock.Random.integer(100, 500),
          userName: Mock.Random.word(5, 10),
          name: Mock.Random.cname(),
        },
      ],
      creator: {
        userId: Mock.Random.integer(100, 500),
        userName: Mock.Random.word(5, 10),
        name: Mock.Random.cname(),
      },
      createTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      updateTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      // 表单数据 - 使用JSON字符串格式
      formData: JSON.stringify({
        basicInfo: {
          title: Mock.Random.ctitle(5, 20),
          category: Mock.Random.pick(['电子产品', '服装', '家居', '食品']),
          brand: Mock.Random.pick(['品牌A', '品牌B', '品牌C']),
        },
        productInfo: {
          weight: Mock.Random.float(0.1, 10, 1, 2),
          length: Mock.Random.integer(1, 100),
          width: Mock.Random.integer(1, 100),
          height: Mock.Random.integer(1, 100),
          material: Mock.Random.pick(['塑料', '金属', '木材', '布料']),
        },
        priceInfo: {
          costPrice: Mock.Random.float(10, 1000, 1, 2),
          suggestedPrice: Mock.Random.float(20, 2000, 1, 2),
          marketPrice: Mock.Random.float(15, 1500, 1, 2),
        },
        remarks: Mock.Random.cparagraph(1, 3),
      }),
    }

    return {
      code: 200,
      message: 'Success (Mocked)',
      data: detail,
      success: true,
    }
  },
}

// 工单日志查询接口
const ticketLogsMock: MockMethod = {
  url: '/dev-proxy/gaia-workflow/api/ticket-instances/:id/logs',
  method: 'get',
  response: ({ query }) => {
    console.warn('Mock API Hit: /api/ticket-instances/:id/logs', 'Params:', query)

    const id = query.id || '1'

    // 生成3-8条日志记录，严格按照 TaskInstOperationLogVO 接口定义
    const logCount = Mock.Random.integer(3, 8)
    const logs: Array<{
      id: number
      ticketInstId: number
      nodeId: string
      nodeName: string
      opType: string
      opResult: string
      opDesc: string
      operator: {
        userId: number
        userName: string
        name: string
      }
      createTime: string
    }> = []

    for (let i = 0; i < logCount; i++) {
      logs.push({
        id: Mock.Random.integer(1000, 9999),
        ticketInstId: Number(id),
        nodeId: `node_${Mock.Random.string('lower', 5)}`,
        nodeName: Mock.Random.pick(['信息录入', '初审', '复审', '定价审核']),
        opType: Mock.Random.pick(['CREATE', 'SUBMIT', 'ACCEPT', 'APPROVE', 'TRANSFER', 'COMPLETE']),
        opResult: Mock.Random.pick(['', 'APPROVED', 'REJECTED']),
        opDesc: Mock.Random.csentence(5, 20),
        operator: {
          userId: Mock.Random.integer(100, 500),
          userName: Mock.Random.word(5, 10),
          name: Mock.Random.cname(),
        },
        createTime: Mock.Random.datetime('yyyy-MM-dd HH:mm:ss'),
      })
    }

    // 按时间排序，最新的在前面
    logs.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime())

    // 构造符合 ResultVOListTaskInstOperationLogVO 接口的响应数据
    return {
      code: 200,
      message: 'Success (Mocked)',
      data: logs,
      success: true,
    }
  },
}

// 工单表单配置查询接口
const ticketFormConfigMock: MockMethod = {
  url: '/dev-proxy/gaia-workflow/api/ticket-definitions/:id',
  method: 'get',
  response: ({ query }) => {
    console.warn('Mock API Hit: /api/ticket-definitions/:id', 'Params:', query)

    const id = query.id || '1'

    // 生成表单配置数据
    const formConfig = {
      id: Number(id),
      name: `工单类型${id}`,
      code: `TICKET_TYPE_${id}`,
      desc: `这是工单类型${id}的描述信息`,
      ticketRelatedFormDef: {
        id: Mock.Random.integer(1000, 9999),
        name: '产品信息表单',
        code: 'PRODUCT_FORM',
        desc: '用于录入产品基本信息',
        schema: JSON.stringify([
          [
            {
              type: 'input',
              key: 'spu',
              label: 'SPU',
              span: 8,
              props: {},
              required: true,
            },
            {
              type: 'input',
              key: 'sku',
              label: 'SKU',
              span: 8,
              props: {},
            },
          ],
          [
            {
              label: '本地化',
              key: 'id',
              type: 'linkButton',
              span: 6,
              props: {
                to: {
                  name: 'ProductDetail',
                },
                text: '产品详情页',
                type: 'primary',
                link: true,
              },
            },
          ],
          [
            {
              label: '本地化素材',
              key: 'productImages',
              type: 'imageManager',
              span: 24,
              props: {
                uploadStrategy: {},
                skuList: [
                  'SKU001',
                  'SKU002',
                  'SKU003',
                ],
              },
              required: true,
            },
          ],
          [
            {
              type: 'inputNumber',
              key: 'manualImageCount',
              label: '安装说明书图片数量',
              span: 8,
              props: {},
              required: true,
            },
            {
              type: 'inputNumber',
              key: 'workCount',
              label: '工作总数',
              span: 8,
              props: {},
            },
          ],
          [
            {
              type: 'input',
              key: 'psdLink',
              label: 'PSD链接',
              span: 16,
              props: {},
              required: true,
            },
          ],
        ]),
      },
      nodeRelatedFormDefList: [],
      formFieldAuthList: [],
    }

    return {
      code: 200,
      message: 'Success (Mocked)',
      data: formConfig,
      success: true,
    }
  },
}

// 工单处理接口（接受、转移、完成等操作）
const ticketProcessMock: MockMethod = {
  url: '/dev-proxy/gaia-workflow/api/ticket-instances/:id/process',
  method: 'post',
  response: ({ query, body }) => {
    console.warn('Mock API Hit: /api/ticket-instances/:id/process', 'Params:', query, 'Body:', body)

    // 验证请求体是否符合 TicketInstProcessReq 接口
    const { opType, opResult, nodeId } = body

    // 检查必填字段
    if (!opType || !nodeId) {
      return {
        code: 400,
        message: '缺少必填字段 opType 或 nodeId',
        data: null,
        success: false,
      }
    }

    // 根据不同操作类型返回不同结果
    let message = 'Success (Mocked)'

    switch (opType) {
      case 'ACCEPT':
        message = '工单接受成功'
        break
      case 'TRANSFER':
        if (!opResult) {
          return {
            code: 400,
            message: 'TRANSFER 操作需要提供 opResult 字段指定被转交人userId',
            data: null,
            success: false,
          }
        }
        message = '工单转移成功'
        break
      case 'APPROVE':
        if (!opResult || !['APPROVED', 'REJECTED'].includes(opResult)) {
          return {
            code: 400,
            message: 'APPROVE 操作需要提供 opResult 字段，值为 APPROVED 或 REJECTED',
            data: null,
            success: false,
          }
        }
        message = `工单审批${opResult === 'APPROVED' ? '通过' : '拒绝'}成功`
        break
      case 'SUBMIT':
        message = '表单提交成功'
        break
      case 'COMPLETE':
        message = '工单完成成功'
        break
      default:
        message = `操作 ${opType} 成功`
    }

    return {
      code: 200,
      message,
      data: null,
      success: true,
    }
  },
}

// 工单状态统计接口
const ticketStatusCountMock: MockMethod = {
  url: '/dev-proxy/gaia-workflow/api/ticket-instances/count-by-status',
  method: 'post',
  response: ({ body }) => {
    console.warn('Mock API Hit: /api/ticket-instances/count-by-status', 'Request Body:', body)

    // 从请求体中获取用户ID
    const { userId } = body

    // 生成符合 TicketInstStatusCountResultVO 接口的响应数据
    const statusCountResult = {
      userId: userId ? userId[0] : undefined, // 取第一个用户ID，如果有的话
      count: {
        TO_BE_PROCESSED: Mock.Random.integer(5, 30),
        PROCESSED_BEFORE: Mock.Random.integer(10, 50),
        COMPLETED: Mock.Random.integer(3, 15),
      },
    }

    return {
      code: 200,
      message: 'Success (Mocked)',
      data: statusCountResult,
      success: true,
    }
  },
}

export default [
  ticketQueryMock,
  ticketDetailMock,
  ticketLogsMock,
  ticketFormConfigMock,
  ticketProcessMock,
  ticketStatusCountMock,
  // ticketFormOptionsMock, // 这个接口在实际的API定义中不存在，已经注释掉
]
