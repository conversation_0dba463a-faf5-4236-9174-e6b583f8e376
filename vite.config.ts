import { resolve } from 'node:path'
import process from 'node:process'
import { fileURLToPath, URL } from 'node:url'

// import basicSsl from '@vitejs/plugin-basic-ssl'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'

import UnoCSS from 'unocss/vite'
import AutoImport from 'unplugin-auto-import/vite'

import IconsResolver from 'unplugin-icons/resolver'
import Icons from 'unplugin-icons/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Components from 'unplugin-vue-components/vite'

import { defineConfig, loadEnv } from 'vite'

import { lazyImport, VxeResolver } from 'vite-plugin-lazy-import'
import { viteMockServe } from 'vite-plugin-mock'

import vueDevTools from 'vite-plugin-vue-devtools'

const pathSrc = fileURLToPath(new URL('./src', import.meta.url))
// https://vite.dev/config/
export default defineConfig((config) => {
  const { mode } = config
  const env = loadEnv(mode, process.cwd())

  return {
    base: env.VITE_PUBLIC_PATH || '/',
    server: {
      port: 5173,
      host: '0.0.0.0',
      // allowedHosts: ['gaia-test.caguuu.cn'],
      proxy: {
        '/dev-proxy': {
          target: env.VITE_API_BASE_URL,
          changeOrigin: true, // 需要虚拟主机站点
          secure: false,
          rewrite: path => path.replace(/^\/dev-proxy/, ''),
        },
      },
    },
    build: {
      // 启用 sourcemap 以便于调试
      sourcemap: env.VITE_USE_SOURCE_MAP === 'true',
      // 配置生产环境删除console
      terserOptions: {
        compress: {
          drop_console: env.VITE_DROP_CONSOLE === 'true',
          drop_debugger: true,
        },
      },
    },
    plugins: [
      vue(),
      vueJsx(),
      env.VITE_USE_DEV_TOOLS === 'true' ? vueDevTools() : null,
      UnoCSS(),
      Icons({
        compiler: 'vue3', // 指定编译器
        autoInstall: true, // 自动安装
      }),
      AutoImport({
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
          /\.md$/, // .md
        ],
        imports: ['vue', '@vueuse/core', 'pinia', 'vue-router'],
        dts: resolve(pathSrc, 'auto-imports.d.ts'),
        eslintrc: {
          enabled: false, // Default `false`
          filepath: './.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
          globalsPropValue: true, // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        },
        resolvers: [
          IconsResolver({
          // prefix: 'icon',
          // extension: "jsx"
          }),
          ElementPlusResolver({
            importStyle: 'sass',
          }),
        ],
      }),
      Components({
        extensions: ['vue'],
        dirs: [],
        include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/],
        resolvers: [
        // 自动注册图标组件
          IconsResolver({
          // prefix: 'icon',
            enabledCollections: ['ep', 'ci', 'mdi'],
          }),
          ElementPlusResolver({
            importStyle: 'sass',
          }),
        ],
        dts: resolve(pathSrc, 'components.d.ts'),
      }),
      viteMockServe({
        mockPath: './mock',
        watchFiles: true,
        enable: false,
        logger: true,
      }),
      lazyImport({
        resolvers: [
          VxeResolver({
            libraryName: 'vxe-table',
          }),
          VxeResolver({
            libraryName: 'vxe-pc-ui',
          }),
        ],
      }),
      // basicSsl(),
    ].filter(Boolean),
    resolve: {
      alias: {
        '@': pathSrc,
        '~/': `${pathSrc}/`,
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "~/styles/cg-variables.scss" as *;`,
        },
      },
    },
  }
})
