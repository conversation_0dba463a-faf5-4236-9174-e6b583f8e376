import { useTagViewStore } from '@/stores/TagsView'
import { useRoute, useRouter } from 'vue-router'

/**
 * 标签页关闭功能
 * 提供关闭当前标签页并导航到上一个路由的功能
 */
export function useTagsViewClose() {
  const route = useRoute()
  const router = useRouter()
  const tagViewStore = useTagViewStore()

  /**
   * 关闭当前页面并从标签视图中移除
   */
  function closeCurrentPage() {
    return tagViewStore.delView(route).then(() => {
      router.back()
    })
  }

  /**
   * 关闭指定页面并导航到目标路由
   * @param targetRoute 要关闭的路由
   * @param navigateTo 导航目标，默认为返回上一页
   */
  function closePage(targetRoute = route, navigateTo?: string | object) {
    return tagViewStore.delView(targetRoute).then(() => {
      if (navigateTo) {
        router.push(navigateTo)
      }
      else {
        router.back()
      }
    })
  }

  return {
    closeCurrentPage,
    closePage,
  }
}
