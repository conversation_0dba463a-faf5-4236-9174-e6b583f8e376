interface FileChooserOptions {
  accept?: string
  multiple?: boolean
  disabled?: boolean
}

/**
 * 文件选择工具（组合式 API）
 *
 * @returns openFileChooser  触发文件选择
 *          choosing         正在等待用户操作
 */
export function useFileChooser() {
  const choosing = ref(false)
  let inputEl: HTMLInputElement | null = null

  async function triggerInput(opts: FileChooserOptions): Promise<FileList> {
    if (opts.disabled)
      throw new Error('disabled')
    if (choosing.value)
      throw new Error('choosing')
    choosing.value = true

    if ('showOpenFilePicker' in window) {
      try {
        // 处理文件类型，将逗号分隔的扩展名转换为正确的格式
        let acceptTypes
        if (opts.accept) {
          const extensions = opts.accept.split(',').map(ext => ext.trim())
          const mimeTypes: Record<string, string[]> = {}

          // 为每个扩展名创建适当的 MIME 类型映射
          extensions.forEach((ext) => {
            const cleanExt = ext.startsWith('.') ? ext.substring(1) : ext

            let mimeType
            switch (cleanExt.toLowerCase()) {
              case 'zip': mimeType = 'application/zip'
                break
              case 'rar': mimeType = 'application/vnd.rar'
                break
              case '7z': mimeType = 'application/x-7z-compressed'
                break
              default: mimeType = 'application/octet-stream'
            }

            mimeTypes[mimeType] = mimeTypes[mimeType] || []
            mimeTypes[mimeType].push(ext)
          })

          acceptTypes = [{
            description: '压缩文件',
            accept: mimeTypes,
          }]
        }

        const handles = await (window as any).showOpenFilePicker({
          multiple: !!opts.multiple,
          types: acceptTypes,
        })
        const dt = new DataTransfer()
        for (const h of handles) {
          dt.items.add(await h.getFile())
        }
        return dt.files
      }
      catch (e: any) {
        if (e?.name === 'AbortError')
          throw new Error('user-cancelled')
        throw e
      }
      finally {
        choosing.value = false
      }
    }

    return new Promise<FileList>((resolve, reject) => {
      inputEl = document.createElement('input')
      inputEl.type = 'file'
      inputEl.accept = opts.accept ?? '*'
      inputEl.multiple = !!opts.multiple
      inputEl.style.display = 'none'

      const onChange = () => {
        choosing.value = false
        if (inputEl!.files && inputEl!.files.length) {
          resolve(inputEl!.files)
        }
        else {
          reject(new Error('user-cancelled'))
        }
        cleanup()
      }

      const onDialogClose = () => {
        if (choosing.value) {
          choosing.value = false
          reject(new Error('user-cancelled'))
          cleanup()
        }
      }

      inputEl.addEventListener('change', onChange, { once: true })
      window.addEventListener('focus', onDialogClose, { once: true })

      document.body.appendChild(inputEl)
      inputEl.click()
    })
  }

  function cleanup() {
    if (inputEl) {
      inputEl.remove()
      inputEl = null
    }
  }

  // 组件销毁前确保清理
  onBeforeUnmount(cleanup)

  const openFileChooser = (opts: FileChooserOptions = {}) => triggerInput(opts)

  return { openFileChooser, choosing }
}
