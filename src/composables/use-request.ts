import type { BaseResponse, ProxyOption } from '@/components/utils'
import type { Ref, ShallowRef } from 'vue'
import { GlobalConfig } from '@/components/utils'
import { until } from '@vueuse/core'
import { ref, shallowRef } from 'vue'
import { isArray, isFunction } from 'xe-utils'

export interface UseRequestReturn<T = BaseResponse, R = any> {
  response: ShallowRef<T | undefined>
  data: Ref<R | undefined>
  isFinished: Ref<boolean>
  isLoading: Ref<boolean>
  execute: () => Promise<UseRequestReturn>
  error: ShallowRef<unknown | undefined>
}

export function useRequest(proxyOption: ProxyOption, _params?: Record<string, any> | any[], option?: { immediate: boolean }) {
  const response = shallowRef<BaseResponse | undefined>(undefined)
  const data = ref<any>([])
  const isLoading = ref(false)
  const isFinished = ref(false)
  const error = shallowRef<unknown>()

  if (!option) {
    option = {
      immediate: true,
    }
  }

  const loading = (loading: boolean) => {
    isLoading.value = loading
    isFinished.value = !loading
  }

  let execute: () => Promise<UseRequestReturn>

  const waitUntilFinished = () =>
    new Promise<UseRequestReturn>((resolve, reject) => {
      until(isFinished)
        .toBe(true)
        .then(() => {
          if (error.value) {
            reject(error.value)
          }
          else {
            resolve({
              response,
              data,
              error,
              isFinished,
              isLoading,
              execute,
            })
          }
        })
    })

  const promise = {
    then: (...args) => waitUntilFinished().then(...args),
    catch: (...args) => waitUntilFinished().catch(...args),
  } as Promise<UseRequestReturn>

  const { request, query = {}, postParam } = proxyOption || {}
  execute = (): Promise<UseRequestReturn> => {
    error.value = undefined
    if (!request) {
      response.value = {
        IsSuccess: false,
        Message: 'request 请求函数为空!',
      } as BaseResponse
      return promise
    }
    loading(true)
    let params: any = null
    if (isArray(_params))
      params = _params
    else params = Object.assign({}, _params, query)

    if (isFunction(GlobalConfig.adapter?.httpRequest))
      params = GlobalConfig.adapter?.httpRequest(params, postParam)

    request(params)
      .then((_response) => {
        const realResponse = _response?.data || {}
        if (isFunction(GlobalConfig.adapter?.httpResponse))
          response.value = GlobalConfig.adapter?.httpResponse(realResponse)
        else response.value = realResponse
        if (response.value?.IsSuccess)
          data.value = response.value!.Data
      })
      .catch((err) => {
        error.value = err
      })
      .finally(() => loading(false))

    return promise
  }

  if (option.immediate)
    execute()

  const result = {
    response,
    data,
    error,
    isFinished,
    isLoading,
    execute,
  } as UseRequestReturn

  return {
    ...result,
    ...promise,
  }
}
