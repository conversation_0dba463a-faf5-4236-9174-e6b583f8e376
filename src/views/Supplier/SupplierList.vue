<script setup lang="ts">
import type { SupplierPageRequest } from '@/apiv2/supplier'
import type { PageListInstance, PageListProps } from '@/components/CgPageList'
import supplierApi from '@/api.services/supplier.service'
import ComponentTagName from '@/common/component-tag-name'
import { GridCellRenderName } from '@/components/CgGrid'
import { SupplierOperationEnum } from '@/enums/supplier'

defineOptions({
  name: 'SupplierList',
})

const pageListRef = ref<PageListInstance | null>(null)
const queryModel = ref<SupplierPageRequest>({
  pageNum: 1,
  pageSize: 20,
  status: undefined,
  cooperationMode: undefined,
  brandFlag: undefined,
  firstCategory: undefined,
  shortName: undefined,
  contactName: undefined,
  mobile: undefined,
})

const gridOptions = reactive<PageListProps>({
  gridOption: {
    options: {
      columns: [
        {
          field: 'id',
          type: 'checkbox',
          width: 36,
          fixed: 'left',
          resizable: false,
        },
        {
          field: 'shortName',
          title: '供应商简称',
          minWidth: 120,
        },
        {
          field: 'supplierCode',
          title: '供应商编码',
          width: 120,
        },
        {
          field: 'brandFlag',
          title: '有无品牌',
          width: 120,
        },
        {
          field: 'cooperationMode',
          title: '合作模式',
          width: 120,
        },
        {
          field: 'firstCategory',
          title: '一级分类',
          width: 120,
        },
        {
          field: 'secondCategory',
          title: '二级分类',
          width: 120,
        },
        {
          field: 'rating',
          title: '供应商评级',
          width: 120,
        },
        {
          field: 'status',
          title: '状态',
          width: 120,
        },
        {
          field: 'responsibleUserId',
          title: '跟进负责人',
          width: 120,
        },
        {
          field: 'updatedTime',
          title: '更新时间',
          width: 120,
        },
        {
          field: 'operator',
          title: '操作',
          width: 110,
          align: 'center',
          headerAlign: 'center',
          fixed: 'right',
          cellRender: {
            name: GridCellRenderName.Operate,
            props: {
              items: [
                {
                  text: '详情',
                  id: SupplierOperationEnum.VIEW_DETAIL,
                },
                {
                  text: '操作',
                  items: [
                    {
                      text: '编辑',
                      id: SupplierOperationEnum.EDIT,
                    },
                    {
                      text: '生成工单',
                      id: SupplierOperationEnum.GENERATE_WORK_ORDER,
                    },
                    {
                      text: '删除',
                      id: SupplierOperationEnum.DELETE,
                    },
                    {
                      text: '下架',
                      id: SupplierOperationEnum.TAKE_OFF_SHELF,
                    },
                    {
                      text: '上架',
                      id: SupplierOperationEnum.PUT_ON_SHELF,
                    },
                    {
                      text: '操作日志',
                      id: SupplierOperationEnum.OPERATE_LOG,
                    },
                  ],
                },
              ],
            },
            events: { command: () => {} },
          },
        },
      ],
      round: true,
      showOverflow: true,
      // cellConfig: {
      //   padding: false,
      // },
    },
    toolbarConfig: {
      exportFile: {
        exportProxy: {
          request: () => {
            // TODO
            return new Promise(() => { })
          },
        },
        // teleport: '.cg-page-header__right',
      },
      refresh: true,
      buttons: [
        {
          text: '添加供应商',
          on: {
            click: () => {

            },
          },
        },
        {
          text: '批量删除',
          on: {
            click: () => {

            },
          },
        },
      ],
    },
    proxyOption: {
      request: supplierApi.listSuppliers,
      postParam: 'supplierPageRequest',
    },
  },
  maxQueryFormVisibleCount: 8,
  searchMoreDisplayMode: 'icon',
  queryForm: [
    {
      component: ComponentTagName.SelectV2,
      vmodel: 'status',
      props: {
        data: [
          { label: '已上架', value: 3 },
          { label: '已下架', value: 4 },
        ],
        placeholder: '状态',
      },
    },

    {
      component: ComponentTagName.SelectV2,
      vmodel: 'brandFlag',
      props: {
        data: [
          { label: '有', value: true },
          { label: '无', value: false },
        ],
        placeholder: '有无品牌',
      },
    },
  ],
  autoLoad: true,
})
</script>

<template>
  <div class="h-full flex flex-col">
    <CgPageList ref="pageListRef" v-model:query-model="queryModel" v-bind="gridOptions" />
  </div>
</template>

<style scoped>

</style>
