<script setup lang="ts">
defineOptions({
  name: 'SubmitDialog',
})

const props = defineProps<{
  visible: boolean
  ticketId?: number | string
  spuCode?: string
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'confirm': [payload: { description: string }, done: () => void]
}>()

const description = ref('')

watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    description.value = ''
  }
})

function closeDialog() {
  emit('update:visible', false)
}

function confirmSubmit(done: () => void) {
  const payload = {
    description: description.value,
  }

  emit('confirm', payload, done)
}
</script>

<template>
  <CgDialog
    :model-value="visible" title="提交工单" width="480px" :full-height="false" confirm-button-text="确定"
    @update:model-value="emit('update:visible', $event)" @submit="confirmSubmit" @close="closeDialog"
  >
    <div>
      <!-- 工单信息区域 -->
      <div class="mb-6 rounded-md bg-#e8f7f6 p-4">
        <div class="mb-3 flex items-center">
          <span class="w-16 text-right text-gray-500 font-medium">工单ID：</span>
          <span class="text-gray-800 font-medium">{{ ticketId || '-' }}</span>
        </div>
        <div class="flex items-center">
          <span class="w-16 text-right text-gray-500 font-medium">SPU：</span>
          <span class="text-gray-800 font-medium">{{ spuCode || '-' }}</span>
        </div>
      </div>

      <!-- 备注区域 -->
      <div>
        <div class="mb-2 flex items-center">
          <span class="text-gray-700 font-medium">备注</span>
        </div>
        <CgInput
          v-model="description"
          type="textarea"
          :rows="3"
          placeholder="请输入提交备注"
          class="w-full"
        />
      </div>
    </div>
  </CgDialog>
</template>

<style lang="scss" scoped>
:deep(.el-dialog__header) {
  padding: 16px 20px;
  margin-right: 0;
  border-bottom: 1px solid #f0f0f0;
}

:deep(.el-dialog__body) {
  padding: 0;
}

:deep(.el-dialog__headerbtn) {
  top: 16px;
  right: 16px;
}
</style>
