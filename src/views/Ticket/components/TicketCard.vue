<script setup lang="ts">
import type { ExtendedTicketInstListItemVO } from '@/stores/Ticket'
import { useTicketStore } from '@/stores/Ticket'
import { formatDateTime, getPriorityTagType, getPriorityText } from '../utils/formatters'

defineOptions({
  name: 'TicketCard',
})

const props = defineProps<{
  ticket: ExtendedTicketInstListItemVO
  isSelected: boolean
}>()

const emit = defineEmits<{
  select: [id: string | number]
}>()

const ticketStore = useTicketStore()

function getTicketTypeName(ticketDefId?: string | number): string {
  if (!ticketDefId)
    return '工单'

  // 从缓存中获取工单类型定义
  const cacheKey = ticketDefId.toString()
  const cachedDef = ticketStore.cachedTicketDefs[cacheKey]

  if (cachedDef && cachedDef.name) {
    return cachedDef.name
  }

  return `工单${ticketDefId}`
}

function handleSelect() {
  if (props.ticket.id !== undefined) {
    emit('select', props.ticket.id)
  }
}
</script>

<template>
  <div
    class="relative mb-3 cursor-pointer overflow-hidden rounded-lg p-1.5" :class="[
      isSelected
        ? 'border border-primary bg-white shadow-md'
        : 'border border-gray-200 bg-white hover:shadow',
    ]" @click="handleSelect"
  >
    <div v-if="isSelected" class="absolute right-2 top-2 h-2 w-2 rounded-full bg-primary" />

    <!-- Card Header - 工单类型 -->
    <div class="mb-1.5 flex items-center justify-between border-b border-gray-100 pb-1.5">
      <div class="truncate text-primary font-medium">
        {{ ticket.ticketDefName }}
      </div>
    </div>

    <!-- Card Body - 工单内容 -->
    <div class="flex flex-col gap-1.5">
      <div class="flex items-baseline justify-between gap-2">
        <span class="text-gray-800 font-medium">工单ID: {{ ticket.id }}</span>
        <ElTag :type="getPriorityTagType(ticket.priority)" size="small" class="flex-shrink-0" disable-transitions>
          {{ getPriorityText(ticket.priority) }}
        </ElTag>
      </div>

      <div>
        <span class="text-xs text-gray-500">关联编码: {{ ticket.relevantObjId || '-' }}</span>
      </div>

      <div class="mt-0.5 flex justify-between text-xs text-gray-400">
        <span class="max-w-[50%] truncate">创建人: {{ ticket.creator?.name || '' }}</span>
        <span class="whitespace-nowrap">创建日期: {{ formatDateTime(ticket.createTime) }}</span>
      </div>
    </div>
  </div>
</template>
