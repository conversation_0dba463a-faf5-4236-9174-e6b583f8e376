<script setup lang="ts">
import type { FormDefVO, TicketDefDetailVO, TicketInstStartReqVO } from '@/apiv2/workflow/models'
import type CgForm from '@/components/CgElementUI/CgForm'
import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'
import { ticketApi, ticketConfigApi } from '@/api.services/workflow.service'
import ComponentTagsEnum from '@/components/CgElementUI/CgForm/ComponentTagsEnum'
import { useTagViewStore } from '@/stores/TagsView'
import { useRoute, useRouter } from 'vue-router'

interface TicketFormData {
  priority?: number
  ticketType?: number
  [key: string]: any
}

defineOptions({
  name: 'ServiceTicket',
})

const emit = defineEmits(['submitSuccess'])
const router = useRouter()
const baseFormRef = ref<InstanceType<typeof CgForm>>()
const contentFormRef = ref<InstanceType<typeof CgForm>>()
const loading = ref(false)
const loadingSchema = ref(false)

const formData = ref<TicketFormData>({
  priority: 3,
})
const contentFormData = ref<Record<string, any>>({})

// 基本信息表单配置
const baseFormOptions = ref<CgFormOption[][]>([
  [
    {
      label: '基本信息',
      type: 'split',
      span: 24,
    },
  ],
  [
    {
      label: '工单类型',
      key: 'ticketType',
      type: ComponentTagsEnum.SelectV2,
      span: 12,
      required: true,
      props: {
        placeholder: '请选择工单类型',
        proxyOption: {
          request: ticketConfigApi.queryTicketTypeList,
        },
        props: {
          label: 'name',
          value: 'id',
        },
      },
      events: {
        change: handleTicketTypeChange,
      },
    },
    {
      label: '优先级',
      key: 'priority',
      type: ComponentTagsEnum.SelectV2,
      span: 12,
      required: true,
      props: {
        placeholder: '请选择优先级',
        data: [
          { label: '加急', value: 3 },
          { label: '一般', value: 2 },
          { label: '暂缓', value: 1 },
        ],
      },
    },
  ],
])

// 工单内容动态表单配置
const contentFormOptions = ref<CgFormOption[][]>([])
const currentTicketDef = ref<TicketDefDetailVO>()

// 工单类型切换时动态加载内容表单schema
async function handleTicketTypeChange(ticketTypeId: number) {
  if (!ticketTypeId) {
    contentFormOptions.value = []
    contentFormData.value = {}
    return
  }

  try {
    loadingSchema.value = true
    // 获取工单类型的表单schema
    const response = await ticketConfigApi.getDetailById1({ ticketDefId: ticketTypeId })
    if (response.data.success && response.data.data) {
      const ticketDef: TicketDefDetailVO = response.data.data
      // 保存当前工单定义详情，用于后续提交
      currentTicketDef.value = ticketDef
      const formDef: FormDefVO | undefined = ticketDef.ticketRelatedFormDef
      // 字段权限处理
      const formFieldAuthList = ticketDef.formFieldAuthList || []
      // INIT_NODE节点权限
      const initNodeAuth = formFieldAuthList.find(auth => auth.wfNodeId === 'INIT_NODE')
      let fieldAuthInfo: Record<string, { editable: boolean, visible: boolean }> = {}

      // 字段权限解析
      if (initNodeAuth && initNodeAuth.formFieldAuthInfo) {
        try {
          fieldAuthInfo = JSON.parse(initNodeAuth.formFieldAuthInfo)
        }
        catch (e) {
          console.error('解析表单字段权限配置失败', e)
        }
      }

      if (formDef && formDef.schema) {
        try {
          // schema转CgForm格式
          const schemaObj = JSON.parse(formDef.schema)
          contentFormOptions.value = applyFieldAuthToOptions(schemaObj, fieldAuthInfo)

          contentFormData.value = {}
        }
        catch (e) {
          console.error('解析表单schema失败', e)
          contentFormOptions.value = []
        }
      }
      else {
        contentFormData.value = {}
      }
    }
    else {
      contentFormOptions.value = []
    }
  }
  catch (error) {
    console.error('获取工单类型表单失败', error)
    contentFormOptions.value = []
  }
  finally {
    loadingSchema.value = false
  }
}

// 字段权限应用
function applyFieldAuthToOptions(
  options: CgFormOption[][],
  fieldAuthInfo: Record<string, { editable: boolean, visible: boolean }>,
): CgFormOption[][] {
  if (!fieldAuthInfo || Object.keys(fieldAuthInfo).length === 0) {
    return options
  }

  return options.map((row) => {
    // 字段可见性与只读处理
    return row.filter((option) => {
      // 分隔符或无key保留
      if (!option.key || option.type === 'split') {
        return true
      }

      const fieldAuth = fieldAuthInfo[option.key]
      // 默认可见
      if (!fieldAuth || fieldAuth.visible !== false) {
        // 只读字段处理
        if (fieldAuth && !fieldAuth.editable) {
          if (!option.props) {
            option.props = {}
          }
          else if (typeof option.props === 'function') {
            const originalProps = option.props
            option.props = (model, context) => {
              const props = originalProps(model, context)
              return { ...props, disabled: true }
            }
            return true
          }

          if (typeof option.props === 'object') {
            option.props.disabled = true
          }
        }
        return true
      }
      return false
    })
  }).filter(row => row.length > 0) // 过滤掉空行
}

const tagViewStore = useTagViewStore()
const route = useRoute()

// 返回上一页
function handleClose() {
  tagViewStore.delView(route)
  router.back()
}

async function handleSubmit() {
  if (!baseFormRef.value || !contentFormRef.value)
    return

  try {
    loading.value = true
    // 校验基本表单
    const baseValid = await baseFormRef.value.validate()
    if (!baseValid) {
      loading.value = false
      return
    }

    // 校验内容表单
    const contentValid = await contentFormRef.value.validate()
    if (!contentValid) {
      loading.value = false
      return
    }

    // 合并表单数据
    const mergedFormData = {
      ...formData.value,
      ...contentFormData.value,
    }

    // category字段格式化
    const categoryStr = Array.isArray(mergedFormData.category)
      ? mergedFormData.category.join('、')
      : mergedFormData.category || ''

    if (!currentTicketDef.value?.code) {
      ElMessage.error('无工单编码')
      return
    }

    const requestData: TicketInstStartReqVO = {
      // 必填字段
      ticketDefCode: currentTicketDef.value?.code,
      formData: JSON.stringify({ ...mergedFormData, category: categoryStr }),

      // 可选字段
      priority: mergedFormData.priority,
      relevantObjId: mergedFormData.spu,
    }

    const response = await ticketApi.startWorkflowInstance({
      ticketInstStartReqVO: requestData,
    })

    if (response.data.success) {
      ElMessage.success('工单创建成功')
      emit('submitSuccess', response.data.data)
      // 重置表单数据
      formData.value = {}
      contentFormData.value = {}
      // 成功后返回
      handleClose()
    }
    loading.value = false
  }
  catch (error) {
    console.error('创建工单失败', error)
    loading.value = false
  }
}
</script>

<template>
  <div class="after-sales-page p-6">
    <div class="form-container rounded-md bg-white p-6">
      <h2 class="mb-6 text-xl text-gray-800 font-bold">
        服务工单
      </h2>

      <!-- 基本类型表单 -->
      <CgForm ref="baseFormRef" v-model="formData" :options="baseFormOptions" />

      <!-- 工单内容表单 -->
      <div v-loading="loadingSchema" element-loading-text="正在加载表单...">
        <div v-if="contentFormOptions.length > 0">
          <CgForm ref="contentFormRef" v-model="contentFormData" :options="contentFormOptions" />
        </div>
        <div v-else-if="formData.ticketType && !loadingSchema" class="empty-form-tip p-4 text-center text-gray-500">
          该工单类型暂无表单配置
        </div>
        <div v-else-if="!formData.ticketType" class="empty-form-tip p-4 text-center text-gray-500">
          请先选择工单类型
        </div>
      </div>

      <div class="button-container mt-6 flex justify-end gap-4">
        <el-button @click="handleClose">
          关闭
        </el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">
          提交
        </el-button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.after-sales-page {
  min-height: calc(100vh - 64px);
  background-color: #f5f7fa;
}

.form-container {
  max-width: 1200px;
  margin: 0 auto;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
</style>
