import type { FormFieldAuthVO } from '@/apiv2/workflow/models'
import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'

/**
 * 解析表单字段权限配置
 * @param authList 字段权限配置列表
 * @param currentNodeId 当前节点ID
 * @returns 解析后的权限配置对象，key为字段名，value为权限配置
 */
export function parseFieldAuth(authList: FormFieldAuthVO[] = [], currentNodeId: string): Record<string, any> {
  if (!authList || !authList.length || !currentNodeId) {
    return {}
  }

  // 查找当前节点的权限配置
  const nodeAuth = authList.find(auth => auth.wfNodeId === currentNodeId)
  if (!nodeAuth || !nodeAuth.formFieldAuthInfo) {
    return {}
  }

  try {
    return JSON.parse(nodeAuth.formFieldAuthInfo)
  }
  catch (error) {
    console.error('解析表单字段权限配置失败:', error)
    return {}
  }
}

/**
 * 将表单Schema转换为CgForm组件所需的配置格式
 * @param schemaJson 表单Schema的JSON字符串
 * @param authList 字段权限配置列表
 * @param currentNodeId 当前节点ID
 * @returns CgForm组件所需的配置格式
 */
export function transformSchemaToCgOptions(
  schemaJson: string,
  authList: FormFieldAuthVO[] = [],
  currentNodeId: string,
): CgFormOption[][] {
  if (!schemaJson) {
    return []
  }

  try {
    const schema = JSON.parse(schemaJson)

    if (Array.isArray(schema) && schema.every(row => Array.isArray(row))) {
      const fieldAuth = parseFieldAuth(authList, currentNodeId)
      return applyFieldAuthToOptions(schema, fieldAuth)
    }
    return []
  }
  catch (error) {
    console.error('解析表单Schema失败:', error)
    return []
  }
}

/**
 * 将权限配置应用到表单选项
 * @param options 表单选项
 * @param fieldAuth 字段权限配置
 * @returns 应用权限后的表单选项
 */
function applyFieldAuthToOptions(options: CgFormOption[][], fieldAuth: Record<string, any>): CgFormOption[][] {
  if (!options || !options.length || !fieldAuth || Object.keys(fieldAuth).length === 0) {
    return options
  }

  return options.map((row) => {
    return row.map((option) => {
      if (!option.key) {
        return option
      }

      const auth = fieldAuth[option.key]
      if (!auth) {
        return option
      }

      // 复制选项，避免修改原对象
      const newOption = { ...option }

      // 应用权限配置
      // 假设权限配置格式为 { visible: boolean, editable: boolean, required: boolean }
      if (auth.visible === false) {
        newOption.ifRender = () => false
      }
      else if (auth.editable === false) {
        newOption.props = {
          ...(newOption.props || {}),
          disabled: true,
        }
      }

      // 处理必填属性
      if (auth.required !== undefined) {
        newOption.required = auth.required

        if (newOption.rules && Array.isArray(newOption.rules)) {
          const requiredRuleIndex = newOption.rules.findIndex(rule =>
            typeof rule === 'object' && rule.required !== undefined,
          )

          if (requiredRuleIndex >= 0 && !auth.required) {
            // 移除必填规则
            newOption.rules.splice(requiredRuleIndex, 1)
          }
          else if (requiredRuleIndex < 0 && auth.required) {
            // 添加必填规则
            newOption.rules.push({
              required: true,
              message: `请输入${newOption.label}`,
              trigger: ['blur', 'change'],
            })
          }
        }
      }

      return newOption
    })
  })
}

/**
 * 解析表单数据
 * @param formDataStr 表单数据的JSON字符串
 * @returns 解析后的表单数据对象
 */
export function parseFormData(formDataStr?: string): Record<string, any> {
  if (!formDataStr) {
    return {}
  }

  try {
    return JSON.parse(formDataStr)
  }
  catch (error) {
    console.error('解析表单数据失败:', error)
    return {}
  }
}
