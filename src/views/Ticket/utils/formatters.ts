import {
  TicketInstDetailVOCurrentNodeStatusEnum,
  TicketInstProcessReqVOOpResultEnum,
  TicketInstProcessReqVOOpTypeEnum,
} from '@/apiv2/workflow/models'
import dayjs from 'dayjs'

/**
 * 根据优先级返回对应的标签类型
 * @param priority 优先级值
 * @returns 对应的Element Plus标签类型
 */
export function getPriorityTagType(priority?: number): 'danger' | 'warning' | 'success' | 'info' {
  if (!priority)
    return 'info'

  switch (priority as number) {
    case 3: return 'danger' // 加急
    case 2: return 'warning' // 一般
    case 1: return 'success' // 暂缓
    default: return 'info'
  }
}

/**
 * 根据优先级返回中文文本
 * @param priority 优先级值
 * @returns 优先级中文文本
 */
export function getPriorityText(priority?: number): string {
  if (!priority)
    return '一般'

  switch (priority as number) {
    case 3: return '加急'
    case 2: return '一般'
    case 1: return '暂缓'
    default: return priority.toString()
  }
}

/**
 * 格式化日期时间字符串
 * @param dateTimeString 日期时间字符串
 * @returns 格式化后的日期时间字符串
 */
export function formatDateTime(dateTimeString?: string): string {
  if (!dateTimeString)
    return '-'
  try {
    return dayjs(dateTimeString).format('YYYY-MM-DD HH:mm:ss')
  }
  catch {
    return dateTimeString
  }
}

/**
 * 根据操作类型返回时间线项的类型
 * @param opType 操作类型
 * @returns 时间线项类型
 */
export function getLogItemType(opType?: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  if (!opType)
    return 'info'

  switch (opType) {
    case 'CREATE': return 'primary'
    case 'SUBMIT': return 'info'
    case 'ACCEPT': return 'primary'
    case 'APPROVE': return 'success'
    case 'REJECT': return 'danger'
    case 'TRANSFER': return 'warning'
    case 'COMPLETE': return 'success'
    default: return 'info'
  }
}

/**
 * 根据操作类型返回中文操作文本
 * @param opType 操作类型
 * @returns 中文操作文本
 */
export function getLogOperationText(opType?: number): string {
  if (opType === undefined || opType === null)
    return '未知操作'

  switch (opType) {
    case TicketInstProcessReqVOOpTypeEnum.SUBMIT: return '提交表单'
    case TicketInstProcessReqVOOpTypeEnum.ACCEPT: return '接受工单'
    case TicketInstProcessReqVOOpTypeEnum.APPROVE: return '审核工单'
    case TicketInstProcessReqVOOpTypeEnum.TRANSFER: return '转移工单'
    default: return `未知操作(${opType})`
  }
}

/**
 * 根据操作结果返回中文结果文本
 * @param opResult 操作结果
 * @returns 中文结果文本
 */
export function getLogResultText(opResult?: number): string {
  if (opResult === undefined || opResult === null)
    return ''

  switch (opResult) {
    case TicketInstProcessReqVOOpResultEnum.APPROVED: return '已通过'
    case TicketInstProcessReqVOOpResultEnum.REJECTED: return '已驳回'
    default: return `未知结果(${opResult})`
  }
}

/**
 * 根据当前节点状态枚举值返回中文文本
 * @param nodeStatus 当前节点状态枚举值
 * @returns 节点状态中文文本
 */
export function getNodeStatusText(nodeStatus?: number): string {
  if (nodeStatus === undefined || nodeStatus === null)
    return '未知状态'

  switch (nodeStatus) {
    case TicketInstDetailVOCurrentNodeStatusEnum.TO_BE_ACCEPTED: return '待接受'
    case TicketInstDetailVOCurrentNodeStatusEnum.ACCEPTED: return '已接受'
    case TicketInstDetailVOCurrentNodeStatusEnum.PROCESSED: return '已处理'
    case TicketInstDetailVOCurrentNodeStatusEnum.TO_BE_APPROVED: return '待审核'
    case TicketInstDetailVOCurrentNodeStatusEnum.APPROVED: return '已审核'
    case TicketInstDetailVOCurrentNodeStatusEnum.REJECTED: return '已驳回'
    default: return `未知状态(${nodeStatus})`
  }
}
