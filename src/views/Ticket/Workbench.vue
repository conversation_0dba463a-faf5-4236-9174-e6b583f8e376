<script setup lang="ts">
import type { TicketInstOperationLogVO } from '@/apiv2/workflow/models'
import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'
import type { FormInstance } from 'element-plus'
import type { VxeGridProps } from 'vxe-table'
import {
  TicketInstDetailVOCurrentNodeStatusEnum,
  TicketInstDetailVOCurrentNodeTypeEnum,
  TicketInstDetailVOTicketStatusEnum,
  TicketInstProcessReqVOOpResultEnum,
  TicketInstProcessReqVOOpTypeEnum,
} from '@/apiv2/workflow/models'
import { CgForm } from '@/components/CgElementUI/CgForm'
import ComponentTagsEnum from '@/components/CgElementUI/CgForm/ComponentTagsEnum'
import { CgGrid } from '@/components/CgGrid'
import { GridCellRenderName } from '@/components/CgGrid/cell-render-name'
import { useTicketStore } from '@/stores/Ticket'
import { useUserStore } from '@/stores/User'
import { Loading } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import SubmitDialog from './components/SubmitDialog.vue'
import TicketCard from './components/TicketCard.vue'
import TransferDialog from './components/TransferDialog.vue'
import { formatDateTime, getNodeStatusText, getPriorityTagType, getPriorityText } from './utils/formatters'

interface ExtendedTaskInstOperationLogVO extends TicketInstOperationLogVO {
  opResult?: string
  operator?: {
    userId: number
    userName: string
    name: string
  }
  ticketInstId?: number
}

defineOptions({
  name: 'Workbench',
})

const ticketStore = useTicketStore()
const {
  tickets,
  pagination,
  statusCounts,
  isLoadingList,
  selectedTicketId,
  selectedTicketDetail,
  ticketFormOptions,
  isLoadingDetail,
  currentStatusTab,
  // searchQuery,
  error,
} = storeToRefs(ticketStore)

const userStore = useUserStore()
const { userId: currentUserId } = storeToRefs(userStore)

const searchQueryModel = ref(ticketStore.searchQuery)

const cgFormRef = ref<FormInstance | null>(null)

const transferDialogVisible = ref(false)
const approveDialogVisible = ref(false)
const submitDialogVisible = ref(false)
const approveFormRef = ref<FormInstance | null>(null)
const approveFormModel = ref({
  approveResult: TicketInstProcessReqVOOpResultEnum.APPROVED as TicketInstProcessReqVOOpResultEnum,
  approveRemark: '',
})

// 审核表单配置
const approveFormOptions = computed<CgFormOption[][]>(() => [
  [
    {
      type: ComponentTagsEnum.Radio,
      key: 'approveResult',
      span: 24,
      props: {
        options: [
          { label: '通过', value: TicketInstProcessReqVOOpResultEnum.APPROVED },
          { label: '不通过', value: TicketInstProcessReqVOOpResultEnum.REJECTED },
        ],
        optionsAttr: 'label,value', // 指定label和value的属性名
      },
    },
  ],
  [
    {
      type: ComponentTagsEnum.Input,
      key: 'approveRemark',
      span: 24,
      props: {
        type: 'textarea',
        rows: 4,
        placeholder: '请输入审核备注',
      },
      rules: [
        {
          required: approveFormModel.value.approveResult === TicketInstProcessReqVOOpResultEnum.REJECTED,
          message: '审核备注必填',
          trigger: 'blur',
        },
      ],
    },
  ],
])

const listContainerRef = ref<HTMLElement | null>(null)

const shouldScrollToTop = ref(false)

const route = useRoute()

const isDetailPage = computed(() => {
  return route.name === 'TicketDetail'
})

onMounted(() => {
  // 如果是工单详情页，直接获取工单详情
  if (isDetailPage.value) {
    ticketStore.selectTicket(route.params.id as string)

    // 工单详情页不依赖工单列表数据
    return
  }

  ticketStore.fetchTickets()
  ticketStore.fetchStatusCounts()
})

watch(() => isLoadingList.value, (loading, prevLoading) => {
  if (prevLoading && !loading && shouldScrollToTop.value && listContainerRef.value) {
    listContainerRef.value?.scrollTo({
      top: 0,
      behavior: 'instant',
    })
    // 重置标记
    shouldScrollToTop.value = false
  }
})

watch(error, (newError) => {
  if (newError) {
    console.error('Ticket Store Error:', newError)
  }
})

function handleTabChange(tabName: string | number) {
  shouldScrollToTop.value = true
  ticketStore.setStatusTab(tabName as string)

  ticketStore.fetchStatusCounts()
}

function handleSearch() {
  shouldScrollToTop.value = true
  ticketStore.setSearchQuery(searchQueryModel.value)
}

function handlePageChange(page: number) {
  shouldScrollToTop.value = true
  ticketStore.setPage(page)
}

function handleSelectTicket(id: string | number) {
  ticketStore.selectTicket(id)
}

async function handleSaveForm(done: () => void) {
  let isValid = false
  try {
    isValid = await cgFormRef.value!.validate()
    if (isValid) {
      submitDialogVisible.value = true
    }
  }
  catch (err) {
    console.error('表单校验失败:', err)
  }
  finally {
    done()
  }
}

async function handleSubmitConfirm(payload: { description: string }, done: () => void) {
  const success = await ticketStore.saveTicketForm(payload.description)
  if (success) {
    ElMessage.success('提交成功')
    submitDialogVisible.value = false
  }
  else {
    ElMessage.error(error.value || '提交失败')
  }
  done()
}

async function handleAccept(done: () => void) {
  if (!selectedTicketId.value)
    return
  ElMessageBox.confirm('确定要接受此工单吗?', '提示', { type: 'info' })
    .then(async () => {
      const success = await ticketStore.performAction(TicketInstProcessReqVOOpTypeEnum.ACCEPT)
      if (success)
        ElMessage.success('工单已接受')
    })
    .finally(() => {
      done()
    })
}

function handleTransfer() {
  if (!selectedTicketId.value)
    return

  transferDialogVisible.value = true
}

async function handleTransferConfirm(payload: { target: string | number, description: string }, done: () => void) {
  const success = await ticketStore.performAction(TicketInstProcessReqVOOpTypeEnum.TRANSFER, payload)
  if (success) {
    ElMessage.success('转移成功')
    transferDialogVisible.value = false
  }
  done()
}

function handleApprove() {
  if (!selectedTicketId.value)
    return
  // 重置表单数据
  approveFormModel.value = {
    approveResult: TicketInstProcessReqVOOpResultEnum.APPROVED,
    approveRemark: '',
  }
  approveDialogVisible.value = true
}

async function handleApproveConfirm(done: () => void) {
  if (!selectedTicketId.value)
    return

  try {
    // 验证表单
    await approveFormRef.value?.validate()

    const payload = {
      result: approveFormModel.value.approveResult,
      description: approveFormModel.value.approveRemark,
    }
    const success = await ticketStore.performAction(TicketInstProcessReqVOOpTypeEnum.APPROVE, payload)
    if (success) {
      ElMessage.success('审核成功')
      approveDialogVisible.value = false
    }
  }
  catch (err) {
    console.error('表单验证失败:', err)
  }
  finally {
    done()
  }
}

function closeApproveDialog() {
  approveDialogVisible.value = false
  // 重置表单数据和验证状态
  approveFormModel.value = {
    approveResult: TicketInstProcessReqVOOpResultEnum.APPROVED,
    approveRemark: '',
  }
  approveFormRef.value?.resetFields()
}

function updateFormData(newValue: Record<string, any>) {
  if (selectedTicketDetail.value) {
    selectedTicketDetail.value.parsedFormData = newValue
  }
}

/**
 * 根据当前节点状态和类型判断按钮是否应该显示
 * @param buttonType 按钮类型
 * @returns 是否显示按钮
 */
function shouldShowButton(buttonType: 'accept' | 'submit' | 'transfer' | 'approve'): boolean {
  if (!selectedTicketDetail.value)
    return false

  const nodeStatus = selectedTicketDetail.value.currentNodeStatus
  const nodeType = selectedTicketDetail.value.currentNodeType

  // 检查工单状态，如果已完成则不显示任何按钮
  if (selectedTicketDetail.value.ticketStatus === TicketInstDetailVOTicketStatusEnum.COMPLETED)
    return false

  const isCurrentUserProcessor = !!selectedTicketDetail.value.currentNodeProcessor?.some(
    p => p.userId === currentUserId.value,
  )

  if (!isCurrentUserProcessor)
    return false

  switch (buttonType) {
    case 'accept':
      return nodeType === TicketInstDetailVOCurrentNodeTypeEnum.SUBMIT
        && nodeStatus === TicketInstDetailVOCurrentNodeStatusEnum.TO_BE_ACCEPTED

    case 'submit':
      return nodeType === TicketInstDetailVOCurrentNodeTypeEnum.SUBMIT
        && nodeStatus === TicketInstDetailVOCurrentNodeStatusEnum.ACCEPTED

    case 'transfer':
      return (
        (nodeType === TicketInstDetailVOCurrentNodeTypeEnum.SUBMIT
          && nodeStatus === TicketInstDetailVOCurrentNodeStatusEnum.ACCEPTED)
        || (nodeType === TicketInstDetailVOCurrentNodeTypeEnum.APPROVE
          && nodeStatus === TicketInstDetailVOCurrentNodeStatusEnum.TO_BE_APPROVED)
      )

    case 'approve':
      return nodeType === TicketInstDetailVOCurrentNodeTypeEnum.APPROVE
        && nodeStatus === TicketInstDetailVOCurrentNodeStatusEnum.TO_BE_APPROVED

    default:
      return false
  }
}

// 工单日志表格配置
const ticketLogsGridOptions = computed<VxeGridProps>(() => {
  return {
    border: false,
    showOverflow: true,
    height: undefined,
    columnConfig: {
      resizable: true,
    },
    columns: [
      {
        field: 'createTime',
        title: '时间',
        width: 160,
        formatter: ({ cellValue }) => formatDateTime(cellValue),
      },
      {
        field: 'operator',
        title: '操作人',
        width: 120,
        formatter: ({ row }) => row.operator?.name || '-', // TODO 接口缺少操作人字段
      },
      {
        field: 'nodeName',
        title: '节点',
        width: 120,
      },
      {
        field: 'opType',
        title: '操作',
        width: 120,
        cellRender: {
          name: GridCellRenderName.Status,
          props: {
            options: [
              {
                value: 'CREATE',
                label: '创建',
                class: 'text-primary',
              },
              {
                value: 'SUBMIT',
                label: '提交',
                class: 'text-gray-500',
              },
              {
                value: 'ACCEPT',
                label: '接受',
                class: 'text-primary',
              },
              {
                value: 'APPROVE',
                label: '审核',
                class: 'text-green-500',
              },
              {
                value: 'REJECT',
                label: '驳回',
                class: 'text-red-500',
              },
              {
                value: 'TRANSFER',
                label: '转移',
                class: 'text-orange-500',
              },
              {
                value: 'COMPLETE',
                label: '完成',
                class: 'text-green-500',
              },
              {
                value: 'UNKNOWN',
                label: '未知',
                class: 'text-gray-500',
              },
            ],
          },
        },
      },
      {
        field: 'opDesc',
        title: '操作描述',
        minWidth: 200,
        slots: {
          default: 'opDescTemp',
        },
      },
    ],
  }
})
</script>

<template>
  <div class="ticket-workbench-container h-full">
    <div class="h-full flex flex-grow gap-4 overflow-hidden">
      <!-- 左侧面板：列表和操作 —— 进入工单详情页时不渲染左侧面板 -->
      <div v-if="!isDetailPage" class="min-w-360px w-22% flex flex-col overflow-hidden rounded bg-white p-4 pr-0 pt-1">
        <ElTabs v-model="currentStatusTab" class="mb-4 flex-shrink-0 rounded bg-white" @tab-change="handleTabChange">
          <ElTabPane :label="`待处理 (${statusCounts.pending})`" name="pending">
            <template #label>
              <div class="flex items-center">
                <span>待处理</span>
                <ElBadge v-if="statusCounts.pending > 0" :value="statusCounts.pending" :max="99" class="ml-1" />
              </div>
            </template>
          </ElTabPane>
          <ElTabPane name="processing">
            <template #label>
              <div class="flex items-center">
                <span>进行中</span>
                <ElBadge v-if="statusCounts.processing > 0" :value="statusCounts.processing" :max="99" class="ml-1" />
              </div>
            </template>
          </ElTabPane>
          <ElTabPane name="closed">
            <template #label>
              <div class="flex items-center">
                <span>已关闭</span>
              </div>
            </template>
          </ElTabPane>
        </ElTabs>
        <!-- 搜索 -->
        <div class="mb-4 flex flex-shrink-0 gap-2">
          <CgInput
            v-model="searchQueryModel" placeholder="SPU编码、工单ID" clearable class="flex-grow"
            @keyup.enter="handleSearch" @clear="handleSearch"
          >
            <template #suffix>
              <font-awesome-icon
                :icon="['fas', 'magnifying-glass']" class="cursor-pointer" :class="[
                  { 'opacity-50': isLoadingList },
                ]" @click="handleSearch"
              />
            </template>
          </CgInput>
        </div>

        <!-- 工单列表 -->
        <div ref="listContainerRef" class="ticket-list-container flex-1 overflow-y-auto pr-2">
          <div v-if="tickets.length === 0" class="py-10 text-center text-gray-500">
            {{ isLoadingList ? '加载中...' : '暂无工单' }}
          </div>
          <TicketCard
            v-for="(ticket, index) in tickets" :key="ticket.id" :ticket="ticket"
            :is-selected="ticket.id === selectedTicketId" :data-index="index" @select="handleSelectTicket"
          />
        </div>

        <!-- 分页 -->
        <div class="flex flex-shrink-0 justify-end pt-4">
          <div v-if="isLoadingList" class="mr-2 flex items-center text-sm text-gray-500">
            <ElIcon class="mr-1 animate-spin">
              <Loading />
            </ElIcon>
          </div>
          <ElPagination
            v-if="pagination.total > 0" v-model:current-page="pagination.pageNum" :total="pagination.total"
            :page-size="pagination.pageSize" layout="prev, pager, next, total" background size="small"
            :disabled="isLoadingList" :pager-count="5" @current-change="handlePageChange"
          />
        </div>
      </div>

      <!-- 右侧面板：详情和操作 -->
      <div v-loading="isLoadingDetail" class="flex flex-1 flex-col overflow-hidden rounded bg-white p-4 shadow-sm">
        <!-- 工单详情空白态 —— 直接进入工单详情页无需渲染 -->
        <div
          v-if="!selectedTicketDetail && !isLoadingDetail && !isDetailPage"
          class="h-full flex items-center justify-center text-gray-500"
        >
          请在左侧选择一个工单查看详情
        </div>

        <template v-if="selectedTicketDetail">
          <!-- 工单详情头部（基本信息） -->
          <div class="mflex-shrink-0 border-b pb-2">
            <h3 class="mb-4 border-l-3 border-primary pl-2 text-sm text-primary font-semibold">
              基础信息
            </h3>
            <div class="grid grid-cols-4 gap-x-4 gap-y-2 text-sm">
              <!-- 工单ID -->
              <div class="flex items-center">
                <span class="mr-2 w-24 text-right text-labelText">工单ID:</span>
                <span>{{ selectedTicketDetail.ticketInstId }}</span>
              </div>

              <!-- 工单状态 -->
              <div class="flex items-center">
                <span class="mr-2 w-24 text-right text-labelText">工单状态:</span>
                <span>{{ selectedTicketDetail.statusText }}</span>
              </div>

              <!-- 优先级 -->
              <div class="flex items-center">
                <span class="mr-2 w-24 text-right text-labelText">优先级:</span>
                <ElTag :type="getPriorityTagType(selectedTicketDetail.priority)" size="small" disable-transitions>
                  {{ getPriorityText(selectedTicketDetail.priority) }}
                </ElTag>
              </div>

              <!-- 创建人 -->
              <div class="flex items-center">
                <span class="mr-2 w-24 text-right text-labelText">创建人:</span>
                <span>{{ selectedTicketDetail.creator?.name || '' }}</span>
              </div>

              <!-- 当前节点任务 -->
              <div class="flex items-center">
                <span class="mr-2 w-24 text-right text-labelText">当前节点任务:</span>
                <span>{{ selectedTicketDetail.currentNode }}</span>
              </div>

              <!-- 当前节点状态 -->
              <div class="flex items-center">
                <span class="mr-2 w-24 text-right text-labelText">当前节点状态:</span>
                <span>{{ getNodeStatusText(selectedTicketDetail.currentNodeStatus) }}</span>
              </div>

              <!-- 当前节点处理人 -->
              <div class="flex items-center">
                <span class="mr-2 w-24 text-right text-labelText">当前节点处理人:</span>
                <span>{{ selectedTicketDetail.assignee || '未分配' }}</span>
              </div>

              <!-- 创建时间 -->
              <div class="flex items-center">
                <span class="mr-2 w-24 text-right text-labelText">创建时间:</span>
                <span>{{ formatDateTime(selectedTicketDetail.createTime) }}</span>
              </div>
            </div>
          </div>

          <!-- 动态表单区域 -->
          <div class="mt-4 flex-1 overflow-y-auto pr-2">
            <h3 class="mb-4 border-l-3 border-primary pl-2 text-sm text-primary font-semibold">
              工单内容
            </h3>
            <div>
              <CgForm
                ref="cgFormRef" :options="ticketFormOptions" :model-value="selectedTicketDetail.parsedFormData"
                :use-grid="true" label-position="right" :context="selectedTicketDetail"
                @update:model-value="updateFormData"
              />
            </div>

            <!-- 工单日志 -->
            <div class="mt-2">
              <h3 class="mb-4 border-l-3 border-primary pl-2 text-sm text-primary font-semibold">
                工单日志
              </h3>
              <div v-if="selectedTicketDetail.logs && selectedTicketDetail.logs.length > 0">
                <CgGrid
                  :options="ticketLogsGridOptions"
                  :data="selectedTicketDetail.logs as ExtendedTaskInstOperationLogVO[]" :pagination-visible="false"
                >
                  <template #opDescTemp="{ row }">
                    <template v-if="row.opResult === 'APPROVED'">
                      审核通过
                    </template>
                    <template v-else-if="row.opResult === 'REJECTED'">
                      审核不通过，备注：{{ row.opDesc || '-' }}
                    </template>
                    <template v-else>
                      {{ row.opDesc || '-' }}
                    </template>
                  </template>
                </CgGrid>
              </div>
              <div v-else class="text-sm text-gray-500">
                暂无日志信息
              </div>
            </div>
          </div>

          <!-- 操作按钮区域 -->
          <div class="mt-4 flex flex-shrink-0 justify-center gap-2 border-t pt-4">
            <!-- 查看模式操作（根据工单状态/用户权限动态显示） -->
            <CgButton v-if="shouldShowButton('accept')" type="success" auto-loading @click="handleAccept">
              接受
            </CgButton>
            <CgButton v-if="shouldShowButton('submit')" type="primary" auto-loading @click="handleSaveForm">
              提交
            </CgButton>
            <CgButton v-if="shouldShowButton('transfer')" type="warning" @click="handleTransfer">
              转移
            </CgButton>
            <CgButton v-if="shouldShowButton('approve')" type="primary" @click="handleApprove">
              审核
            </CgButton>
          </div>
        </template>
      </div>
    </div>

    <TransferDialog
      v-model:visible="transferDialogVisible" :ticket-id="selectedTicketDetail?.ticketInstId"
      :spu-code="(selectedTicketDetail as any)?.relevantObjId || ''" @confirm="handleTransferConfirm"
    />

    <SubmitDialog
      v-model:visible="submitDialogVisible" :ticket-id="selectedTicketDetail?.ticketInstId"
      :spu-code="(selectedTicketDetail as any)?.relevantObjId || ''" @confirm="handleSubmitConfirm"
    />

    <CgDialog
      v-model="approveDialogVisible" title="审核工单" width="480px" :append-to-body="true" confirm-button-text="确定"
      :full-height="false" @submit="handleApproveConfirm" @close="closeApproveDialog"
    >
      <CgForm
        ref="approveFormRef"
        v-model="approveFormModel"
        :options="approveFormOptions"
        label-position="top"
      />
    </CgDialog>
  </div>
</template>

<style lang="scss" scoped>
// 样式调整以更好地集成
:deep(.el-tabs__header) {
  margin-bottom: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none; // 移除标签页头部的默认底部边框
}

// 确保右侧面板的可滚动区域正常工作
.flex-grow.overflow-y-auto {
  scrollbar-width: thin;
  /* Firefox浏览器 */
  scrollbar-color: #ccc #f0f0f0;
  /* Firefox浏览器 */
}

.flex-grow.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.flex-grow.overflow-y-auto::-webkit-scrollbar-track {
  background: #f0f0f0;
  border-radius: 3px;
}

.flex-grow.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 3px;
}
</style>
