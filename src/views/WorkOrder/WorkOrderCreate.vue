<script setup lang="ts">
import { Graph, Node, Shape } from '@antv/x6'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Stencil } from '@antv/x6-plugin-stencil'
import { Transform } from '@antv/x6-plugin-transform'
import { onBeforeUnmount, onMounted, ref } from 'vue' // 确保导入 onBeforeUnmount

// 当前步骤 (保持不变)
const currentStep = ref(3) // 流程配置步骤

// --- AntV X6 Refs ---
const container = ref<HTMLElement | null>(null) // 画布容器
const stencilContainer = ref<HTMLElement | null>(null) // Stencil容器
let graph: Graph // 画布实例 (设为 let，因为在 onMounted 中赋值)

// --- AntV X6 节点和连接桩配置 ---
const ports = {
  groups: {
    top: {
      position: 'top',
      attrs: { circle: { r: 4, magnet: true, stroke: '#399e96', strokeWidth: 1, fill: '#fff', style: { visibility: 'hidden' } } }, // 使用项目主色
    },
    right: {
      position: 'right',
      attrs: { circle: { r: 4, magnet: true, stroke: '#399e96', strokeWidth: 1, fill: '#fff', style: { visibility: 'hidden' } } }, // 使用项目主色
    },
    bottom: {
      position: 'bottom',
      attrs: { circle: { r: 4, magnet: true, stroke: '#399e96', strokeWidth: 1, fill: '#fff', style: { visibility: 'hidden' } } }, // 使用项目主色
    },
    left: {
      position: 'left',
      attrs: { circle: { r: 4, magnet: true, stroke: '#399e96', strokeWidth: 1, fill: '#fff', style: { visibility: 'hidden' } } }, // 使用项目主色
    },
  },
  items: [
    { group: 'top' },
    { group: 'right' },
    { group: 'bottom' },
    { group: 'left' },
  ],
}

// --- 注册自定义节点 ---
Graph.registerNode(
  'caguu-rect', // 任务节点
  {
    inherit: 'rect',
    width: 100,
    height: 40,
    attrs: {
      body: { strokeWidth: 1, stroke: '#399e96', fill: '#E6F4F4', rx: 5, ry: 5 }, // 使用项目主色系
      text: { fontSize: 12, fill: '#262626' },
    },
    ports: { ...ports },
  },
  true,
)

Graph.registerNode(
  'caguu-decision', // 决策/审核节点 (菱形)
  {
    inherit: 'polygon',
    width: 100,
    height: 50,
    attrs: {
      body: {
        strokeWidth: 1,
        stroke: '#399e96', // 使用项目主色
        fill: '#E6F4F4',
        refPoints: '0,10 10,0 20,10 10,20', // 调整菱形点位
      },
      text: { fontSize: 12, fill: '#262626' },
    },
    ports: { // 菱形通常只有左右或上下连接桩
      groups: {
        top: { ...ports.groups.top },
        bottom: { ...ports.groups.bottom },
        left: { ...ports.groups.left },
        right: { ...ports.groups.right },
      },
      items: [
        { group: 'top' },
        { group: 'bottom' },
        { group: 'left' },
        { group: 'right' },
      ],
    },
  },
  true,
)

Graph.registerNode(
  'caguu-branch', // 分支节点
  {
    inherit: 'rect',
    width: 100,
    height: 40,
    attrs: {
      body: {
        strokeWidth: 1,
        stroke: '#399e96',
        fill: '#E6F4F4',
        rx: 5,
        ry: 5,
        // 添加特殊样式以区分分支节点
        strokeDasharray: '5,2', // 虚线边框
      },
      text: { fontSize: 12, fill: '#262626' },
    },
    // 分支节点需要一个输入和多个输出
    ports: { ...ports },
  },
  true,
)

Graph.registerNode(
  'caguu-start', // 开始节点 (圆角矩形)
  {
    inherit: 'rect',
    width: 100,
    height: 40,
    attrs: {
      body: { strokeWidth: 1, stroke: '#399e96', fill: '#E6F4F4', rx: 20, ry: 20 }, // 圆角
      text: { fontSize: 12, fill: '#262626' },
    },
    ports: { // 开始节点通常只有输出桩
      groups: { right: { ...ports.groups.right } },
      items: [{ group: 'right' }],
    },
  },
  true,
)

Graph.registerNode(
  'caguu-end', // 结束节点 (圆形)
  {
    inherit: 'circle',
    width: 60,
    height: 60,
    attrs: {
      body: { strokeWidth: 1, stroke: '#399e96', fill: '#f0f0f0' }, // 不同填充色
      text: { fontSize: 12, fill: '#262626' },
    },
    ports: { // 结束节点通常只有输入桩
      groups: { left: { ...ports.groups.left } },
      items: [{ group: 'left' }],
    },
  },
  true,
)

// --- 初始化 ---
onMounted(() => {
  const graphContainer = container.value
  const stencilEl = stencilContainer.value
  if (!graphContainer || !stencilEl) {
    console.error('Graph or Stencil container not found')
    return
  }

  // 1. 创建画布实例
  graph = new Graph({
    container: graphContainer,
    width: graphContainer.clientWidth, // 动态获取宽度
    height: graphContainer.clientHeight, // 动态获取高度
    background: { color: '#F5F5F5' },
    grid: { // 网格配置 (保持或根据需要调整)
      visible: true,
      type: 'doubleMesh',
      args: [
        { color: '#F0F0F0', thickness: 1 }, // 非常浅的灰色
        { color: '#E0E0E0', thickness: 1, factor: 4 }, // 稍深的灰色
      ],
    },
    mousewheel: { // 启用滚轮缩放
      enabled: true,
      zoomAtMousePosition: true,
      modifiers: 'ctrl', // 按住 Ctrl 缩放
      minScale: 0.5,
      maxScale: 3,
    },
    connecting: { // 连接配置
      router: 'manhattan', // 智能路由
      connector: { name: 'rounded', args: { radius: 8 } },
      anchor: 'center',
      connectionPoint: 'anchor',
      allowBlank: false, // 不允许连接到空白处
      snap: { radius: 20 }, // 自动吸附
      createEdge() { // 创建边的样式
        return new Shape.Edge({
          attrs: {
            line: { stroke: '#399e96', strokeWidth: 1, targetMarker: { name: 'classic', size: 8 } }, // 使用项目主色
          },
          zIndex: 0, // 确保边在节点下方
        })
      },
      validateConnection({ sourceView, targetView, targetMagnet }) { // 校验连接
        // 必须连接到连接桩
        if (!targetMagnet)
          return false
        // 不能自环连接
        if (sourceView === targetView)
          return false
        // TODO: 可根据需要添加更复杂的连接规则
        return true
      },
    },
    highlighting: { // 高亮选项
      magnetAdsorbed: { // 连接桩吸附高亮
        name: 'stroke',
        args: { attrs: { fill: '#399e96', stroke: '#399e96' } }, // 使用项目主色
      },
    },
  })

  // 2. 使用插件
  graph
    .use(new Transform({ resizing: true, rotating: false })) // 启用调整大小，禁用旋转
    .use(new Selection({ rubberband: true, showNodeSelectionBox: true })) // 启用框选和节点选择框
    .use(new Snapline()) // 启用对齐线
    .use(new Keyboard()) // 启用键盘快捷键
    .use(new Clipboard()) // 启用剪贴板
    .use(new History()) // 启用撤销/重做

  // 3. 初始化 Stencil (左侧模板面板)
  const stencil = new Stencil({
    title: '流程节点',
    target: graph,
    stencilGraphWidth: 200,
    stencilGraphHeight: 0, // 自适应高度
    collapsable: false, // 可折叠
    layoutOptions: { // 布局选项
      columns: 1, // 每行1个
      columnWidth: 150,
      rowHeight: 70,
      marginY: 10,
    },
    groups: [ // 节点分组
      { name: 'basic', title: '基础节点', graphHeight: 400 },
      // 可以添加更多分组
      // { name: 'advanced', title: '高级节点', graphHeight: 300 },
    ],
    getDropNode(node) { // 自定义拖入画布的节点
      return node.clone({ keepId: false }) // 创建副本，并重新生成ID
    },
    // validateNode(droppingNode, options) {
    //    // 可以在这里添加放入画布前的校验逻辑
    //    return true;
    // }
  })
  // 将 stencil 挂载到 DOM
  stencilEl.appendChild(stencil.container)

  // 4. 向 Stencil 加载节点
  const startNode = graph.createNode({ shape: 'caguu-start', label: '开始' })
  const taskNode = graph.createNode({ shape: 'caguu-rect', label: '任务处理' })
  const decisionNode = graph.createNode({ shape: 'caguu-decision', label: '审核/决策' })
  const branchNode = graph.createNode({ shape: 'caguu-branch', label: '并行分支' })
  const endNode = graph.createNode({ shape: 'caguu-end', label: '结束' })

  stencil.load([startNode, taskNode, decisionNode, branchNode, endNode], 'basic')
  // 如果有其他组，类似加载: stencil.load([node1, node2], 'advanced')

  // 5. 绑定快捷键
  bindKeys(graph)

  // 6. 绑定事件 (连接桩显示/隐藏)
  bindEvents(graph)

  // 7. (可选) 初始化示例流程图
  initializeExampleFlow(graph) // 使用新注册的节点类型
})

// --- 清理工作 ---
onBeforeUnmount(() => {
  if (graph) {
    graph.dispose() // 销毁画布实例，防止内存泄漏
  }
})

// --- 快捷键绑定 ---
function bindKeys(graphInstance: Graph) {
  // Copy
  graphInstance.bindKey(['meta+c', 'ctrl+c'], () => {
    const cells = graphInstance.getSelectedCells()
    if (cells.length)
      graphInstance.copy(cells)

    return false // 阻止默认行为
  })
  // Cut
  graphInstance.bindKey(['meta+x', 'ctrl+x'], () => {
    const cells = graphInstance.getSelectedCells()
    if (cells.length)
      graphInstance.cut(cells)

    return false
  })
  // Paste
  graphInstance.bindKey(['meta+v', 'ctrl+v'], () => {
    if (!graphInstance.isClipboardEmpty()) {
      const cells = graphInstance.paste({ offset: 32 })
      graphInstance.cleanSelection()
      graphInstance.select(cells)
    }
    return false
  })
  // Undo
  graphInstance.bindKey(['meta+z', 'ctrl+z'], () => {
    if (graphInstance.canUndo())
      graphInstance.undo()

    return false
  })
  // Redo
  graphInstance.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
    if (graphInstance.canRedo())
      graphInstance.redo()

    return false
  })
  // Select All
  graphInstance.bindKey(['meta+a', 'ctrl+a'], () => {
    const nodes = graphInstance.getNodes()
    if (nodes)
      graphInstance.select(nodes)
  })
  // Delete
  graphInstance.bindKey(['backspace', 'delete'], () => { // 监听 Backspace 和 Delete 键
    const cells = graphInstance.getSelectedCells()
    if (cells.length)
      graphInstance.removeCells(cells)
  })
  // Zoom In
  graphInstance.bindKey(['ctrl+1', 'meta+1'], () => {
    graphInstance.zoom(0.1)
  })
  // Zoom Out
  graphInstance.bindKey(['ctrl+2', 'meta+2'], () => {
    graphInstance.zoom(-0.1)
  })
}

// --- 事件绑定 ---
function bindEvents(graphInstance: Graph) {
  // 控制连接桩显示/隐藏
  const showPorts = (ports: NodeListOf<SVGElement>, show: boolean) => {
    for (let i = 0, len = ports.length; i < len; i += 1)
      ports[i].style.visibility = show ? 'visible' : 'hidden'
  }

  graphInstance.on('node:mouseenter', () => {
    const ports = container.value?.querySelectorAll('.x6-port-body') as NodeListOf<SVGElement>
    if (ports)
      showPorts(ports, true)
  })

  graphInstance.on('node:mouseleave', () => {
    const ports = container.value?.querySelectorAll('.x6-port-body') as NodeListOf<SVGElement>
    if (ports)
      showPorts(ports, false)
  })
}

// --- 初始化示例流程图 (已更新为使用注册的节点) ---
function initializeExampleFlow(graphInstance: Graph) {
  // 创建节点 (使用注册的 shape 名称)
  const start = graphInstance.addNode({
    shape: 'caguu-start', // 使用注册的开始节点
    x: 100,
    y: 150,
    label: '开始',
  })
  const review1 = graphInstance.addNode({
    shape: 'caguu-decision', // 使用注册的决策节点
    x: 250,
    y: 145, // 微调 Y 轴使中心对齐
    label: '选品审核',
  })
  const collection = graphInstance.addNode({
    shape: 'caguu-rect', // 使用注册的任务节点
    x: 400,
    y: 150,
    label: '信息采集',
  })
  const fee = graphInstance.addNode({
    shape: 'caguu-rect', // 使用注册的任务节点
    x: 550,
    y: 80, // 调整 Y 轴位置
    label: '运费预估',
  })
  const pricing = graphInstance.addNode({
    shape: 'caguu-rect', // 使用注册的任务节点
    x: 550,
    y: 220, // 调整 Y 轴位置
    label: '系统定价',
  })
  const review2 = graphInstance.addNode({
    shape: 'caguu-decision', // 使用注册的决策节点
    x: 700,
    y: 145, // 微调 Y 轴使中心对齐
    label: '审核节点',
  })
  const end = graphInstance.addNode({
    shape: 'caguu-end', // 使用注册的结束节点
    x: 850,
    y: 140, // 微调 Y 轴使中心对齐
    label: '结束',
  })

  // 添加连线 (指定源端口和目标端口)
  graphInstance.addEdge({
    source: { cell: start, port: 'right' },
    target: { cell: review1, port: 'left' },
    attrs: { line: { stroke: '#399e96', strokeWidth: 1 } },
  })
  graphInstance.addEdge({
    source: { cell: review1, port: 'right' },
    target: { cell: collection, port: 'left' },
    attrs: { line: { stroke: '#399e96', strokeWidth: 1 } },
  })
  graphInstance.addEdge({
    source: { cell: collection, port: 'right' },
    target: { cell: fee, port: 'left' },
    attrs: { line: { stroke: '#399e96', strokeWidth: 1 } },
  })
  graphInstance.addEdge({
    source: { cell: collection, port: 'right' },
    target: { cell: pricing, port: 'left' },
    attrs: { line: { stroke: '#399e96', strokeWidth: 1 } },
  })
  graphInstance.addEdge({
    source: { cell: fee, port: 'right' },
    target: { cell: review2, port: 'left' },
    attrs: { line: { stroke: '#399e96', strokeWidth: 1 } },
  })
  graphInstance.addEdge({
    source: { cell: pricing, port: 'right' },
    target: { cell: review2, port: 'left' },
    attrs: { line: { stroke: '#399e96', strokeWidth: 1 } },
  })
  graphInstance.addEdge({
    source: { cell: review2, port: 'right' },
    target: { cell: end, port: 'left' },
    attrs: { line: { stroke: '#399e96', strokeWidth: 1 } },
  })
}

onMounted(() => {
  setTimeout(() => {
    console.log(graph.toJSON())
  }, 2000)
})
</script>

<template>
  <div class="flow-editor">
    <!-- 顶部步骤导航 (保持不变) -->
    <div class="step-nav">
      <div class="step-item" :class="{ active: currentStep === 1 }">
        1 设置名称
      </div>
      <div class="step-item" :class="{ active: currentStep === 2 }">
        2 Form配置
      </div>
      <div class="step-item" :class="{ active: currentStep === 3 }">
        3 流程配置
      </div>
      <div class="step-item" :class="{ active: currentStep === 4 }">
        4 消息配置
      </div>
    </div>

    <!-- 主编辑区 - 更新布局 -->
    <div class="editor-main-container">
      <!-- 左侧 Stencil 面板 -->
      <div ref="stencilContainer" class="stencil-panel" />

      <!-- 右侧图形编辑区 -->
      <div ref="container" class="graph-container" />
    </div>
  </div>
</template>

<style scoped>
.flow-editor {
  display: flex;
  flex-direction: column;
  height: 100%; /* 调整高度以适应视口，减去可能的 header/footer 高度 */
  /* 或者根据你的布局设置具体高度 */
  /* height: 700px; */
  width: 100%;
  overflow: hidden; /* 防止内部滚动条影响布局 */
}

.step-nav {
  display: flex;
  background-color: #f8f8f8;
  padding: 10px;
  border-bottom: 1px solid #e0e0e0;
  flex-shrink: 0; /* 防止导航栏被压缩 */
}

.step-item {
  flex: 1;
  text-align: center;
  padding: 10px;
  position: relative;
  color: #666; /* 默认颜色 */
  cursor: default; /* 或者 pointer 如果可点击切换 */
}

.step-item.active {
  background-color: #399e96; /* 使用项目主色 */
  color: #fff;
  font-weight: bold;
}

.editor-main-container {
  display: flex;
  flex: 1; /* 占据剩余空间 */
  min-height: 0; /* 防止 flex item 内容溢出 */
}

.stencil-panel {
  width: 200px; /* Stencil 宽度 */
  height: 100%;
  position: relative; /* Stencil 内部需要相对定位 */
  border-right: 1px solid #e0e0e0;
  flex-shrink: 0; /* 防止 Stencil 面板被压缩 */
}

.graph-container {
  flex: 1; /* 占据剩余宽度 */
  height: 100%; /* 高度占满父容器 */
  overflow: hidden; /* 图本身可能有滚动 */
}

/* --- Stencil 和 X6 插件的深层样式 --- */
/* 可能需要使用 :deep() 或全局样式来覆盖 X6 插件的默认样式 */
:deep(.x6-widget-stencil) {
  background-color: #fdfdfd;
}
:deep(.x6-widget-stencil-title) {
  background-color: #f0f0f0;
  color: #333;
}
:deep(.x6-widget-stencil-group-title) {
  background-color: #f8f8f8 !important; /* 强制覆盖 */
  color: #555;
}

/* 调整 Transform 插件句柄样式 */
:deep(.x6-widget-transform) {
  border: 1px dashed #399e96; /* 使用项目主色 */
  margin: -1px 0 0 -1px;
  padding: 0px;
}
:deep(.x6-widget-transform > div) {
  border: 1px solid #399e96; /* 使用项目主色 */
}
:deep(.x6-widget-transform > div:hover) {
  background-color: #6cc3bb; /* 主色变浅 */
}
:deep(.x6-widget-transform-active-handle) {
  background-color: #6cc3bb; /* 主色变浅 */
}
:deep(.x6-widget-transform-resize) {
  border-radius: 0;
}

/* 调整选择框样式 */
:deep(.x6-widget-selection-inner) {
  border: 1px dashed #399e96; /* 使用项目主色 */
}
:deep(.x6-widget-selection-box) {
  opacity: 0.3;
  background-color: #e6f4f4; /* 主色系浅色 */
}

/* 隐藏滚动条 (如果X6内部有) - 可选 */
:deep(.x6-graph-scroller) {
  overflow: auto; /* 或者 hidden */
}
</style>
