<script lang="ts" setup>
import type { UserLoginReq } from '@/apiv2/user/models'
import { useUserStore } from '@/stores/User'
import { RsaEncryption } from '@/utils/encryption'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { useRoute } from 'vue-router'

const userStore = useUserStore()
const route = useRoute()

const formData = reactive({
  username: '',
  password: '',
  captcha: '',
  rememberMe: false,
})

const rules = {
  username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
  password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
  captcha: [{ required: true, message: '请输入验证码', trigger: 'blur' }],
}

const loginFormRef = ref()
const showPassword = ref(false)
const captchaUrl = ref('')
const captchaKey = ref('')
const captchaLoading = ref(false)

const loading = computed(() => userStore.loginLoading)
const loginError = computed(() => userStore.loginError)

const features = [
  {
    icon: ['fas', 'chart-line'],
    title: 'Lorem Ipsum',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
  },
  {
    icon: ['fas', 'boxes'],
    title: 'Lorem Ipsum',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
  },
  {
    icon: ['fas', 'globe'],
    title: 'Lorem Ipsum',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
  },
  {
    icon: ['fas', 'users'],
    title: 'Lorem Ipsum',
    description: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
  },
]

const socialLinks = [
  ['fab', 'facebook-f', 'https://www.facebook.com/profile.php?id=61560440906560'],
  ['fab', 'twitter', 'https://x.com/caguuu_official'],
  ['fab', 'instagram', 'https://www.instagram.com/caguuu_official/'],
]

function togglePasswordVisibility() {
  showPassword.value = !showPassword.value
}

async function refreshCaptcha() {
  try {
    captchaLoading.value = true
    const captchaData = await userStore.generateCaptcha()
    if (captchaData) {
      captchaUrl.value = captchaData.textImage || ''
      captchaKey.value = captchaData.key || ''
    }
  }
  catch (error) {
    console.error('获取验证码失败', error)
  }
  finally {
    captchaLoading.value = false
  }
}

async function handleSubmit() {
  if (!loginFormRef.value)
    return

  loginFormRef.value.validate(async (valid: boolean) => {
    if (!valid) {
      return false
    }

    if (!captchaKey.value) {
      ElMessage.error('验证码未加载，请刷新验证码后重试')
      return false
    }

    const loginData: UserLoginReq = {
      userName: formData.username,
      // 使用RSA加密密码
      password: RsaEncryption.encrypt(formData.password),
      captcha: formData.captcha,
      captchaKey: captchaKey.value,
    }

    const redirectPath = route.query.redirect as string | undefined

    const success = await userStore.login(loginData, redirectPath)

    if (!success) {
      captchaLoading.value = true
      refreshCaptcha()
    }
  })
}

onMounted(() => {
  captchaLoading.value = true
  refreshCaptcha()
})
</script>

<template>
  <div class="bg-login min-w-[1024px]">
    <div class="main-container relative z-10 max-w-6xl w-full flex flex-row items-stretch">
      <!-- 左侧登录区域 -->
      <div class="wave-bg login-side order-1 w-2/5 flex flex-col justify-center flex-items-stretch p-12">
        <div class="mb-10">
          <div class="mb-10">
            <div class="flex flex-col items-start">
              <div class="logo-container animate-fadeInDown mb-4 flex items-center">
                <div class="flex flex-col items-start">
                  <h2 class="brand-title flex items-baseline">
                    <span
                      class="letter-spacing-tight logo-text text-6xl font-bold"
                    >CAGUUU</span>
                  </h2>
                  <div class="slogan mt-3 text-lg text-gray-600 font-medium tracking-wider">
                    跨境电商管理系统
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="divider" />
        </div>

        <el-form
          ref="loginFormRef" :model="formData" :rules="rules" class="login-form" label-position="top"
          @submit.prevent="handleSubmit" @keyup.enter="handleSubmit"
        >
          <el-form-item prop="username" label="账号">
            <el-input v-model="formData.username" placeholder="请输入账号" size="large">
              <template #prefix>
                <el-icon>
                  <FontAwesomeIcon :icon="['fas', 'user']" />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password" label="密码">
            <el-input
              v-model="formData.password" :type="showPassword ? 'text' : 'password'" placeholder="请输入密码"
              size="large"
            >
              <template #prefix>
                <el-icon>
                  <FontAwesomeIcon :icon="['fas', 'lock']" />
                </el-icon>
              </template>
              <template #suffix>
                <el-icon class="cursor-pointer" @click="togglePasswordVisibility">
                  <FontAwesomeIcon :icon="showPassword ? ['fas', 'eye-slash'] : ['fas', 'eye']" />
                </el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="captcha" label="验证码">
            <div class="w-full flex space-x-2">
              <el-input v-model="formData.captcha" placeholder="请输入验证码" class="flex-grow" size="large">
                <template #prefix>
                  <el-icon class="cursor-pointer">
                    <FontAwesomeIcon :icon="['fas', 'keyboard']" />
                  </el-icon>
                </template>
              </el-input>
              <div class="captcha-container flex flex-shrink-0 items-center">
                <div v-loading="captchaLoading" class="h-40px w-120px overflow-hidden rounded" element-loading-background="rgba(255, 255, 255, 0.8)" element-loading-svg-view-box="-10, -10, 50, 50" element-loading-spinner="rotating-plane" element-loading-text="加载中...">
                  <img v-if="captchaUrl" :src="captchaUrl" alt="验证码" class="h-full w-full cursor-pointer transition-opacity duration-300 hover:opacity-80" title="点击刷新验证码" @click="captchaLoading = true; refreshCaptcha()">
                </div>
              </div>
            </div>
          </el-form-item>

          <el-button type="primary" size="large" class="w-full" :loading="loading" @click="handleSubmit">
            <span class="text-sm">登录</span>
          </el-button>

          <!-- <div class="mt-6 text-center">
            <p class="text-sm text-gray-600">
              还没有账号? <a href="#" class="text-primary hover:underline">联系管理员</a>
            </p>
          </div> -->
        </el-form>
      </div>

      <!-- 右侧特性展示区域 -->
      <div class="feature-side relative order-2 w-3/5">
        <div
          class="absolute inset-0 rounded-12px opacity-80"
          style="background: linear-gradient(to bottom right, #399e96, #2c7a74);"
        />

        <div class="relative z-10 h-full flex flex-col p-12">
          <div class="mb-auto w-full">
            <h1 class="animate-fadeInUp mt-8 text-3xl text-white font-bold text-shadow">
              精细运营 优化成本 提升效率 把控全流程
            </h1>
          </div>

          <div class="grid grid-cols-2 mt-8 gap-6">
            <div
              v-for="(feature, index) in features" :key="index"
              class="glass-effect card-hover animate-fadeInUp rounded-xl p-6" :class="`delay-${(index + 1) * 100}`"
            >
              <div
                class="feature-icon mb-4 h-12 w-12 flex items-center justify-center rounded-lg"
                style="background-color: #399e96;"
              >
                <FontAwesomeIcon :icon="feature.icon" class="text-xl text-white" />
              </div>
              <h3 class="mb-2 text-lg text-white font-semibold">
                {{ feature.title }}
              </h3>
              <p class="text-sm text-teal-50">
                {{ feature.description }}
              </p>
            </div>
          </div>

          <div class="mt-10 text-center">
            <div class="mb-6 inline-flex space-x-4">
              <a
                v-for="(social, index) in socialLinks" :key="index" :href="social[2]" target="_blank"
                class="text-white transition-colors hover:text-teal-50"
              >
                <FontAwesomeIcon :icon="[social[0], social[1]]" />
              </a>
            </div>
            <div class="animate-fadeInUp text-xs text-teal-50 delay-300">
              2025 卡谷 ©
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
// 变量定义
$primary-gradient: linear-gradient(135deg, $cg-color-primary 0%, $cg-color-primary-dark 100%);
$white-transparent: rgba(255, 255, 255, 0.25);
$transition-standard: all 0.3s ease;

// 背景和容器样式
.bg-login {
  min-height: 100vh;
  background: url('@/assets/images/background.avif') no-repeat center/cover;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgb(57 158 150 / 24%), rgb(212 228 227 / 17%));
    backdrop-filter: blur(4px);
  }
}

// 主容器和侧边样式
.main-container {
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15), 0 5px 10px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(3px);
  border: 1px solid $white-transparent;
  background-color: rgba(255, 255, 255, 0.05);
  transition: $transition-standard;
}

// 登录和功能侧边样式
.login-side,
.feature-side {
  border-radius: 12px;
  margin: 4px;
  backdrop-filter: blur(8px);
}

.login-side {
  background-color: rgba(255, 255, 255, 0.98);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.03);
}

.feature-side {
  background: linear-gradient(135deg, rgba($cg-color-primary, 0.85), rgba($cg-color-primary-dark, 0.9));
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out forwards;
}

@keyframes pulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

// 延迟类
@for $i from 1 through 4 {
  .delay-#{$i * 100} {
    animation-delay: #{$i * 0.1}s;
  }
}

// 交互效果
.hover-pulse:hover {
  animation: pulse 2s infinite;
}

.card-hover {
  transition: $transition-standard;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px -3px rgba($cg-color-primary-dark, 0.2);

    .feature-icon {
      transform: scale(1.1);
    }
  }
}

// 特殊效果
.glass-effect {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(8px);
  border: 1px solid $white-transparent;
}

.wave-bg {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23399e96' fill-opacity='0.08' d='M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,149.3C960,160,1056,160,1152,138.7C1248,117,1344,75,1392,53.3L1440,32L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
  background-size: cover;
  background-position: bottom;
}

.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

// 表单和按钮样式
.btn-gradient {
  background: $primary-gradient;
  transition: $transition-standard;

  &:hover {
    box-shadow: 0 8px 12px -3px rgba($cg-color-primary-dark, 0.15), 0 4px 6px -2px rgba($cg-color-primary-dark, 0.08);
  }
}

.input-focus:focus {
  border-color: $cg-color-primary;
  box-shadow: 0 0 0 3px rgba($cg-color-primary, 0.2);
}

.input-icon {
  transition: $transition-standard;
}

input:focus+.input-icon {
  color: $cg-color-primary !important;
}

// Element Plus 组件样式覆盖
:deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.08) inset;
  border-radius: 0.5rem;
  padding: 0 12px;

  &.is-focus {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset;
  }
}

:deep(.el-input__inner) {
  height: 40px;
}

:deep(.el-form-item__error) {
  padding-top: 4px;
}

:deep(.el-checkbox__label) {
  font-weight: normal;
}

:deep(.el-button) {
  border-radius: 0.5rem;
  font-weight: 500;
  height: 44px;
}

:deep(.el-form-item > label) {
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  margin-bottom: 0.25rem;
}

// 装饰元素
.divider {
  height: 1px;
  background: linear-gradient(to right, transparent, $cg-color-primary, transparent);
}

// Logo 和品牌样式
.feature-icon {
  transition: $transition-standard;
}

.logo-icon {
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 15px -3px rgba($cg-color-primary, 0.3);

  &::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transform: scale(0.5);
    transition: transform 0.5s, opacity 0.5s;
  }

  &:hover::after {
    opacity: 1;
    transform: scale(1);
  }
}

.brand-title {
  position: relative;

  span:first-child {
    text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.8), 2px 2px 0 rgba($cg-color-primary, 0.1);
    letter-spacing: -0.5px;
  }
}

.slogan {
  position: relative;
  letter-spacing: 1.5px;
  opacity: 0.9;
  transition: opacity 0.3s ease;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
  margin-left: 4px;
}

.logo-container:hover .slogan {
  opacity: 1;
}
.logo-text {
  color: #399e96;
  text-shadow: 1px 1px 0 rgba(0, 0, 0, 0.05);
  background: linear-gradient(135deg, #399e96 0%, #2c7a74 100%);
  background-clip: text;
  letter-spacing: -1px;
}
</style>
