<script setup lang="ts">
import fulfillmentApi from '@/api.services/fulfillment.service'
import { ElMessage } from 'element-plus'

defineOptions({
  name: 'OrderMonitor',
})

const importApi = fulfillmentApi.importOriginalOrder
const isUploading = ref(false)
const uploadResult = ref<{
  success: boolean
  message?: string
} | null>(null)

function handleFileSelect(event: Event) {
  const input = event.target as HTMLInputElement
  if (input.files && input.files.length > 0) {
    const file = input.files[0]

    // 检查文件类型
    const fileExt = file.name.split('.').pop()?.toLowerCase()
    if (fileExt !== 'xlsx' && fileExt !== 'csv') {
      ElMessage.error('请上传xlsx或csv格式的文件')
      input.value = ''
      return
    }

    // 检查文件大小（10MB = 10 * 1024 * 1024 字节）
    if (file.size > 10 * 1024 * 1024) {
      ElMessage.error('文件大小不能超过10MB')
      input.value = ''
      return
    }

    uploadFile(file)
  }
}

function triggerFileSelect() {
  if (isUploading.value)
    return

  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.xlsx,.csv'
  input.style.display = 'none'
  input.onchange = handleFileSelect

  document.body.appendChild(input)
  input.click()
  document.body.removeChild(input)
}

async function uploadFile(file: File) {
  try {
    isUploading.value = true
    uploadResult.value = null

    const response = await importApi({
      file,
    })

    if (response.data.success) {
      uploadResult.value = {
        success: true,
        message: '导入成功',
      }
      ElMessage.success('导入成功')
    }
    else {
      uploadResult.value = {
        success: false,
        message: response.data.message || '导入失败',
      }
    }
  }
  catch (error: any) {
    uploadResult.value = {
      success: false,
      message: error.message || '导入失败',
    }
  }
  finally {
    isUploading.value = false
  }
}
</script>

<template>
  <div class="order-monitor p-4">
    <div class="mb-4">
      <CgButton type="primary" :loading="isUploading" @click="triggerFileSelect">
        {{ isUploading ? '导入中...' : '数据导入' }}
      </CgButton>

      <!-- 上传结果 -->
      <div
        v-if="uploadResult" class="upload-result ml-4 mt-2 inline-block rounded p-2 text-sm"
        :class="uploadResult.success ? 'bg-green-50 text-green-700' : 'bg-red-50 text-red-700'"
      >
        <font-awesome-icon :icon="['fas', uploadResult.success ? 'check-circle' : 'times-circle']" class="mr-1" />
        {{ uploadResult.message }}
      </div>
    </div>

    <div class="import-rules rounded bg-gray-50 p-4 text-sm text-gray-700">
      <ul class="list-disc pl-5 space-y-1">
        <li>表头字段请勿删除，否则导入不成功</li>
        <li>表格中相同数据存在多条记录时，例如：订单+SKU相同时，会以第一条为准，多余的会报错</li>
        <li>导入格式为xlsx\csv，行数不超过10000条，文件大小不超过10M</li>
      </ul>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.order-monitor {
  .import-rules {
    line-height: 1.5;
  }

  .upload-result {
    transition: all 0.3s;
  }
}
</style>
