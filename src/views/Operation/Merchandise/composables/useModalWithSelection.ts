import type { PageListInstance } from '@/components/CgPageList'

export function useModalWithSelection(pageListRef: Ref<PageListInstance | null>) {
  const selectedIds = ref<number[]>([])
  const selectedCount = ref(0)

  const createModalWatcher = (modalVisible: Ref<boolean>) => {
    return watch(modalVisible, (newValue) => {
      // 只在模态框显示时更新选中数据
      if (newValue && pageListRef.value?.getCheckboxRecords) {
        const selectedRows = pageListRef.value.getCheckboxRecords() || []
        selectedIds.value = selectedRows.map(row => row.id)
        selectedCount.value = selectedRows.length

        if (selectedCount.value === 0) {
          ElMessage.warning('请至少选择一条数据')
          modalVisible.value = false
        }
      }
    })
  }

  return {
    selectedIds,
    selectedCount,
    createModalWatcher,
  }
}
