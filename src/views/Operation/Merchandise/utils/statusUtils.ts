import { CommoditySkuVOStatusEnum } from '@/apiv2/product'

/**
 * 商品SKU状态文本映射
 */
export const CommoditySkuVOStatusTextMap: Record<CommoditySkuVOStatusEnum, string> = {
  [CommoditySkuVOStatusEnum.DRAFT]: '草稿',
  [CommoditySkuVOStatusEnum.CHECKING]: '审核中',
  [CommoditySkuVOStatusEnum.ON_SHELF]: '已上架',
  [CommoditySkuVOStatusEnum.OFF_SHELVES]: '已下架',
  [CommoditySkuVOStatusEnum.FREEZE]: '已冻结',
  [CommoditySkuVOStatusEnum.DELETED]: '已删除',
}

/**
 * 将商品SKU状态枚举值转换为对应的文本描述
 * @param status 商品SKU状态枚举值
 * @returns 状态文本描述
 */
export function getSkuStatusText(status?: CommoditySkuVOStatusEnum): string {
  if (status === undefined || status === null)
    return '未知状态'
  return CommoditySkuVOStatusTextMap[status] || '未知状态'
}

/**
 * 获取商品SKU状态对应的ElTag类型
 * @param status 商品SKU状态枚举值
 * @returns ElTag的type属性值（'success'|'warning'|'info'|'danger'|'primary'）
 */
export function getSkuStatusClass(status?: CommoditySkuVOStatusEnum) {
  if (status === undefined || status === null)
    return 'info'

  const typeMap: Record<CommoditySkuVOStatusEnum, 'success' | 'warning' | 'info' | 'danger' | 'primary'> = {
    [CommoditySkuVOStatusEnum.DRAFT]: 'info',
    [CommoditySkuVOStatusEnum.CHECKING]: 'warning',
    [CommoditySkuVOStatusEnum.ON_SHELF]: 'success',
    [CommoditySkuVOStatusEnum.OFF_SHELVES]: 'info',
    [CommoditySkuVOStatusEnum.FREEZE]: 'danger',
    [CommoditySkuVOStatusEnum.DELETED]: 'danger',
  }

  return typeMap[status] || 'info'
}
