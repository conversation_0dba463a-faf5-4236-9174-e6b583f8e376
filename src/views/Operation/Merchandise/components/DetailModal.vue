<script setup lang="ts">
import type { CommodityDetailVO, CommodityListVO } from '@/apiv2/product'
import type { GridProps } from '@/components/CgGrid'
import { commodityApi } from '@/api.services/product.service'
import { CommodityDetailVOProductStatusEnum } from '@/apiv2/product'
import { GridCellFormatName, GridCellRenderName } from '@/components/CgGrid'
import { formatDimension } from '@/utils/dimension'

const props = defineProps<{
  modelValue: boolean
  merchandiseId?: number
  rowData?: CommodityListVO
}>()

const emit = defineEmits(['update:modelValue'])

const modelValue = useVModel(props, 'modelValue', emit)
const loading = ref(false)

// 商品详情数据
const merchandiseDetail = ref<Partial<CommodityDetailVO & { spuCode?: string, name?: string, mainImage?: string, status?: string, skuList?: any[] }>>({
  id: undefined,
  spuCode: '',
  name: '',
  status: '',
  mainImage: '',
  skuList: [],
})

// 获取商品详情
async function fetchMerchandiseDetail(id: number) {
  loading.value = true
  try {
    const { data } = (await commodityApi.getCommodityDetail({ commodityId: id })).data
    if (data) {
      merchandiseDetail.value = {
        id: data.id,
        spuCode: props.rowData?.productCode,
        name: props.rowData?.commodityName,
        productStatus: data.productStatus,
        mainImage: props.rowData?.thumbnail,
        skuList: props.rowData?.skus || [],
      }
    }
  }
  catch (error) {
    console.error('Failed to fetch merchandise detail:', error)
    merchandiseDetail.value = { id: undefined, skuList: [] }
  }
  finally {
    loading.value = false
  }
}

// 当商品ID变化或模态框打开时获取数据
watch(
  [() => props.merchandiseId, modelValue],
  ([newId, newModelValue]) => {
    if (newModelValue && newId && newId !== merchandiseDetail.value?.id) {
      fetchMerchandiseDetail(newId)
    }
    else if (!newModelValue) {
      merchandiseDetail.value = { id: undefined, skuList: [] }
    }
  },
  { immediate: true },
)

// 状态文本映射
const statusTextMap: Record<number, string> = {
  0: '草稿',
  1: '审核中',
  2: '上架',
  3: '下架',
  4: '冻结',
  5: '已删除',
}

// 状态颜色映射 (Adjust keys based on actual status values used)
const statusColorMap: Record<number, string> = {
  0: 'text-gray-500', // 草稿
  1: 'text-yellow-500', // 审核中
  2: 'text-blue-500', // 上架
  3: 'text-red-500', // 下架
  4: 'text-orange-500', // 冻结
  5: 'text-gray-500', // 已删除
}

// 产品状态文本映射
const productStatusTextMap: Record<CommodityDetailVOProductStatusEnum, string> = {
  [CommodityDetailVOProductStatusEnum.DRAFT]: '草稿',
  [CommodityDetailVOProductStatusEnum.PENDING_REVIEW]: '待审核',
  [CommodityDetailVOProductStatusEnum.APPROVED]: '已审核',
  [CommodityDetailVOProductStatusEnum.ONLINE]: '已上架',
  [CommodityDetailVOProductStatusEnum.OFFLINE]: '已下架',
  [CommodityDetailVOProductStatusEnum.DELETED]: '已删除',
}

// 产品状态颜色映射
const productStatusColorMap: Record<CommodityDetailVOProductStatusEnum, string> = {
  [CommodityDetailVOProductStatusEnum.DRAFT]: 'text-gray-500', // 对应 info 类型
  [CommodityDetailVOProductStatusEnum.PENDING_REVIEW]: 'text-orange-500', // 对应 warning 类型
  [CommodityDetailVOProductStatusEnum.APPROVED]: 'text-orange-500', // 对应 warning 类型
  [CommodityDetailVOProductStatusEnum.ONLINE]: 'text-green-500', // 对应 success 类型
  [CommodityDetailVOProductStatusEnum.OFFLINE]: 'text-red-500', // 对应 danger 类型
  [CommodityDetailVOProductStatusEnum.DELETED]: 'text-gray-500', // 对应 info 类型
}

// CgGrid配置
const gridOption = reactive<GridProps>({
  options: {
    columns: [
      {
        field: 'skuCode',
        title: 'SKU',
        align: 'center',
        minWidth: 140,
      },
      {
        field: 'thumbnail',
        title: '图片',
        align: 'center',
        width: 80,
        cellRender: {
          name: GridCellRenderName.ImageUrl,
          props: {
            preview: true,
            width: 60,
            height: 60,
          },
        },
      },
      {
        field: 'status',
        title: 'SKU状态',
        align: 'center',
        minWidth: 100,
        slots: {
          default: 'statusTemp',
        },
      },
      {
        field: 'specs',
        title: '尺寸',
        align: 'center',
        minWidth: 150,
        slots: {
          default: 'specsTemp',
        },
      },
      {
        field: 'supplyPrice',
        title: '成本价(JPY)',
        align: 'center',
        minWidth: 120,
        formatter: GridCellFormatName.FormatCurrency,
      },
      {
        field: 'coefficient',
        title: '系数',
        align: 'center',
        minWidth: 80,
        formatter: GridCellFormatName.FormatFixedNumber,
      },
      {
        field: 'marketPrice',
        title: '市场售价(JPY)',
        align: 'center',
        minWidth: 120,
        formatter: [GridCellFormatName.FormatCurrency, 0],
      },
      {
        field: 'discountRate',
        title: '划线折扣',
        align: 'center',
        minWidth: 80,
        slots: {
          default: 'discountTemp',
        },
      },
      {
        field: 'promoPrice',
        title: '划线价(JPY)',
        align: 'center',
        minWidth: 120,
        formatter: [GridCellFormatName.FormatCurrency, 0],
      },
    ],
    border: false,
    round: true,
    stripe: false,
  },
  paginationVisible: false,
})

// 表格数据
const tableData = computed(() => merchandiseDetail.value.skuList || [])
</script>

<template>
  <CgDialog v-model="modelValue" title="查看详情" width="70%" :full-height="true" :footer-visible="false">
    <div v-loading="loading" class="p-4">
      <!-- 商品基本信息区域 -->
      <div
        class="merchandise-info mb-5 flex flex-col gap-4 border border-gray-200 rounded-md bg-gray-50 p-4 md:flex-row"
      >
        <div
          class="merchandise-image mx-auto h-32 w-32 flex-shrink-0 overflow-hidden border border-gray-200 rounded-md md:mx-0"
        >
          <el-image
            :src="merchandiseDetail.mainImage" alt="商品图片" class="h-full w-full object-cover"
            :preview-src-list="[merchandiseDetail.mainImage || '']" :initial-index="0" fit="cover"
            :hide-on-click-modal="true"
          />
        </div>
        <div class="merchandise-basic flex-1">
          <div class="flex flex-col gap-y-2">
            <div class="info-item">
              <span class="text-gray-500">SPU: </span>
              <span class="font-medium">{{ merchandiseDetail.spuCode || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="text-gray-500">商品品名: </span>
              <span class="font-medium">{{ merchandiseDetail.name || '-' }}</span>
            </div>
            <div class="info-item">
              <span class="text-gray-500">产品状态: </span>
              <span
                v-if="merchandiseDetail.productStatus !== undefined" class="font-medium"
                :class="productStatusColorMap[merchandiseDetail.productStatus] || ''"
              >
                {{ productStatusTextMap[merchandiseDetail.productStatus] || '未知' }}
              </span>
              <span v-else>-</span>
              <span v-if="merchandiseDetail.selectionReason" class="ml-2 text-sm text-gray-400">({{
                merchandiseDetail.selectionReason }})</span>
            </div>
          </div>
        </div>
      </div>

      <!-- SKU表格 -->
      <div class="sku-table w-full">
        <CgGrid v-bind="gridOption" :data="tableData">
          <!-- 状态列 -->
          <template #statusTemp="{ row }">
            <span :class="statusColorMap[row.status] || ''">{{ statusTextMap[row.status] || '未知' }}</span>
          </template>

          <!-- 折扣列 -->
          <template #discountTemp="{ row }">
            <span>{{ `${row.discountRate}%` || '-' }}</span>
          </template>

          <!-- 规格列 -->
          <template #specsTemp="{ row }">
            <div v-if="!row.specs || row.specs.length === 0">
              -
            </div>
            <div v-else>
              <div v-for="(spec, index) in row.specs" :key="index">
                {{ formatDimension(spec.length) }} × {{ formatDimension(spec.width) }} × {{ formatDimension(spec.height) }} cm
              </div>
            </div>
          </template>
        </CgGrid>
      </div>
    </div>
  </CgDialog>
</template>

<style scoped>
.el-image {
  display: block;
  /* Fix potential layout issues */
}
</style>
