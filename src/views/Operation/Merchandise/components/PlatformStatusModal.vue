<script setup lang="ts">
import type { BatchCommodityPlatformStatusRequest } from '@/apiv2/product'
import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'
import type { FormInstance } from 'element-plus'
import { commodityApi } from '@/api.services/product.service'

interface Props {
  modelValue: boolean
  selectedCount: number
  selectedIds?: number[] | string[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'confirm', 'success'])
const modelValue = useVModel(props, 'modelValue', emit)

const loading = ref(false)
const loadingPlatforms = ref(false)
const platformOptions = ref<{ label: string, value: number }[]>([])

// 商品状态选项 (与 API 枚举 IBatchCommodityPlatformStatusRequestListingStatus 对应)
const statusOptions: { label: string, value: BatchCommodityPlatformStatusRequest['listingStatus'] }[] = [
  { label: '待启动', value: 0 },
  { label: '准备中', value: 1 },
  { label: '已上架', value: 2 },
  { label: '已下架', value: 3 },
]

// 表单数据
const formData = ref<{
  platform: number | undefined
  status: BatchCommodityPlatformStatusRequest['listingStatus'] | undefined
}>({
  platform: undefined,
  status: undefined,
})

// 表单配置
const formOptions = computed<CgFormOption[][]>(() => [
  [
    {
      type: 'selectv2',
      key: 'platform',
      label: '平台',
      required: true,
      span: 24,
      props: {
        placeholder: '请选择',
      },
      dataSource: {
        type: 'static',
        staticData: platformOptions.value,
      },
    },
  ],
  [
    {
      type: 'selectv2',
      key: 'status',
      label: '商品状态',
      required: true,
      span: 24,
      props: {
        placeholder: '请选择状态',
      },
      dataSource: {
        type: 'static',
        staticData: statusOptions,
      },
    },
  ],
])

const formRef = ref<FormInstance>()

// 获取平台列表
async function fetchPlatforms() {
  loadingPlatforms.value = true
  try {
    const { data } = (await commodityApi.listPlatforms()).data
    platformOptions.value = data
      ?.filter(p => p.enabled && p.id !== undefined && p.platformName)
      .map(p => ({
        label: p.platformName!,
        value: p.id!,
      })) || []
  }
  catch (error) {
    console.error('获取平台列表失败:', error)
    platformOptions.value = []
  }
  finally {
    loadingPlatforms.value = false
  }
}

// 组件挂载或模态框打开时获取平台
onMounted(() => {
  if (modelValue.value) {
    fetchPlatforms()
  }
})

watch(modelValue, (newValue) => {
  if (newValue && platformOptions.value.length === 0) {
    fetchPlatforms()
  }
  if (!newValue) {
    formRef.value?.resetFields()
    formData.value.platform = undefined
    formData.value.status = undefined
  }
})

function closeModal() {
  modelValue.value = false
}

// 确认操作
async function handleConfirm() {
  if (!formRef.value)
    return
  try {
    const valid = await formRef.value.validate()
    if (!valid)
      return

    if (!props.selectedIds || props.selectedIds.length === 0) {
      ElMessage.warning('请至少选择一条数据')
      return
    }

    loading.value = true

    const requestBody: BatchCommodityPlatformStatusRequest = {
      commodityIds: props.selectedIds.map(id => Number(id)), // 确保是数字 ID
      platformId: formData.value.platform as number,
      listingStatus: formData.value.status as BatchCommodityPlatformStatusRequest['listingStatus'],
    }

    await commodityApi.batchUpdateCommodityPlatformStatus({ batchCommodityPlatformStatusRequest: requestBody })

    ElMessage.success('平台商品状态设置成功')
    emit('success') // 通知父组件刷新列表
    emit('confirm', { // 保留旧事件以防万一
      platform: formData.value.platform,
      status: formData.value.status,
      ids: props.selectedIds,
    })
    modelValue.value = false // 关闭弹窗
  }
  catch (error) {
    console.error('更新平台状态失败:', error)
    // 错误提示由 serviceOptions 处理
  }
  finally {
    loading.value = false
  }
}
</script>

<template>
  <CgDialog v-model="modelValue" title="设置平台商品状态" width="500px" :full-height="false">
    <div class="platform-status-modal p-5">
      <CgForm ref="formRef" v-model="formData" :options="formOptions" label-position="left" label-width="auto" :use-grid="true">
        <template #top>
          <div class="mb-6 flex items-center rounded-md bg-#e8f7f6 p-4">
            <svg class="mr-3 h-6 w-6 flex-shrink-0 text-#399e96" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="text-gray-700">选中了 <span class="mx-1 text-xl text-#399e96 font-bold">{{ props.selectedCount || 0 }}</span> 条数据</span>
          </div>
        </template>
      </CgForm>
    </div>

    <template #footer>
      <div class="flex justify-center gap-4">
        <ElButton :disabled="loading" @click="closeModal">
          取消
        </ElButton>
        <ElButton type="primary" :loading="loading" @click="handleConfirm">
          确认
        </ElButton>
      </div>
    </template>
  </CgDialog>
</template>
