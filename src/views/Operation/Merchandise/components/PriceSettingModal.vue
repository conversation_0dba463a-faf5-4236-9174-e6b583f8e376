<script setup lang="ts">
import type { CommodityListVO, CommoditySkuVOStatusEnum, SkuPriceParams, SkuSpecVO } from '@/apiv2/product'
import type { GridProps } from '@/components/CgGrid'
import type { PropType } from 'vue'
import { commodityApi } from '@/api.services/product.service'
import { GridCellFormatName, GridCellRenderName } from '@/components/CgGrid'
import { isNil } from 'lodash-es'
import { getSkuStatusClass, getSkuStatusText } from '../utils/statusUtils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    required: true,
  },
  selectedItems: {
    type: Array as PropType<CommodityListVO[]>,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue', 'success'])

const modelValue = useVModel(props, 'modelValue', emit)
const loading = ref(false)
const saving = ref(false)

interface EditableSku {
  id?: number
  skuCode?: string
  image?: string
  status?: CommoditySkuVOStatusEnum
  size?: string
  supplyCost: number
  ratio: number | null // 系数
  marketPrice: number // 市场售价
  discount: number | null // 划线折扣
  activityPrice: number | null // 划线价
  originalSku?: any // 保留原始数据引用
  specs?: Array<SkuSpecVO>
}

interface EditableSpu extends CommodityListVO {
  editableSkuList: EditableSku[]
}
const editableSpuList = ref<EditableSpu[]>([])

const marketRatioInput = ref<number | undefined>(undefined)
const discountInput = ref<number | undefined>(undefined)

function adjustPriceToEndIn90(price: number): number {
  const roundedPrice = Math.round(price)
  if (roundedPrice <= 0)
    return 90 // 小于等于0的特殊处理，按需求可能调整，暂定90

  const lastTwoDigits = roundedPrice % 100
  const hundredsAndUp = Math.floor(roundedPrice / 100) * 100

  if (lastTwoDigits === 90) {
    return roundedPrice
  }
  else if (lastTwoDigits < 90) {
    return hundredsAndUp + 90
  }
  else { // lastTwoDigits > 90
    return hundredsAndUp + 100 + 90 // 百位进1，末尾置90
  }
}

function recalculateActivityPrice(sku: EditableSku) {
  let rawActivityPrice = sku.marketPrice
  if (sku.marketPrice != null && typeof sku.discount === 'number' && sku.discount > 0 && sku.discount < 100) {
    rawActivityPrice = sku.marketPrice / (1 - sku.discount / 100)
  }

  sku.activityPrice = adjustPriceToEndIn90(rawActivityPrice)

  if (sku.activityPrice < sku.marketPrice) {
    console.warn(`计算出的划线价 (${sku.activityPrice}) 低于市场售价 (${sku.marketPrice}) for SKU ${sku.skuCode}`)
  }
}

function recalculateDiscount(sku: EditableSku) {
  if (sku.marketPrice != null && sku.activityPrice != null && sku.marketPrice > 0 && sku.activityPrice >= sku.marketPrice) {
    if (sku.activityPrice === 0) {
      sku.discount = null
      return
    }
    if (sku.activityPrice === sku.marketPrice) {
      sku.discount = null
      return
    }

    const calculatedDiscount = Math.round((1 - sku.marketPrice / sku.activityPrice) * 100 * 10) / 10
    if (calculatedDiscount > 0 && calculatedDiscount < 100) {
      sku.discount = calculatedDiscount
    }
    else {
      sku.discount = null
    }
  }
  else {
    sku.discount = null
  }
}

function recalculateRatio(sku: EditableSku) {
  if (sku.supplyCost != null && sku.supplyCost > 0 && sku.marketPrice != null) {
    const ratio = sku.marketPrice / sku.supplyCost
    sku.ratio = Math.round(ratio * 100) / 100
  }
  else {
    sku.ratio = null
  }
}

watch(
  [() => props.selectedItems, modelValue],
  ([newItems, newModelValue]) => {
    if (newModelValue && newItems && newItems.length > 0) {
      loading.value = true
      try {
        editableSpuList.value = newItems.map((spu) => {
          const editableSkuList = spu.skus?.map((apiSku): EditableSku => {
            const supplyPrice = apiSku.supplyPrice ?? 0
            const initialRatio = apiSku.coefficient ?? null

            // 初始时不计算，直接使用API返回的原始值
            const initialMarketPrice = apiSku.marketPrice ?? 0
            const initialDiscount = apiSku.discountRate ?? null
            const initialActivityPrice = apiSku.promoPrice ?? 0

            let currentRatio = initialRatio
            let currentMarketPrice = initialMarketPrice
            let currentDiscount = initialDiscount
            let currentActivityPrice = initialActivityPrice

            if (currentMarketPrice <= 0 && supplyPrice > 0 && typeof initialRatio === 'number' && initialRatio > 0) {
              currentMarketPrice = adjustPriceToEndIn90(supplyPrice * initialRatio)
            }
            else if (currentMarketPrice <= 0) {
              currentMarketPrice = supplyPrice > 0 ? adjustPriceToEndIn90(supplyPrice) : 0
            }

            if (typeof currentRatio !== 'number' || currentRatio <= 0) {
              if (supplyPrice > 0 && currentMarketPrice > 0) {
                currentRatio = Math.round((currentMarketPrice / supplyPrice) * 100) / 100
              }
              else {
                currentRatio = null
              }
            }

            if (currentActivityPrice <= 0 && currentMarketPrice > 0 && typeof currentDiscount === 'number' && currentDiscount > 0 && currentDiscount < 100) {
              const rawActivity = currentMarketPrice / (1 - currentDiscount / 100)
              currentActivityPrice = adjustPriceToEndIn90(rawActivity)
            }
            else if (currentActivityPrice > 0) {
              currentActivityPrice = adjustPriceToEndIn90(currentActivityPrice)
            }
            else {
              currentActivityPrice = adjustPriceToEndIn90(currentMarketPrice)
            }

            if (currentDiscount === null && currentMarketPrice > 0 && currentActivityPrice > currentMarketPrice) {
              const calculatedDiscount = Math.round((1 - currentMarketPrice / currentActivityPrice) * 100 * 10) / 10
              if (calculatedDiscount > 0 && calculatedDiscount < 100) {
                currentDiscount = calculatedDiscount
              }
            }

            return {
              id: apiSku.id,
              skuCode: apiSku.skuCode,
              image: apiSku.thumbnail,
              status: apiSku.status,
              specs: apiSku.specs,
              supplyCost: supplyPrice,
              ratio: currentRatio,
              marketPrice: currentMarketPrice,
              discount: currentDiscount,
              activityPrice: currentActivityPrice,
              originalSku: apiSku,
            }
          }) || []

          return {
            ...spu,
            editableSkuList,
          }
        })
      }
      catch (error) {
        console.error('处理商品数据失败:', error)
        editableSpuList.value = []
        ElMessage.error('加载商品数据时出错')
      }
      finally {
        loading.value = false
      }
      // 重置批量输入框为默认值
      marketRatioInput.value = undefined
      discountInput.value = undefined
    }
    else if (!newModelValue) {
      editableSpuList.value = []
      marketRatioInput.value = undefined
      discountInput.value = undefined
    }
  },
  { immediate: true, deep: true },
)

function applyMarketRatio() {
  if (marketRatioInput.value === undefined || marketRatioInput.value === null) {
    ElMessage.warning('请填写系数')
    return
  }
  const ratio = Number(marketRatioInput.value)
  if (Number.isNaN(ratio) || ratio <= 0) {
    ElMessage.warning('请输入有效的系数值（大于0）')
    return
  }
  editableSpuList.value.forEach((spu) => {
    spu.editableSkuList?.forEach((sku) => {
      sku.ratio = ratio
      if (sku.supplyCost != null && sku.supplyCost > 0) {
        const rawMarketPrice = sku.supplyCost * ratio
        sku.marketPrice = adjustPriceToEndIn90(rawMarketPrice) // 应用规则
      }
      else {
        ElMessage.warning(`SKU ${sku.skuCode} 缺少成本价，无法通过系数计算市场售价。`)
      }
      recalculateActivityPrice(sku)
    })
  })
}

function applyDiscount() {
  if (discountInput.value === undefined || discountInput.value === null) {
    ElMessage.warning('请填写划线折扣')
    return
  }
  const discountNum = Number(discountInput.value)
  // 划线折扣允许为0，但不允许小于0或大于等于100
  if (Number.isNaN(discountNum) || discountNum < 0 || discountNum >= 100) {
    ElMessage.warning('请输入有效的划线折扣值（0-100之间）')
    return
  }

  editableSpuList.value.forEach((spu) => {
    spu.editableSkuList?.forEach((sku) => {
      // 如果划线折扣为0，视为无效折扣，存为 null
      sku.discount = (discountNum > 0 && discountNum < 100) ? discountNum : null
      recalculateActivityPrice(sku) // 划线折扣变动，重新计算活动价
    })
  })
}

function handleSkuPriceChange(sku: EditableSku, field: keyof EditableSku, value: any) {
  let numericValue: number | null = null
  if (value !== null && value !== undefined && value !== '') {
    numericValue = Number(String(value).replace('%', ''))
    if (Number.isNaN(numericValue)) {
      ElMessage.warning(`输入的 ${field} 值无效`)
      return
    }
  }

  switch (field) {
    case 'ratio':
      if (numericValue !== null && numericValue > 0) {
        sku.ratio = numericValue
        if (sku.supplyCost != null && sku.supplyCost > 0) {
          const rawMarketPrice = sku.supplyCost * sku.ratio
          sku.marketPrice = adjustPriceToEndIn90(rawMarketPrice) // 应用规则
          recalculateActivityPrice(sku) // 重新计算活动价
        }
        else {
          ElMessage.warning(`SKU ${sku.skuCode || '-'} 缺少成本价，无法自动计算市场售价。`)
        }
      }
      else {
        ElMessage.warning('请输入有效的系数值（大于0）')
      }
      break

    case 'marketPrice':
      if (numericValue !== null && numericValue >= 0) {
        sku.marketPrice = numericValue
        recalculateRatio(sku)
        recalculateActivityPrice(sku)
      }
      else { ElMessage.warning('请输入有效的市场售价（非负数）') }
      break

    case 'discount':
      if (value === null || value === undefined || value === '') {
        sku.discount = null
        sku.activityPrice = null
        break
      }
      else {
        if (numericValue !== null && numericValue >= 0 && numericValue < 100) {
          sku.discount = numericValue > 0 ? numericValue : null
        }
        else {
          ElMessage.warning('请输入有效的折扣值（0-100）')
          return
        }
      }
      recalculateActivityPrice(sku)
      break

    case 'activityPrice':
      if (numericValue !== null && numericValue >= 0) {
        sku.activityPrice = numericValue
        recalculateDiscount(sku)
      }
      else { ElMessage.warning('请输入有效的划线价（非负数）') }
      break
  }
}

async function saveAllPriceSettings(done: () => void) {
  if (!editableSpuList.value || editableSpuList.value.length === 0) {
    ElMessage.error('没有需要保存的商品信息')
    return
  }

  saving.value = true

  // 构建SKU价格参数列表
  const skuPriceParamsList: SkuPriceParams[] = []

  for (const spu of editableSpuList.value) {
    if (!spu.editableSkuList) {
      console.warn(`跳过缺少SKU列表的商品: ${spu.commodityName || '未知商品'}`)
      continue
    }

    // 将每个SKU的价格信息添加到参数列表中
    for (const sku of spu.editableSkuList) {
      if (!sku.id) {
        console.warn(`跳过缺少ID的SKU: ${sku.skuCode || '未知SKU'}`)
        continue
      }

      if (sku.marketPrice < 0 || (isNil(sku.activityPrice) || sku.activityPrice! < 0)) {
        ElMessage.error(`SKU ${sku.skuCode || sku.id} 的价格设置无效，市场售价和划线价不能为负数。`)
        saving.value = false
        done()
        return
      }

      // 校验市场售价不能低于成本价
      if (sku.marketPrice < sku.supplyCost) {
        ElMessage.error(`SKU ${sku.skuCode || sku.id} 的市场售价(${sku.marketPrice})低于成本价(${sku.supplyCost})，市场售价不能低于成本价。`)
        saving.value = false
        done()
        return
      }
      if (sku.ratio === null || sku.ratio <= 0) {
        console.warn(`SKU ${sku.skuCode || sku.id} 的系数无效 (${sku.ratio})，将使用默认值 1.0`)
      }

      const skuPriceParams: SkuPriceParams = {
        skuId: sku.id,
        coefficient: (sku.ratio !== null && sku.ratio > 0) ? sku.ratio : 1.0,
        marketPrice: sku.marketPrice,
        discountRate: sku.discount!,
        promoPrice: sku.activityPrice!,
      }

      skuPriceParamsList.push(skuPriceParams)
    }
  }

  if (skuPriceParamsList.length === 0) {
    ElMessage.warning('没有有效的SKU进行价格设置。')
    saving.value = false
    done()
    return
  }

  try {
    // 调用批量更新SKU价格接口
    await commodityApi.batchUpdateSkuPriceParams({ skuPriceParams: skuPriceParamsList })

    ElMessage.success(`成功保存 ${skuPriceParamsList.length} 个SKU的售价设置`)
    emit('success') // 通知父组件
    modelValue.value = false // 关闭弹窗
  }
  catch (error) {
    console.error('保存价格设置失败:', error)
  }
  finally {
    saving.value = false
    done()
  }
}

// --- 表格配置 ---
const gridOption = reactive<GridProps>({
  options: {
    columns: [
      { field: 'skuCode', title: 'SKU', align: 'center', width: 100 },
      {
        field: 'image',
        title: '图片',
        align: 'center',
        width: 80,
        cellRender: { name: GridCellRenderName.ImageUrl, props: { width: 60, height: 60, preview: true } },
      },
      { field: 'status', title: 'SKU状态', align: 'center', width: 100, slots: { default: 'statusTemp' } },
      {
        field: 'specs',
        title: '尺寸',
        align: 'center',
        minWidth: 150,
        slots: {
          default: 'specsTemp',
        },
      },
      {
        field: 'supplyCost',
        title: '成本价(JPY)',
        align: 'center',
        width: 120,
        formatter: GridCellFormatName.FormatCurrency,
      },
      { field: 'ratio', title: '系数', align: 'center', width: 100, slots: { default: 'ratioTemp' } },
      { field: 'marketPrice', title: '市场售价 (JPY)', align: 'center', width: 150, slots: { default: 'marketPriceTemp' } },
      { field: 'discount', title: '划线折扣 (%)', align: 'center', width: 140, slots: { default: 'discountTemp' } },
      { field: 'activityPrice', title: '划线价 (JPY)', align: 'center', width: 150, slots: { default: 'activityPriceTemp' } },
    ],
    border: true,
    round: true,
    stripe: false,
    height: 'auto',
    showOverflow: false,
  },
  paginationVisible: false,
})
</script>

<template>
  <CgDialog
    v-model="modelValue" title="设置售价" width="70%" :footer-visible="true" @submit="saveAllPriceSettings"
    @cancel="modelValue = false"
  >
    <el-scrollbar class="spu-sku-list flex-1">
      <div v-loading="loading || saving" class="price-setting-container h-full flex flex-col">
        <!-- 批量设置 -->
        <div
          v-if="editableSpuList.length > 0"
          class="batch-setting-area mb-5 min-w-680px flex flex-col gap-4 border border-gray-200 rounded-md bg-gray-50 p-4 md:flex-row md:justify-between"
        >
          <div class="market-price-setting flex-1">
            <div class="mb-2 font-medium">
              市场售价
            </div>
            <div class="flex flex-wrap items-center gap-2">
              <span class="text-nowrap text-gray-500">系数:</span>
              <CgInputNumber
                v-model="marketRatioInput" placeholder="输入系数" class="w-40!" :min="0.01" :precision="2"
                :step="0.1" clearable
              />
              <CgButton type="primary" size="default" :disabled="saving" @click="applyMarketRatio">
                批量修改
              </CgButton>
            </div>
            <p class="mt-1 text-xs text-gray-400">
              修改系数将自动调整所有选中商品SKU的市场售价和划线价。
            </p>
          </div>

          <div class="discount-setting flex-1">
            <div class="mb-2 font-medium">
              划线价
            </div>
            <div class="flex flex-wrap items-center gap-2">
              <span class="text-nowrap text-gray-500">划线折扣:</span>
              <CgInputNumber
                v-model="discountInput" placeholder="0-100" class="w-40!" :min="0" :max="100"
                :precision="1" :step="0.1" clearable suffix="%"
              />
              <CgButton type="primary" size="default" :disabled="saving" @click="applyDiscount">
                批量修改
              </CgButton>
            </div>
            <p class="mt-1 text-xs text-gray-400">
              修改划线折扣将自动调整所有选中商品SKU的划线价。
            </p>
          </div>
        </div>

        <!-- SKU 表格 -->
        <div v-for="spu in editableSpuList" :key="spu.id" class="spu-item mb-6">
          <div class="spu-info pb-3">
            <div class="info-item">
              <span class="text-gray-500">商品品名: </span>
              <span class="font-medium">{{ spu.commodityName || '-' }}</span>
              <span class="ml-4 text-gray-500">SPU: </span>
              <span class="font-medium">{{ spu.productCode || '-' }}</span>
            </div>
          </div>

          <div class="sku-table-container h-300px shadow-sm">
            <CgGrid v-bind="gridOption" :data="spu.editableSkuList">
              <!-- 状态列 -->
              <template #statusTemp="{ row }">
                <ElTag disable-transitions :type="getSkuStatusClass(row.status)" size="small" effect="light">
                  {{ getSkuStatusText(row.status) }}
                </ElTag>
              </template>
              <!-- 系数编辑 -->
              <template #ratioTemp="{ row }">
                <CgInputNumber
                  :model-value="row.ratio" :min="0.01" :step="0.01" :precision="2" placeholder="系数"
                  :disabled="saving" clearable
                  @change="(val: number | undefined | null) => handleSkuPriceChange(row, 'ratio', val)"
                />
              </template>
              <!-- 市场售价编辑 -->
              <template #marketPriceTemp="{ row }">
                <CgInputNumber
                  :model-value="row.marketPrice" :min="0" :precision="0" placeholder="市场售价" :step="1"
                  :disabled="saving"
                  @change="(val: number | undefined) => handleSkuPriceChange(row, 'marketPrice', val)"
                />
              </template>
              <!-- 划线折扣编辑 -->
              <template #discountTemp="{ row }">
                <CgInputNumber
                  :model-value="row.discount" :min="0" :max="100" :precision="1" placeholder="划线折扣"
                  :step="0.1" :disabled="saving" clearable suffix="%"
                  @change="(val: number | undefined | null) => handleSkuPriceChange(row, 'discount', val)"
                />
              </template>
              <!-- 划线价编辑 -->
              <template #activityPriceTemp="{ row }">
                <CgInputNumber
                  :model-value="row.activityPrice" :min="0" :precision="0" placeholder="划线价" :step="1"
                  :disabled="saving"
                  @change="(val: number | undefined) => handleSkuPriceChange(row, 'activityPrice', val)"
                />
              </template>
              <!-- 规格列 -->
              <template #specsTemp="{ row }">
                <div v-if="!row.specs || row.specs.length === 0">
                  -
                </div>
                <div v-else>
                  <div v-for="(spec, index) in row.specs" :key="index">
                    {{ spec.length }} × {{ spec.width }} × {{ spec.height }} cm
                  </div>
                </div>
              </template>
            </CgGrid>
          </div>
        </div>

        <el-empty v-if="!loading && editableSpuList.length === 0" description="请先在列表页勾选商品" />
      </div>
    </el-scrollbar>
  </CgDialog>
</template>

<style scoped>
:deep(.vxe-cell .cg-input-number),
:deep(.vxe-cell .el-input-number) {
  width: 100%;
}

:deep(.el-input-group__append) {
  padding: 0 8px;
}

.batch-setting-area {
  width: 60%;
  margin: 0 0 0 auto;
}
</style>
