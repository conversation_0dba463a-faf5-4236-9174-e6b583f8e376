<script setup lang="ts">
import type { BatchCommoditySelectionRequest } from '@/apiv2/product'
import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'
import type { FormInstance, FormRules } from 'element-plus'
import { commodityApi } from '@/api.services/product.service'

interface Props {
  modelValue: boolean
  selectedCount: number
  selectedIds?: number[] | string[]
}

const props = defineProps<Props>()
const emit = defineEmits(['update:modelValue', 'confirm', 'success'])
const modelValue = useVModel(props, 'modelValue', emit)

// 状态选项 (与 API 枚举 IBatchCommoditySelectionRequestSelectionStatus 对应)
const statusOptions: { label: string, value: BatchCommoditySelectionRequest['selectionStatus'] }[] = [
  { label: '通过', value: 1 },
  { label: '不通过', value: 2 },
]

// 表单数据
const formData = ref<{
  status: BatchCommoditySelectionRequest['selectionStatus'] | undefined
  remark: string | undefined
}>({
  status: undefined,
  remark: undefined,
})

// 表单配置
const formOptions = ref<CgFormOption[][]>([
  [
    {
      type: 'selectv2',
      key: 'status',
      label: '选品状态',
      required: true,
      span: 24,
      props: {
        placeholder: '请选择状态',
      },
      dataSource: {
        type: 'static',
        staticData: statusOptions,
      },
    },
  ],
  [
    {
      type: 'input',
      key: 'remark',
      span: 24,
      props: {
        placeholder: '请输入处理意见',
        rows: 4,
        type: 'textarea',
      },
    },
  ],
])

// 表单验证规则
const formRules = reactive<FormRules>({
  status: [{ required: true, message: '请选择选品状态', trigger: 'change' }],
  remark: [
    {
      validator: (rule, value, callback) => {
        // 仅当状态为“不通过”（2）时，备注为必填
        if (formData.value.status === 2 && !value) {
          callback(new Error('选品不通过时，处理意见不能为空'))
        }
        else {
          callback()
        }
      },
      trigger: ['blur', 'change'], // 状态改变或失焦时都校验
    },
  ],
})

const formRef = ref<FormInstance>()

// 模态框关闭时重置表单
watch(modelValue, (newValue) => {
  if (!newValue) {
    formRef.value?.resetFields()
    formData.value.status = undefined
    formData.value.remark = undefined
  }
})

function closeModal() {
  modelValue.value = false
}

// 确认操作
async function handleConfirm(done: () => void) {
  if (!formRef.value) {
    done()
    return
  }
  try {
    const valid = await formRef.value.validate()
    if (!valid) {
      done()
      return
    }

    if (!props.selectedIds || props.selectedIds.length === 0) {
      ElMessage.warning('请至少选择一条数据')
      done()
      return
    }

    const requestBody: BatchCommoditySelectionRequest = {
      commodityIds: props.selectedIds.map(id => Number(id)), // 确保是数字 ID
      selectionStatus: formData.value.status as BatchCommoditySelectionRequest['selectionStatus'],
      selectionReason: formData.value.remark,
    }

    await commodityApi.batchUpdateCommoditySelectionStatus({ batchCommoditySelectionRequest: requestBody })

    ElMessage.success('选品状态设置成功')
    emit('success') // 通知父组件刷新
    emit('confirm', { // 保留旧事件
      status: formData.value.status,
      remark: formData.value.remark,
      ids: props.selectedIds,
    })
    modelValue.value = false // 关闭弹窗
  }
  catch (error) {
    console.error('更新选品状态失败:', error)
    // 错误提示由 serviceOptions 处理
  }
  finally {
    done()
  }
}
</script>

<template>
  <CgDialog v-model="modelValue" title="设置选品状态" width="500px" :full-height="false">
    <div class="selection-status-modal p-5">
      <CgForm
        ref="formRef" v-model="formData" :options="formOptions" :rules="formRules" label-position="left"
        label-width="auto" :use-grid="false"
      >
        <template #top>
          <div class="mb-6 flex items-center rounded-md bg-#e8f7f6 p-4">
            <svg
              class="mr-3 h-6 w-6 flex-shrink-0 text-#399e96" xmlns="http://www.w3.org/2000/svg" fill="none"
              viewBox="0 0 24 24" stroke="currentColor"
            >
              <path
                stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <span class="text-gray-700">已选中 <span class="mx-1 text-xl text-#399e96 font-bold">{{ props.selectedCount
              || 0 }}</span> 条数据</span>
          </div>
        </template>
      </CgForm>
    </div>

    <template #footer>
      <div class="flex justify-center gap-4">
        <CgButton @click="closeModal">
          取消
        </CgButton>
        <CgButton type="primary" auto-loading @click="handleConfirm">
          确认
        </CgButton>
      </div>
    </template>
  </CgDialog>
</template>
