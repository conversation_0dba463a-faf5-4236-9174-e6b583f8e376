<script setup lang="ts">
import { ref } from 'vue'

defineOptions({
  name: 'LinkButtonDemo',
})

const title = ref('链接按钮组件示例')
</script>

<template>
  <div class="link-button-demo">
    <h2>{{ title }}</h2>

    <div class="demo-section">
      <h3>基本用法</h3>
      <div class="button-group">
        <!-- 基本用法 - 使用路径 -->
        <CgLinkButton to="/product/list" type="primary">
          跳转到产品列表
        </CgLinkButton>

        <!-- 使用路由名称 -->
        <CgLinkButton :to="{ name: 'ProductList' }" type="success">
          跳转到产品列表（使用路由名称）
        </CgLinkButton>
      </div>
    </div>

    <div class="demo-section">
      <h3>带参数的路由</h3>
      <div class="button-group">
        <!-- 带参数的路由 -->
        <CgLinkButton :to="{ name: 'ProductDetail', params: { id: 123 } }" type="info">
          查看产品详情
        </CgLinkButton>

        <!-- 带查询参数的路由 -->
        <CgLinkButton :to="{ path: '/product/list', query: { category: 'electronics' } }">
          查看电子产品
        </CgLinkButton>
      </div>
    </div>

    <div class="demo-section">
      <h3>特殊行为</h3>
      <div class="button-group">
        <!-- 替换当前路由 -->
        <CgLinkButton to="/product/list" replace type="warning">
          替换当前路由
        </CgLinkButton>

        <!-- 在新窗口打开 -->
        <CgLinkButton to="/product/list" target="_blank" type="danger">
          在新窗口打开
        </CgLinkButton>
      </div>
    </div>

    <div class="demo-section">
      <h3>不同样式</h3>
      <div class="button-group">
        <CgLinkButton to="/product/list" type="primary" plain>
          朴素按钮
        </CgLinkButton>

        <CgLinkButton to="/product/list" type="success" round>
          圆角按钮
        </CgLinkButton>

        <CgLinkButton to="/product/list" type="info" circle>
          <ElIcon><IEpArrowDown /></ElIcon>
        </CgLinkButton>

        <CgLinkButton to="/product/list" type="warning" text>
          文字按钮
        </CgLinkButton>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.link-button-demo {
  padding: 20px;

  .demo-section {
    margin-bottom: 30px;

    h3 {
      margin-bottom: 15px;
      font-size: 16px;
      border-left: 4px solid var(--el-color-primary);
      padding-left: 10px;
    }

    .button-group {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }
  }
}
</style>
