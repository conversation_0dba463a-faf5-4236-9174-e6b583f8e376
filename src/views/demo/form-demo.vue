<script setup lang="ts">
import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'
import type { ImageValue } from '@/components/CgImageManager'
import type { FormInstance } from 'element-plus'
import ComponentTagsEnum from '@/components/CgElementUI/CgForm/ComponentTagsEnum'
import CgForm from '@/components/CgElementUI/CgForm/Form.vue'
// CgImageManager 已经在 ComponentTags.ts 中注册，不需要再导入

const formRef = ref<FormInstance>()
const isDetail = ref(false)
const useGrid = ref(false)
const layout = ref(false)

// 模拟工单相关ID
const relevantId = ref('SPU-456')

// 获取动态ID
// 暂时不使用该函数
// function _getDynamicId(): string | undefined {
//   if (relevantId.value && relevantId.value.includes('-')) {
//     const parts = relevantId.value.split('-')
//     if (parts.length > 1) {
//       return parts[1] // 返回 ID 部分
//     }
//   }
//   return undefined
// }

// 模拟 SKU 列表
const skuList = ref(['SKU001', 'SKU002', 'SKU003'])

// 自定义区域配置
const customSections = [
  {
    key: 'installation',
    title: '安装说明',
    emptyText: '拖拽文件到此区域或点击上传安装说明',
    useDraggable: true,
  },
  {
    key: 'multimedia',
    title: '多媒体',
    emptyText: '拖拽文件到此区域或点击上传多媒体文件',
    useDraggable: true,
  },
]

const newFormModel = ref({
  // 基本信息
  id: '123456',
  spu: 'SPU-2023-001',
  sku: '',
  manualImageCount: 2,
  workCount: 5,
  psdLink: 'https://example.com/psd-file',

  // 图片数据
  productImages: {
    main: [
      {
        url: 'https://img.alicdn.com/imgextra/i4/O1CN01Ky3sCz1JvJ5dJRGMC_!!6000000001088-0-tps-800-800.jpg',
        imageKey: 'product/main/sample-main-1.jpg',
        filename: '产品主图1.jpg',
        size: 245,
        dimensions: { width: 800, height: 800 },
      },
      {
        url: 'https://img.alicdn.com/imgextra/i2/O1CN01Qz3s8Y1yCWkNXEQlT_!!6000000006550-0-tps-800-800.jpg',
        imageKey: 'product/main/sample-main-2.jpg',
        filename: '产品主图2.jpg',
        size: 198,
        dimensions: { width: 800, height: 800 },
      },
    ],
    sku: {
      SKU001: [
        {
          url: 'https://img.alicdn.com/imgextra/i3/O1CN01vwc6Xt1zbRPjZFPUP_!!6000000006730-0-tps-600-600.jpg',
          imageKey: 'product/sku/sku001-1.jpg',
          filename: 'SKU001-图片1.jpg',
          size: 156,
          dimensions: { width: 600, height: 600 },
        },
      ],
      SKU002: [
        {
          url: 'https://img.alicdn.com/imgextra/i1/O1CN01Yq3HiY1VGAdp5kxgI_!!6000000002637-0-tps-600-600.jpg',
          imageKey: 'product/sku/sku002-1.jpg',
          filename: 'SKU002-图片1.jpg',
          size: 142,
          dimensions: { width: 600, height: 600 },
        },
      ],
    },
    detail: [
      {
        url: 'https://img.alicdn.com/imgextra/i4/O1CN01Ky3sCz1JvJ5dJRGMC_!!6000000001088-0-tps-800-800.jpg',
        imageKey: 'product/detail/detail-1.jpg',
        filename: '详情图1.jpg',
        size: 320,
        dimensions: { width: 1200, height: 1600 },
      },
      {
        url: 'https://img.alicdn.com/imgextra/i2/O1CN01Qz3s8Y1yCWkNXEQlT_!!6000000006550-0-tps-800-800.jpg',
        imageKey: 'product/detail/detail-2.jpg',
        filename: '详情图2.jpg',
        size: 285,
        dimensions: { width: 1200, height: 1600 },
      },
    ],
    // 新增安装说明区域
    installation: [
      {
        url: 'https://example.com/product.jpg',
        imageKey: 'product/files/installation-1.jpg',
        filename: '安装说明1.jpg',
        size: 156,
        dimensions: { width: 600, height: 600 },
      },
      {
        url: 'https://example.com/product.jpg',
        imageKey: 'product/files/installation-guide.jpg',
        filename: '安装手册.jpg',
        size: 1024,
      },
    ],
    // 新增多媒体区域
    multimedia: [
      {
        url: 'https://example.com/product.jpg',
        imageKey: 'product/files/product.jpg',
        filename: '产品展示视频.jpg',
        size: 5120,
      },
    ],
  } as ImageValue,
})
const newFormOptions = computed<CgFormOption[][]>(() => [
  [
    {
      key: 'productCode',
      label: 'SPU',
      props: {
        isEditing: false,
      },
      required: true,
      span: 8,
      type: 'input',
    },
  ],
  [
    {
      key: 'id',
      label: '本地化',
      props: {
        link: true,
        target: '_blank',
        text: '产品详情页',
        to: 'https://erp-test.caguuu.cn/#/design?transId=$model.transId&spu=$model.productCode',
        type: 'primary',
      },
      span: 6,
      type: 'linkButton',
    },
  ],
  [
    {
      key: 'productImages',
      label: '本地化素材',
      props: {
        customSections: [
          {
            emptyText: '拖拽文件到此区域或点击上传安装说明',
            key: 'installation',
            title: '安装说明',
            useDraggable: true,
          },
          {
            emptyText: '拖拽文件到此区域或点击上传多媒体文件',
            key: 'multimedia',
            title: '多媒体',
            useDraggable: true,
          },
        ],
        skuList: [],
        uploadStrategyName: 'extendedProduct',
      },
      required: true,
      span: 24,
      type: 'imageManager',
    },
  ],
  [
    {
      key: 'manualImageCount',
      label: '安装说明书图片数量',
      props: {},
      required: true,
      span: 8,
      type: 'inputNumber',
    },
    {
      key: 'workCount',
      label: '工作总数',
      props: {},
      span: 8,
      type: 'inputNumber',
    },
  ],
  [
    {
      key: 'psdLink',
      label: 'PSD链接',
      props: {},
      required: true,
      span: 16,
      type: 'input',
    },
  ],
  [
    {
      key: 'interpreter',
      label: '翻译处理人',
      type: 'selectv2',
      span: 6,
      props: {
        placeholder: '请选择翻译处理人',
        filterable: true,
        props: {
          label: 'name',
          value: 'id',
        },
      },
      dataSource: {
        type: 'api',
        apiIdentifier: 'listUsers',
        queryParams: {
          userSearchReq: {
            pageNum: 1,
            pageSize: 10000,
          },
        },
        valueField: 'id',
        labelField: 'name',
      },
      required: true,
    },
  ],
  [
    {
      key: 'designer',
      label: '设计处理人',
      type: 'selectv2',
      span: 6,
      props: {
        placeholder: '请选择设计处理人',
        filterable: true,
        props: {
          label: 'name',
          value: 'id',
        },
      },
      dataSource: {
        type: 'api',
        apiIdentifier: 'listUsers',
        queryParams: {
          userSearchReq: {
            pageNum: 1,
            pageSize: 10000,
          },
        },
        valueField: 'id',
        labelField: 'name',
      },
      required: true,
    },
  ],
])
const productFormOptions = computed<CgFormOption[][]>(() => [
  [
    {
      type: ComponentTagsEnum.Input,
      key: 'spu',
      label: 'SPU',
      span: 8,
      props: {
        isEditing: false,
      },
    },
    {
      type: ComponentTagsEnum.Input,
      key: 'productName',
      label: '产品名称',
      span: 8,
      props: {
        isEditing: false,
      },
    },
    {
      label: '产品类目',
      key: 'leafCategoryId',
      type: ComponentTagsEnum.CategoryTree,
      span: 8,
      props: {
        leafOnly: true,
        checkStrictly: false,
        isEditing: false,
      },
    },
  ],
  [
    {
      label: '开发方式',
      key: 'developMethod',
      type: ComponentTagsEnum.SelectV2,
      span: 8,
      props: {
        placeholder: '请选择开发方式',
        filterable: false,
        isEditing: false,
      },
      dataSource: {
        type: 'api',
        apiIdentifier: 'listDicts',
        queryParams: {
          codes: 'develop-method',
        },
        transformIdentifier: 'transformDictData',
      },
    },
    {
      label: '产品链接',
      key: 'productLink',
      type: ComponentTagsEnum.Input,
      span: 8,
      props: {
        placeholder: '请输入产品链接',
        isEditing: false,
      },
    },
    {
      label: '供应商',
      key: 'supplierId',
      type: ComponentTagsEnum.SelectV2,
      span: 8,
      props: {
        placeholder: '请选择供应商',
        filterable: true,
        multiple: false,
        props: {
          label: 'supplierName',
          value: 'id',
        },
        isEditing: false,
      },
      dataSource: {
        type: 'api',
        apiIdentifier: 'listSuppliers',
        queryParams: {},
        valueField: 'id',
        labelField: 'supplierName',
      },
    },
  ],
  [
    {
      label: '产品主图',
      key: 'thumbnail',
      type: ComponentTagsEnum.Image,
      span: 4,
      props: {
        width: 120,
        height: 120,
      },
    },
  ],
  [
    {
      label: '选品审核',
      key: 'id',
      type: ComponentTagsEnum.LinkButton,
      span: 4,
      props: {
        to: {
          name: 'ProductDetail',
          params: { id: '$model.id' },
        },
        text: '产品详情页',
        type: 'primary',
        link: true,
      },
    },
  ],
  [
    {
      label: '信息采集',
      key: 'id',
      type: ComponentTagsEnum.LinkButton,
      span: 4,
      props: {
        to: {
          name: 'ProductEdit',
          params: { id: '$model.id' },
        },
        text: '产品编辑页',
        type: 'primary',
        link: true,
      },
    },
  ],
  [
    {
      type: ComponentTagsEnum.SelectV2,
      key: 'sourceLang',
      label: '素材源语言',
      span: 8,
      props: {
        data: [
          { label: '中文', value: 'zh' },
          { label: '中文(繁体)', value: 'zh-hant' },
          { label: '中英文', value: 'both' },
          { label: '英文', value: 'en' },
        ],
      },
      required: true,
    },
  ],
  [
    {
      type: 'richText',
      key: 'content',
      label: '内容描述',
      span: 24,
      required: true,
    },
  ],
])

// 提交表单
async function handleSubmit() {
  if (!formRef.value)
    return

  try {
    await formRef.value.validate()
    // 使用 newFormModel 而不是 formModel
    console.warn('表单数据:', newFormModel.value)
    ElMessage.success('提交成功')
  }
  catch (error) {
    console.error('表单验证失败:', error)
    ElMessage.error('请检查表单填写是否正确')
  }
}

// 重置表单
function handleReset() {
  if (!formRef.value)
    return
  formRef.value.resetFields()
  ElMessage.info('表单已重置')
}

// 切换布局方式
function toggleLayout(type: 'default' | 'grid' | 'column') {
  // 重置所有布局状态
  useGrid.value = false
  layout.value = false
  // 如果点击当前激活的布局，不做任何改变
  if (
    (type === 'default' && !useGrid.value && !layout.value)
    || (type === 'grid' && useGrid.value)
    || (type === 'column' && layout.value)
  ) {
    return
  }

  // 设置新的布局状态
  switch (type) {
    case 'grid':
      useGrid.value = true
      break
    case 'column':
      layout.value = true
      break
    // default 布局不需要设置任何状态
  }
}

// 产品表单数据模型
const productFormModel = ref({
  id: 13433,
  spu: 'PROD-2025-001',
  productName: '智能家居控制器',
  leafCategoryId: 320,
  developMethod: 1,
  productLink: 'https://example.com/product',
  supplierId: 4,
  thumbnail: 'https://img.alicdn.com/imgextra/i4/O1CN01Ky3sCz1JvJ5dJRGMC_!!6000000001088-0-tps-800-800.jpg',
  sourceLang: 'zh',
  content: '',
})

// 产品表单引用
const productFormRef = ref<FormInstance>()

// 处理产品表单提交
async function handleProductSubmit() {
  if (!productFormRef.value)
    return

  try {
    await productFormRef.value.validate()
    console.warn('产品表单数据:', productFormModel.value)
    ElMessage.success('产品表单提交成功')
  }
  catch (error) {
    console.error('产品表单验证失败:', error)
    ElMessage.error('请检查产品表单填写是否正确')
  }
}

// 重置产品表单
function handleProductReset() {
  if (!productFormRef.value)
    return
  productFormRef.value.resetFields()
  ElMessage.info('产品表单已重置')
}

const content = ref('')
</script>

<template>
  <div class="min-h-screen p-6">
    <div class="rounded-lg bg-white p-6 shadow-sm">
      <div class="mb-4 flex justify-between">
        <div class="flex items-center space-x-4">
          <span class="text-gray-600">当前相关ID: {{ relevantId }}</span>
          <el-button size="small" @click="relevantId = `SPU-${Math.floor(Math.random() * 1000)}`">
            随机更改ID
          </el-button>
        </div>

        <div class="flex space-x-4">
          <el-switch v-model="isDetail" active-text="详情模式" inactive-text="编辑模式" />
          <el-button-group>
            <el-button :type="!useGrid && !layout ? 'primary' : ''" @click="toggleLayout('default')">
              默认布局
            </el-button>
            <el-button :type="useGrid ? 'primary' : ''" @click="toggleLayout('grid')">
              栅格布局
            </el-button>
            <el-button :type="layout ? 'primary' : ''" @click="toggleLayout('column')">
              列布局
            </el-button>
          </el-button-group>
        </div>
      </div>

      <CgForm
        ref="formRef" v-model="newFormModel" :options="newFormOptions" :detail="isDetail" :use-grid="useGrid"
        :layout="layout" label-position="right" :col-span="8" :gutter="20" :context="{ spu: relevantId }"
      >
        <template #top>
          <div class="mb-4 bg-blue text-lg font-bold">
            <!-- {{ newFormModel }} -->
          </div>
        </template>
        <template #bottom>
          <div class="mt-4 bg-emerald text-sm text-gray-500">
            注：所有带 * 的字段为必填项
          </div>
        </template>
      </CgForm>
      <!-- 当前表单数据展示 -->
      <div class="mt-6 border-t pt-4">
        <h3 class="mb-2 text-lg font-bold">
          当前表单数据
        </h3>
        <pre>{{ newFormModel }}</pre>
        <h3 class="mb-2 text-lg font-bold">
          当前表单schema
        </h3>
        <pre>{{ newFormOptions }}</pre>

        <!-- 产品主图展示 -->
        <div class="mb-4">
          <h4 class="text-md mb-2 font-semibold">
            产品主图
          </h4>
          <div class="flex items-center gap-4">
            <div
              v-if="newFormModel.productImages?.main?.[0]?.url"
              class="h-32 w-32 flex items-center justify-center overflow-hidden border rounded"
            >
              <img
                :src="newFormModel.productImages?.main?.[0]?.url" alt="产品主图"
                class="max-h-full max-w-full object-contain"
              >
            </div>
            <div v-else class="h-32 w-32 flex items-center justify-center border rounded text-gray-400">
              暂无主图
            </div>
            <div>
              <p>
                <span class="font-semibold">主图 Key:</span> {{ newFormModel.productImages?.main?.[0]?.imageKey || '无' }}
              </p>
            </div>
          </div>
        </div>

        <!-- 基本信息展示 -->
        <div class="mb-4">
          <h4 class="text-md mb-2 font-semibold">
            基本信息
          </h4>
          <div class="grid grid-cols-2 gap-4">
            <div><span class="font-semibold">ID:</span> {{ newFormModel.id }}</div>
            <div><span class="font-semibold">SPU:</span> {{ newFormModel.spu }}</div>
            <div><span class="font-semibold">SKU:</span> {{ newFormModel.sku }}</div>
            <div><span class="font-semibold">安装说明书图片数量:</span> {{ newFormModel.manualImageCount }}</div>
            <div><span class="font-semibold">工作总数:</span> {{ newFormModel.workCount }}</div>
            <div><span class="font-semibold">PSD链接:</span> {{ newFormModel.psdLink }}</div>
          </div>
        </div>

        <!-- 图片数据统计 -->
        <div class="mb-4">
          <h4 class="text-md mb-2 font-semibold">
            图片数据统计
          </h4>
          <div class="grid grid-cols-3 gap-4">
            <div><span class="font-semibold">主图数量:</span> {{ newFormModel.productImages?.main.length }}</div>
            <div>
              <span class="font-semibold">SKU 图片数量:</span> {{ Object.values(newFormModel.productImages?.sku
                || []).flat().length }}
            </div>
            <div><span class="font-semibold">详情图数量:</span> {{ newFormModel.productImages?.detail.length }}</div>
            <div>
              <span class="font-semibold">安装说明数量:</span> {{ newFormModel.productImages?.installation?.length || 0 }}
            </div>
            <div>
              <span class="font-semibold">多媒体数量:</span> {{ newFormModel.productImages?.multimedia?.length || 0 }}
            </div>
          </div>
        </div>

        <!-- 完整数据展示 (折叠) -->
        <div class="mb-4">
          <el-collapse>
            <el-collapse-item title="查看完整表单数据">
              <pre class="max-h-80 overflow-auto rounded bg-gray-100 p-2 text-xs">{{ JSON.stringify(newFormModel, null, 2) }}
        </pre>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>

    <!-- 产品表单区域 -->
    <div class="mt-10 rounded-lg bg-white p-6 shadow-sm">
      <h2 class="mb-4 text-xl text-gray-800 font-bold">
        产品表单测试
      </h2>

      <div class="mb-4 flex justify-between">
        <div class="flex items-center space-x-4">
          <span class="text-gray-600">产品ID: {{ productFormModel.spu }}</span>
        </div>

        <div class="flex space-x-4">
          <el-switch v-model="isDetail" active-text="详情模式" inactive-text="编辑模式" />
        </div>
      </div>

      <CgForm
        ref="productFormRef" v-model="productFormModel" :options="productFormOptions" :detail="isDetail"
        :use-grid="useGrid" :layout="layout" label-position="right" :col-span="8" :gutter="20"
        :context="{ spu: relevantId }"
      >
        <template #top>
          <div class="mb-4 rounded bg-blue-50 p-2 text-lg font-bold">
            产品基本信息
          </div>
        </template>
        <template #bottom>
          <div class="mt-4 rounded bg-emerald-50 p-2 text-sm text-gray-500">
            产品表单说明：所有带 * 的字段为必填项
          </div>
        </template>
      </CgForm>

      <!-- 产品表单数据展示 -->
      <div class="mt-6 border-t pt-4">
        <h3 class="mb-2 text-lg font-bold">
          产品表单数据
        </h3>
        <pre>{{ productFormModel }}</pre>
        <h3 class="mb-2 text-lg font-bold">
          产品表单schema
        </h3>
        <pre>{{ productFormOptions }}</pre>

        <!-- 产品主图展示 -->
        <div class="mb-4">
          <h4 class="text-md mb-2 font-semibold">
            产品主图
          </h4>
          <div class="flex items-center gap-4">
            <div
              v-if="productFormModel.thumbnail"
              class="h-32 w-32 flex items-center justify-center overflow-hidden border rounded"
            >
              <img :src="productFormModel.thumbnail" alt="产品主图" class="max-h-full max-w-full object-contain">
            </div>
            <div v-else class="h-32 w-32 flex items-center justify-center border rounded text-gray-400">
              暂无主图
            </div>
            <div>
              <p>
                <span class="font-semibold">产品名称:</span> {{ productFormModel.productName || '无' }}
              </p>
              <p>
                <span class="font-semibold">SPU编码:</span> {{ productFormModel.spu || '无' }}
              </p>
            </div>
          </div>
        </div>

        <!-- 产品基本信息展示 -->
        <div class="mb-4">
          <h4 class="text-md mb-2 font-semibold">
            产品基本信息
          </h4>
          <div class="grid grid-cols-2 gap-4">
            <div><span class="font-semibold">SPU:</span> {{ productFormModel.spu }}</div>
            <div><span class="font-semibold">产品名称:</span> {{ productFormModel.productName }}</div>
            <div><span class="font-semibold">产品类目:</span> {{ productFormModel.leafCategoryId }}</div>
            <div><span class="font-semibold">开发方式:</span> {{ productFormModel.developMethod }}</div>
            <div><span class="font-semibold">产品链接:</span> {{ productFormModel.productLink }}</div>
            <div><span class="font-semibold">供应商ID:</span> {{ productFormModel.supplierId }}</div>
            <div><span class="font-semibold">内容描述:</span> {{ productFormModel.content }}</div>
          </div>
        </div>

        <!-- 产品表单按钮 -->
        <div class="mt-4 flex justify-center gap-4">
          <el-button type="primary" @click="handleProductSubmit">
            提交产品表单
          </el-button>
          <el-button @click="handleProductReset">
            重置产品表单
          </el-button>
        </div>
      </div>
    </div>

    <div class="mt-6">
      <h3 class="mb-2 text-lg font-bold">
        富文本编辑器测试
      </h3>
      <CgTinyMCE v-model="content" :height="200" />
    </div>

    <div>
      {{ content }}
    </div>

    <!-- submit and reset buttons -->
    <div class="mt-8 flex justify-center gap-4">
      <el-button type="primary" @click="handleSubmit">
        提交
      </el-button>
      <el-button @click="handleReset">
        重置
      </el-button>
    </div>
  </div>
</template>
