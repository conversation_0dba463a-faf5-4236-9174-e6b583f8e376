<script setup lang="ts">
import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'
import type { FormInstance } from 'element-plus'
import { categoryApi } from '@/api.services/product.service'
import CgCategoryTree from '@/components/CgCategoryTree'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  editData: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': [value: any, isEdit: boolean]
}>()

const modelValue = useVModel(props, 'modelValue', emits)

const formRef = ref<FormInstance>()

const title = computed(() => {
  return Object.keys(props.editData).length > 0 ? '编辑类目' : '添加类目'
})

const formData = reactive({
  parentCategory: '' as string | undefined, // 父类目的完整路径名称（显示用）
  parentCategoryId: undefined as number | undefined, // 父类目ID（提交用）
  selectedCategoryName: '' as string | undefined, // 选中的类目名称
  categoryName: '',
  categoryCode: '',
  categoryImage: '',
  description: '',
})

function initFormData() {
  if (Object.keys(props.editData).length > 0) {
    // 编辑模式，填充表单数据
    formData.parentCategory = props.editData.parentName || ''
    formData.parentCategoryId = props.editData.parentId || null
    formData.selectedCategoryName = ''
    formData.categoryName = props.editData.name || ''
    formData.categoryCode = props.editData.code || ''
    formData.categoryImage = props.editData.icon || ''
    formData.description = props.editData.description || ''
  }
  else {
    // 添加模式，重置表单数据
    formData.parentCategory = ''
    formData.parentCategoryId = undefined
    formData.selectedCategoryName = ''
    formData.categoryName = ''
    formData.categoryCode = ''
    formData.categoryImage = ''
    formData.description = ''
  }
}

const formOptions = computed<CgFormOption[][]>(() => [
  [
    {
      label: '上级类目',
      key: 'parentCategoryId',
      type: 'slot',
      span: 24,
      slotName: 'parentCategory',
      labelTips: '为空时，表示添加一级类目',
    },
  ],
  [
    {
      label: '类目名称',
      key: 'categoryName',
      type: 'input',
      span: 24,
      required: true,
      props: {
        placeholder: '请输入类目名称',
        maxlength: 50,
        showWordLimit: true,
      },
    },
  ],
  [
    {
      label: '类目编码',
      key: 'categoryCode',
      type: 'input',
      span: 24,
      required: false,
      props: {
        placeholder: '请输入类目编码，全局不能重复',
        maxlength: 20,
      },
    },
  ],
  [
    {
      label: '类目图片',
      key: 'categoryImage',
      type: 'slot',
      span: 24,
      slotName: 'categoryImage',
    },
  ],
  [
    {
      label: '描述',
      key: 'description',
      type: 'input',
      span: 24,
      props: {
        type: 'textarea',
        placeholder: '请输入描述，不超过200个字符',
        maxlength: 200,
        showWordLimit: true,
        rows: 4,
      },
    },
  ],
])

function handleUploadSuccess(url: string, key: string) {
  formData.categoryImage = key
}

async function handleConfirm(done: () => void) {
  if (!formRef.value)
    return

  await formRef.value.validate(async (valid: boolean) => {
    if (valid) {
      try {
        const requestData = {
          categoryName: formData.categoryName,
          categoryCode: formData.categoryCode,
          description: formData.description,
          parentId: formData.parentCategoryId,
          icon: formData.categoryImage,
          id: undefined,
        }
        requestData.id = props.editData.id

        let response
        const isEdit = Object.keys(props.editData).length > 0
        if (isEdit) {
          // 编辑模式
          response = (await categoryApi.updateCategory({
            id: props.editData.id,
            categoryRequest: requestData,
          })).data
        }
        else {
          // 添加模式
          response = (await categoryApi.createCategory({
            categoryRequest: requestData,
          })).data
        }

        if (response.success) {
          ElMessage.success('保存成功')
          modelValue.value = false
          emits('success', response.data, isEdit)
        }
      }
      finally {
        done()
      }
    }
    done()
  })
}

function handleClose() {
  formRef.value?.resetFields()
}

watch(() => modelValue.value, (val) => {
  if (val) {
    initFormData()
  }
  else {
    handleClose()
  }
}, { immediate: true })
</script>

<template>
  <CgDialog
    v-model="modelValue" :full-height="false" :title="title" width="550px"
    @submit="handleConfirm" @close="handleClose"
  >
    <CgForm ref="formRef" v-model="formData" :options="formOptions">
      <template #parentCategory>
        <CgCategoryTree
          v-model="formData.parentCategoryId"
          @change="(id, name) => {
            formData.parentCategory = Array.isArray(name) ? name.join(', ') : name
            formData.selectedCategoryName = Array.isArray(name) ? name.join(', ') : name
          }"
        />
      </template>
      <template #categoryImage>
        <CgImageUploader
          v-model="formData.categoryImage"
          folder-name="category-icon"
          access-type="private"
          upload-text="上传类目图片"
          @upload-success="handleUploadSuccess"
        />
      </template>
    </CgForm>
  </CgDialog>
</template>
