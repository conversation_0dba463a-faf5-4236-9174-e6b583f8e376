<script setup lang="ts">
import type { CategoryVO } from '@/apiv2/product'
import type { GridInstance, GridProps } from '@/components/CgGrid'
import type { VxeGlobalRendererHandles, VxeGridPropTypes } from 'vxe-table'
import { categoryApi } from '@/api.services/product.service'
import { GridCellRenderName } from '@/components/CgGrid'
import { CategoryOperationEnum } from '@/enums/category'
import { Search } from '@element-plus/icons-vue'
import { ElTree } from 'element-plus'
import CategoryEdit from './components/CategoryEdit.vue'

defineOptions({
  name: 'CategoryList',
})

const gridRef = ref<GridInstance | null>(null)
const treeRef = ref<InstanceType<typeof ElTree>>()
const searchKeyword = ref('')
const categoryTreeData = ref<CategoryVO[]>([])
const isSearching = ref(false)
const originalTreeData = ref<CategoryVO[]>([])

const queryModel = reactive({
  categoryId: undefined as number | undefined,
})

const treeProps = {
  label: 'categoryName',
  children: 'children',
  isLeaf: (data: any, node: any) => node.level >= 4,
}

function filterNode(value: string, data: CategoryVO, _: CategoryVO): boolean {
  if (!value)
    return true
  return data.categoryName?.includes(value) || false
}

const debouncedSearch = useDebounceFn(async (val: string) => {
  if (!val) {
    // 如果搜索关键词为空，恢复原始树数据
    if (isSearching.value) {
      categoryTreeData.value = [...originalTreeData.value]
      isSearching.value = false
    }
    return
  }

  try {
    isSearching.value = true
    const response = (await categoryApi.searchCategories({ keyword: val })).data
    if (response.success && response.data) {
      categoryTreeData.value = response.data
    }
  }
  catch (error) {
    console.error('搜索类目失败:', error)
    ElMessage.error('搜索类目失败')
  }
}, 500)

watch(searchKeyword, (val) => {
  debouncedSearch(val)
})

function handleNodeClick(data: CategoryVO) {
  if (data.id !== undefined) {
    queryModel.categoryId = data.id
    loadCategoryProducts(data.id)
  }
}

const columns: VxeGridPropTypes.Columns = [
  {
    field: 'icon',
    title: '图片',
    cellRender: {
      name: GridCellRenderName.ImageUrl,
    },
    width: 80,
  },
  {
    field: 'name',
    title: '类目名称',
    minWidth: 140,
  },
  {
    field: 'code',
    title: '类目编码',
    minWidth: 140,
  },
  {
    field: 'parentName',
    title: '上级类目',
    minWidth: 140,
  },
  {
    field: 'description',
    title: '描述',
    minWidth: 140,
  },
  {
    title: '操作',
    minWidth: 140,
    cellRender: {
      name: GridCellRenderName.Operate,
      props: {
        items: [
          {
            text: '编辑',
            id: CategoryOperationEnum.EDIT,
          },
          {
            text: '删除',
            id: CategoryOperationEnum.DELETE,
          },
        ],
      },
      events: { command: operatorCmd },
    },
  },
]

const currentEditData = ref<any>({})
const openEdit = ref(false)

const gridOption = reactive<GridProps>({
  // data: [],
  options: {
    columns,
    border: false,
    height: 'auto',
    round: true,
  },
  toolbarConfig: {
    refresh: true,
    buttons: [
      {
        text: '添加类目',
        on: {
          click: () => {
            // 清空当前编辑数据，确保打开的是空白表单
            currentEditData.value = {}
            openEdit.value = true
          },
        },
      },
      // {
      //   text: '导入新增',
      //   on: {
      //     click: () => {
      //       ElMessage.info('导入新增功能待实现')
      //     },
      //   },
      // },
    ],
    // exportFile: {
    //   exportProxy: {
    //     request: () => {
    //       // TODO
    //       return new Promise(() => { })
    //     },
    //   },
    // },
  },
  paginationVisible: false,
})

const categoryProducts = ref<any[]>([])
async function loadCategoryProducts(categoryId: number) {
  try {
    gridRef.value?.setLoading(true)
    const [categoryRes, childrenRes] = await Promise.all([
      categoryApi.getCategory({ id: categoryId }),
      categoryApi.getChildCategories({ id: categoryId }),
    ])

    const formatCategory = (data: any, parentName = '') => ({
      id: data.id ?? null,
      icon: data.icon ?? '',
      name: data.categoryName ?? '',
      code: data.categoryCode ?? '',
      parentName,
      parentId: data.parentId ?? null,
      description: data.description ?? '',
    })

    categoryProducts.value = [
      ...(categoryRes.data && categoryRes.data.data
        ? [formatCategory(categoryRes.data.data, categoryRes.data.data.parent?.categoryName ?? '')]
        : []),
      ...(childrenRes.data && childrenRes.data.data
        ? childrenRes.data.data.map((item: any) =>
            formatCategory(item, categoryRes.data.data?.categoryName ?? ''))
        : []),
    ]
  }
  catch (error) {
    console.error('加载类目数据失败:', error)
    categoryProducts.value = []
  }
  finally {
    gridRef.value?.setLoading(false)
  }
}

// 获取树数据（此处后端会全量返回1-3级数据加上第一条的完整子链）
async function fetchCategoryTreeData() {
  try {
    gridRef.value?.setLoading(true)
    const response = (await categoryApi.getCategoryTree()).data
    if (response.success && response.data) {
      categoryTreeData.value = response.data
      // 保存原始树数据，用于搜索后恢复
      originalTreeData.value = [...response.data]
      return response.data
    }
    return []
  }
  catch (error) {
    console.error('获取类目树数据失败:', error)
    return []
  }
  finally {
    gridRef.value?.setLoading(false)
  }
}

async function lazyLoad(node: any, resolve: (data: CategoryVO[]) => void) {
  // 如果节点没有 id 或 children 已存在，直接返回已存在的子节点数据或者空数组
  if (!node.data || node.data.id === undefined) {
    resolve([])
    return
  }
  if (node.data.children && node.data.children.length > 0) {
    resolve(node.data.children)
    return
  }
  try {
    const res = (await categoryApi.getChildCategories({ id: node.data.id })).data
    if (res.success && res.data) {
      const childrenNodes = res.data.map((item: CategoryVO) => ({
        ...item,
      }))
      // 将懒加载得到的节点数据赋值给当前节点，防止后续重复请求
      node.data.children = childrenNodes
      resolve(childrenNodes)
    }
    else {
      resolve([])
    }
  }
  catch (error) {
    console.error('懒加载节点失败:', error)
    resolve([])
  }
}

// 计算默认展开的节点，即每层仅展开第一个子节点，直到没有子节点为止
const defaultExpandedKeys = ref<number[]>([])
function computeDefaultExpandedKeys(nodes: CategoryVO[]): number[] {
  const keys: number[] = []
  let currentNodes = nodes
  while (currentNodes && currentNodes.length > 0) {
    const firstNode = currentNodes[0]
    if (firstNode.id !== undefined) {
      keys.push(firstNode.id)
    }
    currentNodes = firstNode.children || []
  }
  return keys
}

onMounted(async () => {
  const treeData = await fetchCategoryTreeData()

  if (treeData.length > 0) {
    // 计算默认只展开第一子链的节点 key 数组
    defaultExpandedKeys.value = computeDefaultExpandedKeys(treeData)

    const firstCategory = treeData[0]
    queryModel.categoryId = firstCategory.id
    firstCategory.id && loadCategoryProducts(firstCategory.id)

    nextTick(() => {
      if (treeRef.value) {
        treeRef.value.setCurrentKey(firstCategory.id)
      }
    })
  }
})

function operatorCmd(params: VxeGlobalRendererHandles.RenderTableCellParams, cmd: { id: string, text: string }) {
  const row = params.row
  switch (cmd.id) {
    case CategoryOperationEnum.EDIT:
      currentEditData.value = { ...row }
      openEdit.value = true
      break
    case CategoryOperationEnum.DELETE:
      ElMessageBox({
        title: '提示',
        message: '确定要删除该类目吗？',
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            // 设置按钮加载状态
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '删除中...'

            try {
              // 调用删除接口
              const response = (await categoryApi.deleteCategory({ id: row.id })).data

              if (response.success) {
                ElMessage.success('删除类目成功')
                if (queryModel.categoryId) {
                  loadCategoryProducts(queryModel.categoryId)
                }

                await fetchCategoryTreeData()
                defaultExpandedKeys.value = computeDefaultExpandedKeys(categoryTreeData.value)
              }
              else {
                ElMessage.error(response.message || '删除类目失败')
              }
            }
            finally {
              done()
              instance.confirmButtonLoading = false
            }
          }
          else {
            done()
          }
        },
      })
      break
  }
}

function handleEditSuccess(data: any, isEdit: boolean) {
  if (queryModel.categoryId) {
    loadCategoryProducts(queryModel.categoryId)
  }
  currentEditData.value = {}

  if (!isEdit) {
    fetchCategoryTreeData().then(() => {
      // 重新计算默认展开链条（如更新了树结构）
      defaultExpandedKeys.value = computeDefaultExpandedKeys(categoryTreeData.value)
      treeRef.value?.setCurrentKey(queryModel.categoryId ?? undefined)
    })
  }
}

async function handleBeforeRefresh() {
  if (queryModel.categoryId !== undefined) {
    await loadCategoryProducts(queryModel.categoryId)
    return false
  }
  else {
    ElMessage.info('请先选择一个类目再刷新')
    return false
  }
}
</script>

<template>
  <div class="category-list h-fullw-full h-full flex gap-4">
    <CgGrid ref="gridRef" v-bind="gridOption" :data="categoryProducts" :before-refresh="handleBeforeRefresh">
      <template #grid-left>
        <div class="mr-3 h-full w-1/5 flex flex-col gap-4 rounded-1 bg-white" style="border: 1px solid #e8eaec;">
          <div class="w-full p-xl pb-0">
            <ElInput v-model="searchKeyword" size="large" placeholder="搜索类目" clearable :loading="isSearching">
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </ElInput>
          </div>

          <div class="h-full w-full overflow-auto rounded-1">
            <ElTree
              ref="treeRef" :data="categoryTreeData" :props="treeProps" :filter-node-method="filterNode"
              :default-expanded-keys="defaultExpandedKeys" :lazy="true" :load="lazyLoad" highlight-current node-key="id"
              :expand-on-click-node="false" class="h-full w-full" @node-click="handleNodeClick"
            />
          </div>
        </div>
      </template>
    </CgGrid>
    <CategoryEdit v-if="openEdit" v-model="openEdit" :edit-data="currentEditData" @success="handleEditSuccess" />
  </div>
</template>

<style lang="scss" scoped>
.category-list {
  padding: $cg-layout-right-padding;
  padding-top: 8px;
  background-color: $cg-layout-right-bgcolor;
}

.el-tree {
  --el-tree-node-hover-bg-color: #f5f7fa;
  --el-tree-node-content-height: 32px;
}

:deep(.el-tree-node__content) {
  height: var(--el-tree-node-content-height);
}

:deep(.el-tree-node__label) {
  font-size: 15px;
}

:deep(.el-tree-node__expand-icon) {
  font-size: 15px;
}

:deep(.el-tree-node.is-current > .el-tree-node__content) {
  background-color: #ecf5ff;
  color: #399e96;
}
</style>
