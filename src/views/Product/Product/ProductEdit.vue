<script setup lang="ts">
import CgCategoryTree from '@/components/CgCategoryTree'
import { useTagsViewClose } from '@/composables/useTagsViewClose'
import CommodityProductSku from './components/CommodityProductSku.vue'
import ProductDescription from './components/ProductDescription.vue'
import ProductFile from './components/ProductFile.vue'
import ProductImage from './components/ProductImage.vue'
import ProductSku from './components/ProductSku.vue'
import PropertiesDialog from './components/PropertiesDialog.vue'
import { useMaterialZipUploader } from './composables/useMaterialZipUploader'
import { useProductForm } from './composables/useProductForm'
import { useUnsavedChangesWarning } from './composables/useUnsavedChangesWarning'

defineOptions({
  name: 'ProductEdit',
})

const props = defineProps({
  editData: {
    type: Object,
    default: () => ({}),
  },
})
const emits = defineEmits<{
  success: [value: any, isEdit: boolean]
}>()

const { closeCurrentPage } = useTagsViewClose()

const {
  formRef,
  formData,
  isEditLoading,
  productImage,
  formOptions,
  handleUpdateSpuImage,
  handleUpdateSkuThumbnail,
  handleSave,
  handleMerchantSave,
  activeTab,
  activeMerchantTab,
  isMerchantView,
  commodityFormData,
  isFormDirty,
} = useProductForm(props, {
  success: (value, isEdit) => emits('success', value, isEdit),
})

useUnsavedChangesWarning(isFormDirty)

const skuComponentRef = useTemplateRef<InstanceType<typeof ProductSku>>('skuComponent')
const commoditySkuComponentRef = useTemplateRef<InstanceType<typeof CommodityProductSku>>('commoditySkuComponent')

const reactiveSkuCodes = ref<string[]>([])
const reactiveCommoditySkuCodes = ref<string[]>([])

// --- 更新 SKU Codes 的函数 ---
async function updateReactiveSkuCodes() {
  await nextTick()
  if (skuComponentRef.value) {
    try {
      const currentSkus = await skuComponentRef.value.getSkuData()
      if (Array.isArray(currentSkus)) {
        reactiveSkuCodes.value = currentSkus
          .map(sku => sku?.skuCode)
          .filter(Boolean) as string[]
      }
      else {
        reactiveSkuCodes.value = []
      }
    }
    catch (error) {
      console.error('Error getting SKU data:', error)
      reactiveSkuCodes.value = []
    }
  }
  else {
    reactiveSkuCodes.value = []
  }
}

async function updateReactiveCommoditySkuCodes() {
  await nextTick()
  if (commoditySkuComponentRef.value) {
    try {
      const currentCommoditySkus = await commoditySkuComponentRef.value.getSkuData()
      if (Array.isArray(currentCommoditySkus)) {
        reactiveCommoditySkuCodes.value = currentCommoditySkus
          .map(sku => sku?.skuCode)
          .filter(Boolean) as string[]
      }
      else {
        reactiveCommoditySkuCodes.value = []
      }
    }
    catch (error) {
      console.error('Error getting Commodity SKU data:', error)
      reactiveCommoditySkuCodes.value = []
    }
  }
  else {
    reactiveCommoditySkuCodes.value = []
  }
}

// --- 监听 Tab 切换 ---
watch(activeTab, (newTab) => {
  if (newTab === 'images') {
    updateReactiveSkuCodes()
  }
})

watch(activeMerchantTab, (newTab) => {
  if (newTab === 'images') {
    updateReactiveCommoditySkuCodes()
  }
})

function handleEditSave(done: () => void, isDraft = false) {
  try {
    if (!isMerchantView.value) {
      handleSave(done, isDraft, skuComponentRef.value?.getSkuData() || [])
    }
    else {
      handleMerchantSave(done, commoditySkuComponentRef.value?.getSkuData() || [])
    }
  }
  catch {
    done()
  }
}

const {
  isUploading: isUploadingZip,
  progress: zipUploadProgress,
  unzipStatus,
  startUpload: startUploadMaterialZip,
} = useMaterialZipUploader(toRef(formData, 'imagesComponent'), toRef(formData, 'files'))

const commodityFormReactive = reactive(commodityFormData.value) // 保证是 reactive 对象
const commodityImagesRef = toRef(commodityFormReactive, 'imagesComponent')
const commodityFilesRef = toRef(commodityFormReactive, 'files')
const {
  isUploading: isUploadingZipLocal,
  progress: zipUploadProgressLocal,
  unzipStatus: unzipStatusLocal,
  startUpload: startUploadMaterialZipLocal,
} = useMaterialZipUploader(
  commodityImagesRef,
  commodityFilesRef as any,
)
</script>

<template>
  <div v-loading="isEditLoading" class="product-create h-full w-full flex flex-col overflow-y-auto">
    <div class="flex flex-shrink-0">
      <div>
        <CgImageUploader
          v-model="productImage" folder-name="category-icon" upload-text="上传图片"
          :size="{ width: 166, height: 166 }" :view-only="true" tip-text="请选图片信息<br>上传SPU主图" access-type="private"
        />
      </div>
      <div class="ml-4 flex-1">
        <CgForm ref="formRef" v-model="formData" :options="formOptions" :table-style="true" :col-span="12">
          <template #category>
            <CgCategoryTree
              v-model="formData.leafCategoryId" leaf-only emit-full-path :check-strictly="false" @change="(id: any) => {
                formData.leafCategoryId = id
              }"
            />
          </template>
          <template #properties>
            <PropertiesDialog v-model="formData.properties" />
          </template>
        </CgForm>
      </div>
    </div>

    <div
      class="product-content relative mt-2" :class="{
        'flex-1 overflow-y-auto': !isMerchantView,
        'h-[560px] overflow-y-auto flex-shrink-0': isMerchantView,
      }"
    >
      <ElTabs v-model="activeTab" class="h-full max-w-full">
        <ElTabPane key="description" label="产品描述" name="description" class="h-full">
          <ProductDescription v-model="formData.description" />
        </ElTabPane>
        <ElTabPane key="skus" label="SKU 信息" name="skus" class="h-full">
          <ProductSku ref="skuComponent" v-model="formData.skus as any" edit />
        </ElTabPane>
        <ElTabPane key="images" label="图片信息" name="images" class="h-full">
          <ProductImage
            v-model="formData.imagesComponent" :sku-list="reactiveSkuCodes"
            @update-spu-image="handleUpdateSpuImage" @update:sku-thumbnail="handleUpdateSkuThumbnail"
          />
        </ElTabPane>
        <ElTabPane key="files" label="文件信息" name="files" class="h-full">
          <ProductFile v-model="formData.files as any" />
        </ElTabPane>
      </ElTabs>
      <CgButton
        v-show="activeTab === 'images' || activeTab === 'files'"
        class="right-0 top-0 flex items-center absolute!" type="primary" plain size="small" :loading="isUploadingZip"
        @click="startUploadMaterialZip"
      >
        <template #default>
          <span v-if="isUploadingZip && zipUploadProgress > 0">
            上传中 {{ zipUploadProgress }}%
          </span>
          <span v-else-if="unzipStatus === 0 || unzipStatus === 1">
            解压中...
          </span>
          <span v-else>
            上传素材压缩包
          </span>
        </template>
      </CgButton>
    </div>

    <div v-if="isMerchantView" class="relative mt-2 flex flex-shrink-0 flex-col overflow-y-auto">
      <div class="mt-1 text-lg font-medium">
        本地化信息：
      </div>
      <ElTabs v-model="activeMerchantTab" class="h-full max-w-full">
        <ElTabPane key="base" label="SKU信息" name="base" class="h-full">
          <div>
            <div class="mb-4 rounded-md bg-gray-50 p-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="flex items-center">
                  <span class="min-w-80px w-24 text-gray-600">商品品名：</span>
                  <CgInput v-model="commodityFormData.commodityName" class="flex-1" />
                </div>
              </div>
            </div>
            <div class="mb-4 rounded-md bg-white">
              <CommodityProductSku ref="commoditySkuComponent" v-model="commodityFormData.skus as any" />
            </div>
          </div>
        </ElTabPane>
        <ElTabPane key="images" label="图片信息" name="images" class="h-full" lazy>
          <div>
            <div class="rounded-md bg-white">
              <ProductImage
                v-model="commodityFormData.imagesComponent" :sku-list="reactiveCommoditySkuCodes"
                @update:sku-thumbnail="handleUpdateSkuThumbnail"
              />
            </div>
          </div>
        </ElTabPane>
        <ElTabPane key="files" label="文件信息" name="files" class="h-full" lazy>
          <div class="max-h-400px overflow-auto">
            <div class="rounded-md bg-white">
              <ProductFile v-model="commodityFormData.files as any" />
            </div>
          </div>
        </ElTabPane>
      </ElTabs>
      <CgButton
        v-show="activeMerchantTab === 'images' || activeMerchantTab === 'files'" class="right-0 top-8 absolute!"
        type="primary" plain size="small" :loading="isUploadingZipLocal" @click="startUploadMaterialZipLocal"
      >
        <template #default>
          <span v-if="isUploadingZipLocal && zipUploadProgressLocal > 0">
            上传中 {{ zipUploadProgressLocal }}%
          </span>
          <span v-else-if="unzipStatusLocal === 0 || unzipStatusLocal === 1">
            解压中...
          </span>
          <span v-else>
            上传素材压缩包
          </span>
        </template>
      </CgButton>
    </div>

    <div class="mt-4 flex flex-shrink-0 justify-end gap-2">
      <CgButton @click="closeCurrentPage">
        关闭
      </CgButton>
      <CgButton auto-loading type="primary" plain @click="handleEditSave($event, true)">
        提交
      </CgButton>
    </div>
  </div>
</template>

<style scoped lang="scss">
.product-create {
  padding: 16px;
}

.product-content {
  :deep(.el-tabs__content) {
    overflow-y: auto;
  }
}
</style>
