import type { ImageValue } from '@/components/CgImageManager'
import type { ProductImagesValue } from '../components/types'

/**
 * 将旧格式的图片数据转换为新格式
 * @param oldFormat 旧格式的图片数据
 * @returns 新格式的图片数据
 */
export function convertToNewImageFormat(oldFormat: ProductImagesValue): ImageValue {
  return {
    main: oldFormat.mainImages || [],
    sku: oldFormat.skuImages || {},
    detail: oldFormat.detailImages || [],
  }
}

/**
 * 将新格式的图片数据转换为旧格式
 * @param newFormat 新格式的图片数据
 * @returns 旧格式的图片数据
 */
export function convertToOldImageFormat(newFormat: ImageValue): ProductImagesValue {
  return {
    mainImages: newFormat.main || [],
    skuImages: newFormat.sku || {},
    detailImages: newFormat.detail || [],
  }
}
