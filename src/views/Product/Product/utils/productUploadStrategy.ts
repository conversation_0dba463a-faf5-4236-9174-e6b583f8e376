import type { ImageSection, UploadStrategy } from '@/components/CgImageManager'
import { GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'

/**
 * 产品模块的图片上传策略
 */
export const productUploadStrategy: UploadStrategy = {
  getUploadFileType(section: ImageSection, _sku?: string) {
    switch (section) {
      case 'main':
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_IMAGES_MAIN
      case 'detail':
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_IMAGES_DETAIL
      case 'sku':
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_IMAGES_SKU
      default:
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_FILES
    }
  },
}
