import type { CommodityImageVO, ProductImageUpdateRequest, ProductImageVO, ProductSkuCreateRequest, ProductSkuUpdateRequest } from '@/apiv2/product'
import type { ImageInfo, ProductImagesValue } from '../components/types'
import { ProductImageVOImageTypeEnum } from '@/apiv2/product'
import { isNil } from 'lodash-es'

/**
 * 从接口返回的 images (ProductImageVO[]) 转换为组件可使用的图片数据 (ProductImagesValue)
 */
export function convertApiImagesToComponentFormat(apiImages: (ProductImageVO | CommodityImageVO)[]): ProductImagesValue {
  const result: ProductImagesValue = {
    mainImages: [],
    skuImages: {},
    detailImages: [],
  }

  apiImages.forEach((img) => {
    if (!img.imageKey)
      return
    // TODO 待确认 假设 img.imageSize 是图片尺寸，格式为 "1920x1080"
    const dimensions = img.imageSize ? img.imageSize.split('x').map(Number) : undefined

    // 将 imageKey 映射为 ImageInfo.url
    const imageInfo: ImageInfo = {
      id: img.id,
      skuCode: img.skuCode || undefined,
      url: img.imageUrl || '',
      imageKey: img.imageKey,
      filename: img.filename,
      dimensions: dimensions ? { width: dimensions[0], height: dimensions[1] } : undefined,
    }

    // 根据 imageType 进行分类处理
    switch (img.imageType) {
      case ProductImageVOImageTypeEnum.PRODUCT_MAIN_IMAGE: {
        // 主图
        result.mainImages.push(imageInfo)
        break
      }
      case ProductImageVOImageTypeEnum.PRODUCT_SKU_IMAGE: {
        // 从文件名或其他地方提取SKU Code
        // 假设在格式中有额外字段或者能从文件名提取skuCode
        const skuCode = img.skuCode
        if (!skuCode)
          break

        if (!result.skuImages[skuCode]) {
          result.skuImages[skuCode] = []
        }
        result.skuImages[skuCode].push(imageInfo)
        break
      }
      case ProductImageVOImageTypeEnum.PRODUCT_DETAIL_IMAGE: {
        // 详情图
        result.detailImages.push(imageInfo)
        break
      }
      default:
        break
    }
  })

  // 处理封面图逻辑：确保cover=true的图片放在mainImages的首位
  const coverImageIndex = apiImages.findIndex(img => img.cover && img.imageType === ProductImageVOImageTypeEnum.PRODUCT_MAIN_IMAGE)
  if (coverImageIndex !== -1 && result.mainImages.length > 0) {
    // 如果找到了封面图，且不在第一位，则调整位置
    const coverImage = result.mainImages.find((_, index) => {
      const apiImg = apiImages.find(img => img.imageKey === result.mainImages[index].imageKey && img.cover)
      return !!apiImg
    })

    if (coverImage) {
      // 移除当前位置
      result.mainImages = result.mainImages.filter(img => img !== coverImage)
      // 放到首位
      result.mainImages.unshift(coverImage)
    }
  }

  return result
}

/**
 * 将组件使用的图片数据 (ProductImagesValue) 转换回接口需要的 images (ProductImageCreateRequest[])
 */
export function convertComponentImagesToApiFormat(
  componentImages: ProductImagesValue,
  skus: (ProductSkuUpdateRequest | ProductSkuCreateRequest | any)[] = [],
): ProductImageUpdateRequest[] {
  const result: ProductImageUpdateRequest[] = []

  // 主图
  componentImages.mainImages.forEach((image, index) => {
    result.push({
      imageKey: image.imageKey,
      filename: image.filename,
      imageType: ProductImageVOImageTypeEnum.PRODUCT_MAIN_IMAGE,
      isCover: index === 0, // 第一张设为封面
      order: index,
      imageId: image.id,
    })
  })

  // SKU 图
  Object.keys(componentImages.skuImages).forEach((skuCode) => {
    const images = componentImages.skuImages[skuCode]
    const relatedSku = skus.find(sku => sku.skuCode === skuCode)
    const skuIdForImage = relatedSku?.skuId

    images.forEach((image, index) => {
      const imageData: ProductImageUpdateRequest = {
        imageKey: image.imageKey,
        filename: image.filename,
        imageType: ProductImageVOImageTypeEnum.PRODUCT_SKU_IMAGE,
        isCover: false,
        order: index,
        imageId: image.id,
      }

      if (!isNil(skuIdForImage)) {
        imageData.skuId = skuIdForImage
      }

      result.push(imageData)
    })
  })

  // 详情图
  componentImages.detailImages.forEach((image, index) => {
    result.push({
      imageKey: image.imageKey,
      filename: image.filename,
      imageType: ProductImageVOImageTypeEnum.PRODUCT_DETAIL_IMAGE,
      isCover: false,
      order: index,
      imageId: image.id,
    })
  })

  return result
}
