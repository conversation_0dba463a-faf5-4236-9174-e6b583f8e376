import type { <PERSON><PERSON>, UnzipTaskVO } from '@/apiv2/file'
import type { ProductFileVO } from '@/apiv2/product'
import type { ImageInfo, ProductImagesValue } from '../components/types'
import {
  UnzipResultVOClassifyNameEnum,
} from '@/apiv2/file'
import { ProductFileVOFileTypeEnum } from '@/apiv2/product'

/** 把后端解压结果转成组件能读的结构 */
export function convertUnzipResultsToImagesValue(task: UnzipTaskVO): ProductImagesValue {
  const value: ProductImagesValue = { mainImages: [], skuImages: {}, detailImages: [] }

  task.result?.forEach((res) => {
    const imgs: ImageInfo[] = (res.fileUrl || []).map(f => ({
      url: f.fileUrl!,
      localUrl: undefined,
      imageKey: f.fileOssKey!,
      filename: f.filename || extractFileName(f.fileUrl!),
    }))

    switch (res.classifyName) {
      case UnzipResultVOClassifyNameEnum.PRODUCT_MAIN_IMAGE:
        value.mainImages.push(...imgs)
        break
      case UnzipResultVOClassifyNameEnum.PRODUCT_DETAIL_IMAGE:
        value.detailImages.push(...imgs)
        break
      case UnzipResultVOClassifyNameEnum.PRODUCT_SKU_IMAGE:
      {
        const skuCode = res.secondName
        if (!skuCode)
          break
        imgs.forEach((img) => {
          if (!value.skuImages[skuCode])
            value.skuImages[skuCode] = []
          value.skuImages[skuCode].push(img)
        })
        break
      }
    }
  })

  return value
}

/** 把后端解压结果转成文件组件能读的结构 */
export function convertUnzipResultsToFiles(task: UnzipTaskVO): ProductFileVO[] {
  const files: ProductFileVO[] = []

  task.result?.forEach((res) => {
    // 只处理文件类型的解压结果
    if (
      res.classifyName === UnzipResultVOClassifyNameEnum.PRODUCT_3D_FILE
      || res.classifyName === UnzipResultVOClassifyNameEnum.PRODUCT_MANUAL
      || res.classifyName === UnzipResultVOClassifyNameEnum.PRODUCT_CERTIFICATION
      || res.classifyName === UnzipResultVOClassifyNameEnum.PRODUCT_SEO_VIDEO
      || res.classifyName === UnzipResultVOClassifyNameEnum.PRODUCT_ATTACHMENT
    ) {
      (res.fileUrl || []).forEach((f: FileVO) => {
        const filename = f.filename || extractFileName(f.fileUrl!)
        let extension: string | undefined
        const dotIndex = filename.lastIndexOf('.')
        if (dotIndex > 0)
          extension = filename.substring(dotIndex + 1).toUpperCase()

        files.push({
          id: undefined,
          filename,
          filePath: f.fileOssKey,
          fileUrl: f.fileUrl,
          fileType: mapClassifyNameToFileType(res.classifyName),
          fileDesc: '',
          fileSize: undefined,
          extension,
        })
      })
    }
  })

  return files
}

/** 将解压结果的分类名称映射到文件类型枚举 */
function mapClassifyNameToFileType(classifyName?: UnzipResultVOClassifyNameEnum): ProductFileVOFileTypeEnum {
  switch (classifyName) {
    case UnzipResultVOClassifyNameEnum.PRODUCT_3D_FILE:
      return ProductFileVOFileTypeEnum.THREE_D
    case UnzipResultVOClassifyNameEnum.PRODUCT_MANUAL:
      return ProductFileVOFileTypeEnum.INSTRUMENT
    case UnzipResultVOClassifyNameEnum.PRODUCT_CERTIFICATION:
      return ProductFileVOFileTypeEnum.CERTIFICATION
    case UnzipResultVOClassifyNameEnum.PRODUCT_SEO_VIDEO:
      return ProductFileVOFileTypeEnum.SEO_VIDEO
    case UnzipResultVOClassifyNameEnum.PRODUCT_ATTACHMENT:
      return ProductFileVOFileTypeEnum.ATTACHMENT
    default:
      return ProductFileVOFileTypeEnum.ATTACHMENT
  }
}

function extractFileName(url: string) {
  return decodeURIComponent(url.split('/').pop() || '')
}
