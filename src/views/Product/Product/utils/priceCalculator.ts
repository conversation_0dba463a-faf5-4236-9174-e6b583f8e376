/**
 * 价格规则：调整价格，使其以90结尾
 * @param price 原始价格
 * @returns 调整后的价格 (JPY 整数)
 */
export function adjustPriceToEndIn90(price: number): number {
  if (typeof price !== 'number' || Number.isNaN(price)) {
    console.warn('adjustPriceToEndIn90: Invalid input price, returning 0.')
    return 0
  }
  const roundedPrice = Math.round(price) // 先四舍五入取整
  if (roundedPrice <= 0) {
    return 90 // 小于等于0的特殊处理，暂定90
  }

  const lastTwoDigits = roundedPrice % 100
  const hundredsAndUp = Math.floor(roundedPrice / 100) * 100

  if (lastTwoDigits === 90) {
    return roundedPrice
  }
  else if (lastTwoDigits < 90) {
    return hundredsAndUp + 90
  }
  else { // lastTwoDigits > 90
    return hundredsAndUp + 100 + 90 // 百位进1，末尾置90
  }
}

/**
 * 根据成本价和系数计算市场售价（应用价格规则）
 * @param supplyPrice 成本价 (JPY)
 * @param coefficient 系数
 * @returns 计算后的市场售价 (JPY 整数)
 */
export function calculateMarketPrice(supplyPrice: number, coefficient: number): number {
  if (typeof supplyPrice !== 'number' || Number.isNaN(supplyPrice) || supplyPrice < 0
    || typeof coefficient !== 'number' || Number.isNaN(coefficient) || coefficient <= 0) {
    console.warn('calculateMarketPrice: Invalid input, returning 0.')
    return 0 // 输入无效或非正数时返回0
  }
  const rawMarketPrice = supplyPrice * coefficient
  return adjustPriceToEndIn90(rawMarketPrice)
}

/**
 * 根据市场售价和折扣计算划线价（应用价格规则）
 * 假设：市场售价是折后价，划线价是原价
 * @param marketPrice 市场售价 (JPY)
 * @param discount 折扣率 (0-100), null 或 undefined 表示无折扣
 * @returns 计算后的划线价 (JPY 整数)
 */
export function calculateActivityPrice(marketPrice: number, discount: number | null | undefined): number {
  if (typeof marketPrice !== 'number' || Number.isNaN(marketPrice) || marketPrice < 0) {
    console.warn('calculateActivityPrice: Invalid marketPrice, returning 0.')
    return 0
  }

  let rawActivityPrice = marketPrice // 默认等于市场价

  // 仅当折扣是有效数字 (0 < discount < 100) 时计算
  if (typeof discount === 'number' && !Number.isNaN(discount) && discount > 0 && discount < 100) {
    // 防止除以0或负数
    if (1 - discount / 100 <= 0) {
      console.warn('calculateActivityPrice: Invalid discount resulted in non-positive divisor.')
      // 处理无效折扣，可以返回 marketPrice 或其他默认值
      return adjustPriceToEndIn90(marketPrice)
    }
    rawActivityPrice = marketPrice / (1 - discount / 100)
  }

  // 对计算结果应用价格规则
  return adjustPriceToEndIn90(rawActivityPrice)
}

/**
 * 根据市场售价和划线价反算折扣率
 * @param marketPrice 市场售价 (JPY)
 * @param activityPrice 划线价 (JPY)
 * @returns 计算后的折扣率 (1-99 整数)，如果无法计算或无效则返回 null
 */
export function calculateDiscount(marketPrice: number, activityPrice: number): number | null {
  if (typeof marketPrice !== 'number' || Number.isNaN(marketPrice) || marketPrice < 0
    || typeof activityPrice !== 'number' || Number.isNaN(activityPrice) || activityPrice <= 0
    || activityPrice < marketPrice) { // 划线价必须严格大于市场售价才有折扣
    return null // 输入无效或逻辑不符 (划线价小于等于市场价)
  }

  // 折扣 = (1 - 市场售价 / 划线价) * 100
  const rawDiscount = (1 - marketPrice / activityPrice) * 100
  const calculatedDiscount = Math.round(rawDiscount) // 四舍五入取整

  // 确保折扣在有效范围内 (0 < discount < 100)
  if (calculatedDiscount > 0 && calculatedDiscount < 100) {
    return calculatedDiscount
  }
  else {
    return null // 计算结果无效（例如等于0或100）
  }
}
