import type { ImageSection, UploadStrategy } from '@/components/CgImageManager'
import { GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'

/**
 * 扩展的产品模块上传策略，支持更多文件类型
 */
export const extendedProductUploadStrategy: UploadStrategy = {
  /**
   * 获取上传文件类型
   * @param section 区域类型
   * @param sku SKU ID（可选）
   * @returns 上传文件类型枚举值
   */
  getUploadFileType(section: ImageSection, _sku?: string) {
    switch (section) {
      case 'main':
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_IMAGES_MAIN
      case 'detail':
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_IMAGES_DETAIL
      case 'sku':
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_IMAGES_SKU
      case 'installation':
      case 'multimedia':
      case 'documents':
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_FILES
      default:
        // 默认使用产品文件类型
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_FILES
    }
  },

  /**
   * 获取文件接受类型
   * @param section 区域类型
   * @returns 文件接受类型
   */
  getAcceptType(section: ImageSection): string {
    switch (section) {
      case 'main':
      case 'detail':
      case 'sku':
        // 图片区域只接受图片
        return 'image/*'
      case 'installation':
        // 安装说明接受PDF
        return '.pdf'
      case 'multimedia':
        // 多媒体接受视频
        return 'video/*'
      case 'documents':
        // 文档接受各种文档格式
        return '.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt'
      default:
        // 默认接受所有文件
        return '*/*'
    }
  },

  /**
   * 检查文件类型是否有效
   * @param file 文件
   * @param section 区域类型
   * @returns 是否有效
   */
  isValidFileType(file: File, section: ImageSection): boolean {
    const type = file.type
    const name = file.name.toLowerCase()

    switch (section) {
      case 'main':
      case 'detail':
      case 'sku':
        // 图片区域只接受图片
        return type.startsWith('image/')
      case 'installation':
        // 安装说明接受PDF
        return type === 'application/pdf'
      case 'multimedia':
        // 多媒体接受视频
        return type.startsWith('video/')
      case 'documents':
        // 文档接受各种文档格式
        return (
          type === 'application/pdf'
          || type === 'application/msword'
          || type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
          || type === 'application/vnd.ms-excel'
          || type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          || type === 'application/vnd.ms-powerpoint'
          || type === 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
          || type === 'text/plain'
          || name.endsWith('.pdf')
          || name.endsWith('.doc')
          || name.endsWith('.docx')
          || name.endsWith('.xls')
          || name.endsWith('.xlsx')
          || name.endsWith('.ppt')
          || name.endsWith('.pptx')
          || name.endsWith('.txt')
        )
      default:
        // 默认接受所有文件
        return true
    }
  },
}
