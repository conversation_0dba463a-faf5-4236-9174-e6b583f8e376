function lightenColor(hex: string, percent: number): string {
  hex = hex.replace('#', '')
  const num = Number.parseInt(hex, 16)
  const amt = Math.round(2.55 * percent)
  const R = (num >> 16) + amt
  const G = ((num >> 8) & 0x00FF) + amt
  const B = (num & 0x0000FF) + amt
  const newColor = (
    0x1000000
    + (R < 255 ? (R < 1 ? 0 : R) : 255) * 0x10000
    + (G < 255 ? (G < 1 ? 0 : G) : 255) * 0x100
    + (B < 255 ? (B < 1 ? 0 : B) : 255)
  )
    .toString(16)
    .slice(1)
  return `#${newColor.padStart(6, '0')}`
}

function getContrastColor(backgroundColor: string) {
  let hex = backgroundColor.replace('#', '')
  if (hex.length === 3) {
    hex = hex.split('').map(c => c + c).join('')
  }
  if (hex.length !== 6) {
    console.warn(`Invalid hex color passed to getContrastColor: #${hex}. Defaulting contrast check.`)
    hex = 'ffffff'
  }

  const r = Number.parseInt(hex.substring(0, 2), 16)
  const g = Number.parseInt(hex.substring(2, 4), 16)
  const b = Number.parseInt(hex.substring(4, 6), 16)

  const yiq = ((r * 299) + (g * 587) + (b * 114)) / 1000
  return yiq >= 128 ? '#000000' : '#ffffff'
}

function getTagStyle(color: string | undefined) {
  if (!color)
    return {}

  const baseColor = color
  const lightenedColor = lightenColor(baseColor, 15)
  const textColor = getContrastColor(baseColor)

  let textShadow = 'none'
  if (textColor === '#ffffff') {
    textShadow = '0px 1px 2px rgba(0, 0, 0, 0.4)'
  }
  else {
    textShadow = '0px 1px 1px rgba(0, 0, 0, 0.1)'
  }

  return {
    background: `linear-gradient(to bottom right, ${baseColor}, ${lightenedColor})`,
    color: textColor,
    borderColor: baseColor,
    borderWidth: '1px',
    borderStyle: 'solid',
    textShadow,
  }
}

export { getContrastColor, getTagStyle }
