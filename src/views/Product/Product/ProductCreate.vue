<script setup lang="ts">
import CgCategoryTree from '@/components/CgCategoryTree'
import { useTagsViewClose } from '@/composables/useTagsViewClose'
import { useUserStore } from '@/stores/User'
import PriorityDialog from './components/PriorityDialog.vue'
import ProductDescription from './components/ProductDescription.vue'
import ProductFile from './components/ProductFile.vue'
import ProductImage from './components/ProductImage.vue'
import ProductSku from './components/ProductSku.vue'
import PropertiesDialog from './components/PropertiesDialog.vue'
import { useProductForm } from './composables/useProductForm'
import { useUnsavedChangesWarning } from './composables/useUnsavedChangesWarning'

defineOptions({
  name: 'ProductCreate',
})

const props = defineProps({
  editData: {
    type: Object,
    default: () => ({}),
  },
})

const emits = defineEmits<{
  success: [value: any, isEdit: boolean]
}>()

const { closeCurrentPage } = useTagsViewClose()

const {
  formRef,
  formData,
  productImage,
  formOptions,
  handleUpdateSpuImage,
  handleSave,
  activeTab,
  isFormDirty,
} = useProductForm(props, {
  success: (value, isEdit) => emits('success', value, isEdit),
})

useUnsavedChangesWarning(isFormDirty)

// 设置当前用户为默认负责人
const userStore = useUserStore()
onMounted(() => {
  if (userStore.userId) {
    formData.maintainerId = userStore.userId
  }
})

const skuComponentRef = useTemplateRef<InstanceType<typeof ProductSku>>('skuComponent')
function handleCreateSave(done: () => void, isDraft = false, priority?: number, onValidSuccess?: () => void) {
  try {
    handleSave(done, isDraft, skuComponentRef.value?.getSkuData() || [], priority, onValidSuccess)
  }
  catch {
    done()
  }
}

const showPriorityDialog = ref(false)
function handlePriorityConfirm(priority: number, done: () => void) {
  handleCreateSave(done, false, priority)
}
</script>

<template>
  <div class="product-create h-full w-full flex flex-col">
    <div class="flex">
      <div>
        <CgImageUploader
          v-model="productImage" folder-name="category-icon" upload-text="上传图片"
          :size="{ width: 166, height: 166 }" :view-only="true" tip-text="请选图片信息<br>上传SPU主图" access-type="private"
        />
      </div>
      <div class="ml-4 flex-1">
        <CgForm ref="formRef" v-model="formData" :options="formOptions" :table-style="true" :col-span="12">
          <template #category>
            <CgCategoryTree
              v-model="formData.leafCategoryId" leaf-only emit-full-path :check-strictly="false" @change="(id: any) => {
                formData.leafCategoryId = id
              }"
            />
          </template>
          <template #properties>
            <PropertiesDialog v-model="formData.properties" />
          </template>
        </CgForm>
      </div>
    </div>
    <div class="mt-2 flex-1 overflow-y-auto">
      <ElTabs v-model="activeTab" class="h-full max-w-full">
        <ElTabPane key="description" label="产品描述" name="description" class="h-full">
          <ProductDescription v-model="formData.description" />
        </ElTabPane>
        <ElTabPane key="skus" label="SKU 信息" name="skus" class="h-full">
          <ProductSku ref="skuComponent" v-model="formData.skus as any" create />
        </ElTabPane>
        <ElTabPane key="images" label="图片信息" name="images" class="h-full">
          <ProductImage v-model="formData.imagesComponent" @update-spu-image="handleUpdateSpuImage" />
        </ElTabPane>
        <ElTabPane key="files" label="文件信息" name="files" class="h-full">
          <ProductFile v-model="formData.files as any" />
        </ElTabPane>
      </ElTabs>
    </div>

    <div class="mt-4 flex justify-end gap-2">
      <CgButton @click="closeCurrentPage">
        取消
      </CgButton>
      <CgButton auto-loading type="primary" plain @click="handleCreateSave($event, true)">
        保存
      </CgButton>
      <CgButton type="primary" @click="handleCreateSave(() => { }, false, undefined, () => showPriorityDialog = true)">
        保存并发起工单
      </CgButton>

      <PriorityDialog v-model:visible="showPriorityDialog" @confirm="handlePriorityConfirm" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.product-create {
  padding: 16px;
}

:deep(.el-tabs__content) {
  overflow-y: auto;
}
</style>
