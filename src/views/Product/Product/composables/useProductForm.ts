import type { CommodityImageUpdateRequest, CommodityUpdateRequest, ProductCreateRequest, ProductDetailVO, ProductSkuVO, ProductUpdateRequest } from '@/apiv2/product'
import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'
import type { FormInstance } from 'element-plus'
import type { ProductImagesValue } from '../components/types'
import { commodityApi, dictApi, productApi, supplierApi } from '@/api.services/product.service'
import userApi from '@/api.services/user.service'
import ComponentTagsEnum from '@/components/CgElementUI/CgForm/ComponentTagsEnum'
import { transformDictData } from '@/components/utils/shared'
import { useProductStore } from '@/stores/Product'
import { useTagViewStore } from '@/stores/TagsView'
import { isEqual, isNil } from 'lodash-es'
import { useRoute, useRouter } from 'vue-router'
import ProductDescription from '../components/ProductDescription.vue'
import ProductFile from '../components/ProductFile.vue'
import ProductImage from '../components/ProductImage.vue'
import ProductSku from '../components/ProductSku.vue'

import {
  convertApiImagesToComponentFormat,
  convertComponentImagesToApiFormat,
} from '../utils/imageDataConverter'
import skuDataConverter from '../utils/skuDataConverter'
import { useDirtyFormDetection } from './useDirtyFormDetection'

export function useProductForm(props: {
  editData: object
}, emits?: {
  success?: (value: any, isEdit: boolean) => void
}, options?: {
  readonly?: boolean
}) {
  const route = useRoute()
  const router = useRouter()
  const productId = Number(route.params.id)
  const formRef = ref<FormInstance>()
  const formData = reactive<Partial<ProductCreateRequest> &
    {
      imagesComponent?: ProductImagesValue
      supplierName?: string
      shipTimeFrameName?: string
    }>({
    properties: [],
    description: '',
    skus: [],
    images: [],
    files: [],
    productName: '',
    leafCategoryId: undefined,
    supplierId: undefined,
    supplierName: undefined,
    maintainerId: undefined,
    developMethod: undefined, // 开发方式
    instrumentState: undefined, // 仪器状态
    productChecks: undefined, // 产品检查
    assembly: undefined, // 组装
    supportCustomizes: undefined, // 支持定制
    shipTimeFrame: undefined, // 发货时效
    shipTimeFrameName: undefined, // 发货时效名称
    imagesComponent: {
      mainImages: [],
      skuImages: {},
      detailImages: [],
    },
  })
  const originalSkuDataFromApi = ref<ProductSkuVO[]>([])
  provide('formData', formData)
  const isMerchantView = computed(() => {
    return route.query.isOperation === '1'
  })

  const commodityFormRef = ref<FormInstance>()
  const commodityFormData = ref<Partial<CommodityUpdateRequest> & { imagesComponent?: ProductImagesValue }>({
    commodityId: undefined,
    commodityName: undefined,
    skus: [],
    images: [],
    files: [],
    imagesComponent: {
      mainImages: [],
      skuImages: {},
      detailImages: [],
    },
  })

  const activeMerchantTab = ref('base')
  provide('activeMerchantTab', activeMerchantTab)

  const productStore = useProductStore()
  const tagViewStore = useTagViewStore()

  // 自定义比较函数
  const lengthComparator = (initialValue: any, currentValue: any): boolean => {
    if (Array.isArray(initialValue) && Array.isArray(currentValue)) {
      return initialValue.length === currentValue.length
    }
    return isEqual(initialValue, currentValue)
  }

  const productFieldsToWatchForDirtyCheck: ReadonlyArray<keyof typeof formData> = [
    'productName',
    'leafCategoryId',
    'supplierId',
    'developMethod',
    'instrumentState',
    'productChecks',
    'assembly',
    'supportCustomizes',
    'shipTimeFrame',
    'properties', // 产品标签
    'description', // 产品描述
    'skus', // SKU 信息
    // 'imagesComponent', // 图片信息 (通过 UI 修改的部分)
    'files', // 文件信息
    'productLink',
    'buyerRecommend',
  ]

  const commodityFieldsToWatchForDirtyCheck: ReadonlyArray<keyof typeof commodityFormData.value> = [
    'commodityName',
    'skus',
    // 'imagesComponent',
    'files',
  ]
  const productDirtyTracker = useDirtyFormDetection(formData, productFieldsToWatchForDirtyCheck, {
    customComparators: {
      skus: lengthComparator,
      files: lengthComparator,
    },
  })
  const commodityDirtyTracker = useDirtyFormDetection(commodityFormData, commodityFieldsToWatchForDirtyCheck, {
    customComparators: {
      skus: lengthComparator,
      files: lengthComparator,
    },
  })
  const isFormDirty = computed(() => {
    if (options?.readonly)
      return false
    if (isMerchantView.value) {
      return commodityDirtyTracker.isDirty.value
    }
    return productDirtyTracker.isDirty.value
  })
  function captureAllInitialFormStates() {
    productDirtyTracker.captureInitialState()
    commodityDirtyTracker.captureInitialState()
  }

  function resetAllFormDirtyStates() {
    productDirtyTracker.resetDirtyState()
    commodityDirtyTracker.resetDirtyState()
  }

  const closeCurrentPage = (name?: 'MerchandiseList' | 'ProductList') => {
    const currentRoute = route
    tagViewStore.delView(currentRoute)
    if (name === 'ProductList')
      productStore.refreshList()
    router.push({
      name: name || 'ProductList',
    })
  }

  const isEdit = computed(() => Boolean(productId))
  const isReadonly = ref(options?.readonly ?? false)
  provide('isEdit', isEdit)
  provide('isDetail', isReadonly)
  const isEditLoading = ref(false)
  const productName = computed(() => {
    if (formData.productName) {
      return formData.productName
    }
    return productStore.getProductName(String(productId))
  })

  watch(productName, (newName) => {
    if (productId && newName && newName !== String(productId)) {
      productStore.setProductName(String(productId), newName)

      updateTagsViewTitle(newName)
    }
  }, { immediate: true })

  function updateTagsViewTitle(name: string) {
    if (!route.name)
      return

    const titlePrefix = isReadonly.value
      ? '产品详情'
      : isEdit.value
        ? '编辑产品'
        : '新建产品'

    tagViewStore.changeTitle(
      { name: route.name },
      `${titlePrefix} - ${name}`,
    )
  }
  const productImage = ref('') // SPU 主图

  // 如果是编辑模式，获取产品详情
  onMounted(async () => {
    if (isEdit.value && productId) {
      isEditLoading.value = true
      const response = (await productApi.getProduct({ productId })).data

      if (response.success && response.data) {
        const detailData = response.data
        fillFormDataFromDetail(detailData)
        // 在填充表单后，存储原始的 SKU 数据
        originalSkuDataFromApi.value = detailData.skus || []
      }
      isEditLoading.value = false
    }
    if (isMerchantView.value) {
      const commodityId = Number(route.query.commodityId)
      const res = await commodityApi.getCommodityDetail({ commodityId })
      if (res.data?.data) {
        const resData = res.data.data

        commodityFormData.value.commodityId = commodityId
        commodityFormData.value.commodityName = resData.commodityName
        // 转换SKU数据
        commodityFormData.value.skus = skuDataConverter.convertSkuVOToUpdateRequest(resData.skus)

        // 保存原始图片和文件数据
        commodityFormData.value.images = resData.images || []
        commodityFormData.value.files = resData.files || []

        // 使用转换工具将API图片数据转换为组件格式
        commodityFormData.value.imagesComponent = convertApiImagesToComponentFormat(resData.images || [])
      }
    }
    captureAllInitialFormStates()
  })

  /**
   * 将 ProductDetailVO 转换为表单数据
   * @param detailData 产品详情数据
   */
  function fillFormDataFromDetail(detailData: ProductDetailVO) {
    // 基础字段直接赋值
    const basicFields = {
      thumbnail: detailData.thumbnail,
      productName: detailData.productName,
      productCode: detailData.productCode,
      productLink: detailData.productLink,
      description: detailData.description,
      buyerRecommend: detailData.buyerRecommend,
    }

    // 从关联对象中提取ID的字段
    const relationFields = {
      leafCategoryId: detailData.categories?.[detailData.categories.length - 1]?.id,
      supplierId: detailData.supplier?.id,
      maintainerId: detailData.maintainer?.id,
    }

    // 从字典对象中提取ID的字段
    const dictFields = {
      developMethod: detailData.developMethod?.id,
      instrumentState: detailData.instrumentState?.id,
      productChecks: detailData.productChecks?.map(p => p.id),
      assembly: detailData.assembly?.id,
      supportCustomizes: detailData.supportCustomizes?.map(p => p.id),
      shipTimeFrame: detailData.shipTimeFrame?.id,
    }

    // 转换属性数组
    const properties = detailData.properties
      ?.filter(p => p.id !== undefined)
      .map(p => p.id as number) || []

    // 转换文件数据
    const files = detailData.files
      ?.filter(file => file !== undefined)
      .filter(file => file.filename && file.filePath)
      .map(file => ({
        filename: file.filename!,
        filePath: file.filePath!,
        fileType: file.fileType,
        fileDesc: file.fileDesc,
        fileUrl: file.fileUrl,
        fileSize: file.fileSize,
        extension: file.extension,
        id: file.id,
      })) || []

    // 使用转换工具将API图片数据转换为组件格式
    const images = detailData.images || []
    const convertedImages = convertApiImagesToComponentFormat(images)

    // 提取 SKU 数据
    const skus = skuDataConverter.convertProductSkuVOListToUpdateRequestList(detailData.skus)

    // 合并所有数据到表单
    Object.assign(formData, basicFields, relationFields, dictFields, {
      properties,
      images,
      files,
      skus,
      imagesComponent: convertedImages,
    })

    // 设置SPU主图
    const mainImage = detailData.images?.find(img => img.cover)
    if (mainImage) {
      productImage.value = mainImage.imageUrl || ''
    }
    else {
      productImage.value = detailData.thumbnail || ''
    }
  }

  const formOptions = computed<CgFormOption[][]>(() => {
    const baseOptions: CgFormOption[][] = [
      [
        {
          label: '款名',
          key: 'productName',
          type: ComponentTagsEnum.Input,
          span: 6,
          required: true,
          props: {
            placeholder: '请输入款名',
          },
        },
        {
          label: 'SPU',
          key: 'productCode',
          type: ComponentTagsEnum.Input,
          span: 6,
          props: {
            placeholder: '系统自动生成',
            readonly: true,
          },
        },
        {
          label: '产品类目',
          key: 'leafCategoryId',
          type: 'slot',
          slotName: 'category',
          span: 12,
          required: true,
          props: {
            placeholder: '请选择产品类目',
            options: [],
            filterable: true,
          },
        },
      ],
      [
        {
          label: '开发方式',
          key: 'developMethod',
          type: ComponentTagsEnum.SelectV2,
          span: 6,
          required: true,
          props: {
            placeholder: '请选择开发方式',
            proxyOption: {
              request: dictApi.listDicts,
              query: {
                codes: 'develop-method',
              },
            },
            transform: transformDictData,
            filterable: false,
          },
        },
        {
          label: '产品链接',
          key: 'productLink',
          type: ComponentTagsEnum.Input,
          span: 6,
          props: {
            placeholder: '请输入产品链接',
          },
        },
        {
          label: '供应商',
          key: 'supplierId',
          type: ComponentTagsEnum.SelectV2,
          span: 6,
          required: true,
          props: {
            placeholder: '请选择供应商',
            proxyOption: {
              request: supplierApi.listSuppliers,
            },
            filterable: true,
            multiple: false,
            props: {
              label: 'supplierName',
              value: 'id',
            },
          },
          events: {
            change: (value: number, supplier: any) => {
              formData.supplierName = supplier?.supplierName
            },
          },
        },
        {
          label: '发货时效',
          key: 'shipTimeFrame',
          type: ComponentTagsEnum.SelectV2,
          span: 6,
          props: {
            placeholder: '请选择发货时效',
            proxyOption: {
              request: dictApi.listDicts,
              query: {
                codes: 'ship-timeframe',
              },
            },
            transform: transformDictData,
            filterable: false,
          },
          events: {
            change: (value: number, shipTimeFrame: any) => {
              formData.shipTimeFrameName = shipTimeFrame?.label
            },
          },
        },
      ],
      [
        {
          label: '说明书',
          key: 'instrumentState',
          type: ComponentTagsEnum.SelectV2,
          span: 6,
          required: true,
          props: {
            placeholder: '请选择说明书状态',
            proxyOption: {
              request: dictApi.listDicts,
              query: {
                codes: 'instrument-state',
              },
            },
            transform: transformDictData,
            filterable: false,
          },
        },
        {
          label: '实体确认',
          key: 'productChecks',
          type: ComponentTagsEnum.SelectV2,
          span: 6,
          required: true,
          props: {
            placeholder: '有LOGO，有中文',
            proxyOption: {
              request: dictApi.listDicts,
              query: {
                codes: 'product-check',
              },
            },
            multiple: true,
            transform: transformDictData,
            filterable: false,
          },
        },
        {
          label: '组装',
          key: 'assembly',
          type: ComponentTagsEnum.SelectV2,
          span: 6,
          required: true,
          props: {
            placeholder: '请选择是否组装',
            proxyOption: {
              request: dictApi.listDicts,
              query: {
                codes: 'assembly',
              },
            },
            transform: transformDictData,
            filterable: false,
          },
        },
        {
          label: '定制',
          key: 'supportCustomizes',
          type: ComponentTagsEnum.SelectV2,
          span: 6,
          required: true,
          props: {
            placeholder: '定制颜色，定制尺',
            proxyOption: {
              request: dictApi.listDicts,
              query: {
                codes: 'support-customize',
              },
            },
            multiple: true,
            transform: transformDictData,
            filterable: false,
          },
        },
      ],
      [
        {
          label: '买手推荐',
          key: 'buyerRecommend',
          type: ComponentTagsEnum.SelectV2,
          span: 6,
          props: {
            placeholder: '请选择是否买手推荐',
            filterable: false,
          },
          dataSource: {
            type: 'static',
            staticData: [
              { label: 'S', value: 0 },
              { label: 'A', value: 1 },
              { label: 'B', value: 2 },
              { label: 'C', value: 3 },
            ],
          },
        },
        {
          label: '产品负责人',
          key: 'maintainerId',
          type: ComponentTagsEnum.SelectV2,
          span: 6,
          required: true,
          props: {
            placeholder: '请选择产品负责人',
            nullToZero: true,
            proxyOption: {
              request: userApi.search,
              query: {
                pageNum: 1,
                pageSize: 10000,
              },
              postParam: 'userSearchReq',
            },
            filterable: true,
            props: {
              label: 'name',
              value: 'id',
            },
          },
        },
        {
          type: 'slot',
        },
      ],
      [
        {
          label: '产品标签',
          key: 'properties',
          type: 'slot',
          slotName: 'properties',
          span: 24,
          props: {
            placeholder: '请选择产品标签',
          },
        },
      ],
    ]

    if (isReadonly.value) {
      return baseOptions.map(group =>
        group.map(option => ({
          ...option,
          props: {
            ...option.props,
            readonly: true,
            // disabled: true,
          },
        })),
      )
    }

    return baseOptions
  })

  /**
   * 处理SPU主图更新事件
   * @param {string} imageUrl - 图片的blob URL
   * @param {string} imageKey - 图片的oss key
   */
  function handleUpdateSpuImage(imageUrl: string, imageKey: string) {
    productImage.value = imageUrl
    formData.thumbnail = imageKey
  }

  const tabs = ref([
    { label: '产品描述', value: 'description', component: markRaw(ProductDescription) },
    { label: 'SKU 信息', value: 'skus', component: markRaw(ProductSku) },
    { label: '图片信息', value: 'images', component: markRaw(ProductImage) },
    { label: '文件信息', value: 'files', component: markRaw(ProductFile) },
  ])
  const activeTab = ref('description')
  provide('activeTab', activeTab)

  /**
   * 保存产品信息
   */
  async function handleSave(done: () => void, isDraft = false, skusData: any[], priority?: number, onValidSuccess?: () => void) {
    const valid = await formRef.value?.validate().catch(() => {
      done()
    })

    if (!valid) {
      return
    }

    if (skusData.length === 0) {
      ElMessage.error('请至少添加一行SKU信息')
      done()
      return
    }

    const hasMissingPrimaryMaterial = skusData.some(sku => !sku.priMatLv1)
    if (hasMissingPrimaryMaterial) {
      ElMessage.error('主材质(一级)必填')
      done()
      return
    }

    const hasMissingPurchasePrice = skusData.some(sku => isNil(sku.purchasePrice))
    if (hasMissingPurchasePrice) {
      ElMessage.error('采购单价(含税含运)必填')
      done()
      return
    }

    if (onValidSuccess) {
      onValidSuccess()
      return
    }

    // 3. --- 合并 skuId ---
    const mergedSkus = skusData.map((editedSku) => {
      const originalSku = originalSkuDataFromApi.value.find(
        o => o.skuCode && o.skuCode === editedSku.skuCode,
      )

      if (originalSku && originalSku.id !== undefined) {
        return {
          ...editedSku,
          skuId: editedSku.skuId === undefined ? originalSku.id : editedSku.skuId,
        }
      }
      else {
        return {
          ...editedSku,
          skuId: undefined,
        }
      }
    })

    // 4. --- 对 SKU 数据进行单位转换 (UI 单位 -> API 单位) ---
    const convertedSkus = skuDataConverter.convertEditedSkuListToApiFormat(mergedSkus)

    // 将组件格式的图片数据转换回API格式
    const apiImages = convertComponentImagesToApiFormat(formData.imagesComponent!, convertedSkus)
    // 构建通用基础数据
    const baseData = {
      ...formData,
      images: apiImages,
      mainImage: formData.imagesComponent?.mainImages?.[0]?.imageKey || '',
      isDraft,
      skus: convertedSkus,
    }

    // 删除组件专用字段，避免传给后端
    delete baseData.imagesComponent

    let result
    if (isEdit.value) {
      // 更新产品 - 使用 ProductUpdateRequest 类型并确保包含 productId
      // 断言必填字段存在，因为表单验证已经通过
      const updateData = {
        ...baseData,
        productId,
        // 确保必填字段存在
        productName: formData.productName ?? '',
        leafCategoryId: formData.leafCategoryId ?? 0,
        supplierId: formData.supplierId ?? 0,
        maintainerId: formData.maintainerId ?? 0,
      } as ProductUpdateRequest

      result = await productApi.updateProduct({
        productUpdateRequest: updateData,
      }).finally(() => {
        done()
      })
    }
    else {
      // 创建产品 - 使用 ProductCreateRequest 类型并确保必填字段有值
      // 断言必填字段存在，因为表单验证已经通过
      const createData = {
        ...baseData,
        // 确保必填字段存在
        productName: formData.productName ?? '',
        leafCategoryId: formData.leafCategoryId ?? 0,
        supplierId: formData.supplierId ?? 0,
        maintainerId: formData.maintainerId ?? 0,
      } as ProductCreateRequest

      result = await productApi.createProduct({
        productCreateRequest: createData,
        priority: isDraft ? undefined : priority,
        ticketCreated: !isDraft,
      }).finally(() => {
        done()
      })
    }

    if (result.data.success) {
      ElMessage.success(isDraft ? '保存成功' : '保存并发起工单成功')
      resetAllFormDirtyStates()
      emits?.success?.(isEdit.value ? { ...baseData, productId } : baseData, isEdit.value)
      closeCurrentPage()
    }
  }

  /**
   * 保存商品
   */
  async function handleMerchantSave(done: () => void, skusData: any[]) {
    try {
      // 对 SKU 数据进行单位转换 (UI 单位 -> API 单位)
      const convertedSkus = skuDataConverter.convertEditedSkuListToApiFormat(skusData)

      // 将组件格式的图片数据转换回API格式
      const apiImages = convertComponentImagesToApiFormat(commodityFormData.value.imagesComponent!, convertedSkus) as unknown as CommodityImageUpdateRequest[]

      // 校验 skusData， 市场售价不能小于成本价
      const hasInvalidPrice = skusData.some(sku => sku.marketPrice < sku.supplyPrice)
      if (hasInvalidPrice) {
        ElMessage.error('市场售价不能小于成本价')
        return
      }

      // 构建通用基础数据
      const updateData: CommodityUpdateRequest = {
        commodityId: Number(route.query.commodityId),
        commodityName: commodityFormData.value.commodityName,
        skus: convertedSkus,
        images: apiImages,
        files: commodityFormData.value.files,
      }

      // 调用商品更新接口
      const result = await commodityApi.updateCommodity({
        commodityId: updateData.commodityId,
        commodityUpdateRequest: updateData,
      })

      if (result.data.success) {
        ElMessage.success('商品更新成功')
        resetAllFormDirtyStates()
        emits?.success?.(result.data, true)
        closeCurrentPage('MerchandiseList')
      }
    }
    finally {
      done()
    }
  }

  function handleUpdateSkuThumbnail(payload: { skuCode: string, thumbnail?: string }) {
    const { skuCode, thumbnail } = payload
    const targetSkusArray = isMerchantView.value ? commodityFormData.value.skus as any[] : formData.skus

    if (targetSkusArray) {
      const skuToUpdate = targetSkusArray.find(sku => sku.skuCode === skuCode)
      if (skuToUpdate) {
        skuToUpdate.thumbnail = thumbnail
      }
    }
  }

  return {
    formRef,
    formData,
    isEdit,
    isReadonly,
    isEditLoading,
    productName,
    productImage,
    formOptions,
    handleUpdateSpuImage,
    handleSave,
    handleMerchantSave,
    tabs,
    activeTab,
    commodityFormRef,
    commodityFormData,
    activeMerchantTab,
    updateTagsViewTitle,
    isMerchantView,
    handleUpdateSkuThumbnail,
    isFormDirty,
  }
}
