import { cloneDeep, isEqual, pick } from 'lodash-es'

/**
 * 比较函数类型。
 * @template V 字段值的类型
 * @param initialValue 字段的初始值 (可能为 undefined)
 * @param currentValue 字段的当前值
 * @returns 如果值被认为相等则返回 true，否则返回 false
 */
type Comparator<V> = (initialValue: V | undefined, currentValue: V) => boolean

/**
 * 自定义比较器对象的类型。
 * 键是表单数据的字段名，值是对应字段的 Comparator 函数。
 * @template T 表单数据的类型
 */
type CustomComparators<T extends Record<string, any>> = {
  [K in keyof T]?: Comparator<T[K]>
}

/**
 * useDirtyFormDetection Composable 的选项接口。
 * @template T 表单数据的类型
 */
interface UseDirtyFormDetectionOptions<T extends Record<string, any>> {
  /** 是否在 formDataRef 或 fieldsToWatchRef 变化时自动重新捕获初始状态 */
  autoCaptureOnSourceChange?: boolean
  /** 自定义字段比较函数 */
  customComparators?: CustomComparators<T>
}

/**
 * 一个通用的 Composable，用于检测响应式对象的指定字段是否变“脏”。
 * @template T 表单数据的类型，必须是 Record<string, any>
 * @param formDataRef 需要监控的响应式表单数据 (Ref 或 reactive 对象)
 * @param fieldsToWatchRef 需要监控的字段名数组 (Ref 或普通数组)
 * @param options 可选配置
 */
export function useDirtyFormDetection<T extends Record<string, any>>(
  formDataRef: Ref<T> | T,
  fieldsToWatchRef: Ref<ReadonlyArray<keyof T>> | ReadonlyArray<keyof T>,
  options?: UseDirtyFormDetectionOptions<T>,
) {
  const initialSnapshot = ref<Partial<T> | null>(null)

  /**
   * 捕获受监控字段的当前值作为初始快照。
   */
  const captureInitialState = () => {
    const currentFormData = toValue(formDataRef)
    const fields = toValue(fieldsToWatchRef)
    if (currentFormData && fields) {
      initialSnapshot.value = cloneDeep(pick(currentFormData, fields))
    }
    else {
      initialSnapshot.value = null
    }
  }

  /**
   * 重置脏状态，通过重新捕获当前表单数据作为新的初始快照。
   */
  const resetDirtyState = () => {
    captureInitialState()
  }

  /**
   * 计算属性：如果任何受监控的字段自上次捕获初始状态以来已更改，则为 true。
   * 使用提供的自定义比较器（如果可用）或 lodash-es 的 isEqual 进行比较。
   */
  const isDirty = computed(() => {
    if (initialSnapshot.value === null) {
      // 如果没有初始快照，则认为表单不是脏的
      return false
    }
    const currentFormData = toValue(formDataRef)
    const fields = toValue(fieldsToWatchRef)
    if (!currentFormData || !fields) {
      // 如果当前表单数据或字段列表无效，也认为不是脏的
      return false
    }

    const comparators: CustomComparators<T> = options?.customComparators || {}

    for (const field of fields) {
      const initialValue = initialSnapshot.value[field]
      const currentValue = currentFormData[field]
      let fieldIsEqual: boolean

      const comparatorFn = comparators[field]
      if (comparatorFn) {
        fieldIsEqual = comparatorFn(initialValue, currentValue)
      }
      else {
        fieldIsEqual = isEqual(initialValue, currentValue)
      }

      if (!fieldIsEqual) {
        return true
      }
    }
    return false
  })

  // 如果配置了 autoCaptureOnSourceChange，则监视表单数据和字段列表的变化，
  // 并在它们发生变化时自动重新捕获初始状态。
  if (options?.autoCaptureOnSourceChange) {
    watch([() => toValue(formDataRef), () => toValue(fieldsToWatchRef)], () => {
      captureInitialState()
    }, { deep: true })
  }

  return {
    isDirty,
    captureInitialState,
    resetDirtyState,
  }
}
