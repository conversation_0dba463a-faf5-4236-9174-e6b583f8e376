import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { onBeforeRouteLeave } from 'vue-router'

interface UnsavedChangesWarningOptions {
  dialogTitle?: string
  dialogMessage?: string
  confirmButtonText?: string
  cancelButtonText?: string
  beforeUnloadMessage?: string // 注意：现代浏览器通常会显示一个通用消息
}

const defaultOptions: Required<UnsavedChangesWarningOptions> = {
  dialogTitle: '警告',
  dialogMessage: '您有未保存的更改，确定要离开吗？',
  confirmButtonText: '离开',
  cancelButtonText: '取消',
  beforeUnloadMessage: '您有未保存的更改，确定要离开吗？',
}

export function useUnsavedChangesWarning(
  isDirty: Ref<boolean>,
  options?: UnsavedChangesWarningOptions,
) {
  const mergedOptions = { ...defaultOptions, ...options }

  const handleBeforeUnload = (event: BeforeUnloadEvent) => {
    if (isDirty.value) {
      event.preventDefault()
      // 大多数现代浏览器会忽略此自定义消息，并显示标准提示
      event.returnValue = mergedOptions.beforeUnloadMessage
    }
  }

  // 组件挂载时添加beforeunload监听器
  onMounted(() => {
    window.addEventListener('beforeunload', handleBeforeUnload)
  })

  // 组件卸载时移除beforeunload监听器
  onBeforeUnmount(() => {
    window.removeEventListener('beforeunload', handleBeforeUnload)
  })

  // Vue Router导航守卫逻辑
  const routeLeaveGuard = (
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext,
  ) => {
    if (isDirty.value) {
      ElMessageBox.confirm(
        mergedOptions.dialogMessage,
        mergedOptions.dialogTitle,
        {
          confirmButtonText: mergedOptions.confirmButtonText,
          cancelButtonText: mergedOptions.cancelButtonText,
          type: 'warning',
          draggable: true, // Element Plus 2.3+
        },
      )
        .then(() => {
          next() // 用户确认离开
        })
        .catch(() => {
          next(false) // 用户取消，停留在当前页面
        })
    }
    else {
      next() // 没有未保存的更改，正常导航
    }
  }

  // 使用 Vue Router 的 onBeforeRouteLeave
  // 这个 composable 需要在组件的 setup 函数中被调用，以便 onBeforeRouteLeave 能正确注册
  onBeforeRouteLeave(routeLeaveGuard)

  // 通常这个 composable 不需要返回什么，因为它主要通过副作用（事件监听和导航守卫）工作
}
