import type { UnzipTaskVO } from '@/apiv2/file'
import type { ProductFileVO } from '@/apiv2/product'
import type { ProductImagesValue } from '../components/types'
import { zipTaskApi } from '@/api.services/file.service'
import {
  UnzipRequestUploadFileTypeEnum,
  UnzipRequestZipBizTypeEnum,

  UnzipTaskVOStatusEnum,
} from '@/apiv2/file'
import { GetOssUploadCredentialAccessTypeEnum, GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'
import { useFileChooser } from '@/composables/useFileChooser'
import { OssUploader } from '@/utils/ossUploader'
import { ElMessage } from 'element-plus'
import { convertUnzipResultsToFiles, convertUnzipResultsToImagesValue } from '../utils/unzipResultConverter'

/**
 * 把素材压缩包上传并解压，成功后把返回的图片写进 imagesRef，文件写进 filesRef
 *
 * @param imagesRef  组件侧的图片数据的可变引用 (ProductImagesValue)
 * @param filesRef   组件侧的文件数据模型 (formData.files)
 */
export function useMaterialZipUploader(
  imagesRef: Ref<ProductImagesValue | undefined>,
  filesRef: Ref<ProductFileVO[] | undefined>,
) {
  const { openFileChooser, choosing } = useFileChooser()
  const isUploading = ref(false)
  const progress = ref(0)
  const unzipStatus = ref<UnzipTaskVOStatusEnum | null>(null)

  /** 用户点击按钮时调用 */
  async function startUpload() {
    if (isUploading.value || choosing.value)
      return

    try {
      const [file] = await openFileChooser({ accept: '.zip,.rar,.7z', multiple: false })
      if (!file)
        return

      isUploading.value = true
      progress.value = 0

      const { key } = await new Promise<{ url: string, key: string }>((resolve, reject) => {
        OssUploader.uploadFile(
          file,
          GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_FILES,
          GetOssUploadCredentialAccessTypeEnum.PRIVATE,
          pct => (progress.value = pct),
          (url, k) => resolve({ url, key: k }),
          reject,
        )
      })

      const { data } = await zipTaskApi.submitZipTask({
        unzipRequest: {
          filePath: key,
          uploadFileType: UnzipRequestUploadFileTypeEnum.PRODUCT_MATERIAL,
          zipBizType: UnzipRequestZipBizTypeEnum.UNZIP_PRODUCT_FILE,
        },
      })
      if (!data.success || !data.data?.zipTaskId)
        throw new Error(data.message || '提交解压任务失败')

      await pollUntilDone(data.data.zipTaskId)
    }
    finally {
      isUploading.value = false
    }
  }

  /** 轮询方法 */
  function pollUntilDone(taskId: string) {
    return new Promise<void>((resolve, reject) => {
      const timer = setInterval(async () => {
        try {
          const { data } = await zipTaskApi.queryUnzipResult({ taskId })
          if (!data.success)
            throw new Error(data.message || '查询失败')

          unzipStatus.value = data.data?.status ?? null
          const status = unzipStatus.value

          if (status === UnzipTaskVOStatusEnum.WAITING || status === UnzipTaskVOStatusEnum.PROCESSING)
            return

          clearInterval(timer)

          if (status === UnzipTaskVOStatusEnum.SUCCESS || status === UnzipTaskVOStatusEnum.PARTIAL_SUCCESS) {
            const taskData = data.data as UnzipTaskVO

            // 处理图片数据
            const images = convertUnzipResultsToImagesValue(taskData)
            mergeImages(imagesRef?.value, images)

            // 处理文件数据
            if (filesRef?.value) {
              const files = convertUnzipResultsToFiles(taskData)
              if (files.length > 0) {
                if (!filesRef.value) {
                  filesRef.value = []
                }
                mergeFiles(filesRef.value, files)
              }
            }

            ElMessage.success(status === 4 ? '部分成功，已导入可用素材' : '素材解压成功')
            resolve()
          }
          else {
            reject(new Error('解压失败'))
          }
        }
        catch (e) {
          clearInterval(timer)
          reject(e)
        }
      }, 2000)
    })
  }

  /** 合并图片 全部 append */
  function mergeImages(target: ProductImagesValue | undefined, incoming: ProductImagesValue) {
    if (!imagesRef.value) {
      imagesRef.value = reactive(structuredClone(incoming)) as ProductImagesValue
      return
    }
    imagesRef.value.mainImages.push(...incoming.mainImages)

    Object.entries(incoming.skuImages).forEach(([code, imgs]) => {
      imagesRef.value!.skuImages[code] = [...(imagesRef.value!.skuImages[code] || []), ...imgs]
    })

    imagesRef.value.detailImages.push(...incoming.detailImages)
  }

  /** 添加文件 */
  function mergeFiles(target: ProductFileVO[], incoming: ProductFileVO[]) {
    if (!incoming.length)
      return

    if (filesRef.value)
      filesRef.value = [...target, ...incoming]
  }

  return { isUploading, progress, unzipStatus, startUpload }
}
