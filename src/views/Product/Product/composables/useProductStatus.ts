import type { ProductBatchStatusUpdateRequest } from '@/apiv2/product'
import { productApi } from '@/api.services/product.service'

/**
 * 产品状态管理 Composable
 * @param pageListRef 列表组件引用，用于刷新列表
 */
export function useProductStatus(pageListRef: Ref<any | null>) {
  /**
   * 更新产品状态
   * @param productId 产品ID
   * @param targetStatus 目标状态
   * @param operationName 操作名称（上架/下架等）
   * @returns Promise
   */
  async function updateProductStatus(
    productId: number,
    targetStatus: ProductBatchStatusUpdateRequest['targetStatus'],
    operationName: string,
  ) {
    const result = await batchUpdateProductStatus([productId], targetStatus)
    if (result.data.success) {
      ElMessage.success(`产品${operationName}成功`)
    }
    return result
  }

  /**
   * 批量更新产品状态
   * @param productIds 产品ID列表
   * @param targetStatus 目标状态
   * @returns Promise
   */
  async function batchUpdateProductStatus(
    productIds: number[],
    targetStatus: ProductBatchStatusUpdateRequest['targetStatus'],
  ) {
    const response = await productApi.batchUpdateStatus({
      productBatchStatusUpdateRequest: {
        productIds,
        targetStatus,
      },
    })

    if (response.data.success) {
      pageListRef.value?.reload()
    }

    return response
  }

  /**
   * 删除确认并执行
   */
  async function confirmDelete(
    productIds: number[],
    confirmMessage: string,
    successMessage: string,
  ): Promise<boolean> {
    return new Promise<boolean>((resolve) => {
      ElMessageBox({
        title: '提示',
        message: confirmMessage,
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '删除中...'
            try {
              const response = await batchUpdateProductStatus(productIds, 5)
              if (response.data.success) {
                ElMessage.success(successMessage)
                resolve(true)
              }
              else {
                resolve(false)
              }
            }
            catch (error) {
              console.error('删除失败', error)
              resolve(false)
            }
            finally {
              done()
              instance.confirmButtonLoading = false
            }
          }
          else {
            done()
            resolve(false)
          }
        },
      })
    })
  }

  async function deleteProduct(productId: number) {
    return confirmDelete(
      [productId],
      '确定要删除该产品吗？',
      '删除成功',
    )
  }

  async function batchDeleteProducts(productIds: number[]) {
    return confirmDelete(
      productIds,
      `确定要删除选中的 ${productIds.length} 个产品吗？`,
      '批量删除成功',
    )
  }

  return {
    updateProductStatus,
    batchUpdateProductStatus,
    deleteProduct,
    batchDeleteProducts,
  }
}
