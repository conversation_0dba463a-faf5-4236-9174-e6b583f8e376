<script setup lang="ts">
import CgCategoryTree from '@/components/CgCategoryTree'
import { useTagsViewClose } from '@/composables/useTagsViewClose'
import CommodityProductSku from './components/CommodityProductSku.vue'
import ProductDescription from './components/ProductDescription.vue'
import ProductFile from './components/ProductFile.vue'
import ProductImage from './components/ProductImage.vue'
import ProductSku from './components/ProductSku.vue'
import PropertiesDialog from './components/PropertiesDialog.vue'
import { useProductForm } from './composables/useProductForm'

defineOptions({
  name: 'ProductDetail',
})

const { closeCurrentPage } = useTagsViewClose()

const {
  formData,
  isReadonly,
  isEditLoading: isLoading,
  productImage,
  formOptions,
  activeTab,
  activeMerchantTab,
  isMerchantView,
  commodityFormData,
} = useProductForm({ editData: {} }, {}, { readonly: true })

const skuComponentRef = useTemplateRef<InstanceType<typeof ProductSku>>('skuComponent')
const commoditySkuComponentRef = useTemplateRef<InstanceType<typeof CommodityProductSku>>('commoditySkuComponent')

const reactiveSkuCodes = ref<string[]>([])
const reactiveCommoditySkuCodes = ref<string[]>([])

// --- 更新 SKU Codes 的函数 ---
async function updateReactiveSkuCodes() {
  await nextTick()
  if (skuComponentRef.value) {
    try {
      const currentSkus = await skuComponentRef.value.getSkuData()
      if (Array.isArray(currentSkus)) {
        reactiveSkuCodes.value = currentSkus
          .map(sku => sku?.skuCode)
          .filter(Boolean) as string[]
      }
      else {
        reactiveSkuCodes.value = []
      }
    }
    catch (error) {
      console.error('Error getting SKU data:', error)
      reactiveSkuCodes.value = []
    }
  }
  else {
    reactiveSkuCodes.value = []
  }
}

async function updateReactiveCommoditySkuCodes() {
  await nextTick()
  if (commoditySkuComponentRef.value) {
    try {
      const currentCommoditySkus = await commoditySkuComponentRef.value.getSkuData()
      if (Array.isArray(currentCommoditySkus)) {
        reactiveCommoditySkuCodes.value = currentCommoditySkus
          .map(sku => sku?.skuCode)
          .filter(Boolean) as string[]
      }
      else {
        reactiveCommoditySkuCodes.value = []
      }
    }
    catch (error) {
      console.error('Error getting Commodity SKU data:', error)
      reactiveCommoditySkuCodes.value = []
    }
  }
  else {
    reactiveCommoditySkuCodes.value = []
  }
}

// --- 监听 Tab 切换 ---
watch(activeTab, (newTab) => {
  if (newTab === 'images') {
    updateReactiveSkuCodes()
  }
})

watch(activeMerchantTab, (newTab) => {
  if (newTab === 'images') {
    updateReactiveCommoditySkuCodes()
  }
})

provide('isDetail', true)
</script>

<template>
  <div v-loading="isLoading" class="product-detail h-full w-full flex flex-col overflow-y-auto">
    <div class="flex flex-shrink-0">
      <div>
        <CgImageUploader
          v-model="productImage"
          folder-name="category-icon"
          upload-text="产品主图"
          :size="{ width: 166, height: 166 }"
          :view-only="true"
          tip-text="SPU主图"
          access-type="private"
          :disabled="true"
        />
      </div>
      <div class="ml-4 flex-1">
        <CgForm
          v-model="formData"
          :options="formOptions"
          :table-style="true"
          :col-span="12"
        >
          <template #category>
            <CgCategoryTree
              v-model="formData.leafCategoryId"
              :disabled="isReadonly"
              @change="(id: any) => {
                formData.leafCategoryId = id
              }"
            />
          </template>
          <template #properties>
            <PropertiesDialog
              v-model="formData.properties"
              :readonly="isReadonly"
            />
          </template>
        </CgForm>
      </div>
    </div>
    <div
      class="product-content mt-2"
      :class="{
        'flex-1 overflow-y-auto': !isMerchantView,
        'h-[560px] overflow-y-auto flex-shrink-0': isMerchantView,
      }"
    >
      <ElTabs v-model="activeTab" class="h-full max-w-full">
        <ElTabPane key="description" label="产品描述" name="description" class="h-full" lazy>
          <ProductDescription v-model="formData.description" detail />
        </ElTabPane>
        <ElTabPane key="skus" label="SKU 信息" name="skus" class="h-full">
          <ProductSku ref="skuComponent" v-model="formData.skus as any" detail />
        </ElTabPane>
        <ElTabPane key="images" label="图片信息" name="images" class="h-full" lazy>
          <ProductImage v-model="formData.imagesComponent" detail :sku-list="reactiveSkuCodes" />
        </ElTabPane>
        <ElTabPane key="files" label="文件信息" name="files" class="h-full" lazy>
          <ProductFile v-model="formData.files as any" detail />
        </ElTabPane>
      </ElTabs>
    </div>

    <div v-if="isMerchantView" class="mt-2 flex flex-shrink-0 flex-col overflow-y-auto">
      <div class="mt-1 text-lg font-medium">
        本地化信息：
      </div>
      <ElTabs v-model="activeMerchantTab" class="h-full max-w-full">
        <ElTabPane key="base" label="基础信息" name="base" class="h-full">
          <div>
            <div class="mb-4 rounded-md bg-gray-50 p-4">
              <div class="grid grid-cols-2 gap-4">
                <div class="flex items-center">
                  <span class="min-w-80px w-24 text-gray-600">商品品名：</span>
                  <CgInput v-model="commodityFormData.commodityName" class="flex-1" />
                </div>
              </div>
            </div>
            <div class="mb-4 rounded-md bg-white">
              <CommodityProductSku ref="commoditySkuComponent" v-model="commodityFormData.skus as any" />
            </div>
          </div>
        </ElTabPane>
        <ElTabPane key="images" label="本地化图片" name="images" class="h-full" lazy>
          <div>
            <div class="rounded-md bg-white">
              <ProductImage v-model="commodityFormData.imagesComponent" detail />
            </div>
          </div>
        </ElTabPane>
        <ElTabPane key="files" label="本地化文件" name="files" class="h-full" lazy>
          <div class="max-h-400px overflow-auto">
            <div class="rounded-md bg-white">
              <ProductFile v-model="commodityFormData.files as any" detail />
            </div>
          </div>
        </ElTabPane>
      </ElTabs>
    </div>

    <div class="mt-4 flex justify-end gap-2">
      <CgButton @click="closeCurrentPage">
        关闭
      </CgButton>
    </div>
  </div>
</template>

<style scoped lang="scss">
.product-detail {
  padding: 16px;
}

.product-content {
  :deep(.el-tabs__content) {
    overflow-y: auto;
  }
}
</style>
