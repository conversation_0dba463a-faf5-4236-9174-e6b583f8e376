<script setup lang="ts">
import type { ImageInfo, ImageSection } from './types'

const props = withDefaults(defineProps<{
  images?: ImageInfo[]
  section: ImageSection
  skuId?: string
  isDragOver?: boolean
}>(), {
  images: () => [],
  isDragOver: false,
})

const emit = defineEmits<{
  drop: [event: DragEvent, section: ImageSection, skuId?: string]
  click: []
  dragover: [section: ImageSection, skuId?: string]
  dragleave: []
}>()

const isDetailView = inject('isDetailView', ref(false))

function handleDragOver(event: DragEvent) {
  event.preventDefault()

  if (isDetailView.value)
    return

  if (event.dataTransfer && event.dataTransfer.types.includes('application/internal-image-card')) {
    return
  }

  emit('dragover', props.section, props.skuId)
}

function handleDragLeave(event: DragEvent) {
  event.preventDefault()

  if (isDetailView.value)
    return

  if (event.dataTransfer && event.dataTransfer.types.includes('application/internal-image-card')) {
    return
  }

  emit('dragleave')
}

function handleDrop(event: DragEvent) {
  event.preventDefault()

  if (isDetailView.value)
    return

  if (event.dataTransfer && event.dataTransfer.types.includes('application/internal-image-card')) {
    return
  }

  emit('drop', event, props.section, props.skuId)
}

function handleClick() {
  if (isDetailView.value)
    return

  emit('click')
}

const isEmpty = computed(() => !props.images || props.images.length === 0)
</script>

<template>
  <div
    class="min-h-48 border rounded p-4"
    :class="{
      'border-dashed': !isDetailView,
      'border-#399e96 bg-#399e96/10': isDragOver && !isDetailView,
      'border-gray-300': !isDragOver || isDetailView,
      'cursor-pointer': !isDetailView,
      'border-none shadow-none': isDetailView,
    }"
    @dragover.prevent="handleDragOver"
    @dragleave.prevent="handleDragLeave"
    @drop.prevent="handleDrop"
    @click="handleClick"
  >
    <div v-if="isEmpty" class="h-32 flex flex-col items-center justify-center" :class="{ 'cursor-pointer': !isDetailView }">
      <template v-if="!isDetailView">
        <i class="fa fa-cloud-upload-alt mb-2 text-2xl text-gray-400" />
        <div class="color-placeholder text-sm">
          <slot name="empty-text">
            拖拽图片到此区域或点击上传图片
          </slot>
        </div>
      </template>
      <template v-else>
        <!-- <i class="fa fa-image mb-2 text-2xl text-gray-400" /> -->
        <font-awesome-icon :icon="['fa', 'images']" class="mb-2 text-2xl text-gray-400" />
        <div class="color-placeholder text-sm">
          暂无图片
        </div>
      </template>
    </div>

    <slot v-else />
  </div>
</template>
