<script setup lang="ts">
defineOptions({
  name: 'PriorityDialog',
})

defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'confirm': [priority: number, done: () => void]
}>()

const priorityOptions = [
  { value: 3, label: '加急', color: '#f56c6c', bgColor: 'rgba(245, 108, 108, 0.1)', desc: '紧急上新产品，需优先审核并快速处理的产品建档工单' },
  { value: 2, label: '一般', color: '#e6a23c', bgColor: 'rgba(230, 162, 60, 0.1)', desc: '常规产品建档，按正常流程处理的标准工单' },
  { value: 1, label: '暂缓', color: '#67c23a', bgColor: 'rgba(103, 194, 58, 0.1)', desc: '非紧急产品，可在其他优先级工单处理完后再处理' },
]

const selectedPriority = ref(2) // 默认选择"一般"优先级

function handleConfirm(done: () => void) {
  const wrappedDone = () => {
    done()
    emit('update:visible', false)
  }

  // 调用父组件的confirm事件处理函数，传入优先级和包装后的done函数
  emit('confirm', selectedPriority.value, wrappedDone)
}

function closeDialog() {
  emit('update:visible', false)
}
</script>

<template>
  <CgDialog
    :model-value="visible"
    title="选择工单优先级"
    width="480px"
    :full-height="false"
    confirm-button-text="确定"
    @update:model-value="emit('update:visible', $event)"
    @submit="handleConfirm"
    @close="closeDialog"
  >
    <div class="space-y-4">
      <div
        v-for="option in priorityOptions"
        :key="option.value"
        class="cursor-pointer border-2 border-transparent rounded-lg p-4 transition-all duration-200 hover:shadow-sm"
        :class="{
          'border-primary bg-primary/5': selectedPriority === option.value,
          'bg-gray-50 hover:border-gray-200': selectedPriority !== option.value,
        }"
        @click="selectedPriority = option.value"
      >
        <div class="mb-3 flex items-center justify-between">
          <div class="flex items-center gap-2">
            <div
              class="h-3 w-3 rounded-full"
              :style="{ backgroundColor: option.color }"
            />
            <span class="text-base font-medium" :style="{ color: option.color }">{{ option.label }}</span>
          </div>
          <div>
            <ElRadio v-model="selectedPriority" :label="option.value">
              {{ '' }}
            </ElRadio>
          </div>
        </div>
        <div
          class="ml-5 text-sm text-gray-500"
          :class="{ 'text-gray-700': selectedPriority === option.value }"
        >
          {{ option.desc }}
        </div>
      </div>
    </div>
  </CgDialog>
</template>
