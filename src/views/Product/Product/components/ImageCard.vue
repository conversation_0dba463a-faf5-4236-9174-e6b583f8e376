<script setup lang="ts">
import type { ImageInfo } from './types'

const props = defineProps<{
  image: ImageInfo
  index: number
  primaryLabel?: string
}>()

const emit = defineEmits<{
  'remove': [index: number]
  'preview': []
  'update:filename': [index: number, newFilename: string]
}>()

const isDetailView = inject('isDetailView', ref(false))

// --- 文件名编辑状态 ---
const isEditingFilename = ref(false)
const editedFilename = ref('')
const filenameInputRef = ref<HTMLInputElement | null>(null)

// --- 图片加载状态 ---
const imageLoaded = ref(false)
const imageError = ref(false)

function handleImageLoad() {
  imageLoaded.value = true
}

function handleImageError() {
  imageError.value = true
}

function handleDragStart(event: DragEvent) {
  if (isDetailView.value)
    return

  if (event.dataTransfer) {
    event.dataTransfer.setData('application/internal-image-card', 'true')
    event.dataTransfer.setData('image-index', props.index.toString())
  }
}

function startEditingFilename(event: MouseEvent) {
  event.stopPropagation()
  editedFilename.value = props.image.filename || ''
  isEditingFilename.value = true
  nextTick(() => {
    filenameInputRef.value?.focus()
    filenameInputRef.value?.select()
  })
}

function saveFilename() {
  if (!isEditingFilename.value)
    return
  const newName = editedFilename.value.trim()
  if (newName && newName !== props.image.filename) {
    emit('update:filename', props.index, newName)
  }
  isEditingFilename.value = false
}

function cancelEdit() {
  isEditingFilename.value = false
}

function removeImage(event: Event) {
  event.stopPropagation()
  emit('remove', props.index)
}

function triggerPreview() {
  if (!isEditingFilename.value) {
    emit('preview')
  }
}
</script>

<template>
  <div
    class="group relative flex flex-col overflow-hidden border border-gray-200 rounded-md shadow-sm transition-all duration-200"
    :class="{ 'hover:shadow-md': !isDetailView }" :draggable="!isDetailView"
    @dragstart="handleDragStart"
  >
    <!-- 加载状态 -->
    <div
      v-if="!imageLoaded && !imageError"
      class="absolute inset-0 z-20 flex items-center justify-center bg-gray-50/80"
    >
      <div class="h-8 w-8 animate-spin border-2 border-gray-300 border-t-#399e96 rounded-full" />
    </div>

    <!-- 错误状态 -->
    <div v-if="imageError" class="absolute inset-0 z-20 flex flex-col items-center justify-center bg-gray-50/80 p-2">
      <font-awesome-icon :icon="['fas', 'exclamation-triangle']" class="mb-1 text-lg text-orange-500" />
      <span class="text-center text-xs text-gray-500">图片加载失败</span>
    </div>

    <!-- 标签 -->
    <div
      v-if="primaryLabel"
      class="absolute left-0 top-0 z-10 rounded-br-md from-#399e96 to-#2c7e78 bg-gradient-to-r px-2 py-1 text-sm text-white font-medium shadow-sm"
    >
      {{ primaryLabel }}
    </div>

    <!-- 图片容器 (添加了点击预览) -->
    <div class="relative h-54 flex-shrink-0 cursor-zoom-in overflow-hidden bg-gray-50" @click.stop="triggerPreview">
      <img
        :src="image.localUrl || image.url" alt="Product image"
        class="h-full w-full object-contain transition-transform duration-300 group-hover:scale-105"
        @load="handleImageLoad" @error="handleImageError"
      >

      <!-- 删除按钮 (内部已有 .stop) - 仅在非预览模式显示 -->
      <div
        v-if="!isDetailView"
        class="absolute right-2 top-2 z-10 transform opacity-0 transition-all duration-200 group-hover:opacity-100"
      >
        <button
          class="h-6 w-6 flex items-center justify-center rounded-full bg-white/90 text-xs text-red-500 shadow-md transition-all hover:bg-red-500 hover:text-white"
          title="删除图片" @click.stop="removeImage"
        >
          <font-awesome-icon :icon="['fas', 'trash-alt']" />
        </button>
      </div>
    </div>

    <!-- 图片元数据 -->
    <div class="flex-1 bg-white p-2">
      <div class="flex justify-between text-xs">
        <div class="flex items-center text-gray-600">
          <font-awesome-icon :icon="['fas', 'ruler']" class="mr-1 text-#399e96/80" />
          <span>{{ image.dimensions?.width || '-' }} × {{ image.dimensions?.height || '-' }}</span>
        </div>
        <div class="flex items-center text-gray-600">
          <font-awesome-icon :icon="['fas', 'weight']" class="mr-1 text-#399e96/80" />
          <span>{{ image.size || '-' }} KB</span>
        </div>
      </div>
      <!-- 文件名（在编辑模式下可编辑） -->
      <div class="mt-1 min-h-[1.5em] text-xs text-gray-500" :title="image.filename">
        <!-- 预览模式下的文件名显示 -->
        <div v-if="isDetailView" class="flex items-center truncate">
          <span class="truncate">{{ image.filename }}</span>
        </div>
        <!-- 编辑模式下的文件名显示与编辑 -->
        <template v-else>
          <div
            v-if="!isEditingFilename" class="flex cursor-pointer items-center truncate hover:text-#399e96"
            @click.stop="startEditingFilename"
          >
            <span class="truncate">{{ image.filename }}</span>
            <font-awesome-icon
              :icon="['fas', 'pencil-alt']"
              class="ml-1.5 flex-shrink-0 text-gray-400 opacity-0 transition-opacity group-hover:opacity-100"
            />
          </div>
          <input
            v-else ref="filenameInputRef" v-model="editedFilename" type="text"
            class="w-full border border-gray-300 rounded px-1 py-0.5 text-xs focus:border-#399e96 focus:outline-none"
            @blur="saveFilename" @keyup.enter="saveFilename" @keyup.esc="cancelEdit" @click.stop
          >
        </template>
      </div>
    </div>
  </div>
</template>
