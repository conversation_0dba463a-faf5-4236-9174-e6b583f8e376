<script setup lang="ts">
import { getTagStyle } from '../utils/color'

interface Property {
  id: number | string
  valueName: string
  attrs?: {
    color?: string
  }
}

const props = withDefaults(defineProps<{
  properties?: Property[]
  maxVisible?: number
}>(), {
  properties: () => [],
  maxVisible: 10,
})

const visibleProperties = computed(() => {
  return props.properties.slice(0, props.maxVisible)
})

const hasMore = computed(() => {
  return props.properties.length > props.maxVisible
})
const isTooltipDisabled = computed(() => {
  return !props.properties || props.properties.length === 0
})
</script>

<template>
  <ElTooltip
    v-if="properties && properties.length > 0" placement="top" :show-after="200" :hide-after="500"
    effect="light" :disabled="isTooltipDisabled"
  >
    <!-- Tooltip 内容: 显示所有标签 -->
    <template #content>
      <div class="max-w-400px flex flex-wrap items-center gap-6px">
        <span
          v-for="property in properties" :key="property.id"
          class="inline-block whitespace-nowrap border border-[#dcdfe6] rounded-md bg-[#f4f4f5] px-2 py-0.5 text-xs text-[#909399] leading-normal"
          :style="property.attrs?.color ? getTagStyle(property.attrs?.color) : {}"
        >
          {{ property.valueName }}
        </span>
      </div>
    </template>

    <!-- 单元格内显示: 最多显示 maxVisible 个标签 -->
    <!-- <div
      class="max-h-42px flex flex-wrap items-center gap-x-1 gap-y-0.5 overflow-hidden after:absolute after:bottom-1px after:left-0 after:right-0 after:h-6 after:from-white after:to-transparent after:bg-gradient-to-t after:content-['']"
    > -->
    <div class="max-h-42px flex flex-wrap items-center gap-x-1 gap-y-0.5 overflow-hidden">
      <span
        v-for="property in visibleProperties" :key="property.id"
        class="inline-block whitespace-nowrap border border-[#dcdfe6] rounded-md bg-[#f4f4f5] px-1.5 py-0 text-xs text-[#909399] leading-normal"
        :style="property.attrs?.color ? getTagStyle(property.attrs?.color) : {}"
      >
        {{ property.valueName }}
      </span>
      <span v-if="hasMore" class="ml-1 cursor-default whitespace-nowrap text-xs text-gray-500">
        更多...
      </span>
    </div>
  </ElTooltip>
  <!-- 如果没有属性，显示占位符 -->
  <span v-else class="text-[#909399]">-</span>
</template>\
