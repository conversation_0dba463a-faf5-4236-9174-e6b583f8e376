<script setup lang="ts">
import type { ImageInfo, ImageSection as ImageSectionType, ProductImagesValue } from './types'
import { useProductImages } from './composables/useProductImages'
import ImageSection from './ImageSection.vue'

const props = withDefaults(defineProps<{
  modelValue?: ProductImagesValue
  skuList?: string[]
  detail?: boolean
}>(), {
  modelValue: () => ({
    mainImages: [],
    skuImages: {},
    detailImages: [],
  }),
})

const emits = defineEmits<{
  'update:modelValue': [value: ProductImagesValue]
  'uploadSuccess': [url: string, key: string]
  'uploadError': [error: any]
  'updateSpuImage': [imageUrl: string, key: string]
  'update:skuThumbnail': [payload: { skuCode: string, thumbnail?: string }]
}>()

const modelValue = useVModel(props, 'modelValue')
provide('isDetailView', computed(() => props.detail || false))

// 图片预览相关状态
const showPreview = ref(false)
const previewImages = ref<string[]>([])
const previewIndex = ref(0)

// 处理图片预览
function handlePreview(images: ImageInfo[], index: number) {
  previewImages.value = images.map(img => img.localUrl || img.url).filter(Boolean) // 过滤掉无效url
  if (previewImages.value.length > 0 && index >= 0 && index < previewImages.value.length) {
    previewIndex.value = index
    showPreview.value = true
  }
}

const {
  setFileInputRef,
  handleFileSelect,
  getSkuIds,
  isDragTarget,
  allInputKeys,
  createImageSection,
} = useProductImages(
  modelValue,
  props.skuList,
  (url, key) => emits('updateSpuImage', url, key),
  (url, key) => emits('uploadSuccess', url, key),
  error => emits('uploadError', error),
)

const mainImagesData = computed(() => modelValue.value.mainImages || [])
const detailImagesData = computed(() => modelValue.value.detailImages || [])

function getSkuImagesData(skuId: string) {
  return modelValue.value.skuImages?.[skuId] || []
}

watch(() => modelValue.value?.mainImages?.[0], (newFirstImage) => {
  const urlToEmit = newFirstImage?.localUrl || newFirstImage?.url || ''
  emits('updateSpuImage', urlToEmit, newFirstImage?.imageKey || '')
}, { immediate: true, deep: true })

watch(
  () => modelValue.value.skuImages,
  (newSkuImages, oldSkuImages) => {
    if (newSkuImages) {
      for (const skuCode in newSkuImages) {
        const currentImages = newSkuImages[skuCode]
        const oldFirstImageKey = oldSkuImages?.[skuCode]?.[0]?.imageKey
        const currentFirstImageKey = currentImages?.[0]?.imageKey

        if (currentFirstImageKey !== oldFirstImageKey) {
          emits('update:skuThumbnail', { skuCode, thumbnail: currentFirstImageKey })
        }
      }
    }

    if (oldSkuImages) {
      for (const skuCode in oldSkuImages) {
        if ((!newSkuImages || !newSkuImages[skuCode]) && oldSkuImages[skuCode]?.length > 0) {
          emits('update:skuThumbnail', { skuCode, thumbnail: undefined })
        }
      }
    }
  },
  { deep: true },
)

function handleFilenameUpdate(payload: { index: number, newFilename: string, section: ImageSectionType, skuId?: string }) {
  if (!modelValue.value)
    return

  const { index, newFilename, section, skuId } = payload

  try {
    if (section === 'mainImages' && modelValue.value.mainImages?.[index]) {
      modelValue.value.mainImages[index].filename = newFilename
    }
    else if (section === 'detailImages' && modelValue.value.detailImages?.[index]) {
      modelValue.value.detailImages[index].filename = newFilename
    }
    else if (section === 'skuImages' && skuId && modelValue.value.skuImages?.[skuId]?.[index]) {
      // 创建 skuImages 的副本以确保响应性
      const updatedSkuImages = { ...modelValue.value.skuImages }
      if (updatedSkuImages[skuId]?.[index]) {
        // 创建对应 skuId 数组的副本
        const updatedSkuArray = [...updatedSkuImages[skuId]]
        updatedSkuArray[index] = { ...updatedSkuArray[index], filename: newFilename }
        updatedSkuImages[skuId] = updatedSkuArray
        modelValue.value.skuImages = updatedSkuImages
      }
    }
    // 触发 update:modelValue 以确保父组件知道数据已更改（虽然 useVModel 通常会处理）
    emits('update:modelValue', modelValue.value)
  }
  catch (error) {
    console.error('Failed to update filename:', error)
    // 这里可以添加一些错误处理逻辑，比如 ElMessage
  }
}

// --- 处理排序更新 ---
function handleSectionUpdate(images: ImageInfo[], section: ImageSectionType, skuId?: string) {
  if (!modelValue.value)
    return
  if (section === 'mainImages') {
    modelValue.value.mainImages = images
  }
  else if (section === 'detailImages') {
    modelValue.value.detailImages = images
  }
  else if (section === 'skuImages' && skuId) {
    const newSkuImages = { ...(modelValue.value.skuImages || {}) }
    newSkuImages[skuId] = images
    modelValue.value.skuImages = newSkuImages
  }
  // 触发 update:modelValue
  emits('update:modelValue', modelValue.value)
}

// --- 处理移除图片 ---
function handleRemoveImage(index: number, section: ImageSectionType, skuId?: string) {
  if (!modelValue.value)
    return

  let targetArray: ImageInfo[] | undefined
  let updateFn: (arr: ImageInfo[]) => void = () => {}

  if (section === 'mainImages') {
    targetArray = modelValue.value.mainImages
    updateFn = (arr) => {
      modelValue.value.mainImages = arr
    }
  }
  else if (section === 'detailImages') {
    targetArray = modelValue.value.detailImages
    updateFn = (arr) => {
      modelValue.value.detailImages = arr
    }
  }
  else if (section === 'skuImages' && skuId && modelValue.value.skuImages?.[skuId]) {
    targetArray = modelValue.value.skuImages[skuId]
    updateFn = (arr) => {
      const newSkuImages = { ...(modelValue.value.skuImages || {}) }
      newSkuImages[skuId] = arr
      modelValue.value.skuImages = newSkuImages
    }
  }

  if (targetArray && index >= 0 && index < targetArray.length) {
    const newArray = [...targetArray]
    newArray.splice(index, 1)
    updateFn(newArray)
    // 触发 update:modelValue
    emits('update:modelValue', modelValue.value)
  }
}

watch(() => props.skuList, (newSkuList) => {
  if (newSkuList) {
    allInputKeys.value = ['mainImages', 'detailImages', ...(newSkuList.map(code => `sku-${code}`))]
    if (!modelValue.value.skuImages) {
      modelValue.value.skuImages = {}
    }
    newSkuList.forEach((skuCode) => {
      if (!modelValue.value.skuImages![skuCode]) {
        modelValue.value.skuImages![skuCode] = []
      }
    })
  }
}, { immediate: true, deep: true })
</script>

<template>
  <div class="h-full pr-4">
    <!-- 隐藏的文件输入框 -->
    <template v-if="!detail">
      <input
        v-for="key in allInputKeys" :key="`input-${key}`"
        :ref="el => setFileInputRef(el, key)" type="file" accept="image/*" class="hidden" multiple
        @change="handleFileSelect($event,
                                  key.startsWith('sku-') ? 'skuImages' : key as any,
                                  key.startsWith('sku-') ? key.substring(4) : undefined)"
      >
    </template>

    <!-- 产品主图 Section -->
    <ImageSection
      title="产品主图"
      section="mainImages"
      :images="mainImagesData"
      empty-text="拖拽图片到此区域或点击上传产品主图"
      key-prefix="main"
      :is-drag-over="isDragTarget('mainImages')"
      primary-label="SPU主图"
      :use-draggable="true"
      @dragover="(section) => createImageSection('', section, [], '', '').handleDragOver()"
      @dragleave="createImageSection('', 'mainImages', [], '', '').handleDragLeave"
      @drop="(event, section) => createImageSection('', section, [], '', '').handleDrop(event)"
      @click="createImageSection('', 'mainImages', [], '', '').handleClick"
      @preview="(index: number) => handlePreview(mainImagesData, index)"
      @remove="(index) => handleRemoveImage(index, 'mainImages')"
      @update="(images) => handleSectionUpdate(images, 'mainImages')"
      @update-filename="handleFilenameUpdate"
    />

    <!-- SKU图 Section -->
    <div class="mb-6">
      <div class="mb-3 flex items-center justify-between">
        <div class="flex items-center">
          <h3
            class="flex items-center border-l-4 border-#399e96 rounded-r bg-gray-50 py-1 pl-2 text-sm font-medium shadow-sm"
          >
            <span class="pr-1 text-gray-700 transition-colors group-hover:text-#399e96">SKU图</span>
          </h3>
        </div>
      </div>

      <div>
        <div v-if="skuList?.length === 0" class="color-placeholder py-4 text-center text-sm">
          暂无SKU信息
        </div>

        <div v-for="skuCode in skuList" :key="skuCode" class="mb-6 pl-6">
          <h4 class="mb-2 ml-1 flex items-center text-xs font-medium">
            <font-awesome-icon :icon="['fas', 'tag']" class="mr-1.5 text-#399e96" />
            <span class="text-gray-600">{{ skuCode }}</span>
          </h4>

          <ImageSection
            title=""
            section="skuImages"
            :images="getSkuImagesData(skuCode)"
            empty-text="拖拽图片到此区域或点击上传SKU图片"
            :key-prefix="`sku-${skuCode}`"
            :sku-id="skuCode"
            :is-drag-over="isDragTarget('skuImages', skuCode)"
            :use-draggable="true"
            @dragover="(section, skuCode) => createImageSection('', section, [], '', '', skuCode).handleDragOver()"
            @dragleave="createImageSection('', 'skuImages', [], '', '', skuCode).handleDragLeave"
            @drop="(event, section, skuCode) => createImageSection('', section, [], '', '', skuCode).handleDrop(event)"
            @click="createImageSection('', 'skuImages', [], '', '', skuCode).handleClick"
            @preview="(index: number) => handlePreview(getSkuImagesData(skuCode), index)"
            @remove="(index, sId) => handleRemoveImage(index, 'skuImages', sId)"
            @update="(images) => handleSectionUpdate(images, 'skuImages', skuCode)"
            @update-filename="handleFilenameUpdate"
          />
        </div>
      </div>
    </div>

    <!-- 详情图 Section -->
    <ImageSection
      title="详情图"
      section="detailImages"
      :images="detailImagesData"
      empty-text="拖拽图片到此区域或点击上传详情图片"
      key-prefix="detail"
      :is-drag-over="isDragTarget('detailImages')"
      :use-draggable="true"
      @dragover="(section) => createImageSection('', section, [], '', '').handleDragOver()"
      @dragleave="createImageSection('', 'detailImages', [], '', '').handleDragLeave"
      @drop="(event, section) => createImageSection('', section, [], '', '').handleDrop(event)"
      @click="createImageSection('', 'detailImages', [], '', '').handleClick"
      @preview="(index: number) => handlePreview(detailImagesData, index)"
      @remove="(index) => handleRemoveImage(index, 'detailImages')"
      @update="(images) => handleSectionUpdate(images, 'detailImages')"
      @update-filename="handleFilenameUpdate"
    />
    <!-- 图片预览组件 -->
    <ElImageViewer
      v-if="showPreview"
      :url-list="previewImages"
      :initial-index="previewIndex"
      :hide-on-click-modal="true"
      teleported
      show-progress
      infinite
      @close="showPreview = false"
    />
  </div>
</template>
