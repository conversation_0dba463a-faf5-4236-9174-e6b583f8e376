<script setup lang="ts">
import type { ProductInitTicketCreateResultDetailVO } from '@/apiv2/product'

defineOptions({
  name: 'TicketCreateResultDialog',
  inheritAttrs: false,
})

const props = defineProps<{
  visible: boolean
  result: ProductInitTicketCreateResultDetailVO | null
  productCodeMap?: Record<string, string>
}>()
const emit = defineEmits(['update:visible'])

const failedList = computed(() => {
  if (!props.result?.failed)
    return []
  return Object.entries(props.result.failed).map(([id, err]: any) => ({
    id: Number(id),
    productCode: err?.productCode,
    msg: err?.message ?? '未知错误',
  }))
})

/** 成功明细：工单ID与产品ID映射 */
const successList = computed(() => {
  if (!props.result?.succeeded)
    return []
  return Object.entries(props.result.succeeded).map(([productId, ticketId]: [string, string]) => ({
    productId: Number(productId),
    ticketId,
    productCode: props.productCodeMap?.[productId],
  }))
})
</script>

<template>
  <CgDialog
    :model-value="visible"
    title="工单创建结果"
    width="520px"
    :full-height="false"
    @close="emit('update:visible', false)"
  >
    <!-- 成功列表 -->
    <template v-if="successList.length">
      <div class="mb-2 text-sm text-gray-700 font-medium">
        成功创建的工单：
      </div>
      <ElScrollbar v-if="successList.length" height="100" class="mb-4">
        <ul class="space-y-2">
          <li
            v-for="item in successList"
            :key="item.productCode"
            class="flex gap-2 text-sm leading-5"
          >
            <span class="w-32 text-gray-800 font-mono">{{ item.productCode }}</span>
            <span class="flex-1 text-gray-500">工单ID: {{ item.ticketId }}</span>
          </li>
        </ul>
      </ElScrollbar>
    </template>

    <!-- 失败列表 -->
    <template v-if="failedList.length">
      <div class="mb-2 text-sm text-gray-700 font-medium">
        创建失败的产品：
      </div>
      <ElScrollbar height="100">
        <ul class="space-y-2">
          <li
            v-for="item in failedList"
            :key="item.id"
            class="flex gap-2 text-sm leading-5"
          >
            <span class="w-32 text-gray-800 font-mono">{{ item.productCode }}</span>
            <span class="flex-1 text-gray-500">{{ item.msg }}</span>
          </li>
        </ul>
      </ElScrollbar>
    </template>

    <template #footer>
      <CgButton type="primary" @click="emit('update:visible', false)">
        知道了
      </CgButton>
    </template>
  </CgDialog>
</template>

<style scoped>
ul {
  padding-left: 0;
}
</style>
