/**
 * 图片信息接口
 */
export interface ImageInfo {
  /**
   * 图片ID
   */
  id?: number
  /**
   * 图片的URL（OSS URL）
   */
  url: string
  /**
   * 实际提交给后端的图片oss key
   */
  imageKey: string
  /**
   * 本地Blob URL，用于上传前的图片回显
   */
  localUrl?: string
  /**
   * 图片的大小（可选）
   */
  size?: number
  /**
   * 图片的尺寸（可选）
   */
  dimensions?: { width: number, height: number, type?: string }
  /**
   * 图片的文件名（可选）
   */
  filename?: string
  /**
   * skuCode
   */
  skuCode?: string
}

/**
 * 产品图片值接口
 */
export interface ProductImagesValue {
  /**
   * 主图片列表
   */
  mainImages: ImageInfo[]
  /**
   * SKU图片列表
   */
  skuImages: Record<string, ImageInfo[]>
  /**
   * 详情图片列表
   */
  detailImages: ImageInfo[]
}

/**
 * 图片部分类型
 */
export type ImageSection = 'mainImages' | 'skuImages' | 'detailImages'

/**
 * 拖拽目标接口
 */
export interface DragTarget {
  /**
   * 图片部分
   */
  section: ImageSection
  /**
   * SKU ID（可选）
   */
  skuId?: string
}
