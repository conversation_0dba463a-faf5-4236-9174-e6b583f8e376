<script setup lang="ts">
import type { ProductSkuUpdateRequest } from '@/apiv2/product'

defineProps({
  rowData: {
    type: Object as () => ProductSkuUpdateRequest,
    default: () => ({}),
  },
  seq: {
    type: Number,
    default: 0,
  },
})
const emit = defineEmits(['deleteRow'])

const detail = inject<Ref<boolean>>('isDetail', ref(false))
</script>

<template>
  <div class="cell-container">
    <div :class="{ 'is-active': !detail }">
      {{ seq }}
    </div>
    <div v-if="!detail" class="delete">
      <font-awesome-icon
        :icon="['fas', 'trash']"
        class="cursor-pointer rounded p-1 text-red-500 hover:text-red-600"
        @click="emit('deleteRow')"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.cell-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  width: 100%;
  position: relative;
}

.is-active {
  display: flex;
  justify-content: center;
  align-items: center;
}

.delete {
  display: none;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  justify-content: center;
  align-items: center;
}

.vxe-table .vxe-body--row.row--current .is-active,
.vxe-table .vxe-body--row.row--hover .is-active {
  display: none;
}

.vxe-table .vxe-body--row.row--current .delete,
.vxe-table .vxe-body--row.row--hover .delete {
  display: flex;
}
</style>
