<script setup lang="ts">
import type { FreightMatchRequest } from '@/apiv2/fulfillment/models/freight-match-request'
import type { ProductSkuPriceVO, ProductSkuUpdateRequest } from '@/apiv2/product'
import type CgForm from '@/components/CgElementUI/CgForm'
import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'
import { freightApi, logisticsProviderApi, transportApi } from '@/api.services/fulfillment.service'
import { dictApi, productApi } from '@/api.services/product.service'
import { LogisticsProviderVOLogisticsStageEnum } from '@/apiv2/fulfillment'
import CgComplexInputV2 from '@/components/CgElementUI/CgComplexInputV2'
import ComponentTagsEnum from '@/components/CgElementUI/CgForm/ComponentTagsEnum'
import CgInput from '@/components/CgElementUI/CgInput'
import CgInputNumber from '@/components/CgElementUI/CgInputNumber'
import CgSelectV2 from '@/components/CgElementUI/CgSelectV2'
import { transformDictData } from '@/components/utils/shared'
import { isNil } from 'lodash-es'
import { useRoute } from 'vue-router'
import PropertiesDialog from './PropertiesDialog.vue'
import SizeInput from './SizeInput.vue'

defineOptions({
  name: 'SkuEditDrawer',
})

const props = defineProps<{
  modelValue: boolean
  skuData: Partial<ProductSkuUpdateRequest>
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [payload: { editedSkuData: Partial<ProductSkuUpdateRequest>, batchUpdate: Record<string, boolean> }]
}>()

const localVisible = useVModel(props, 'modelValue', emit)
const drawerLoading = ref(false)

const editingSkuData = ref<Partial<ProductSkuUpdateRequest>>({})
const packagingTypeDisplay = ref('无包装尺寸')

const route = useRoute()
const isDetailUrl = computed(() => {
  return route.path.includes('/product/detail/')
})

watch(() => props.skuData, (newSkuData) => {
  editingSkuData.value = JSON.parse(JSON.stringify(newSkuData || {}))
  if (!Array.isArray(editingSkuData.value.specs) || editingSkuData.value.specs.length === 0) {
    editingSkuData.value.specs = [{ length: undefined, width: undefined, height: undefined }]
  }
  if (!Array.isArray(editingSkuData.value.packages) || editingSkuData.value.packages.length === 0) {
    editingSkuData.value.packages = [{ length: undefined, width: undefined, height: undefined }] as any[]
  }
  updatePackagingTypeDisplay()
}, { immediate: true, deep: true })

function updatePackagingTypeDisplay() {
  if (Array.isArray(editingSkuData.value.packages) && editingSkuData.value.packages.length > 0) {
    packagingTypeDisplay.value = `${editingSkuData.value.packages.length} 个包装尺寸`
  }
  else {
    packagingTypeDisplay.value = '无包装尺寸'
  }
}

const logisticsFormOptions = computed<CgFormOption[][]>(() => [
  [
    {
      type: 'slot',
      span: 24,
      slotName: 'logisticsSlot',
    },
    {
      type: ComponentTagsEnum.InputNumber,
      key: 'oceanFreightEstimate',
      label: '海运干线运费预估',
      span: 24,
      props: (model: any) => ({
        suffix: model.oceanFreightEstimateCurrency || 'JPY',
        min: 0,
        precision: (model.oceanFreightEstimateCurrency === 'JPY' || !model.oceanFreightEstimateCurrency) ? 0 : 2,
        isEditing: !isDetailUrl.value,
      }),
    },
    {
      type: ComponentTagsEnum.InputNumber,
      key: 'lastMileFreightEstimate',
      label: '尾程派送预估',
      span: 24,
      props: (model: any) => ({
        suffix: model.lastMileFreightEstimateCurrency || 'JPY',
        min: 0,
        precision: (model.lastMileFreightEstimateCurrency === 'JPY' || !model.lastMileFreightEstimateCurrency) ? 0 : 2,
        isEditing: !isDetailUrl.value,
      }),
    },
    {
      type: ComponentTagsEnum.SelectV2,
      key: 'transportMethod',
      label: '运输方式',
      span: 24,
      props: {
        placeholder: '例如：海运、空运',
        isEditing: !isDetailUrl.value,
        proxyOption: {
          request: transportApi.page,
        },
        props: {
          label: 'name',
          value: 'id',
        },
      },
    },
    {
      type: ComponentTagsEnum.SelectV2,
      key: 'logisticsChannel',
      label: '尾端派送渠道',
      span: 24,
      props: {
        placeholder: '例如：海运、空运',
        isEditing: !isDetailUrl.value,
        proxyOption: {
          request: logisticsProviderApi.listAll,
          query: {
            stage: LogisticsProviderVOLogisticsStageEnum.TAIL,
          },
        },
        props: {
          label: 'name',
          value: 'id',
        },
      },
    },
    {
      type: ComponentTagsEnum.Input,
      key: 'estimatedDeliveryDays',
      label: '整体时效',
      span: 24,
      props: {
        placeholder: '例如：3-5天',
        isEditing: !isDetailUrl.value,
        disabled: true,
      },
    },
  ],
])

const batchUpdateFlags = reactive({
  skuNameEn: false,
  priMatLv1: false,
  priMatLv2: false,
  secMatLv1: false,
  secMatLv2: false,
  electronicState: false,
  electronicCertification: false,
  materialDesc: false,
  spec1: false,
  spec2: false,
  spec3: false,
  spec4: false,
  spec5: false,
  installDocCollectionState: false,
  appendant: false,
  installDifficulty: false,
  electricAccessory: false,
  battery: false,
  fragile: false,
})

const drawerFormOptions = computed<CgFormOption[][]>(() => [
  [
    {
      type: ComponentTagsEnum.Input,
      key: 'skuName',
      label: '品名',
      required: true,
      span: 24,
      props: { placeholder: '不填写，默认 SPU 款名', isEditing: !isDetailUrl.value },
    },
  ],
  [
    {
      type: 'slot',
      key: 'properties',
      label: '产品标签',
      span: 24,
      slotName: 'propertiesSlot',
    },
  ],
  [
    { type: 'split', label: '基础信息', span: 24 },
    {
      type: 'slot',
      key: 'skuNameEn',
      label: '品名(英文)',
      span: 24,
      slotName: 'batch-skuNameEn',
    },
    {
      type: ComponentTagsEnum.SelectV2,
      key: 'color',
      label: '颜色',
      required: true,
      span: 24,
      props: {
        proxyOption: { request: dictApi.listDicts, query: { codes: 'color' } },
        transform: transformDictData,
        filterable: true,
        placeholder: '请选择颜色',
        isEditing: !isDetailUrl.value,
      },
    },
    {
      type: 'slot',
      key: 'specs',
      label: '尺寸(cm)',
      span: 24,
      slotName: 'specsDrawer',
    },
    {
      type: ComponentTagsEnum.InputNumber,
      key: 'netWeight',
      label: '净重(kg)',
      span: 24,
      props: { suffix: 'kg', min: 0, precision: 3, isEditing: !isDetailUrl.value },
    },
    {
      type: 'slot',
      key: 'priMatLv1',
      label: '主材质(一级)',
      span: 24,
      slotName: 'batch-priMatLv1',
    },
    {
      type: 'slot',
      key: 'priMatLv2',
      label: '主材质(二级)',
      span: 24,
      slotName: 'batch-priMatLv2',
    },
    {
      type: 'slot',
      key: 'secMatLv1',
      label: '次级材质(一级)',
      span: 24,
      slotName: 'batch-secMatLv1',
    },
    {
      type: 'slot',
      key: 'secMatLv2',
      label: '次级材质(二级)',
      span: 24,
      slotName: 'batch-secMatLv2',
    },
    {
      type: 'slot',
      key: 'electronicState',
      label: '带电',
      span: 24,
      slotName: 'batch-electronicState',
    },
    {
      type: 'slot',
      key: 'electronicCertification',
      label: '带电认证',
      span: 24,
      slotName: 'batch-electronicCertification',
    },
    {
      type: 'slot',
      key: 'materialDesc',
      label: '材质描述',
      span: 24,
      slotName: 'batch-materialDesc',
    },
    {
      type: 'slot',
      key: 'spec1',
      label: '规格1',
      span: 24,
      slotName: 'batch-spec1',
    },
    {
      type: 'slot',
      key: 'spec2',
      label: '规格2',
      span: 24,
      slotName: 'batch-spec2',
    },
    {
      type: 'slot',
      key: 'spec3',
      label: '规格3',
      span: 24,
      slotName: 'batch-spec3',
    },
    {
      type: 'slot',
      key: 'spec4',
      label: '规格4',
      span: 24,
      slotName: 'batch-spec4',
    },
    {
      type: 'slot',
      key: 'spec5',
      label: '规格5',
      span: 24,
      slotName: 'batch-spec5',
    },
    {
      type: 'slot',
      key: 'installDocCollectionState',
      label: '说明书采集结果',
      span: 24,
      slotName: 'batch-installDocCollectionState',
    },
    {
      type: 'slot',
      key: 'appendant',
      label: '附属品',
      span: 24,
      slotName: 'batch-appendant',
    },
    {
      type: 'slot',
      key: 'installDifficulty',
      label: '组装难易度',
      span: 24,
      slotName: 'batch-installDifficulty',
    },
    {
      type: 'slot',
      key: 'electricAccessory',
      label: '带电配件',
      span: 24,
      slotName: 'batch-electricAccessory',
    },
    {
      type: 'slot',
      key: 'battery',
      label: '电池',
      span: 24,
      slotName: 'batch-battery',
    },
    {
      type: 'slot',
      key: 'fragile',
      label: '是否有易碎部件',
      span: 24,
      slotName: 'batch-fragile',
    },
    {
      type: 'slot',
      key: 'marketPrice',
      label: '市场成本价',
      span: 24,
      slotName: 'batch-marketPrice',
      labelTips: '多国家商品价格，系统自动计算',
    },
  ],
  [
    { type: 'split', label: '采购信息', span: 24 },
    {
      type: 'slot',
      span: 24,
      slotName: 'purchaseInfo',
    },
    {
      type: 'slot',
      label: '包装方式',
      span: 24,
      slotName: 'packagesType',
    },
    {
      type: 'slot',
      key: 'packages',
      label: '包装尺寸',
      span: 24,
      slotName: 'packages',
    },
    {
      type: ComponentTagsEnum.InputNumber,
      key: 'grossWeight',
      label: '产品毛重(kg)',
      span: 24,
      props: { suffix: 'kg', min: 0, precision: 3, isEditing: !isDetailUrl.value },
    },
    {
      type: ComponentTagsEnum.InputNumber,
      key: 'purchasePrice',
      label: '采购单价(含税含运)',
      span: 24,
      required: true,
      props: { suffix: 'CNY', min: 0, precision: 2, isEditing: !isDetailUrl.value },
    },
    {
      type: ComponentTagsEnum.InputNumber,
      key: 'purchasePriceExFreight',
      label: '采购单价(含税不含运)',
      span: 24,
      props: { suffix: 'CNY', min: 0, precision: 2, isEditing: !isDetailUrl.value },
    },
    {
      type: ComponentTagsEnum.InputNumber,
      key: 'taxRate',
      label: '税点(%)',
      span: 24,
      props: { suffix: '%', min: 0, precision: 2, isEditing: !isDetailUrl.value },
    },
    {
      type: ComponentTagsEnum.InputNumber,
      key: 'platformPrice',
      label: '国内平台售价',
      span: 24,
      props: { suffix: 'CNY', min: 0, precision: 2, isEditing: !isDetailUrl.value },
    },
    {
      type: ComponentTagsEnum.Input,
      key: 'onlinePurchaseLink',
      label: '网采链接',
      span: 24,
      props: { placeholder: '请输入', isEditing: !isDetailUrl.value },
    },
    {
      type: ComponentTagsEnum.Input,
      key: 'supplierDeliveryCode',
      label: '供应商发货编码',
      span: 24,
      props: { placeholder: '请输入', isEditing: !isDetailUrl.value },
    },
    {
      type: ComponentTagsEnum.Input,
      key: 'purchaseRemark',
      label: '采购备注',
      span: 24,
      props: { type: 'textarea', rows: 3, placeholder: '请输入', isEditing: !isDetailUrl.value },
    },
  ],
  [
    { type: 'split', label: '物流信息', span: 24 },
    {
      type: 'slot',
      key: 'logistics',
      span: 24,
      slotName: 'logisticsSlot',
    },
  ],
])

const sizeComponents = reactive([
  {
    type: 'input',
    label: '长',
    key: 'length',
    span: 8,
    component: markRaw(CgInputNumber),
    props: { type: 'number', placeholder: '长', min: 0, clearable: false, precision: 0 },
  },
  {
    type: 'input',
    label: '宽',
    key: 'width',
    span: 8,
    component: markRaw(CgInputNumber),
    props: { type: 'number', placeholder: '宽', min: 0, clearable: false, precision: 0 },
  },
  {
    type: 'input',
    label: '高',
    key: 'height',
    span: 8,
    component: markRaw(CgInputNumber),
    props: { type: 'number', placeholder: '高', min: 0, clearable: false, precision: 0 },
  },
])

function addLogisticsEntry() {
  if (!editingSkuData.value.logistics)
    editingSkuData.value.logistics = []
  editingSkuData.value.logistics.push({ locale: undefined, transportMethod: undefined, logisticsChannel: undefined, oceanFreightEstimate: undefined, lastMileFreightEstimate: undefined, estimatedDeliveryDays: undefined })
}

const formData = inject('formData') as any

async function calculateFreight(index: number) {
  // 验证必填字段
  if (editingSkuData.value.grossWeight === undefined || editingSkuData.value.grossWeight === null) {
    ElMessage.error('请填写产品毛重')
    return
  }

  if (!formData?.shipTimeFrame) {
    ElMessage.error('请选择发货时效')
    return
  }

  const dimensionsWithUndefined = editingSkuData.value.packages?.map(p => ({
    length: isNil(p.length) ? undefined : p.length * 10,
    width: isNil(p.width) ? undefined : p.width * 10,
    height: isNil(p.height) ? undefined : p.height * 10,
  })) ?? []

  if (dimensionsWithUndefined.length === 0) {
    ElMessage.error('请添加包装尺寸信息')
    return
  }

  const hasIncompleteDimensions = dimensionsWithUndefined.some(dim =>
    isNil(dim.length) || isNil(dim.width) || isNil(dim.height),
  )
  if (hasIncompleteDimensions) {
    ElMessage.error('请完善包装尺寸的长宽高信息')
    return
  }

  const dimensions = dimensionsWithUndefined.map(dim => ({
    length: dim.length!,
    width: dim.width!,
    height: dim.height!,
  }))

  const item = editingSkuData.value.logistics![index]
  const request: FreightMatchRequest = {
    actualWeight: editingSkuData.value.grossWeight * 1000,
    dimensions,
    quantity: 1,
    timelinessId: formData?.shipTimeFrame,
  }

  try {
    const response = await freightApi.matchFreight({ freightMatchRequest: request })
    const data = response.data.data
    item.oceanFreightEstimate = data?.headFreight
    item.lastMileFreightEstimate = data?.tailFreight
    item.oceanFreightEstimateCurrency = data?.headCurrency
    item.lastMileFreightEstimateCurrency = data?.tailCurrency
    item.estimatedDeliveryDays = data?.totalTimeliness
    item.transportMethod = data?.shippingMethod
    item.logisticsChannel = data?.tailChannelCode
  }
  catch (error) {
    console.error('calculateFreight error:', error)
    ElMessage.error('运费计算失败')
  }
}

function removeLogisticsEntry(index: number) {
  if (editingSkuData.value.logistics && editingSkuData.value.logistics.length > index) {
    editingSkuData.value.logistics.splice(index, 1)
  }
}
const innerFormRefs = ref<Record<number, InstanceType<typeof CgForm>>>({})
function setInnerFormRef(el: any, index: number) {
  if (el) {
    innerFormRefs.value[index] = el
  }
}

async function handleConfirm() {
  let allInnerFormsValid = true
  const validationPromises = Object.values(innerFormRefs.value).map(formRef => formRef?.validate())
  try {
    await Promise.all(validationPromises)
  }
  catch (error) {
    allInnerFormsValid = false
    console.error('内部物流表单验证失败:', error)
    ElMessage.error('请检查物流信息的必填项')
  }
  if (allInnerFormsValid) {
    emit('confirm', { editedSkuData: editingSkuData.value, batchUpdate: { ...batchUpdateFlags } })
    localVisible.value = false
  }
}

function handleCancel() {
  localVisible.value = false
}

const marketPriceValue = reactive({
  country: '',
  price: '',
})

const priceList = ref<ProductSkuPriceVO[]>([])

async function loadSkuPrices() {
  if (!editingSkuData.value.skuId)
    return

  try {
    const response = await productApi.listProductSkuPrices({ skuId: editingSkuData.value.skuId })

    if (response.data?.data) {
      priceList.value = response.data.data

      // 如果有数据但没有选择国家，默认选择日本
      marketPriceValue.country = 'JP'
      updatePriceDisplay(marketPriceValue.country)
    }
  }
  catch (error) {
    console.error('Failed to load price data:', error)
  }
}

// 更新价格显示
function updatePriceDisplay(countryCode: string) {
  if (!countryCode || !priceList.value.length) {
    marketPriceValue.price = ''
    return
  }

  const currentPrice = priceList.value.find(item => item.country === countryCode)

  if (currentPrice && currentPrice.calculatedPrice !== undefined && currentPrice.targetCurrency) {
    marketPriceValue.price = `${currentPrice.calculatedPrice} ${currentPrice.targetCurrency}`
  }
  else {
    marketPriceValue.price = ''
  }
}

// 监听国家选择变化
watch(() => marketPriceValue.country, (newCountry) => {
  if (newCountry) {
    updatePriceDisplay(newCountry)
  }
})

// 当skuId变更时，重新加载价格
watch(() => editingSkuData.value.skuId, (newSkuId) => {
  if (newSkuId) {
    loadSkuPrices()
  }
  else {
    priceList.value = []
    marketPriceValue.price = ''
  }
}, { immediate: true })

const marketPriceComponents = computed(() => [
  {
    type: 'select',
    key: 'country',
    span: 10,
    component: markRaw(CgSelectV2),
    props: {
      'proxy-option': { request: productApi.listCountries },
      'props': { label: 'name', value: 'code' },
      'placeholder': '选择国家',
    },
  },
  {
    type: 'input',
    key: 'price',
    span: 14,
    component: markRaw(CgInput),
    props: {
      placeholder: '-',
      disabled: true,
    },
  },
])
</script>

<template>
  <ElDrawer
    :model-value="localVisible" direction="rtl" size="450" :before-close="handleCancel" :with-header="false"
    @update:model-value="(val: boolean) => localVisible = val"
  >
    <div v-loading="drawerLoading">
      <CgForm
        v-if="localVisible" v-model="editingSkuData" :options="drawerFormOptions" label-position="right"
        label-width="120px" :use-grid="true" :gutter="20"
      >
        <!-- 基础信息部分：批量修改字段对应的 slot 模板 -->
        <template #batch-skuNameEn>
          <div class="batch-field-wrapper flex items-center">
            <CgInput
              v-model="editingSkuData.skuNameEn!" placeholder="请输入" :disabled="isDetailUrl"
              :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.skuNameEn" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-priMatLv1>
          <div class="batch-field-wrapper flex items-center">
            <CgSelectV2
              v-model="editingSkuData.priMatLv1"
              :proxy-option="{ request: dictApi.listDicts, query: { codes: 'Primar-material-level-one' } }"
              :transform="transformDictData" placeholder="请选择" :disabled="isDetailUrl" :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.priMatLv1" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-priMatLv2>
          <div class="batch-field-wrapper flex items-center">
            <CgSelectV2
              v-model="editingSkuData.priMatLv2"
              :proxy-option="{ request: dictApi.listDicts, query: { codes: 'Primar-material-level-two' } }"
              :transform="transformDictData" placeholder="请选择" :disabled="isDetailUrl" :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.priMatLv2" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-secMatLv1>
          <div class="batch-field-wrapper flex items-center">
            <CgSelectV2
              v-model="editingSkuData.secMatLv1"
              :proxy-option="{ request: dictApi.listDicts, query: { codes: 'secondary-material-level-one' } }"
              :transform="transformDictData" placeholder="请选择" :disabled="isDetailUrl" :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.secMatLv1" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-secMatLv2>
          <div class="batch-field-wrapper flex items-center">
            <CgSelectV2
              v-model="editingSkuData.secMatLv2"
              :proxy-option="{ request: dictApi.listDicts, query: { codes: 'secondary-material-level-two' } }"
              :transform="transformDictData" placeholder="请选择" :disabled="isDetailUrl" :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.secMatLv2" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-electronicState>
          <div class="batch-field-wrapper flex items-center">
            <CgSelectV2
              v-model="editingSkuData.electronicState"
              :data="[{ label: '带电', value: true }, { label: '不带电', value: false }]" placeholder="请选择"
              :disabled="isDetailUrl" :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.electronicState" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-electronicCertification>
          <div class="batch-field-wrapper flex items-center">
            <CgSelectV2
              v-model="editingSkuData.electronicCertification"
              :proxy-option="{ request: dictApi.listDicts, query: { codes: 'electrified-certificate' } }"
              :transform="transformDictData" placeholder="请选择" :disabled="isDetailUrl" :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.electronicCertification" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-materialDesc>
          <div class="batch-field-wrapper flex items-center">
            <CgInput
              v-model="editingSkuData.materialDesc!" placeholder="请输入规格产品材质描述" :disabled="isDetailUrl"
              :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.materialDesc" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-spec1>
          <div class="batch-field-wrapper flex items-center">
            <CgInput
              v-model="editingSkuData.spec1!" placeholder="规格1（沙发例：单人位/双人位）" :disabled="isDetailUrl"
              :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.spec1" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-spec2>
          <div class="batch-field-wrapper flex items-center">
            <CgInput
              v-model="editingSkuData.spec2!" placeholder="规格2（沙发例：有扶手/无扶手）" :disabled="isDetailUrl"
              :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.spec2" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-spec3>
          <div class="batch-field-wrapper flex items-center">
            <CgInput
              v-model="editingSkuData.spec3!" placeholder="规格3（沙发例：有脚踏/无脚踏）" :disabled="isDetailUrl"
              :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.spec3" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-spec4>
          <div class="batch-field-wrapper flex items-center">
            <CgInput
              v-model="editingSkuData.spec4!" placeholder="规格4（沙发例：有靠背/无靠背）" :disabled="isDetailUrl"
              :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.spec4" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-spec5>
          <div class="batch-field-wrapper flex items-center">
            <CgInput
              v-model="editingSkuData.spec5!" placeholder="规格5（沙发例：有抱枕/无抱枕）" :disabled="isDetailUrl"
              :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.spec5" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-installDocCollectionState>
          <div class="batch-field-wrapper flex items-center">
            <CgSelectV2
              v-model="editingSkuData.installDocCollectionState"
              :proxy-option="{ request: dictApi.listDicts, query: { codes: 'install-doc-collection-state' } }"
              :transform="transformDictData" placeholder="请选择" :disabled="isDetailUrl" :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.installDocCollectionState" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-appendant>
          <div class="batch-field-wrapper flex items-center">
            <CgInput
              v-model="editingSkuData.appendant!" placeholder="请输入" :disabled="isDetailUrl"
              :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.appendant" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-installDifficulty>
          <div class="batch-field-wrapper flex items-center">
            <CgSelectV2
              v-model="editingSkuData.installDifficulty"
              :proxy-option="{ request: dictApi.listDicts, query: { codes: 'Assembly-difficulty' } }"
              :transform="transformDictData" placeholder="请选择" :disabled="isDetailUrl" :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.installDifficulty" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-electricAccessory>
          <div class="batch-field-wrapper flex items-center">
            <CgSelectV2
              v-model="editingSkuData.electricAccessory"
              :proxy-option="{ request: dictApi.listDicts, query: { codes: 'electrified-accessory' } }"
              :transform="transformDictData" placeholder="请选择" :disabled="isDetailUrl" :is-editing="!isDetailUrl"
              multiple
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.electricAccessory" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-battery>
          <div class="batch-field-wrapper flex items-center">
            <CgSelectV2
              v-model="editingSkuData.battery"
              :proxy-option="{ request: dictApi.listDicts, query: { codes: 'had-battery' } }"
              :transform="transformDictData" placeholder="请选择" :disabled="isDetailUrl" :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.battery" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-fragile>
          <div class="batch-field-wrapper flex items-center">
            <CgSelectV2
              v-model="editingSkuData.fragile"
              :proxy-option="{ request: dictApi.listDicts, query: { codes: 'had-fragile-components' } }"
              :transform="transformDictData" placeholder="请选择" :disabled="isDetailUrl" :is-editing="!isDetailUrl"
            />
            <ElCheckbox v-if="!isDetailUrl" v-model="batchUpdateFlags.fragile" class="ml-2">
              批量
            </ElCheckbox>
          </div>
        </template>
        <template #batch-marketPrice>
          <CgComplexInputV2
            :model-value="marketPriceValue"
            :components="marketPriceComponents"
          />
        </template>
        <!-- 其他已有的 slot，例如尺寸、产品标签、物流信息等 -->
        <template #specsDrawer>
          <SizeInput
            v-model="editingSkuData.specs!" :components="sizeComponents" append="cm"
            :is-editing="!isDetailUrl"
          />
        </template>
        <template #propertiesSlot>
          <PropertiesDialog v-model="editingSkuData.properties" :show-border="true" :is-editing="!isDetailUrl" />
        </template>
        <template #packages>
          <SizeInput
            v-model="editingSkuData.packages!" :components="sizeComponents" append="cm"
            :is-editing="!isDetailUrl"
          />
        </template>
        <template #purchaseInfo>
          <div class="ml-14px flex items-center gap-4">
            <div class="flex items-center gap-1">
              <font-awesome-icon :icon="['fas', 'store']" class="text-#399e96" />
              <span class="text-gray-500">供应商:</span>
              <span class="font-medium">{{ formData?.supplierName || '暂无' }}</span>
            </div>
            <div class="flex items-center gap-1">
              <font-awesome-icon :icon="['fas', 'clock']" class="text-#399e96" />
              <span class="text-gray-500">发货时效:</span>
              <span class="font-medium">{{ formData?.shipTimeFrameName || '暂无' }}</span>
            </div>
          </div>
        </template>
        <template #packagesType>
          <div class="flex flex-col gap-1">
            <div class="flex items-center gap-2">
              <div
                class="flex items-center justify-center border border-#399e96/20 rounded-md bg-#399e96/10 px-2 py-1 text-#399e96"
              >
                <font-awesome-icon :icon="['fas', 'box']" class="mr-2" />
                <span class="font-medium">{{ editingSkuData.packages?.length || 0 }}</span>
                <span class="ml-1">个包裹</span>
              </div>
            </div>
          </div>
        </template>
        <template #logisticsSlot>
          <div class="w-full">
            <div v-for="(logisticsItem, index) in editingSkuData.logistics" :key="`logistics-${index}`" class="mb-4">
              <CgForm
                :ref="(el: any) => setInnerFormRef(el, index)" v-model="editingSkuData.logistics![index]"
                :options="logisticsFormOptions" label-position="right" label-width="120px" :use-grid="true"
                :gutter="15"
              >
                <template #logisticsSlot>
                  <div class="mb-2 ml-14px w-full flex items-center justify-between">
                    <div class="flex flex-1 items-center gap-2">
                      <span class="whitespace-nowrap text-gray-600">国家/地区：</span>
                      <CgSelectV2
                        v-model="logisticsItem.locale" :proxy-option="{ request: productApi.listCountries }"
                        :props="{ label: 'name', value: 'code' }" class="w-30!" :is-editing="!isDetailUrl"
                      />
                    </div>
                    <CgButton
                      v-if="!isDetailUrl && index > 0" type="danger" text bg size="small" class="ml-2 flex-shrink-0"
                      @click="removeLogisticsEntry(index)"
                    >
                      <font-awesome-icon :icon="['fas', 'trash-alt']" />
                      <span class="ml-1">删除</span>
                    </CgButton>
                    <CgButton
                      v-if="!isDetailUrl && index === 0" type="primary" text bg size="small" class="ml-2 flex-shrink-0"
                      @click="calculateFreight(index)"
                    >
                      计算运费
                    </CgButton>
                  </div>
                </template>
              </CgForm>
            </div>
            <div v-if="!isDetailUrl" class="mt-2">
              <CgButton type="primary" plain @click="addLogisticsEntry">
                <font-awesome-icon :icon="['fas', 'plus']" class="mr-1" /> 添加物流配置
              </CgButton>
            </div>
          </div>
        </template>
      </CgForm>
    </div>
    <template #footer>
      <div style="flex: auto">
        <CgButton @click="handleCancel">
          关闭
        </CgButton>
        <CgButton v-show="!isDetailUrl" type="primary" @click="handleConfirm">
          保存
        </CgButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style lang="scss" scoped>
:deep(.el-drawer__body) {
  padding: 0;
}

.cg-select-v2 {
  width: 100%;
}

:deep(.form-grid-split__title) {
  font-size: 14px;
}

:deep(.el-form-item) {
  margin-bottom: 18px !important;
}

.batch-field-wrapper {
  width: 100%;
}
</style>
