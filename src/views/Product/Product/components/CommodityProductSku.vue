<script setup lang="ts">
import type { GridInstance, GridProps } from '@/components/CgGrid'
import type { VxeGridPropTypes } from 'vxe-table'
import CgInputNumber from '@/components/CgElementUI/CgInputNumber'
import { GridCellRenderName } from '@/components/CgGrid'
import PropertiesDialog from './PropertiesDialog.vue'

defineOptions({
  name: 'CommodityProductSku',
})

const props = defineProps<{
  modelValue?: any[]
}>()

const modelValue = useVModel(props, 'modelValue')
const gridRef = ref<GridInstance | null>(null)
const columns: VxeGridPropTypes.Columns = [
  {
    field: 'skuCode',
    title: 'SKU',
    minWidth: 120,
  },
  {
    field: 'thumbnail',
    title: '图片',
    width: 60,
    cellRender: {
      name: GridCellRenderName.ImageUrl,
    },
  },
  {
    field: 'status',
    title: '状态',
    width: 80,
    cellRender: {
      name: GridCellRenderName.Status,
      props: {
        options: [
          {
            value: 0,
            label: '草稿',
            class: 'text-gray-400',
          },
          {
            value: 1,
            label: '审核中',
            class: 'text-orange-500',
          },
          {
            value: 2,
            label: '已上架',
            class: 'text-green-500',
          },
          {
            value: 3,
            label: '已下架',
            class: 'text-gray-500',
          },
          {
            value: 4,
            label: '冻结',
            class: 'text-gray-500',
          },
          {
            value: 5,
            label: '已删除',
            class: 'text-red-500',
          },
        ],
      },
    },
  },
  {
    field: 'properties',
    title: '产品标签',
    minWidth: 300,
    slots: {
      default: 'propertiesTemp',
    },
  },
  // {
  //   field: 'priMatLv1',
  //   title: '主材质(一级)',
  //   width: 100,
  //   slots: {
  //     default: 'priMatLv1Temp',
  //   },
  // },
  // {
  //   field: 'priMatLv2',
  //   title: '主材质(二级)',
  //   width: 100,
  //   slots: {
  //     default: 'priMatLv2Temp',
  //   },
  // },
  // {
  //   field: 'secMatLv1',
  //   title: '次材质(一级)',
  //   width: 100,
  //   slots: {
  //     default: 'secMatLv1Temp',
  //   },
  // },
  // {
  //   field: 'secMatLv2',
  //   title: '次材质(二级)',
  //   width: 100,
  //   slots: {
  //     default: 'secMatLv2Temp',
  //   },
  // },
  // {
  //   field: 'color',
  //   title: '颜色',
  //   width: 100,
  //   slots: {
  //     default: 'colorTemp',
  //   },
  // },
  {
    field: 'supplyPrice',
    title: '成本价(JPY)',
    minWidth: 120,
    slots: {
      default: 'supplyPriceTemp',
    },
  },
  {
    field: 'installationFee',
    title: '安装费用(JPY)',
    minWidth: 120,
    slots: {
      default: 'installationFeeTemp',
    },
  },
  {
    field: 'coefficient',
    title: '系数',
    minWidth: 100,
    slots: {
      default: 'coefficientTemp',
    },
  },
  {
    field: 'marketPrice',
    title: '市场售价(JPY)',
    minWidth: 120,
    slots: {
      default: 'marketPriceTemp',
    },
  },
  {
    field: 'discountRate',
    title: '划线折扣',
    minWidth: 100,
    slots: {
      default: 'discountRateTemp',
    },
  },
  {
    field: 'promoPrice',
    title: '划线价(JPY)',
    minWidth: 120,
    slots: {
      default: 'promoPriceTemp',
    },
  },
]
const gridOption = reactive<GridProps>({
  options: {
    columns,
    border: false,
    height: '400px',
    round: true,
    showOverflow: false,
  },
  paginationVisible: false,
})

function getSkuData() {
  const gridData = gridRef.value?.vxeGridRef?.getData() || []

  return gridData.map((row: any, index: number) => {
    if (!row.skuId && modelValue.value?.[index]?.skuId) {
      // 确保 properties 字段存在，如果不存在则初始化为空数组
      return {
        ...row,
        properties: modelValue.value?.[index]?.properties || [],
        skuId: modelValue.value?.[index]?.skuId,
      }
    }
    // 确保返回的每一行都有 properties 字段
    if (row.properties === undefined) {
      return { ...row, properties: [] }
    }
    return row
  })
}

// 暴露方法给父组件
defineExpose({
  gridRef,
  getSkuData,
})
</script>

<template>
  <div class="h-full">
    <CgGrid ref="gridRef" v-bind="gridOption" :data="modelValue">
      <template #propertiesTemp="{ row }">
        <PropertiesDialog v-model="row.properties" language="jp" :show-border="true" />
      </template>

      <template #priMatLv1Temp="{ row }">
        <CgInput v-model="row.priMatLv1" placeholder="请输入" />
      </template>

      <template #priMatLv2Temp="{ row }">
        <CgInput v-model="row.priMatLv2" placeholder="请输入" />
      </template>

      <template #secMatLv1Temp="{ row }">
        <CgInput v-model="row.secMatLv1" placeholder="请输入" />
      </template>

      <template #secMatLv2Temp="{ row }">
        <CgInput v-model="row.secMatLv2" placeholder="请输入" />
      </template>

      <template #colorTemp="{ row }">
        <CgInput v-model="row.color" placeholder="请输入" />
      </template>

      <template #supplyPriceTemp="{ row }">
        <CgInputNumber
          :model-value="row.supplyPrice"
          type="number"
          :is-editing="false"
          format-type="currency"
          :format-options="{ digits: 0 }"
        />
      </template>

      <template #installationFeeTemp="{ row }">
        <CgInputNumber v-model="row.installationFee" type="number" />
      </template>

      <template #coefficientTemp="{ row }">
        <CgInputNumber
          :model-value="row.coefficient" type="number" :precision="2" :step="0.01" :min="0"
          :is-editing="false"
        />
      </template>

      <template #marketPriceTemp="{ row }">
        <CgInputNumber
          :model-value="row.marketPrice"
          type="number"
          :is-editing="false"
          format-type="currency"
          :format-options="{ digits: 0 }"
        />
      </template>

      <template #discountRateTemp="{ row }">
        <CgInputNumber
          :model-value="row.discountRate" type="number" suffix="%" :precision="1" :step="0.1" :min="0"
          :max="100" clearable :is-editing="false"
        />
      </template>

      <template #promoPriceTemp="{ row }">
        <CgInputNumber
          :model-value="row.promoPrice"
          type="number"
          :is-editing="false"
          format-type="currency"
          :format-options="{ digits: 0 }"
        />
      </template>
    </CgGrid>
  </div>
</template>
