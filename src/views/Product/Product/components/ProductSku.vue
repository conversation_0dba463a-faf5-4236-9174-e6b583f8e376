<script setup lang="ts">
import type { ProductSkuUpdateRequest } from '@/apiv2/product'
import type { GridInstance, GridProps } from '@/components/CgGrid'
import type { VxeGridPropTypes, VxeTableEvents } from 'vxe-table'
import { dictApi } from '@/api.services/product.service'
import CgInput from '@/components/CgElementUI/CgInput'
import CgInputNumber from '@/components/CgElementUI/CgInputNumber'
import CgSelectV2 from '@/components/CgElementUI/CgSelectV2'
import { transformDictData } from '@/components/utils/shared'
import required from '@/directives/required'
import router from '@/router'
import { withDirectives } from 'vue'
import BatchEditPopover from './BatchEditPopover.vue'
import IndexTemp from './IndexTemp.vue'
import PropertiesDialog from './PropertiesDialog.vue'
import SizeInput from './SizeInput.vue'
import SkuEditDrawer from './SkuEditDrawer.vue'

defineOptions({
  name: 'ProductSku',
})

const props = defineProps<{
  modelValue?: ProductSkuUpdateRequest[]
  detail?: boolean
  create?: boolean
  edit?: boolean
}>()

const parentFormData = inject<any>('formData')
const modelValue = useVModel(props, 'modelValue')
const gridRef = ref<GridInstance | null>(null)
const skuList = ref<Partial<ProductSkuUpdateRequest>[]>([])

const isDrawerOpen = ref(false)
const selectedSkuForEdit = ref<Partial<ProductSkuUpdateRequest> | null>(null)
const selectedSkuIndex = ref(-1)

function openSkuDrawer(row: Partial<ProductSkuUpdateRequest>, index: number) {
  if (!row)
    return
  selectedSkuForEdit.value = JSON.parse(JSON.stringify(row))
  selectedSkuIndex.value = index
  isDrawerOpen.value = true
}

const handleRowClick: VxeTableEvents.CellClick<Partial<ProductSkuUpdateRequest>> = ({ row, column, $rowIndex }) => {
  if (column.slots?.default === 'actionsTemp') {
    return
  }

  if ($rowIndex !== undefined) {
    openSkuDrawer(row, $rowIndex)
  }
}

function handleDrawerConfirm(payload: { editedSkuData: Partial<ProductSkuUpdateRequest>, batchUpdate: Record<string, boolean> }) {
  const { editedSkuData: editedData, batchUpdate } = payload

  // 先更新当前选中行的数据
  if (selectedSkuIndex.value > -1 && skuList.value[selectedSkuIndex.value]) {
    Object.assign(skuList.value[selectedSkuIndex.value], editedData)
    gridRef.value?.vxeGridRef?.reloadRow(skuList.value[selectedSkuIndex.value])
  }
  else {
    console.error('无法找到要更新的 SKU 数据')
  }

  (Object.keys(batchUpdate) as Array<keyof ProductSkuUpdateRequest>).forEach((field) => {
    if (batchUpdate[field]) {
      skuList.value.forEach((item) => {
        if (editedData[field] !== undefined) {
          item[field] = editedData[field] as any
        }
      })
    }
  })

  gridRef.value?.vxeGridRef?.reloadData(skuList.value)
  resetDrawerState()
}

function handleDrawerCancel() {
  resetDrawerState()
}

function resetDrawerState() {
  selectedSkuForEdit.value = null
  selectedSkuIndex.value = -1
  isDrawerOpen.value = false
}

function handleBatchUpdate(columnKey: string, value: any) {
  if (!skuList.value || skuList.value.length === 0) {
    ElMessage.warning('没有 SKU 数据可供批量修改')
    return
  }
  skuList.value.forEach((sku) => {
    // SizeInput 的值是数组，需要直接赋值
    if (columnKey === 'specs' || columnKey === 'packages') {
      // 检查 value 是否是有效的 SizeInput 结构（数组），避免赋错值
      if (Array.isArray(value)) {
        (sku as any)[columnKey] = JSON.parse(JSON.stringify(value)) // 深拷贝赋值
      }
      else {
        console.warn(`Invalid value type for ${columnKey}:`, value)
      }
    }
    // 其他类型直接赋值
    else {
      (sku as any)[columnKey] = value
    }
  })
  gridRef.value?.vxeGridRef?.reloadData(skuList.value)
}

const columns: VxeGridPropTypes.Columns = [
  {
    field: 'id',
    title: '序号',
    type: 'seq',
    width: 60,
    align: 'center',
    headerAlign: 'center',
    slots: {
      footer: 'IndexFooter',
      default: 'actionsTemp',
    },
  },
  {
    field: 'skuCode',
    title: 'SKU',
    minWidth: 120,
    slots: {
      default: 'skuTemp',
    },
  },
  {
    field: 'skuName',
    title: '品名(中文)',
    minWidth: 150,
    slots: {
      default: 'nameTemp',
      header: 'skuNameHeader',
    },
  },
  {
    field: 'skuNameEn',
    title: '品名(英文)',
    minWidth: 150,
    slots: {
      default: 'nameEnTemp',
      header: 'skuNameEnHeader',
    },
    visible: !!props.create,
  },
  {
    field: 'properties',
    title: '产品标签',
    minWidth: 300,
    slots: {
      default: 'productTagsTemp',
    },
  },
  {
    field: 'color',
    title: '颜色',
    minWidth: 120,
    slots: {
      default: 'colorTemp',
      header: 'colorHeader',
    },
  },
  {
    field: 'specs',
    title: '尺寸(cm)',
    minWidth: 240,
    slots: {
      default: 'specsTemp',
      header: 'specsHeader',
    },
  },
  {
    field: 'netWeight',
    title: '净重(kg)',
    minWidth: 120,
    slots: {
      default: 'netWeightTemp',
      header: 'netWeightHeader',
    },
  },
  {
    field: 'priMatLv1',
    title: '主材质(一级)',
    minWidth: 140,
    slots: {
      default: 'primaryMaterialTemp',
      // 移除 header 函数
      // header: () => {
      //   return withDirectives(h('span', {}, '主材质(一级)'), [
      //     [required],
      //   ])
      // },
      header: 'priMatLv1Header',
    },
  },
  {
    field: 'priMatLv2',
    title: '主材质(二级)',
    minWidth: 140,
    slots: {
      default: 'secondaryPrimaryMaterialTemp',
      header: 'priMatLv2Header',
    },
  },
  {
    field: 'secMatLv1',
    title: '次级材质(一级)',
    minWidth: 140,
    slots: {
      default: 'secondaryMaterialTemp',
      header: 'secMatLv1Header',
    },
  },
  {
    field: 'secMatLv2',
    title: '次级材质(二级)',
    minWidth: 140,
    slots: {
      default: 'secondarySecondaryMaterialTemp',
      header: 'secMatLv2Header',
    },
  },
  {
    field: 'electronicState',
    title: '带电',
    minWidth: 120,
    slots: {
      default: 'electronicStateTemp',
      // 移除 header 函数
      // header: () => {
      //   return withDirectives(h('span', {}, '带电'), [
      //     [required],
      //   ])
      // },
      header: 'electronicStateHeader', // 新增 header slot
    },
  },

  {
    field: 'packages',
    title: '包装尺寸',
    minWidth: 240,
    slots: {
      default: 'packageSizeTemp',
      header: 'packagesHeader',
    },
  },
  {
    field: 'grossWeight',
    title: '产品毛重',
    minWidth: 120,
    slots: {
      default: 'grossWeightTemp',
      header: 'grossWeightHeader',
    },
  },
  {
    field: 'purchasePrice',
    title: '采购单价(含税含运)',
    minWidth: 180,
    slots: {
      default: 'purchasePriceTemp',
      header: 'purchasePriceHeader',
    },
  },
  {
    field: 'purchasePriceExFreight',
    title: '采购单价(含税不含运)',
    minWidth: 178,
    slots: {
      default: 'purchasePriceExFreightTemp',
      header: 'purchasePriceExFreightHeader',
    },
  },
  {
    field: 'taxRate',
    title: '税点',
    minWidth: 100,
    slots: {
      default: 'taxRateTemp',
      header: 'taxRateHeader',
    },
  },
  {
    field: 'platformPrice',
    title: '国内平台售价',
    minWidth: 128,
    slots: {
      default: 'platformPriceTemp',
      header: 'platformPriceHeader',
    },
  },
  {
    field: 'purchaseRemark',
    title: '采购备注',
    minWidth: 170,
    slots: {
      default: 'purchaseRemarkTemp',
      header: 'purchaseRemarkHeader',
    },
  },
]

const gridOption = computed<GridProps>(() => ({
  options: {
    columns,
    border: false,
    height: 'auto',
    round: true,
    showFooter: props.create || props.edit,
    showFooterOverflow: true,
    footerMethod: ({ columns }) => {
      return [
        columns.map(() => null),
      ]
    },
    showOverflow: false,
  },
  paginationVisible: false,
}))

function addNewRow() {
  // 从父组件的 formData 中获取 SPU 维度的产品名称和产品标签
  const spuProductName = parentFormData?.productName
  const spuProperties = parentFormData?.properties || []

  skuList.value.push({
    // 基本信息
    skuCode: '',
    // 默认使用 SPU 维度的产品名称
    skuName: spuProductName || undefined,
    skuNameEn: undefined,
    skuStatus: undefined,

    // 产品属性
    // 默认使用 SPU 维度的产品标签
    properties: [...spuProperties],
    color: undefined,
    specs: [{ length: undefined, width: undefined, height: undefined }], // 初始化 specs
    netWeight: undefined,

    // 材质信息
    priMatLv1: undefined,
    priMatLv2: undefined,
    secMatLv1: undefined,
    secMatLv2: undefined,
    electronicState: undefined,

    // 包装信息
    packages: [{ length: undefined, width: undefined, height: undefined }] as any[], // 初始化 packages
    grossWeight: undefined,

    // 价格信息
    purchasePrice: undefined,
    purchasePriceExFreight: undefined,
    taxRate: undefined,
    platformPrice: undefined,

    // 备注
    purchaseRemark: undefined,
  })
}

/**
 * 处理尺寸数组，过滤空项并处理边界情况
 */
function processDimensionArray(arr: any[] | undefined) {
  if (!Array.isArray(arr) || arr.length === 0)
    return undefined

  const filtered = arr.filter(item =>
    item && (item.length !== undefined || item.width !== undefined || item.height !== undefined),
  )

  return filtered.length > 0 ? filtered : undefined
}

/**
 * 获取当前的SKU数据
 * @returns 当前的SKU数据列表
 */
function getSkuData() {
  const skuData = gridRef.value?.vxeGridRef?.getData() || []

  return skuData.map((sku: any) => {
    const processedSku = { ...sku }

    // 处理尺寸和包装数组
    processedSku.specs = processDimensionArray(processedSku.specs)
    processedSku.packages = processDimensionArray(processedSku.packages)

    return processedSku
  })
}

// 暴露方法给父组件
defineExpose({
  gridRef,
  getSkuData,
})

watch(() => modelValue.value, (newValue) => {
  skuList.value = (newValue || []).map(sku => ({
    ...sku,
    // 确保 specs 和 packages 总是数组，并且至少有一个空对象用于 SizeInput
    specs: (Array.isArray(sku.specs) && sku.specs.length > 0) ? sku.specs : [{ length: undefined, width: undefined, height: undefined } as any],
    packages: (Array.isArray(sku.packages) && sku.packages.length > 0) ? sku.packages : [{ length: undefined, width: undefined, height: undefined } as any],
  }))
}, { immediate: true, deep: true })

const sizeComponents = reactive([
  {
    type: 'input',
    label: '长',
    key: 'length',
    span: 8,
    component: markRaw(CgInputNumber),
    props: {
      type: 'number',
      placeholder: '长',
      min: 0,
      clearable: false,
      precision: 0,
    },
  },
  {
    type: 'input',
    label: '宽',
    key: 'width',
    span: 8,
    component: markRaw(CgInputNumber),
    props: {
      type: 'number',
      placeholder: '宽',
      min: 0,
      clearable: false,
      precision: 0,
    },
  },
  {
    type: 'input',
    label: '高',
    key: 'height',
    span: 8,
    component: markRaw(CgInputNumber),
    props: {
      type: 'number',
      placeholder: '高',
      min: 0,
      clearable: false,
      precision: 0,
    },
  },
])

const skuInputHolder = computed(() => {
  if (props.detail)
    return ''
  else if (router.currentRoute.value.name === 'ProductCreate')
    return '系统自动生成'
  else
    return ''
})
const skuInputReadOnly = computed(() => {
  if (props.detail)
    return true
  else if (router.currentRoute.value.name === 'ProductCreate')
    return true
  else
    return false
})

function handleDeleteRow(row: ProductSkuUpdateRequest, index: number) {
  // 使用数组索引直接删除指定行
  if (index >= 0 && index < skuList.value.length) {
    skuList.value.splice(index, 1)
    // 可能需要通知 VxeTable 数据已更改
    // gridRef.value?.vxeGridRef?.loadData(skuList.value) // 或者 reloadData
  }
}

// 提供给 BatchEditPopover 的 SelectV2 的通用请求和转换
function dictRequest(codes: string) {
  return {
    request: dictApi.listDicts,
    query: { codes },
  }
}
const dictTransform = transformDictData
</script>

<template>
  <div class="product-sku-grid h-full">
    <CgGrid ref="gridRef" v-bind="gridOption" :data="skuList" @cell-click="handleRowClick">
      <template #IndexFooter>
        <font-awesome-icon
          v-if="create || edit" :icon="['fas', 'square-plus']" size="2x"
          class="ml-2 cursor-pointer text-gray-500 hover:text-gray-700" @click="addNewRow"
        />
      </template>

      <template #actionsTemp="{ row, $rowIndex }">
        <IndexTemp :row-data="row" :seq="$rowIndex + 1" @delete-row="handleDeleteRow(row, $rowIndex)" />
      </template>

      <!-- SKU 列 -->
      <template #skuTemp="{ row }">
        <CgInput
          v-model="row.skuCode" :is-editing="create" :placeholder="skuInputHolder" :readonly="skuInputReadOnly"
          :disabled="skuInputReadOnly"
        />
      </template>
      <!-- 品名(中文) Header -->
      <template #skuNameHeader="{ column }">
        <span class="required-mark">*</span>
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="skuName" component-type="Input"
          :component-props="{ placeholder: '输入品名' }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 品名(中文) Cell -->
      <template #nameTemp="{ row }">
        <CgInput v-model="row.skuName" :is-editing="create" placeholder="请输入品名" />
      </template>

      <!-- 品名(英文) Header -->
      <template #skuNameEnHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="skuNameEn" component-type="Input"
          @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 品名(英文) Cell -->
      <template #nameEnTemp="{ row }">
        <CgInput v-model="row.skuNameEn" :is-editing="create" />
      </template>

      <!-- 产品标签 -->
      <template #productTagsTemp="{ row }">
        <PropertiesDialog v-model="row.properties" :show-border="true" :is-editing="create" />
      </template>

      <!-- 颜色 Header -->
      <template #colorHeader="{ column }">
        <span class="required-mark">*</span>
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="color" component-type="SelectV2" :component-props="{
            proxyOption: dictRequest('color'),
            transform: dictTransform,
            filterable: true,
            placeholder: '选择颜色',
          }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 颜色 Cell -->
      <template #colorTemp="{ row }">
        <CgSelectV2
          v-model="row.color" :proxy-option="dictRequest('color')" :transform="dictTransform"
          :filterable="true" :placeholder="create ? '' : '请选择颜色'" :is-editing="create"
        />
      </template>

      <!-- 尺寸 Header -->
      <template #specsHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="specs" component-type="SizeInput"
          :component-props="{ components: sizeComponents, append: 'cm' }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 尺寸 Cell -->
      <template #specsTemp="{ row }">
        <SizeInput v-model="row.specs" :components="sizeComponents" append="cm" :is-editing="create" />
      </template>

      <!-- 净重 Header -->
      <template #netWeightHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="netWeight" component-type="InputNumber"
          :component-props="{ suffix: 'kg', min: 0, precision: 3 }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 净重 Cell -->
      <template #netWeightTemp="{ row }">
        <CgInputNumber
          v-model="row.netWeight" type="number" suffix="kg" :is-editing="create" :min="0"
          :precision="3"
        />
      </template>

      <!-- 主材质(一级) Header -->
      <template #priMatLv1Header="{ column }">
        <span class="required-mark">*</span>
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="priMatLv1" component-type="SelectV2" :component-props="{
            proxyOption: dictRequest('Primar-material-level-one'),
            transform: dictTransform,
            filterable: false,
            placeholder: '选择材质',
          }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 主材质(一级) Cell -->
      <template #primaryMaterialTemp="{ row }">
        <CgSelectV2
          v-model="row.priMatLv1" :proxy-option="dictRequest('Primar-material-level-one')"
          :transform="dictTransform" :filterable="false" placeholder="请选择" :is-editing="create"
        />
      </template>

      <!-- 主材质(二级) Header -->
      <template #priMatLv2Header="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="priMatLv2" component-type="SelectV2" :component-props="{
            proxyOption: dictRequest('Primar-material-level-two'),
            transform: dictTransform,
            filterable: false,
            placeholder: '选择材质',
          }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 主材质(二级) Cell -->
      <template #secondaryPrimaryMaterialTemp="{ row }">
        <CgSelectV2
          v-model="row.priMatLv2" :proxy-option="dictRequest('Primar-material-level-two')"
          :transform="dictTransform" :filterable="false" placeholder="请选择" :is-editing="create"
        />
      </template>

      <!-- 次级材质(一级) Header -->
      <template #secMatLv1Header="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="secMatLv1" component-type="SelectV2" :component-props="{
            proxyOption: dictRequest('secondary-material-level-one'),
            transform: dictTransform,
            filterable: false,
            placeholder: '选择材质',
          }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 次级材质(一级) Cell -->
      <template #secondaryMaterialTemp="{ row }">
        <CgSelectV2
          v-model="row.secMatLv1" :proxy-option="dictRequest('secondary-material-level-one')"
          :transform="dictTransform" :filterable="false" placeholder="请选择" :is-editing="create"
        />
      </template>

      <!-- 次级材质(二级) Header -->
      <template #secMatLv2Header="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="secMatLv2" component-type="SelectV2" :component-props="{
            proxyOption: dictRequest('secondary-material-level-two'),
            transform: dictTransform,
            filterable: false,
            placeholder: '选择材质',
          }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 次级材质(二级) Cell -->
      <template #secondarySecondaryMaterialTemp="{ row }">
        <CgSelectV2
          v-model="row.secMatLv2" :proxy-option="dictRequest('secondary-material-level-two')"
          :transform="dictTransform" :filterable="false" placeholder="请选择" :is-editing="create"
        />
      </template>

      <!-- 带电 Header -->
      <template #electronicStateHeader="{ column }">
        <span class="required-mark">*</span>
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="electronicState" component-type="SelectV2" :component-props="{
            data: [{ label: '带电', value: true }, { label: '不带电', value: false }],
            placeholder: '选择状态',
          }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 带电 Cell -->
      <template #electronicStateTemp="{ row }">
        <CgSelectV2
          v-model="row.electronicState" :data="[{ label: '带电', value: true }, { label: '不带电', value: false }]"
          :is-editing="create"
        />
      </template>

      <!-- 带电认证 Header -->
      <template #electronicCertificationHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="electronicCertification" component-type="SelectV2"
          :component-props="{
            proxyOption: dictRequest('electrified-certificate'),
            transform: dictTransform,
            filterable: false,
            placeholder: '选择认证',
          }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 带电认证 Cell -->
      <template #electronicCertificationTemp="{ row }">
        <CgSelectV2
          v-model="row.electronicCertification" :proxy-option="dictRequest('electrified-certificate')"
          :transform="dictTransform" :filterable="false" placeholder="请选择" :is-editing="create"
        />
      </template>

      <!-- 附属品 Header -->
      <template #appendantHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="appendant" component-type="Input"
          :component-props="{ placeholder: '输入附属品' }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 附属品 Cell -->
      <template #appendantTemp="{ row }">
        <CgInput v-model="row.appendant" :is-editing="create" />
      </template>

      <!-- 组装难易度 Header -->
      <template #installDifficultyHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="installDifficulty" component-type="SelectV2" :component-props="{
            proxyOption: dictRequest('Assembly-difficulty'),
            transform: dictTransform,
            filterable: false,
            placeholder: '选择难易度',
          }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 组装难易度 Cell -->
      <template #installDifficultyTemp="{ row }">
        <CgSelectV2
          v-model="row.installDifficulty" :proxy-option="dictRequest('Assembly-difficulty')"
          :transform="dictTransform" :filterable="false" placeholder="请选择" :is-editing="create"
        />
      </template>

      <!-- 带电配件 Header -->
      <template #electricAccessoryHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="electricAccessory" component-type="SelectV2" :component-props="{
            proxyOption: dictRequest('electrified-accessory'),
            transform: dictTransform,
            multiple: true,
            filterable: false,
            placeholder: '选择配件',
          }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 带电配件 Cell -->
      <template #electricAccessoryTemp="{ row }">
        <CgSelectV2
          v-model="row.electricAccessory" :proxy-option="dictRequest('electrified-accessory')"
          :transform="dictTransform" multiple :filterable="false" placeholder="请选择" :is-editing="create"
        />
      </template>

      <!-- 电池 Header -->
      <template #batteryHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="battery" component-type="SelectV2" :component-props="{
            proxyOption: dictRequest('had-battery'),
            transform: dictTransform,
            filterable: false,
            placeholder: '选择电池状态',
          }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 电池 Cell -->
      <template #batteryTemp="{ row }">
        <CgSelectV2
          v-model="row.battery" :proxy-option="dictRequest('had-battery')" :transform="dictTransform"
          :filterable="false" placeholder="请选择" :is-editing="create"
        />
      </template>

      <!-- 易碎部件 Header -->
      <template #fragileHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="fragile" component-type="SelectV2" :component-props="{
            proxyOption: dictRequest('had-fragile-components'),
            transform: dictTransform,
            filterable: false,
            placeholder: '选择是否易碎',
          }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 易碎部件 Cell -->
      <template #fragileTemp="{ row }">
        <CgSelectV2
          v-model="row.fragile" :proxy-option="dictRequest('had-fragile-components')"
          :transform="dictTransform" :filterable="false" placeholder="请选择" :is-editing="create"
        />
      </template>

      <!-- 包装尺寸 Header -->
      <template #packagesHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="packages" component-type="SizeInput"
          :component-props="{ components: sizeComponents, append: 'cm' }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 包装尺寸 Cell -->
      <template #packageSizeTemp="{ row }">
        <SizeInput v-model="row.packages" :components="sizeComponents" append="cm" :is-editing="create" />
      </template>

      <!-- 产品毛重 Header -->
      <template #grossWeightHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="grossWeight" component-type="InputNumber"
          :component-props="{ suffix: 'kg', min: 0, precision: 3 }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 产品毛重 Cell -->
      <template #grossWeightTemp="{ row }">
        <CgInputNumber
          v-model="row.grossWeight" type="number" suffix="kg" :is-editing="create" :min="0"
          :precision="3"
        />
      </template>

      <!-- 采购单价(含税含运) Header -->
      <template #purchasePriceHeader="{ column }">
        <span class="required-mark">*</span>
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="purchasePrice" component-type="InputNumber"
          :component-props="{ suffix: 'CNY', min: 0, precision: 2 }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 采购单价(含税含运) Cell -->
      <template #purchasePriceTemp="{ row }">
        <CgInputNumber
          v-model="row.purchasePrice" type="number" suffix="CNY" :is-editing="create" :min="0"
          :precision="2"
        />
      </template>

      <!-- 采购单价(含税不含运) Header -->
      <template #purchasePriceExFreightHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="purchasePriceExFreight" component-type="InputNumber"
          :component-props="{ suffix: 'CNY', min: 0, precision: 2 }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 采购单价(含税不含运) Cell -->
      <template #purchasePriceExFreightTemp="{ row }">
        <CgInputNumber
          v-model="row.purchasePriceExFreight" type="number" suffix="CNY" :is-editing="create" :min="0"
          :precision="2"
        />
      </template>

      <!-- 税点 Header -->
      <template #taxRateHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="taxRate" component-type="InputNumber"
          :component-props="{ suffix: '%', min: 0, precision: 2 }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 税点 Cell -->
      <template #taxRateTemp="{ row }">
        <CgInputNumber v-model="row.taxRate" type="number" suffix="%" :is-editing="create" :min="0" :precision="2" />
      </template>

      <!-- 国内平台售价 Header -->
      <template #platformPriceHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="platformPrice" component-type="InputNumber"
          :component-props="{ suffix: 'CNY', min: 0, precision: 2 }" @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 国内平台售价 Cell -->
      <template #platformPriceTemp="{ row }">
        <CgInputNumber
          v-model="row.platformPrice" type="number" suffix="CNY" :is-editing="create" :min="0"
          :precision="2"
        />
      </template>

      <!-- 采购备注 Header -->
      <template #purchaseRemarkHeader="{ column }">
        <span>{{ column.title }}</span>
        <BatchEditPopover
          v-if="create" column-key="purchaseRemark" component-type="Input"
          :component-props="{ type: 'textarea', rows: 3, placeholder: '输入备注' }"
          @confirm-batch-update="handleBatchUpdate"
        />
      </template>
      <!-- 采购备注 Cell -->
      <template #purchaseRemarkTemp="{ row }">
        <CgInput v-model="row.purchaseRemark" :is-editing="create" />
      </template>
    </CgGrid>
    <!-- 抽屉组件 -->
    <SkuEditDrawer
      v-if="isDrawerOpen && selectedSkuForEdit && !create" v-model="isDrawerOpen"
      :sku-data="selectedSkuForEdit" @confirm="handleDrawerConfirm" @cancel="handleDrawerCancel"
    />
  </div>
</template>

<style scoped>
/* 添加必填标记样式 */
.required-mark {
  color: red;
  margin-right: 4px;
}

/* 确保表格高度占满容器 */
.product-sku-grid :deep(.vxe-grid) {
  height: 100%;
}

.product-sku-grid :deep(.vxe-table--body-wrapper) {
  height: calc(100% - 48px - 38px) !important;
  /* 根据你的表头和表尾高度调整 */
  overflow-y: auto;
}
</style>
