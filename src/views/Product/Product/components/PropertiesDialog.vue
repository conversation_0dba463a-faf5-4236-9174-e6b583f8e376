<script setup lang="ts">
import type { PropertyValueVO, PropertyVO } from '@/apiv2/product'
import { propertyApi } from '@/api.services/product.service'
import { getContrastColor, getTagStyle } from '../utils/color'

defineOptions({
  name: 'PropertiesDialog',
})

const props = withDefaults(defineProps<{
  modelValue?: number[]
  showBorder?: boolean
  isEditing?: boolean
  emptyText?: string
  language?: string
  dialogOnly?: boolean
}>(), {
  modelValue: () => [],
  showBorder: false,
  isEditing: true,
  emptyText: '-',
  language: 'zh',
  dialogOnly: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: number[]]
  'change': [value: number[]]
}>()

const dialogVisible = ref(false)
const loading = ref(false)

const propertyList = ref<PropertyVO[]>([])

const selectedProperties = useVModel(props, 'modelValue', emit, {
  passive: true,
})

async function loadProperties() {
  loading.value = true
  try {
    const response = (await propertyApi.listProperties()).data
    if (response.success && response.data) {
      propertyList.value = response.data
    }
  }
  catch (error) {
    console.error('加载属性数据失败', error)
    ElMessage.error('加载属性数据失败')
  }
  finally {
    loading.value = false
  }
}

async function openDialog() {
  if (props.isEditing === false)
    return

  dialogVisible.value = true
  if (propertyList.value.length === 0) {
    await loadProperties()
  }
}

onMounted(async () => {
  await loadProperties()
})

function confirmSelection(done: () => void) {
  emit('change', selectedProperties.value || [])
  dialogVisible.value = false
  done()
}

function cancelSelection() {
  selectedProperties.value = props.modelValue ? [...props.modelValue] : []
  dialogVisible.value = false
}

function toggleSelection(propertyValueId: number) {
  const currentSelection = selectedProperties.value || []
  const index = currentSelection.indexOf(propertyValueId)
  if (index > -1) {
    currentSelection.splice(index, 1)
  }
  else {
    currentSelection.push(propertyValueId)
  }
}

function isSelected(propertyValueId: number) {
  return (selectedProperties.value || []).includes(propertyValueId)
}

interface SelectedTagInfo {
  id: number
  name: string
  color?: string
}

// 根据语言获取属性值的显示名称
function getLocalizedValueName(value: PropertyValueVO): string {
  if (value.i18nValueNames && props.language) {
    const langKey = props.language.toLowerCase()
    const matchedKey = Object.keys(value.i18nValueNames).find(
      key => key.toLowerCase() === langKey,
    )

    if (matchedKey && value.i18nValueNames[matchedKey]) {
      return value.i18nValueNames[matchedKey]
    }
  }
  return value.valueName || ''
}

const selectedTagsInfo = computed((): SelectedTagInfo[] => {
  const tags: SelectedTagInfo[] = []
  const currentSelection = selectedProperties.value || []
  if (currentSelection.length === 0) {
    return tags
  }

  propertyList.value.forEach((property) => {
    property.values?.forEach((value: PropertyValueVO) => {
      if (value.id && currentSelection.includes(value.id)) {
        const color = (value.attrs as { color?: string })?.color
        tags.push({
          id: value.id,
          name: getLocalizedValueName(value),
          color,
        })
      }
    })
  })
  return tags
})

const selectedTagNames = computed(() => {
  return selectedTagsInfo.value.map(tag => tag.name).join('、')
})

function getColorStyle(value: PropertyValueVO) {
  const color = (value.attrs as { color?: string })?.color
  if (!color)
    return {}

  const textColor = getContrastColor(color)

  return {
    backgroundColor: color,
    color: textColor,
    borderColor: color,
    padding: '0 6px',
    borderRadius: '4px',
    display: 'inline-block',
    lineHeight: '18px',
    ...getTagStyle(color),
  }
}

defineExpose({
  openDialog,
  getSelectedTagNames: () => selectedTagNames.value,
  getSelectedTagsInfo: () => selectedTagsInfo.value,
})
</script>

<template>
  <div class="w-full">
    <template v-if="!dialogOnly">
      <div v-if="isEditing === false" class="min-h-32px flex items-center break-words text-14px text-[#606266]">
        <template v-if="selectedTagsInfo.length > 0">
          <div class="w-0 flex flex-1 flex-wrap items-center gap-6px">
            <span
              v-for="tag in selectedTagsInfo" :key="tag.id"
              class="inline-block whitespace-nowrap border-1 border-[#dcdfe6] rounded-4px bg-[#f4f4f5] px-6px text-12px text-[#909399] leading-normal"
              :style="tag.color ? getTagStyle(tag.color) : {}"
            >
              {{ tag.name }}
            </span>
          </div>
        </template>
        <template v-else>
          <span class="text-[#909399]">{{ props.emptyText }}</span>
        </template>
      </div>

      <div v-else>
        <div
          class="h-32px flex cursor-pointer items-center justify-between truncate px-12px leading-32px transition-all duration-300"
          :class="{ 'bg-white border-1 border-[#dcdfe6] rounded-4px hover:border-[#c0c4cc]': props.showBorder }"
          @click="openDialog"
        >
          <template v-if="selectedTagsInfo.length > 0">
            <div class="max-w-[calc(100%-24px)] w-0 flex flex-1 gap-4px overflow-hidden">
              <span
                v-for="tag in selectedTagsInfo" :key="tag.id"
                class="inline-block whitespace-nowrap border-1 border-[#dcdfe6] rounded-4px bg-[#f4f4f5] px-6px text-12px text-[#909399] leading-normal"
                :style="tag.color ? getTagStyle(tag.color) : {}"
              >
                {{ tag.name }}
              </span>
            </div>
          </template>
          <span v-else class="color-placeholder truncate">请选择产品标签</span>
          <font-awesome-icon :icon="['fas', 'tags']" class="text-[#399e96]" />
        </div>
      </div>
    </template>

    <CgDialog
      v-model="dialogVisible" title="编辑产品标签" width="800px" :full-height="false" confirm-button-text="确定"
      position="center" @submit="confirmSelection" @close="cancelSelection"
    >
      <div v-loading="loading" class="max-h-60vh min-h-220px overflow-y-auto p-10px">
        <div v-for="property in propertyList" :key="property.id" class="mb-16px">
          <div class="mb-12px rounded-md bg-gray-50 px-12px py-8px shadow-sm">
            <div class="flex items-center gap-8px">
              <div class="h-16px w-4px rounded-full bg-[#399e96]" />
              <span class="text-15px text-gray-800 font-bold">{{ property.propertyName }}</span>
            </div>
          </div>
          <div class="flex flex-wrap gap-10px">
            <ElCheckbox
              v-for="value in property.values" :key="value.id" :model-value="isSelected(value.id!)"
              class="mb-8px mr-10px" size="large" @change="() => toggleSelection(value.id!)"
            >
              <span :style="getColorStyle(value)">
                {{ getLocalizedValueName(value) }}
              </span>
            </ElCheckbox>
          </div>
        </div>
      </div>
    </CgDialog>
  </div>
</template>
