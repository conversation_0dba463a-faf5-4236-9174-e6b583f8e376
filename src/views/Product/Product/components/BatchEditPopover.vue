<script setup lang="ts">
import type { Component } from 'vue'
import CgButton from '@/components/CgElementUI/CgButton'
import CgInput from '@/components/CgElementUI/CgInput'
import CgInputNumber from '@/components/CgElementUI/CgInputNumber'
import CgSelectV2 from '@/components/CgElementUI/CgSelectV2'
import SizeInput from './SizeInput.vue'

interface Props {
  columnKey: string
  componentType: 'SelectV2' | 'InputNumber' | 'SizeInput' | 'Input'
  componentProps?: Record<string, any>
}

const props = defineProps<Props>()

const emit = defineEmits<{
  confirmBatchUpdate: [columnKey: string, value: any]
}>()

const popoverVisible = ref(false)
const editValue = ref<any>(null)

const componentMap: Record<string, Component> = {
  SelectV2: CgSelectV2,
  InputNumber: CgInputNumber,
  SizeInput,
  Input: CgInput,
}

const internalComponent = computed(() => componentMap[props.componentType])

watch(popoverVisible, (visible) => {
  if (visible) {
    if (props.componentType === 'SizeInput') {
      editValue.value = [{ length: undefined, width: undefined, height: undefined }]
    }
    else {
      editValue.value = undefined
    }
  }
})

function handleCancel() {
  popoverVisible.value = false
}

function handleConfirm() {
  if (editValue.value === undefined || editValue.value === null || (Array.isArray(editValue.value) && editValue.value.length === 0)) {
    if (props.componentType === 'SizeInput' && Array.isArray(editValue.value) && editValue.value.every(item => item.length == null && item.width == null && item.height == null)) {
      // 允许传递空数组以清空
    }
    else if (props.componentType !== 'SizeInput') {
      ElMessage.warning('请输入要批量修改的值')
      return
    }
  }

  emit('confirmBatchUpdate', props.columnKey, editValue.value)
  popoverVisible.value = false
}

const mergedComponentProps = computed(() => {
  const baseProps = { ...props.componentProps }
  baseProps.isEditing = true

  if (props.componentType === 'SelectV2') {
    baseProps.teleported = false
  }

  return baseProps
})
</script>

<template>
  <ElPopover
    v-model:visible="popoverVisible"
    placement="top"
    :width="componentType === 'SizeInput' ? 300 : 200"
    trigger="click"
    :persistent="false"
    :popper-options="{ strategy: 'fixed' }"
    popper-class="batch-edit-popper"
  >
    <template #reference>
      <CgButton type="primary" link class="ml-1">
        批量
      </CgButton>
    </template>
    <div class="batch-edit-popover-content">
      <component
        :is="internalComponent"
        v-model="editValue"
        v-bind="mergedComponentProps"
        class="w-full"
        :placeholder="`输入批量${componentProps?.label || '值'}`"
      />
      <div class="mt-2 flex justify-end gap-2">
        <ElButton size="small" @click="handleCancel">
          取消
        </ElButton>
        <ElButton type="primary" size="small" @click="handleConfirm">
          确定
        </ElButton>
      </div>
    </div>
  </ElPopover>
</template>

<style scoped>
.batch-edit-popover-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style>
