import type { DragTarget, ImageInfo, ImageSection, ProductImagesValue } from '../types'
import { GetOssUploadCredentialAccessTypeEnum, GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'
import { OssUploader } from '@/utils/ossUploader'
import { imageSize } from 'image-size'

/**
 * 产品图片管理的composable
 * @param imagesValueRef 图片数据的响应式引用
 * @param skuList SKU列表
 * @param emitUpdateSpuImage 更新SPU主图的回调函数
 * @param emitUploadSuccess 上传成功回调
 * @param emitUploadError 上传失败回调
 */
export function useProductImages(
  imagesValueRef: Ref<ProductImagesValue | undefined>,
  skuList: string[] | undefined,
  emitUpdateSpuImage: (url: string, key: string) => void,
  emitUploadSuccess: (url: string, key: string) => void,
  emitUploadError: (error: any) => void,
) {
  const fileInputRefs = ref<Record<string, HTMLInputElement | null>>({})
  const dragTarget = ref<DragTarget | null>(null)

  /**
   * 设置文件输入引用
   */
  function setFileInputRef(el: Element | ComponentPublicInstance | null, key: string) {
    if (el instanceof HTMLInputElement) {
      fileInputRefs.value[key] = el
    }
    else {
      fileInputRefs.value[key] = null
    }
  }

  /**
   * 获取上传文件类型
   */
  function getUploadFileType(section: ImageSection): GetOssUploadCredentialUploadFileTypeEnum {
    switch (section) {
      case 'mainImages':
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_IMAGES_MAIN
      case 'detailImages':
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_IMAGES_DETAIL
      case 'skuImages':
        return GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_IMAGES_SKU
    }
  }

  /**
   * 上传图片
   */
  function uploadImage(file: File, section: ImageSection): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const progress = (percent: number) => {
        console.log(`Upload progress: ${percent}%`)
      }

      const uploadFileType = getUploadFileType(section)

      OssUploader.uploadFile(
        file,
        uploadFileType,
        GetOssUploadCredentialAccessTypeEnum.PRIVATE,
        progress,
        (url, key) => {
          resolve([url, key])
        },
        (error) => {
          reject(new Error(`图片上传失败: ${error}`))
          emitUploadError(error)
        },
      )
    })
  }

  /**
   * 处理文件上传
   */
  async function handleFileUpload(file: File, section: ImageSection, skuId?: string) {
    if (!file.type.startsWith('image/')) {
      ElMessage.error('请上传图片文件')
      return
    }

    // 创建本地Blob URL用于图片回显
    const localBlobUrl = URL.createObjectURL(file)

    const imageInfo: ImageInfo = reactive({
      url: localBlobUrl, // 初始化时使用本地URL进行回显
      localUrl: localBlobUrl, // 保存本地URL便于之后替换
      imageKey: '', // 初始化时为空，上传后会填充
      size: Math.round(file.size / 1024), // KB
      filename: file.name,
      dimensions: undefined,
    })

    // 先添加图片，实现即时回显
    updateImageInfo(imageInfo, section, skuId)

    // 异步加载图片尺寸
    const img = new Image()
    img.onload = async () => {
      try {
        const arrayBuffer = await file.arrayBuffer()
        const uint8Array = new Uint8Array(arrayBuffer)
        imageInfo.dimensions = imageSize(uint8Array) as { width: number, height: number, type?: string }
      }
      catch (err) {
        console.error('获取图片尺寸失败:', err)
      }
    }
    img.onerror = () => {
      console.error('Image failed to load for dimension check:', localBlobUrl)
    }
    img.src = localBlobUrl

    try {
      // 上传到OSS并获取实际URL
      const [ossUrl, imageKey] = await uploadImage(file, section)
      imageInfo.url = ossUrl
      imageInfo.imageKey = imageKey
      emitUploadSuccess(ossUrl, imageKey)
      ElMessage({
        message: '图片上传成功',
        grouping: true,
        type: 'success',
      })
    }
    catch (uploadError) {
      console.error('Upload failed:', uploadError)
      removeImageByLocalUrl(localBlobUrl)
    }
  }

  /**
   * 更新图片信息
   */
  function updateImageInfo(image: ImageInfo, section: ImageSection, skuId?: string) {
    if (!imagesValueRef.value) {
      imagesValueRef.value = { mainImages: [], skuImages: {}, detailImages: [] }
    }
    const currentImagesValue = imagesValueRef.value

    if (section === 'mainImages') {
      if (!currentImagesValue.mainImages)
        currentImagesValue.mainImages = []
      currentImagesValue.mainImages = [...currentImagesValue.mainImages, image]
      if (currentImagesValue.mainImages.length === 1) {
        emitUpdateSpuImage(image.url, image.imageKey)
      }
    }
    else if (section === 'detailImages') {
      if (!currentImagesValue.detailImages)
        currentImagesValue.detailImages = []
      currentImagesValue.detailImages = [...currentImagesValue.detailImages, image]
    }
    else if (section === 'skuImages' && skuId) {
      if (!currentImagesValue.skuImages)
        currentImagesValue.skuImages = {}
      if (!currentImagesValue.skuImages[skuId]) {
        currentImagesValue.skuImages[skuId] = []
      }
      currentImagesValue.skuImages[skuId] = [...currentImagesValue.skuImages[skuId], image]
    }
  }

  /**
   * 移除图片
   */
  function removeImage(section: ImageSection, index: number, skuId?: string) {
    if (!imagesValueRef.value)
      return

    const newValue: ProductImagesValue = JSON.parse(JSON.stringify(imagesValueRef.value))
    let imageToRemove: ImageInfo | undefined

    if (section === 'mainImages' && newValue.mainImages && newValue.mainImages[index]) {
      const isSpuMainImage = index === 0
      imageToRemove = newValue.mainImages[index]
      newValue.mainImages.splice(index, 1)

      if (isSpuMainImage) {
        emitUpdateSpuImage(newValue.mainImages[0]?.url || '', newValue.mainImages[0]?.imageKey || '')
      }
    }
    else if (section === 'detailImages' && newValue.detailImages && newValue.detailImages[index]) {
      imageToRemove = newValue.detailImages[index]
      newValue.detailImages.splice(index, 1)
    }
    else if (section === 'skuImages' && skuId && newValue.skuImages && newValue.skuImages[skuId] && newValue.skuImages[skuId][index]) {
      imageToRemove = newValue.skuImages[skuId][index]
      newValue.skuImages[skuId].splice(index, 1)
    }

    // 释放本地Blob URL资源
    if (imageToRemove?.localUrl) {
      try {
        URL.revokeObjectURL(imageToRemove.localUrl)
      }
      catch (error) {
        console.error(`释放本地Blob URL失败:`, error)
      }
    }

    // 将修改后的副本赋值回 ref, 触发更新
    imagesValueRef.value = newValue
  }

  /**
   * 移除图片
   */
  function removeImageByLocalUrl(localUrl: string) {
    if (!imagesValueRef.value)
      return

    const newValue: ProductImagesValue = JSON.parse(JSON.stringify(imagesValueRef.value))
    let found = false
    let isSpuMainImageRemoved = false

    if (newValue.mainImages) {
      const index = newValue.mainImages.findIndex(img => img.localUrl === localUrl)
      if (index !== -1) {
        if (index === 0)
          isSpuMainImageRemoved = true
        newValue.mainImages.splice(index, 1)
        found = true
      }
    }

    if (newValue.detailImages) {
      const index = newValue.detailImages.findIndex(img => img.localUrl === localUrl)
      if (index !== -1) {
        newValue.detailImages.splice(index, 1)
        found = true
      }
    }

    if (newValue.skuImages) {
      Object.keys(newValue.skuImages).forEach((key) => {
        if (newValue.skuImages[key]) {
          const index = newValue.skuImages[key].findIndex(img => img.localUrl === localUrl)
          if (index !== -1) {
            newValue.skuImages[key].splice(index, 1)
            found = true
          }
        }
      })
    }

    if (found) {
      try {
        URL.revokeObjectURL(localUrl)
      }
      catch (e) { console.error(e) }
      // 更新 SPU 主图如果需要
      if (isSpuMainImageRemoved) {
        emitUpdateSpuImage(newValue.mainImages?.[0]?.url || '', newValue.mainImages?.[0]?.imageKey || '')
      }
      // 赋值副本回 ref
      imagesValueRef.value = newValue
    }
  }

  /**
   * 处理拖拽区域拖拽开始
   */
  function handleDropZoneDragOver(section: ImageSection, skuId?: string, event?: DragEvent) {
    if (event?.dataTransfer && event.dataTransfer.types.includes('application/internal-image-card')) {
      return
    }

    dragTarget.value = { section, skuId }
  }

  /**
   * 处理拖拽区域拖拽离开
   */
  function handleDropZoneDragLeave(event?: DragEvent) {
    if (event?.dataTransfer && event.dataTransfer.types.includes('application/internal-image-card')) {
      return
    }

    dragTarget.value = null
  }

  /**
   * 处理拖拽区域放置
   */
  function handleDropZoneDrop(event: DragEvent, section: ImageSection, skuId?: string) {
    if (event.dataTransfer && event.dataTransfer.types.includes('application/internal-image-card')) {
      return
    }

    dragTarget.value = null
    const files = event.dataTransfer?.files
    if (files && files.length > 0) {
      Array.from(files).forEach((file) => {
        handleFileUpload(file, section, skuId)
      })
    }
  }

  /**
   * 处理文件选择
   */
  function handleFileSelect(event: Event, section: ImageSection, skuId?: string) {
    const input = event.target as HTMLInputElement
    if (input.files && input.files.length > 0) {
      Array.from(input.files).forEach((file) => {
        handleFileUpload(file, section, skuId)
      })
      input.value = ''
    }
  }

  /**
   * 打开文件输入
   */
  function openFileInput(key: string) {
    const input = fileInputRefs.value[key]
    if (input) {
      input.click()
    }
  }

  /**
   * 获取SKU ID列表
   */
  function getSkuIds() {
    if (skuList && skuList.length > 0) {
      return skuList
    }

    const existingSkus = Object.keys(imagesValueRef.value?.skuImages || {})

    if (existingSkus.length === 0) {
      return []
    }

    return existingSkus
  }

  /**
   * 判断是否为拖拽目标
   */
  function isDragTarget(section: ImageSection, skuId?: string): boolean {
    if (!dragTarget.value)
      return false

    if (dragTarget.value.section !== section)
      return false

    if (section === 'skuImages') {
      return dragTarget.value.skuId === skuId
    }

    return true
  }

  /**
   * 获取所有输入键
   */
  const allInputKeys = ref<string[]>(['mainImages', 'detailImages'])

  /**
   * 创建图片区域组件
   */
  function createImageSection(
    title: string,
    section: ImageSection,
    images: ImageInfo[] | undefined,
    emptyText: string,
    keyPrefix: string,
    skuCode?: string,
    extraHeader?: any,
  ) {
    const sectionKey = skuCode ? `sku-${skuCode}` : section

    return {
      title,
      section,
      images,
      emptyText,
      keyPrefix,
      skuCode,
      sectionKey,
      extraHeader,
      isDragOver: isDragTarget(section, skuCode),
      handleDragOver: () => handleDropZoneDragOver(section, skuCode),
      handleDragLeave: () => handleDropZoneDragLeave(),
      handleDrop: (event: DragEvent) => handleDropZoneDrop(event, section, skuCode),
      handleClick: () => openFileInput(sectionKey),
      handleRemove: (index: number) => removeImage(section, index, skuCode),
    }
  }

  return {
    fileInputRefs,
    dragTarget,
    setFileInputRef,
    handleFileSelect,
    getSkuIds,
    isDragTarget,
    allInputKeys,
    createImageSection,
  }
}
