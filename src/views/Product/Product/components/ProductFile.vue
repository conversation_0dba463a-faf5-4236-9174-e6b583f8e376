<script setup lang="ts">
import type { ProductFileVO } from '@/apiv2/product'
import type { CgFile } from '@/components/CgFileUploader/FileUploader.vue'
import type { GridInstance, GridProps } from '@/components/CgGrid'
import type { VxeGridPropTypes } from 'vxe-table'
import { GetOssUploadCredentialAccessTypeEnum, GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'

defineOptions({
  name: 'ProductFile',
})

const props = withDefaults(defineProps<{
  modelValue?: ProductFileVO[]
  detail?: boolean
  showFooter?: boolean
}>(), {
  showFooter: true,
})

const emit = defineEmits<{
  'update:modelValue': [value: ProductFileVO[]]
}>()

const modelValue = useVModel(props, 'modelValue', emit, { defaultValue: [] })
const activeTab = inject<Ref<string>>('activeTab')
const isDetail = inject<Ref<boolean>>('isDetail', ref(false))
const gridRef = ref<GridInstance | null>(null)

// 处理初始数据中的extension字段
function processInitialExtensions() {
  if (!modelValue.value?.length)
    return

  modelValue.value.forEach((file) => {
    if (file.extension || !file.filename)
      return

    const dotIndex = file.filename.lastIndexOf('.')
    if (dotIndex > 0) {
      file.extension = file.filename.substring(dotIndex + 1).toUpperCase()
    }
  })
}

const columns: VxeGridPropTypes.Columns = [
  {
    field: 'fileType',
    title: '文件类型',
    minWidth: 120,
    slots: {
      footer: 'fileFooter',
      default: 'fileTypeTemp',
    },
  },
  {
    title: '附件',
    minWidth: 150,
    slots: {
      default: 'fileTemp',
    },
  },
  {
    field: 'extension',
    title: '格式',
    minWidth: 120,
  },
  {
    field: 'fileSize',
    title: '大小',
    minWidth: 100,
    slots: {
      default: 'fileSizeTemp',
    },
  },
  {
    field: 'fileDesc',
    title: '文件说明',
    minWidth: 100,
    slots: {
      default: 'fileDescTemp',
    },
  },
  {
    field: 'fileOperation',
    title: '操作',
    minWidth: 100,
    slots: {
      default: 'fileOperationTemp',
    },
  },
]

const showFooter = computed(() => props.showFooter && !props.detail)

const gridOption = reactive<GridProps>({
  options: {
    columns,
    border: false,
    minHeight: 300,
    height: 'auto',
    round: true,
    showFooter: showFooter.value,
    showFooterOverflow: true,
    footerMethod: ({ columns }) => {
      return [
        columns.map(() => null),
      ]
    },
  },
  paginationVisible: false,
})

function addNewRow() {
  const newFileRow = reactive<Partial<ProductFileVO> & { fileName?: string }>({
    id: undefined,
    filename: undefined,
    fileName: undefined,
    filePath: undefined, // OSS key
    fileUrl: undefined, // 下载 URL
    fileType: undefined, // 文件分类类型
    fileDesc: undefined,
    fileSize: undefined, // 文件大小字符串 "100 KB"
  })
  modelValue.value = [...(modelValue.value || []), newFileRow]
}

function deleteRow(row: ProductFileVO) {
  const index = modelValue.value?.findIndex(item => item === row)
  if (index !== undefined && index !== -1 && modelValue.value) {
    modelValue.value = [
      ...modelValue.value.slice(0, index),
      ...modelValue.value.slice(index + 1),
    ]
  }
}

watchEffect(() => {
  if (activeTab?.value === 'files' && !modelValue.value) {
    modelValue.value = []
  }
})

watchEffect(() => {
  if (modelValue.value?.length) {
    processInitialExtensions()
  }
})

// 格式化文件大小 (bytes to KB/MB string)
function formatFileSize(sizeInBytes?: number): string | undefined {
  if (sizeInBytes === undefined || sizeInBytes === null)
    return undefined
  const sizeInKB = sizeInBytes / 1024
  if (sizeInKB < 1024) {
    return `${Math.round(sizeInKB * 100) / 100} KB`
  }
  else {
    const sizeInMB = sizeInKB / 1024
    return `${Math.round(sizeInMB * 100) / 100} MB`
  }
}

function handleFileUpdate(row: ProductFileVO, updatedFile: CgFile | null) {
  if (updatedFile) {
    row.filePath = updatedFile.key
    row.filename = updatedFile.name
    row.fileUrl = updatedFile.url
    row.fileSize = updatedFile.size // 直接存储原始字节数

    // 从文件名中提取文件后缀
    const dotIndex = updatedFile.name.lastIndexOf('.')
    if (dotIndex > 0) {
      row.extension = updatedFile.name.substring(dotIndex + 1).toUpperCase()
    }
    else {
      row.extension = undefined
    }
  }
  else {
    // 文件被移除，清空相关字段
    row.filePath = undefined
    row.filename = undefined
    row.fileUrl = undefined
    row.fileSize = undefined
    row.extension = undefined
  }
}

const fileTypeOptions = [
  { label: '产品附件', value: 0 },
  { label: '说明书', value: 1 },
  { label: '认证', value: 2 },
  { label: '3D模型', value: 3 },
  { label: '视频', value: 4 },
]

function getAcceptByFileType(fileType?: number): string {
  switch (fileType) {
    case 0: // 产品附件
      return '.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar'
    case 1: // 说明书
      return '.pdf'
    case 2: // 认证
      return '.pdf,.jpg,.jpeg,.png'
    case 3: // 3D模型
      return '.obj,.stl,.fbx,.glb,.gltf'
    case 4: // 视频
      return 'video/*'
    default:
      return '*' // 默认接受所有文件类型
  }
}

function getFileUploaderValue(row: ProductFileVO): CgFile | null {
  return {
    key: row.filePath || '',
    name: row.filename || '',
    url: row.fileUrl || '',
    size: row.fileSize,
    type: row.extension ? `application/${row.extension.toLowerCase()}` : undefined,
  }
}
</script>

<template>
  <div class="h-full">
    <CgGrid ref="gridRef" v-bind="gridOption" :data="modelValue">
      <template #fileFooter>
        <font-awesome-icon
          v-if="!props.detail && !isDetail"
          :icon="['fas', 'square-plus']" size="2x"
          class="ml-2 cursor-pointer text-gray-500 hover:text-gray-700"
          @click="addNewRow"
        />
      </template>
      <template #fileTypeTemp="{ row }">
        <CgSelectV2 v-model="row.fileType" :data="fileTypeOptions" :disabled="props.detail || isDetail" />
      </template>
      <template #fileTemp="{ row }">
        <CgFileUploader
          :model-value="getFileUploaderValue(row)"
          :multiple="false"
          :folder-name="GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_FILES"
          :access-type="GetOssUploadCredentialAccessTypeEnum.PRIVATE"
          :disabled="props.detail || isDetail"
          :accept="getAcceptByFileType(row.fileType)"
          upload-text="上传附件"
          @update:model-value="handleFileUpdate(row, $event)"
        />
      </template>
      <template #fileSizeTemp="{ row }">
        {{ formatFileSize(row.fileSize) }}
      </template>
      <template #fileDescTemp="{ row }">
        <CgInput v-model="row.fileDesc" type="text" :disabled="props.detail || isDetail" />
      </template>
      <template #fileOperationTemp="{ row }">
        <CgButton v-if="!props.detail && !isDetail" type="text" @click="deleteRow(row)">
          删除
        </CgButton>
        <span v-else>-</span>
      </template>
    </CgGrid>
  </div>
</template>
