<script setup lang="ts">
import CgComplexInputV2 from '@/components/CgElementUI/CgComplexInputV2'

defineOptions({
  name: 'SizeInput',
})

const props = withDefaults(defineProps<{
  modelValue: SizeItem[]
  components: any[]
  append?: string
  isEditing?: boolean
}>(), {
  modelValue: () => [{ length: undefined, width: undefined, height: undefined }],
  append: 'cm',
  isEditing: true,
})

const emit = defineEmits(['update:modelValue'])

interface SizeItem {
  id?: number
  length?: number
  width?: number
  height?: number
}

const modelValue = useVModel(props, 'modelValue', emit, {
  defaultValue: [{ length: undefined, width: undefined, height: undefined }],
})

// 使用计算属性确保响应式
const sizeArray = computed((): SizeItem[] => {
  if (!modelValue.value) {
    return [{ length: undefined, width: undefined, height: undefined }]
  }
  if (!Array.isArray(modelValue.value)) {
    return [modelValue.value as SizeItem]
  }
  return modelValue.value
})

function addSizeRow() {
  const newArray = [...sizeArray.value]
  newArray.push({ length: undefined, width: undefined, height: undefined })
  modelValue.value = newArray
}

function removeSizeRow(index: number) {
  const newArray = [...sizeArray.value]
  if (newArray.length > 1) {
    newArray.splice(index, 1)
    modelValue.value = newArray
  }
}

onMounted(() => {
  if (!modelValue.value?.length) {
    addSizeRow()
  }
})
</script>

<template>
  <div class="flex flex-col gap-2">
    <div v-for="(sizeItem, index) in sizeArray" :key="index" class="flex items-center gap-2">
      <CgComplexInputV2
        v-model="sizeArray[index]"
        :components="components"
        :append="append"
        :is-editing="isEditing"
      />
      <div v-if="isEditing" class="flex items-center">
        <font-awesome-icon
          v-if="sizeArray.length > 1"
          :icon="['fas', 'minus-circle']"
          class="ml-2 cursor-pointer text-red-500 hover:text-red-700"
          @click="removeSizeRow(index)"
        />
        <font-awesome-icon
          v-if="index === sizeArray.length - 1"
          :icon="['fas', 'plus-circle']"
          class="ml-2 cursor-pointer text-green-500 hover:text-green-700"
          @click="addSizeRow"
        />
      </div>
    </div>
  </div>
</template>
