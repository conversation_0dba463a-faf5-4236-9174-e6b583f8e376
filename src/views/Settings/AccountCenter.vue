<script setup lang="ts">
import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'
import ComponentTagsEnum from '@/components/CgElementUI/CgForm/ComponentTagsEnum'
import { useUserStore } from '@/stores/User'
import { usePasswordManagement } from '@/views/Settings/composables/usePasswordManagement'
import { useProfileManagement } from '@/views/Settings/composables/useProfileManagement'
import { User, UserFilled } from '@element-plus/icons-vue'

// 获取用户信息
const userStore = useUserStore()

// 当前激活的折叠面板
const activeName = ref('')

// 使用密码管理组合式函数
const {
  formRef: pwdFormRef,
  formModel: pwdFormModel,
  formRules: pwdRules,
  submitForm: pwdBtnClick,
} = usePasswordManagement()

// 使用个人资料管理组合式函数
const {
  isEditing: showNameInput,
  editingName,
  startEditing: nameClick,
  cancelEditing: cancelNameEdit,
  saveName,
} = useProfileManagement()

// 密码表单配置
const pwdFormOptions = computed<CgFormOption[][]>(() => [
  [
    {
      label: '旧密码',
      key: 'oldPassword',
      type: ComponentTagsEnum.Input,
      span: 24,
      props: {
        'show-password': true,
        'autocomplete': 'new-password',
      },
    },
    {
      label: '新密码',
      key: 'newPassword',
      type: ComponentTagsEnum.Input,
      span: 24,
      props: {
        'show-password': true,
        'autocomplete': 'new-password',
      },
    },
    {
      label: '确认密码',
      key: 'confirmPassword',
      type: ComponentTagsEnum.Input,
      span: 24,
      props: {
        'show-password': true,
        'autocomplete': 'new-password',
      },
    },
    {
      label: '',
      key: 'button',
      type: ComponentTagsEnum.Button,
      span: 24,
      props: {
        type: 'primary',
        autoLoading: true,
        btnText: '保存',
        class: 'w-80px! ml-auto block'
      },
      events: {
        click: pwdBtnClick,
      },
    },
  ],
])
</script>

<template>
  <div class="account-center">
    <div>
      <div class="title">
        账号信息
      </div>
      <div class="flex p-y-7.5 p-l-5">
        <div class="flex flex-grow flex-wrap">
          <div class="h-8.5 w-1/2">
            <span class="m-x-4.5 m-l-7.5 inline-block w-15 text-gray-500">用户名</span>
            <span>{{ userStore.userName }}</span>
          </div>
          <div class="h-8.5 w-1/2">
            <span class="m-x-4.5 m-l-7.5 inline-block w-15 text-gray-500">真实姓名</span>
            <span v-if="!showNameInput">
              {{ userStore.name }}
              <!-- <ElIcon class="ml-1.5 cursor-pointer text-sm text-primary" @click="nameClick">
                <i-ep-edit />
              </ElIcon> -->
            </span>
            <span v-else class="relative">
              <ElInput v-model="editingName" class="w-40" />
              <div v-if="editingName && editingName.length === 0" class="el-form-item__error">
                真实姓名不能为空
              </div>
              <span class="ml-2 cursor-pointer text-primary" @click="cancelNameEdit">取消</span>
              <span class="ml-2 cursor-pointer text-primary" @click="saveName">保存</span>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div class="title">
        安全设置
      </div>
      <ElCollapse v-model="activeName" accordion>
        <ElCollapseItem name="2">
          <template #title>
            <div class="w-full flex justify-between">
              <div>
                <span class="mr-4.75 text-sm font-semibold">登录密码</span>
                <span class="text-xs">
                  安全性高的密码可以使帐号更安全。建议您定期更换，设置一个包含字母，符号或数字中至少两项，且长度在6-20位的密码。
                </span>
              </div>
              <span class="text-primary">修改</span>
            </div>
          </template>
          <CgForm
            ref="pwdFormRef"
            v-model="pwdFormModel"
            :options="pwdFormOptions"
            :rules="pwdRules"
            class="ml-25 w-90"
          />
        </ElCollapseItem>
      </ElCollapse>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.account-center {
  .title {
    height: 48px;
    line-height: 48px;
    padding-left: 20px;
    font-size: 14px;
    border-bottom: 1px solid #dadce0;
  }

  :deep(.el-form-item__error) {
    width: 400px;
  }

  :deep(.el-collapse-item__header) {
    height: 70px;
    line-height: 70px;
    font-size: 12px;
    padding: 0 20px;
    position: relative;
  }

  .custom-avatar {
    :deep(.el-avatar__icon) {
      font-size: 40px;
    }
  }
}
</style>
