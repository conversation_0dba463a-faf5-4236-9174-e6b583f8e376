import { useUserStore } from '@/stores/User'

export function useProfileManagement() {
  const userStore = useUserStore()

  const isEditing = ref(false)

  const originalName = ref(userStore.name)

  const editingName = ref(userStore.name)

  function startEditing() {
    originalName.value = userStore.name
    editingName.value = userStore.name
    isEditing.value = true
  }

  function cancelEditing() {
    isEditing.value = false
  }

  async function saveName() {
    if (!editingName.value) {
      ElMessage.warning('真实姓名不能为空')
      return
    }

    try {
      // 这里应该调用实际的API，但由于没有找到对应的API，使用模拟数据
      // await userApi.updateName({ name: editingName.value })

      // 更新 store 中的用户名
      // userStore.setName(editingName.value)

      ElMessage.success('姓名更新成功')
      isEditing.value = false
    }
    catch (error) {
      console.error('更新姓名失败', error)
    }
  }

  return {
    isEditing,
    editingName,
    startEditing,
    cancelEditing,
    saveName,
  }
}
