import type { PasswordUpdateReq } from '@/apiv2/user/models'
import type { FormInstance, FormRules } from 'element-plus'
import userApi from '@/api.services/user.service'
import { usePermissionStore } from '@/stores/Permission'
import { useUserStore } from '@/stores/User'
import { RsaEncryption } from '@/utils/encryption'
import { useRouter } from 'vue-router'

export function usePasswordManagement() {
  const router = useRouter()
  const userStore = useUserStore()
  const permissionStore = usePermissionStore()

  const formRef = ref<FormInstance>()

  const formModel = reactive<{
    oldPassword: string
    newPassword: string
    confirmPassword: string
  }>({
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  })

  const formRules = reactive<FormRules>({
    oldPassword: [
      { required: true, message: '请输入旧密码', trigger: 'blur' },
      { validator: validatePassword, trigger: 'blur' },
    ],
    newPassword: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      { validator: validatePassword, trigger: 'blur' },
    ],
    confirmPassword: [
      { required: true, message: '请输入确认密码', trigger: 'blur' },
      { validator: validateConfirmPassword, trigger: 'blur' },
    ],
  })

  function validatePassword(_rule: any, value: string, callback: (error?: Error) => void) {
    // 密码必须包含字母、数字和特殊字符中的至少两种，长度6-20位
    const pattern = /^(?=.{6,20}$)(?:(?=.*[A-Z])(?=.*\d)|(?=.*[A-Z])(?=.*[^A-Z0-9])|(?=.*\d)(?=.*[^A-Z0-9]))[\w!@#$%^&*()+\-=[\]{};:'",.<>/?]{6,20}$/i

    if (!pattern.test(value)) {
      callback(new Error('密码必须包含字母、符号或数字中至少两项，且长度在6-20位之间'))
    }
    else {
      callback()
    }
  }

  function validateConfirmPassword(_rule: any, value: string, callback: (error?: Error) => void) {
    if (value !== formModel.newPassword) {
      callback(new Error('两次输入密码不一致'))
    }
    else {
      callback()
    }
  }

  async function submitForm(done: () => void) {
    if (!formRef.value) {
      done()
      return
    }

    try {
      await formRef.value.validate()

      const passwordUpdateReq: PasswordUpdateReq = {
        oldPassword: RsaEncryption.encrypt(formModel.oldPassword),
        newPassword: RsaEncryption.encrypt(formModel.newPassword),
      }

      await userApi.updatePassword({ passwordUpdateReq })

      ElMessage.success('密码更新成功')

      resetForm()

      setTimeout(() => {
        userStore.resetToken()
        permissionStore.resetToken()
        router.push('/login')
      }, 1800)
    }
    catch (error) {
      console.error('密码更新失败', error)
    }
    finally {
      done()
    }
  }

  // 重置表单
  function resetForm() {
    if (formRef.value) {
      formRef.value.resetFields()
    }

    formModel.oldPassword = ''
    formModel.newPassword = ''
    formModel.confirmPassword = ''
  }

  return {
    formRef,
    formModel,
    formRules,
    submitForm,
    resetForm,
  }
}
