import type { ConfigurationParameters } from '@/apiv2/product/configuration'
import type { AxiosInstance } from 'axios'
import axiosInstance from '@/common/Request'

interface ApiConstructors<C, A> {
  Configuration: new (params?: ConfigurationParameters) => C
  API: new (config?: C, basePath?: string, axios?: AxiosInstance) => A
}

const devProxyPrefix = '/dev-proxy'

/**
 * 创建 API 服务实例的工厂函数
 * @param modulePath API 模块路径 (例如 /gaia-product)
 * @param apiConstructors 包含 Configuration 和 API 构造函数的对象
 * @param baseConfig 可选的基础配置参数，会合并 basePath
 * @returns 配置好的 API 代理实例
 */
export function createApiProxy<
  C extends Record<string, any>,
  A extends Record<string, any>,
>(
  modulePath: string,
  apiConstructors: ApiConstructors<C, A>,
  baseConfig: Omit<ConfigurationParameters, 'basePath'> = {},
): A {
  const basePath = import.meta.env.MODE === 'development' && import.meta.env.DEV
    ? `${devProxyPrefix}${modulePath}`
    : `${import.meta.env.VITE_API_BASE_URL}${modulePath}`

  const configParams: ConfigurationParameters = {
    ...baseConfig,
    basePath,
  }

  const apiConfig = new apiConstructors.Configuration(configParams)

  const targetApi = new apiConstructors.API(apiConfig, undefined, axiosInstance)

  const apiProxy = new Proxy(targetApi, {
    get(target, propKey, receiver) {
      const originalValue = Reflect.get(target, propKey, receiver)
      if (typeof originalValue === 'function') {
        return (originalValue as (...args: any[]) => any).bind(target)
      }
      return originalValue
    },
  }) as A

  return apiProxy
}
