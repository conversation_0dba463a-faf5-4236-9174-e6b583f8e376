import { Configuration, 工作流定义Api, 工单Api, 工单配置Api } from '@/apiv2/workflow'
import { createApiProxy } from './api.factory'

const modulePath = '/gaia-workflow'

const workflowDefApi = createApiProxy(modulePath, { Configuration, API: 工作流定义Api })
const ticketApi = createApiProxy(modulePath, { Configuration, API: 工单Api })
const ticketConfigApi = createApiProxy(modulePath, { Configuration, API: 工单配置Api })

export default workflowDefApi
export { ticketApi, ticketConfigApi, workflowDefApi }
