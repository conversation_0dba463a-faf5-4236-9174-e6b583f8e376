import { Configuration, 物流供应商管理Api, 订单轨迹导入控制器Api, 运费Api, 运输方式管理Api } from '@/apiv2/fulfillment'
import { createApiProxy } from './api.factory'

const modulePath = '/gaia-fulfillment'

const fulfillmentApi = createApiProxy(modulePath, { Configuration, API: 订单轨迹导入控制器Api })
const transportApi = createApiProxy(modulePath, { Configuration, API: 运输方式管理Api })
const logisticsProviderApi = createApiProxy(modulePath, { Configuration, API: 物流供应商管理Api })
const freightApi = createApiProxy(modulePath, { Configuration, API: 运费Api })

export default fulfillmentApi
export { freightApi, fulfillmentApi, logisticsProviderApi, transportApi }
