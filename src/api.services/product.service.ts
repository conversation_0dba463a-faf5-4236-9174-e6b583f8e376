import { Configuration, 产品Api, 产品工单Api, 供应商Api, 商品Api, 字典Api, 属性Api, 操作日志Api, 文件上传接口Api, 类目管理Api, 系统服务Api } from '@/apiv2/product'
import { createApiProxy } from './api.factory'

const modulePath = '/gaia-product'

const productApi = createApiProxy(modulePath, { Configuration, API: 产品Api })
const productTicketApi = createApiProxy(modulePath, { Configuration, API: 产品工单Api })
const supplierApi = createApiProxy(modulePath, { Configuration, API: 供应商Api })
const commodityApi = createApiProxy(modulePath, { Configuration, API: 商品Api })
const dictApi = createApiProxy(modulePath, { Configuration, API: 字典Api })
const propertyApi = createApiProxy(modulePath, { Configuration, API: 属性Api })
const logApi = createApiProxy(modulePath, { Configuration, API: 操作日志Api })
const fileApi = createApiProxy(modulePath, { Configuration, API: 文件上传接口Api })
const categoryApi = createApiProxy(modulePath, { Configuration, API: 类目管理Api })
const systemApi = createApiProxy(modulePath, { Configuration, API: 系统服务Api })

export default productApi
export { categoryApi, commodityApi, dictApi, fileApi, logApi, productApi, productTicketApi, propertyApi, supplierApi, systemApi }
