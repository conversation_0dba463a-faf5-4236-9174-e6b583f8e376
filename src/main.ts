import { setupAuthInterceptor } from '@/common/interceptors/auth.interceptor'
import axiosInstance from '@/common/Request'
import Cg<PERSON>lement<PERSON> from '@/components/CgElementUI'
import { setupRouterGuard } from '@/router/guard'
import { createPinia } from 'pinia'
import { createApp } from 'vue'

import App from './App.vue'
import Components from './components/index'
import { setupDirectives } from './directives'
import plugins from './plugins'

import router from './router'

// import 'element-plus/lib/theme-chalk/index.css'
// import './styles/element/index.scss'
import '@unocss/reset/tailwind.css'
import './styles/index.scss'

import 'virtual:uno.css'

async function bootstrap() {
  const app = createApp(App)

  app.use(createPinia())
  app.use(router)

  setupRouterGuard(router)

  setupAuthInterceptor(axiosInstance)

  app.use(plugins)
  app.use(Components)
  app.use(CgElementUI)

  // 注册自定义指令
  setupDirectives(app)

  app.mount('#app')
}

bootstrap()
