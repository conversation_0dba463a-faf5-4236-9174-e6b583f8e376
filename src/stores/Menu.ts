import type { RouteLocationNormalizedGeneric } from 'vue-router'
import { usePermissionStore } from './Permission'

export interface MenuPointer {
  topId: number | null // 当前激活的一级菜单 id
  leafId: number | null // 当前激活的最终叶子菜单 id
}

export interface MenuItem {
  id: number
  menu: string
  icon?: string
  link?: string
  children?: MenuItem[]
}

export const useMenuStore = defineStore('menu', () => {
  const pointer = reactive<MenuPointer>({ topId: null, leafId: null })
  const menus = usePermissionStore().menus

  function syncWithRoute(route: RouteLocationNormalizedGeneric) {
    let hitTop: number | null = null
    let hitLeaf: number | null = null

    const walk = (items: MenuItem[], parentId: number | null) => {
      items.forEach((item) => {
        if (item.link && route.path.startsWith(item.link)) {
          hitTop = parentId ?? item.id // 没有 parentId 说明自己就是一级
          hitLeaf = item.id
        }
        if (item.children)
          walk(item.children, parentId ?? item.id)
      })
    }
    walk(menus, null)

    pointer.topId = hitTop
    pointer.leafId = hitLeaf
  }

  return {
    pointer,
    syncWithRoute,
  }
})
