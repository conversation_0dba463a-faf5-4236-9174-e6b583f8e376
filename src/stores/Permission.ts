export interface MenuItem {
  id: number
  menu: string
  icon?: string
  link?: string
  children?: MenuItem[]
}

export const usePermissionStore = defineStore('permission', () => {
  // 菜单列表
  const menus = ref<MenuItem[]>([
    {
      id: 1,
      menu: '产品',
      icon: 'fa-box',
      children: [
        {
          id: 11,
          menu: '产品管理',
          link: '/product/list',
        },
        {
          id: 12,
          menu: '类目管理',
          link: '/product/category',
        },
      ],
    },
    {
      id: 2,
      menu: '运营',
      icon: 'fa-store',
      children: [
        {
          id: 21,
          menu: '上架运营',
          link: '/operation/list',
        },
      ],
    },
    // {
    //   id: 3,
    //   menu: '素材',
    //   icon: 'fa-shopping-cart',
    //   children: [
    //     {
    //       id: 31,
    //       menu: '素材库',
    //       link: '/material/list',
    //     },
    //   ],
    // },
    // {
    //   id: 7,
    //   menu: '供应链',
    //   icon: 'fa-truck',
    //   children: [
    //     {
    //       id: 71,
    //       menu: '供应商资料',
    //       link: '/supplier/list',
    //     },
    //   ],
    // },
    {
      id: 5,
      menu: '工单',
      icon: 'fa-ticket',
      children: [
        {
          id: 51,
          menu: '工作台',
          link: '/ticket/workbench',
        },
        {
          id: 52,
          menu: '服务工单',
          link: '/ticket/service',
        },
      ],
    },
    {
      id: 6,
      menu: '订单',
      icon: 'fa-list-alt',
      children: [
        {
          id: 61,
          menu: '订单履约监控',
          link: '/order/monitor',
        },
      ],
    },
    {
      id: 4,
      menu: '设置',
      icon: 'fa-cogs',
      children: [
        // {
        //   id: 41,
        //   menu: '标签管理',
        //   link: '/settings/tag',
        // },
        // {
        //   id: 42,
        //   menu: '数据字典',
        //   link: '/settings/dictionary',
        // },
        {
          id: 43,
          menu: '下载中心',
          link: '/download-center',
        },
      ],
    },
  ])

  // 用户角色
  const roles = ref<string[]>(['admin'])

  // 重置权限
  function resetToken() {
    // 清空权限相关数据
    // 这里可以根据实际需求添加更多逻辑
  }

  return {
    menus,
    roles,
    resetToken,
  }
})
