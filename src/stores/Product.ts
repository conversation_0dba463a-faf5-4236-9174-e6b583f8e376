import { defineStore } from 'pinia'

export const useProductStore = defineStore('product', () => {
  const productNameMap = ref<Record<string, string>>({})
  const refreshFn = ref<() => void>()

  // 设置款名
  function setProductName(id: string, name: string) {
    productNameMap.value[id] = name
  }

  // 获取款名
  function getProductName(id: string) {
    return productNameMap.value[id] || ''
  }

  // 清除款名
  function clearProductName(id: string) {
    delete productNameMap.value[id]
  }

  // 清除所有款名
  function clearAllProductNames() {
    productNameMap.value = {}
  }

  // 设置刷新函数
  function setRefreshFn(fn: () => void) {
    refreshFn.value = fn
  }

  // 执行刷新
  function refreshList() {
    refreshFn.value?.()
  }

  return {
    productNameMap,
    setProductName,
    getProductName,
    clearProductName,
    clearAllProductNames,
    setRefreshFn,
    refreshList,
  }
})
