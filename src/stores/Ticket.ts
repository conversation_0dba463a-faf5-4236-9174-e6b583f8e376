import type {
  TicketDefDetailVO,
  TicketInstDetailVO,
  TicketInstListItemVO,
  TicketInstOperationLogVO,
  TicketInstProcessReqVO,
  TicketInstQueryReqVO,
} from '@/apiv2/workflow/models'

import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'

import { ticketApi, ticketConfigApi } from '@/api.services/workflow.service'
import {
  TicketInstListItemVOStatusEnum,
  TicketInstProcessReqVOOpTypeEnum,
  TicketInstQueryReqVOIncludedTicketStatusListEnum,
  TicketInstQueryReqVOIncludedTicketUserStatusListEnum,
} from '@/apiv2/workflow/models'
import { useAxiosCancelTokenStore } from '@/stores/AxiosCancelToken'
import { getPriorityText } from '@/views/Ticket/utils/formatters'
import { parseFormData, transformSchemaToCgOptions } from '@/views/Ticket/utils/ticketFormTransformer'
import axios from 'axios'
import { defineStore } from 'pinia'
import { useUserStore } from './User'

export interface ExtendedTicketInstListItemVO extends TicketInstListItemVO {
  // UI 展示所需的字段
  priorityText?: string
  statusText?: string
}

export interface ExtendedTaskInstDetailVO extends TicketInstDetailVO {
  // UI 展示所需的字段
  logs?: TicketInstOperationLogVO[]
  statusText?: string
  parsedFormData?: any
  relevantObjId?: string // 工单相关对象ID
  // 兼容现有UI的字段
  currentNode?: string
  assignee?: string
  createdAt?: string
  ticketId?: string
}

interface Pagination {
  pageNum: number
  pageSize: number
  total: number
}

interface StatusCounts {
  pending: number
  processing: number
  closed: number
}

export const useTicketStore = defineStore('ticket', () => {
  const currentStatusTab = ref<string>('pending')
  const searchQuery = ref<string>('')
  const tickets = ref<ExtendedTicketInstListItemVO[]>([])
  const pagination = ref<Pagination>({
    pageNum: 1,
    pageSize: 15,
    total: 0,
  })
  const statusCounts = ref<StatusCounts>({
    pending: 0,
    processing: 0,
    closed: 0,
  })
  const isLoadingList = ref<boolean>(false)
  const isLoadingCounts = ref<boolean>(false)
  const selectedTicketId = ref<string | number | null>(null)
  const selectedTicketDetail = ref<ExtendedTaskInstDetailVO | null>(null)
  const selectedTicketDefId = ref<string | number | null>(null) // 当前选中工单的类型ID
  const cachedTicketDefs = ref<Record<string, TicketDefDetailVO>>({}) // 缓存工单类型定义
  const ticketFormOptions = ref<CgFormOption[][]>([]) // 表单配置
  const currentFormData = ref<any>(null) // 当前工单表单数据
  const isLoadingDetail = ref<boolean>(false)
  const isLoadingDefinition = ref<boolean>(false) // 加载工单定义的状态
  const isEditingForm = ref<boolean>(false)
  const bulkSelectedIds = ref<(string | number)[]>([])
  const error = ref<string | null>(null)

  const isTicketSelected = computed(() => selectedTicketId.value !== null)
  const canBulkOperate = computed(() => bulkSelectedIds.value.length > 0)
  const currentEditingFormData = computed(() => {
    return selectedTicketDetail.value ? selectedTicketDetail.value.parsedFormData : null
  })
  const userStore = useUserStore()

  function resetSelection() {
    selectedTicketId.value = null
    selectedTicketDetail.value = null
    ticketFormOptions.value = []
    isEditingForm.value = false
    isLoadingDetail.value = false
    currentFormData.value = null
  }

  // 获取工单状态计数
  async function fetchStatusCounts() {
    isLoadingCounts.value = true
    error.value = null

    try {
      const response = await ticketApi.countByStatus({ ticketInstStatusCountReqVO: {
        userIds: [userStore.userId!],
      } })

      if (response.data && response.data.data && response.data.data.length > 0) {
        const countData = response.data.data[0].count || {}

        statusCounts.value = {
          pending: countData.TO_BE_PROCESSED || 0,
          processing: countData.PROCESSED_BEFORE || 0,
          closed: countData.COMPLETED || 0,
        }
      }
    }
    catch (err: any) {
      console.error('获取工单状态计数失败:', err)
    }
    finally {
      isLoadingCounts.value = false
    }
  }

  // 获取工单列表
  async function fetchTickets() {
    isLoadingList.value = true
    error.value = null
    resetSelection()
    bulkSelectedIds.value = []
    tickets.value = []

    try {
      const queryParams: TicketInstQueryReqVO = {
        pageNum: pagination.value.pageNum,
        pageSize: pagination.value.pageSize,
        keyword: searchQuery.value,
        includedTicketStatusList: currentStatusTab.value === 'pending'
          ? [TicketInstQueryReqVOIncludedTicketStatusListEnum.PROCESSING]
          : currentStatusTab.value === 'processing'
            ? [TicketInstQueryReqVOIncludedTicketStatusListEnum.PROCESSING]
            : currentStatusTab.value === 'closed'
              ? [TicketInstQueryReqVOIncludedTicketStatusListEnum.COMPLETED]
              : undefined,
        includedTicketUserStatusList: currentStatusTab.value === 'pending'
          ? [TicketInstQueryReqVOIncludedTicketUserStatusListEnum.TO_BE_PROCESSED]
          : undefined,
        orderBy: 'priority desc,create_time desc',
        userId: userStore.userId!,
      }

      const response = await ticketApi.query({ ticketInstQueryReqVO: queryParams })

      if (response.data && response.data.data?.records) {
        // 只添加UI所需的额外字段
        tickets.value = response.data.data.records.map(item => ({
          ...item,
          priorityText: getPriorityText(item.priority),
          statusText: getStatusText(item.status),
        }))
        pagination.value.total = response.data.data.total || 0
      }
      else {
        tickets.value = []
        pagination.value.total = 0
      }
    }
    catch (err: any) {
      error.value = err.message || '获取工单列表失败'
      tickets.value = []
      pagination.value.total = 0
    }
    finally {
      isLoadingList.value = false
    }
  }

  // 获取状态文本
  function getStatusText(status?: number): string {
    if (status === undefined || status === null)
      return ''

    switch (status) {
      case TicketInstListItemVOStatusEnum.PROCESSING: return '处理中'
      case TicketInstListItemVOStatusEnum.COMPLETED: return '已关闭'
      default: return status.toString()
    }
  }

  // 设置搜索查询
  function setSearchQuery(query: string) {
    searchQuery.value = query
    pagination.value.pageNum = 1
    fetchTickets()
  }

  // 设置状态 Tab
  function setStatusTab(status: string) {
    currentStatusTab.value = status
    searchQuery.value = ''
    pagination.value.pageNum = 1
    fetchTickets()
  }

  // 设置页码
  function setPage(page: number) {
    pagination.value.pageNum = page
    fetchTickets()
  }

  // 设置每页大小
  function setPageSize(size: number) {
    if (pagination.value.pageSize !== size) {
      pagination.value.pageSize = size
      pagination.value.pageNum = 1
      fetchTickets()
    }
  }

  // 更新批量选择的 ID
  function updateBulkSelection(selectedIds: (string | number)[]) {
    bulkSelectedIds.value = selectedIds
  }

  // 选择工单并获取详情
  async function selectTicket(id: string | number) {
    if (selectedTicketId.value === id)
      return

    const axiosCancelTokenStore = useAxiosCancelTokenStore()

    // 取消所有相关的请求
    axiosCancelTokenStore.cancelItems.forEach((item) => {
      if (/\/api\/ticket-instances\/\d+$/.test(item.url)) {
        item.cancel()
        return
      }

      if (/\/api\/ticket-instances\/\d+\/logs/.test(item.url)) {
        item.cancel()
        return
      }

      if (/\/api\/ticket-definitions\/\d+/.test(item.url)) {
        item.cancel()
      }
    })

    selectedTicketId.value = id
    isLoadingDetail.value = true
    isEditingForm.value = false
    error.value = null
    selectedTicketDetail.value = null
    ticketFormOptions.value = []
    selectedTicketDefId.value = null

    try {
      // 获取工单详情
      const numericId = typeof id === 'string' ? Number.parseInt(id, 10) : id
      const detailResponse = await ticketApi.getDetailById({ ticketInstId: numericId })

      // 如果在请求过程中选择了其他工单，则放弃处理
      if (selectedTicketId.value !== id) {
        return
      }

      if (detailResponse.data?.data) {
        // 获取工单操作日志
        const logsResponse = await ticketApi.queryLogs({ ticketInstId: numericId })

        if (selectedTicketId.value !== id) {
          return
        }

        const taskDetail = detailResponse.data.data

        const detail: ExtendedTaskInstDetailVO = {
          ...taskDetail,
          parsedFormData: parseFormData(taskDetail.formData),
          statusText: getStatusText(taskDetail.ticketStatus),
          relevantObjId: taskDetail.relevantObjId,
          // 兼容现有UI的字段
          currentNode: taskDetail.currentNodeName,
          assignee: taskDetail.currentNodeProcessor?.[0]?.name || '',
          createdAt: taskDetail.createTime,
          ticketId: taskDetail.ticketInstId?.toString(),
        }

        // 处理日志数据
        if (logsResponse.data?.data) {
          detail.logs = logsResponse.data.data
        }

        selectedTicketDetail.value = detail
        currentFormData.value = detail.parsedFormData

        // 从列表中获取工单类型ID
        const ticketDefId = taskDetail.ticketDefId
        if (!ticketDefId) {
          ticketFormOptions.value = []
          return
        }
        selectedTicketDefId.value = ticketDefId

        // 获取工单类型定义并生成表单配置
        await fetchTicketDefinition(ticketDefId)
      }
      else {
        ticketFormOptions.value = []
      }
    }
    catch (err: any) {
      if (axios.isCancel(err)) {
        console.error('请求已取消')
        return
      }

      if (selectedTicketId.value === id) {
        error.value = err.message || '获取工单详情失败'
        selectedTicketId.value = null
      }
    }
    finally {
      if (selectedTicketId.value === id) {
        isLoadingDetail.value = false
      }
    }
  }

  // 获取工单类型定义
  async function fetchTicketDefinition(ticketDefId: string | number) {
    const numericTicketDefId = typeof ticketDefId === 'string' ? Number.parseInt(ticketDefId, 10) : ticketDefId
    if (!ticketDefId) {
      ticketFormOptions.value = []
      return
    }

    const currentSelectedId = selectedTicketId.value

    // 检查缓存中是否已存在该工单类型定义
    const cacheKey = ticketDefId.toString()
    if (cachedTicketDefs.value[cacheKey]) {
      // 使用缓存的工单类型定义生成表单配置
      generateAndApplyFormConfig(cachedTicketDefs.value[cacheKey])
      return
    }

    // 缓存中不存在，需要从服务器获取
    isLoadingDefinition.value = true
    try {
      const formConfigResponse = await ticketConfigApi.getDetailById1({ ticketDefId: numericTicketDefId })

      if (selectedTicketId.value !== currentSelectedId) {
        return
      }

      if (formConfigResponse.data?.data) {
        // 缓存工单类型定义
        cachedTicketDefs.value[cacheKey] = formConfigResponse.data.data

        // 生成表单配置
        generateAndApplyFormConfig(formConfigResponse.data.data)
      }
      else {
        ticketFormOptions.value = []
      }
    }
    catch (err) {
      if (axios.isCancel(err)) {
        console.error('请求已取消')
        return
      }

      if (selectedTicketId.value === currentSelectedId) {
        console.error('Failed to fetch form config:', err)
        ticketFormOptions.value = []
      }
    }
    finally {
      if (selectedTicketId.value === currentSelectedId) {
        isLoadingDefinition.value = false
      }
    }
  }

  // 生成并应用表单配置
  function generateAndApplyFormConfig(ticketDef: TicketDefDetailVO) {
    if (!ticketDef || !ticketDef.ticketRelatedFormDef?.schema || !selectedTicketDetail.value) {
      ticketFormOptions.value = []
      return
    }

    // 获取当前节点ID
    const currentNodeId = selectedTicketDetail.value.currentNodeId || ''

    // 转换Schema为CgForm组件所需的配置格式，并应用权限配置
    const options = transformSchemaToCgOptions(
      ticketDef.ticketRelatedFormDef.schema,
      ticketDef.formFieldAuthList,
      currentNodeId,
    )

    ticketFormOptions.value = options
  }

  // 设置表单编辑状态
  function setEditingForm(isEditing: boolean) {
    if (!selectedTicketDetail.value)
      return
    isEditingForm.value = isEditing
  }

  // 保存表单数据
  async function saveTicketForm(description: string = ''): Promise<boolean> {
    if (!selectedTicketDetail.value || !selectedTicketId.value) {
      return false
    }
    isLoadingDetail.value = true
    error.value = null
    try {
      // 假设 CgForm 验证已在调用此函数前完成
      const numericId = typeof selectedTicketId.value === 'string'
        ? Number.parseInt(selectedTicketId.value, 10)
        : selectedTicketId.value

      const processReq: TicketInstProcessReqVO = {
        opType: TicketInstProcessReqVOOpTypeEnum.SUBMIT, // 提交表单操作
        // nodeId: selectedTicketDetail.value.currentNodeId || '', // TODO 怎么不要id了？
        formData: JSON.stringify(selectedTicketDetail.value.parsedFormData),
        opDesc: description,
      }

      // 1️⃣ 执行提交操作
      const res = await ticketApi.process({
        ticketInstId: numericId,
        ticketInstProcessReqVO: processReq,
      })

      if (!res.data?.success) {
        error.value = res.data?.message || '保存表单失败'
        return false
      }

      // 2️⃣ 获取最新详情
      const { data } = await ticketApi.getDetailById({ ticketInstId: numericId })
      if (!data?.data)
        return true
      const updated = data.data

      // 2.1️⃣ 获取最新的工单日志
      const logsResponse = await ticketApi.queryLogs({ ticketInstId: numericId })

      // 3️⃣ 更新计数（异步）
      fetchStatusCounts()

      // 关闭编辑模式
      isEditingForm.value = false

      // 4️⃣ 局部更新当前列表中的工单（如果存在）
      const idx = tickets.value.findIndex(t => t.id === numericId)
      if (idx >= 0) {
        tickets.value[idx] = {
          ...tickets.value[idx],
          status: updated.ticketStatus,
          statusText: getStatusText(updated.ticketStatus),
          priority: updated.priority,
          priorityText: getPriorityText(updated.priority),
        }
      }

      // 5️⃣ 刷新右侧详情（无论工单是否在当前列表中）
      selectedTicketDetail.value = {
        ...selectedTicketDetail.value!,
        ...updated,
        statusText: getStatusText(updated.ticketStatus),
        // 兼容现有UI的字段 - 确保当前节点任务正确更新
        currentNode: updated.currentNodeName,
        assignee: updated.currentNodeProcessor?.[0]?.name || '',
        // 更新工单日志数据
        logs: logsResponse.data?.data || selectedTicketDetail.value?.logs || [],
      }
      return true
    }
    catch (err: any) {
      error.value = err.data?.message || '保存表单时发生错误'
      return false
    }
    finally {
      isLoadingDetail.value = false
    }
  }

  // 执行单个工单操作
  async function performAction(action: TicketInstProcessReqVOOpTypeEnum, payload?: any): Promise<boolean> {
    if (!selectedTicketId.value) {
      return false
    }
    isLoadingDetail.value = true
    error.value = null
    try {
      const numericId = typeof selectedTicketId.value === 'string'
        ? Number.parseInt(selectedTicketId.value, 10)
        : selectedTicketId.value

      let res

      // 1️⃣   执行动作
      if (action === TicketInstProcessReqVOOpTypeEnum.TRANSFER) {
        const transferReq = {
          assignee: Number(payload?.target),
          opDesc: payload?.description || '',
        }
        res = await ticketApi.transfer({ ticketInstId: numericId, ticketInstTransferReqVO: transferReq })
      }
      else {
        const processReq: TicketInstProcessReqVO = {
          opType: action,
          opDesc: payload?.description || '',
          ...(action === TicketInstProcessReqVOOpTypeEnum.APPROVE && payload?.result !== undefined
            ? { opResult: payload.result }
            : {}),
        }
        res = await ticketApi.process({ ticketInstId: numericId, ticketInstProcessReqVO: processReq })
      }

      if (!res.data?.success) {
        error.value = res.data?.message || '操作失败'
        return false
      }

      // 2️⃣   取最新详情（拿到最新状态）
      const { data } = await ticketApi.getDetailById({ ticketInstId: numericId })
      if (!data?.data)
        return true
      const updated = data.data

      // 2.1️⃣ 获取最新的工单日志
      const logsResponse = await ticketApi.queryLogs({ ticketInstId: numericId })

      // 3️⃣   更新计数，保证 Tab 徽标即时变化（异步，不阻塞 UI）
      fetchStatusCounts()

      // 4️⃣   局部更新当前列表中的工单（如果存在）
      const idx = tickets.value.findIndex(t => t.id === numericId)
      if (idx >= 0) {
        tickets.value[idx] = {
          ...tickets.value[idx],
          status: updated.ticketStatus,
          statusText: getStatusText(updated.ticketStatus),
          priority: updated.priority,
          priorityText: getPriorityText(updated.priority),
        }
      }

      // 5️⃣   刷新右侧详情（无论工单是否在当前列表中）
      selectedTicketDetail.value = {
        ...selectedTicketDetail.value!,
        ...updated,
        statusText: getStatusText(updated.ticketStatus),
        // 兼容现有UI的字段 - 确保当前节点任务正确更新
        currentNode: updated.currentNodeName,
        assignee: updated.currentNodeProcessor?.[0]?.name || '',
        // 更新工单日志数据
        logs: logsResponse.data?.data || selectedTicketDetail.value?.logs || [],
      }
      return true
    }
    catch (e: any) {
      error.value = e.message || '执行操作异常'
      return false
    }
    finally {
      isLoadingDetail.value = false
    }
  }

  // 执行批量操作
  async function performBulkAction(action: TicketInstProcessReqVOOpTypeEnum, payload?: any): Promise<boolean> {
    if (bulkSelectedIds.value.length === 0) {
      return false
    }
    isLoadingList.value = true
    error.value = null
    try {
      // 注意：当前API不支持批量操作，需要逐个处理
      let allSuccess = true
      const processedIds: number[] = []

      for (const id of bulkSelectedIds.value) {
        const numericId = typeof id === 'string' ? Number.parseInt(id, 10) : id

        const processReq: TicketInstProcessReqVO = {
          opType: action,
          // nodeId: payload?.nodeId || '', // TODO 怎么不要id了？
          opDesc: payload?.description || '批量操作',
          ...(action === TicketInstProcessReqVOOpTypeEnum.APPROVE && payload?.result !== undefined
            ? { opResult: payload.result }
            : {}),
        }

        const response = await ticketApi.process({
          ticketInstId: numericId,
          ticketInstProcessReqVO: processReq,
        })

        if (!response.data?.success) {
          allSuccess = false
          error.value = response.data?.message || `执行批量操作 ${action} 失败`
          break
        }

        processedIds.push(numericId)
      }

      // 更新计数（异步）
      fetchStatusCounts()

      if (allSuccess) {
        bulkSelectedIds.value = []

        // 重新加载当前Tab的工单列表
        await fetchTickets()

        // 如果之前有选中的工单，尝试重新选中（这会完整地重新获取工单详情和日志）
        if (selectedTicketId.value) {
          const stillExists = tickets.value.some((t) => {
            const ticketId = typeof t.id === 'string' ? Number.parseInt(t.id, 10) : t.id
            const selectedId = typeof selectedTicketId.value === 'string'
              ? Number.parseInt(selectedTicketId.value, 10)
              : selectedTicketId.value
            return ticketId === selectedId
          })

          if (stillExists) {
            await selectTicket(selectedTicketId.value)
          }
        }

        return true
      }
      else {
        isLoadingList.value = false
        return false
      }
    }
    catch (err: any) {
      error.value = err.message || `执行批量操作 ${action} 时发生错误`
      isLoadingList.value = false
      return false
    }
  }

  return {
    // State
    currentStatusTab,
    searchQuery,
    tickets,
    pagination,
    statusCounts,
    isLoadingList,
    isLoadingCounts,
    selectedTicketId,
    selectedTicketDetail,
    selectedTicketDefId,
    cachedTicketDefs,
    ticketFormOptions,
    currentFormData,
    isLoadingDetail,
    isLoadingDefinition,
    isEditingForm,
    bulkSelectedIds,
    error,

    // Getters (Computed)
    isTicketSelected,
    canBulkOperate,
    currentEditingFormData,

    // Actions (Functions)
    fetchTickets,
    fetchStatusCounts,
    setSearchQuery,
    setStatusTab,
    setPage,
    setPageSize,
    updateBulkSelection,
    selectTicket,
    fetchTicketDefinition,
    generateAndApplyFormConfig,
    resetSelection,
    setEditingForm,
    saveTicketForm,
    performAction,
    performBulkAction,
  }
})
