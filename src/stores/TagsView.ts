import type { RouteLocationNamedRaw, RouteLocationNormalized } from 'vue-router'
import router from '@/router'
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useTagViewStore = defineStore('tagView', () => {
  // state
  const count = ref(0)
  const visitedViews = ref<(RouteLocationNormalized & { title: any })[]>([])
  const cachedViews = ref<any[]>(['Home'])

  // actions
  function increment() {
    count.value++
  }

  function changeTitle(route: RouteLocationNamedRaw, title: string) {
    const resolvedRoute = router.resolve(route)
    const path = resolvedRoute.path

    // 使用完整路径匹配标签页
    const view = visitedViews.value.find(v => v.path === path)

    if (view) {
      view.title = title
    }
    else {
      const routes = router.getRoutes()
      const view = routes.find(v => v.name === route.name)
      if (view)
        view.meta.title = title
    }
  }

  function addView(view: { route: RouteLocationNormalized, visibleLength: number }) {
    addVisitedView(view)
    addCachedView(view.route)
    return view
  }

  function addVisitedView(view: { route: RouteLocationNormalized, visibleLength: number }) {
    const { route, visibleLength } = view
    const index = visitedViews.value.findIndex(v => v.path === route.path)
    if (index !== -1) {
      // 隐藏tag 需要显示
      if (index > visibleLength) {
        // 交换两个元素位置
        visitedViews.value.splice(
          visibleLength,
          1,
          ...visitedViews.value.splice(index, 1, visitedViews.value[visibleLength]),
        )
      }
      return
    }

    // 处理动态标题
    let title = route.meta?.title
    if (typeof title === 'function') {
      title = title(route)
    }
    const routeClone = { ...route, title: title || 'no-name' }
    if (visitedViews.value.length >= visibleLength)
      visitedViews.value.splice(visibleLength, 0, routeClone)
    else
      visitedViews.value.push(routeClone)
  }

  function addCachedView(view: RouteLocationNormalized) {
    if (cachedViews.value.includes(view.name))
      return
    if (!view.meta?.noCache)
      cachedViews.value.push(view.name)
  }

  function sortVisitedView(view: { route: RouteLocationNormalized, visibleLength: number }) {
    const { route, visibleLength } = view
    const index = visitedViews.value.findIndex(v => v.path === route.path)
    if (index > visibleLength)
      visitedViews.value[index] = visitedViews.value.splice(visibleLength, 1, visitedViews.value[index])[0]
  }

  function delView(view: RouteLocationNormalized) {
    return new Promise((resolve) => {
      delVisitedView(view)
      delCachedView(view)
      resolve({
        visitedViews: visitedViews.value,
        cachedViews: cachedViews.value,
      })
    })
  }

  function delVisitedView(view: RouteLocationNormalized) {
    return new Promise((resolve) => {
      for (const [i, v] of visitedViews.value.entries()) {
        if (v.path === view.path) {
          visitedViews.value.splice(i, 1)
          break
        }
      }
      resolve(visitedViews.value)
    })
  }

  function delCachedView(view: RouteLocationNormalized) {
    return new Promise((resolve) => {
      const index = cachedViews.value.indexOf(view.name)
      index > -1 && cachedViews.value.splice(index, 1)
      resolve(cachedViews.value)
    })
  }

  function delOthersViews(view: RouteLocationNormalized) {
    return new Promise((resolve) => {
      delOthersVisitedViews(view)
      delOthersCachedViews(view)
      resolve({
        visitedViews: [...visitedViews.value],
        cachedViews: [...cachedViews.value],
      })
    })
  }

  function delOthersVisitedViews(view: RouteLocationNormalized) {
    return new Promise((resolve) => {
      visitedViews.value = visitedViews.value.filter((v) => {
        return v.meta?.affix || v.path === view.path
      })
      resolve(visitedViews.value)
    })
  }

  function delOthersCachedViews(view: RouteLocationNormalized) {
    return new Promise((resolve) => {
      const index = cachedViews.value.indexOf(view.name)
      if (index > -1) {
        cachedViews.value = cachedViews.value.slice(index, index + 1)
      }
      else {
        // if index = -1, there is no cached tags
        cachedViews.value = ['Home']
      }
      resolve(cachedViews.value)
    })
  }

  function delAllViews() {
    return new Promise((resolve) => {
      delAllVisitedViews()
      delAllCachedViews()
      resolve({
        visitedViews: visitedViews.value,
        cachedViews: cachedViews.value,
      })
    })
  }

  function delAllVisitedViews() {
    return new Promise((resolve) => {
      const affixTags = visitedViews.value.filter(tag => tag.meta?.affix)
      visitedViews.value = affixTags
      resolve(visitedViews.value)
    })
  }

  function delAllCachedViews() {
    return new Promise((resolve) => {
      cachedViews.value = ['Home']
      resolve(cachedViews.value)
    })
  }

  function updateVisitedView(view: RouteLocationNormalized) {
    for (let i = 0; i < visitedViews.value.length; i++) {
      if (visitedViews.value[i].path === view.path) {
        // 处理动态标题
        let title = view.meta?.title
        if (typeof title === 'function') {
          title = title(view)
        }

        // 更新视图对象
        visitedViews.value[i] = Object.assign(visitedViews.value[i], view, { title: title || visitedViews.value[i].title })
        break
      }
    }
  }

  function resetTagsView() {
    delAllVisitedViews()
    delAllCachedViews()
  }

  return {
    // state
    count,
    visitedViews,
    cachedViews,

    // actions
    increment,
    changeTitle,
    addView,
    addVisitedView,
    addCachedView,
    sortVisitedView,
    delView,
    delVisitedView,
    delCachedView,
    delOthersViews,
    delOthersVisitedViews,
    delOthersCachedViews,
    delAllViews,
    delAllVisitedViews,
    delAllCachedViews,
    updateVisitedView,
    resetTagsView,
  }
})
