import { defineStore } from 'pinia'

interface CancelItem {
  url: string
  cancel: () => void
}

export const useAxiosCancelTokenStore = defineStore('axiosCancelToken', () => {
  const cancelItems = ref<CancelItem[]>([])

  function addCancel(cancel: CancelItem) {
    cancelItems.value.push(cancel)
  }

  function deleteCancel(url: string) {
    const index = cancelItems.value.findIndex(item => item.url === url)
    if (index > -1) {
      cancelItems.value.splice(index, 1)
    }
  }

  function axiosCancel() {
    cancelItems.value.forEach(item => item.cancel())
    cancelItems.value = []
  }

  function clearCancel() {
    cancelItems.value = []
  }

  return {
    cancelItems,
    addCancel,
    deleteCancel,
    axiosCancel,
    clearCancel,
  }
})
