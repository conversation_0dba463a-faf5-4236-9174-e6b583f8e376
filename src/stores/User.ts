import type { CaptchaValueVO, UserLoginDetailVO, UserLoginReq } from '@/apiv2/user/models'
import userApi from '@/api.services/user.service'
import router from '@/router'
import { defineStore } from 'pinia'

const USER_DATA_KEY = 'user_data'

interface UserData {
  name: string
  avatar: string
  userId: number | null
  userName: string
  permissions: string[]
}

function getUserDataFromStorage(): UserData | null {
  const userDataStr = localStorage.getItem(USER_DATA_KEY)
  if (!userDataStr)
    return null

  try {
    return JSON.parse(userDataStr) as UserData
  }
  catch (e) {
    console.error('解析用户数据失败', e)
    return null
  }
}

export const useUserStore = defineStore('user', () => {
  const storedUserData = getUserDataFromStorage()

  const name = ref(storedUserData?.name || '')
  const userName = ref(storedUserData?.userName || '')
  const avatar = ref(storedUserData?.avatar || '')
  const token = ref(localStorage.getItem('token') || '')
  const userId = ref<number | null>(storedUserData?.userId || null)
  const permissions = ref<string[]>(storedUserData?.permissions || [])
  const loginLoading = ref(false)
  const loginError = ref('')
  const captchaInfo = ref<CaptchaValueVO | null>(null)

  /**
   * 设置令牌
   * @param newToken 新令牌
   */
  function setToken(newToken: string) {
    token.value = newToken
    // 设置cookie，Domain为.caguuu.cn（顶级+1域及其所有子域）
    document.cookie = `GAIA_TOKEN=${newToken}; path=/; domain=.caguuu.cn; secure; SameSite=None`
    localStorage.setItem('token', newToken)
  }

  /**
   * 重置令牌
   */
  function resetToken() {
    token.value = ''
    document.cookie = 'GAIA_TOKEN=; path=/; domain=.caguuu.cn; expires=Thu, 01 Jan 1970 00:00:00 GMT; secure; SameSite=None'
    localStorage.removeItem('token')
  }

  /**
   * 保存用户数据到本地存储
   */
  function saveUserDataToStorage() {
    const userData: UserData = {
      name: name.value,
      avatar: avatar.value,
      userId: userId.value,
      userName: userName.value,
      permissions: permissions.value,
    }
    localStorage.setItem(USER_DATA_KEY, JSON.stringify(userData))
  }

  /**
   * 清除本地存储的用户数据
   */
  function clearUserDataFromStorage() {
    localStorage.removeItem(USER_DATA_KEY)
  }

  /**
   * 设置用户信息
   * @param userInfo 用户信息
   */
  function setUserInfo(userInfo: UserLoginDetailVO) {
    if (userInfo.name) {
      name.value = userInfo.name
    }
    if (userInfo.userName) {
      userName.value = userInfo.userName
    }
    if (userInfo.userId) {
      userId.value = userInfo.userId
    }
    if (userInfo.permissionCodes) {
      permissions.value = userInfo.permissionCodes
    }

    saveUserDataToStorage()
  }

  /**
   * 生成登录验证码
   * @returns 验证码信息
   */
  async function generateCaptcha() {
    try {
      const response = await userApi.generateCaptcha()
      if (response.data && response.data.success && response.data.data) {
        captchaInfo.value = response.data.data
        return response.data.data
      }
      return null
    }
    catch (error: any) {
      loginError.value = error.message || '获取验证码失败'
      return null
    }
  }

  /**
   * 用户登录
   * @param loginData 登录信息
   * @param redirectPath 可选的重定向路径
   */
  async function login(loginData: UserLoginReq, redirectPath?: string) {
    loginLoading.value = true
    loginError.value = ''

    try {
      const response = await userApi.login({ userLoginReq: loginData })

      if (response.data && response.data.success && response.data.data) {
        const userData = response.data.data

        if (userData.jwt) {
          setToken(userData.jwt)
        }

        setUserInfo(userData)

        // 如果有重定向路径，则跳转到该路径，否则跳转到默认页面
        router.push(redirectPath || '/product/list')

        return true
      }
      else {
        loginError.value = response.data?.message || '登录失败，请重试'
        return false
      }
    }
    catch (error: any) {
      loginError.value = error.message || '登录失败，请重试'
      return false
    }
    finally {
      loginLoading.value = false
    }
  }

  /**
   * 用户登出
   */
  async function logout() {
    try {
      if (token.value) {
        await userApi.logout({ jwt: token.value })
      }
    }
    catch (error) {
      console.error('登出时发生错误', error)
    }
    finally {
      resetToken()
      clearUserDataFromStorage()

      name.value = ''
      avatar.value = ''
      userId.value = null
      permissions.value = []

      router.push('/login')
    }
  }

  /**
   * 检查是否已登录
   */
  function isLoggedIn() {
    return !!token.value
  }

  return {
    name,
    userName,
    avatar,
    token,
    userId,
    permissions,
    loginLoading,
    loginError,
    captchaInfo,
    setToken,
    resetToken,
    setUserInfo,
    saveUserDataToStorage,
    clearUserDataFromStorage,
    generateCaptcha,
    login,
    logout,
    isLoggedIn,
  }
})
