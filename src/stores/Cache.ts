import type { PlatformVO } from '@/apiv2/product'
import { commodityApi } from '@/api.services/product.service'

interface ShopInfo {
  id: string
  name: string
  logo?: string
  [key: string]: any
}

export const useCacheStore = defineStore('cache', () => {
  // 商店信息
  const shopInfo = ref<ShopInfo | null>(null)
  // 平台列表
  const platformList = ref<PlatformVO[]>([])
  const platformLoading = ref(false)

  /**
   * 缓存商店信息
   */
  function cacheShopInfo() {
    // 示例数据
    shopInfo.value = {
      id: '1',
      name: 'Caguuu 跨境电商',
      logo: '/logo.png',
    }
  }

  /**
   * 重置商店信息
   */
  function resetShopInfo() {
    shopInfo.value = null
  }

  /**
   * 获取并缓存平台列表
   * @param forceRefresh 是否强制刷新缓存
   */
  async function fetchPlatformList(forceRefresh = false) {
    // 如果已经加载过且不强制刷新，则直接返回
    if (!forceRefresh && platformList.value.length > 0) {
      return
    }
    // 如果正在加载中，则不重复发起请求
    if (platformLoading.value) {
      return
    }

    platformLoading.value = true
    try {
      const { data } = (await commodityApi.listPlatforms()).data
      platformList.value = data || []
    }
    catch (error) {
      console.error('获取平台列表失败 (Cache Store):', error)
      platformList.value = []
    }
    finally {
      platformLoading.value = false
    }
  }

  /**
   * 重置平台列表
   */
  function resetPlatformList() {
    platformList.value = []
  }

  /**
   * 获取适用于 SelectV2 的平台选项
   */
  const getPlatformOptionsForSelect = computed(() => {
    return platformList.value
      .filter(p => p.enabled && p.id !== undefined && p.platformName)
      .map(p => ({
        label: p.platformName!,
        value: p.id!,
      }))
  })

  return {
    shopInfo,
    platformList,
    platformLoading,
    getPlatformOptionsForSelect,
    cacheShopInfo,
    resetShopInfo,
    fetchPlatformList,
    resetPlatformList,
  }
})
