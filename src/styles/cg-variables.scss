@use 'sass:color';

$cg-color-primary: #399e96 !default;
$cg-color-primary-dark: #2c7a74 !default;
$cg-color-success: #67c23a !default;
$cg-color-warning: #e6a23c !default;
$cg-color-danger: #f56c6c !default;
$cg-color-info: #909399 !default;
$cg-color-white: #fff !default;
$cg-color-black: #000 !default;
$cg-color-primary-light-1: color.mix($cg-color-white, $cg-color-primary, 10%) !default;
$cg-color-primary-light-2: color.mix($cg-color-white, $cg-color-primary, 20%) !default;
$cg-color-primary-light-3: color.mix($cg-color-white, $cg-color-primary, 30%) !default;
$cg-color-primary-light-4: color.mix($cg-color-white, $cg-color-primary, 40%) !default;
$cg-color-primary-light-5: color.mix($cg-color-white, $cg-color-primary, 50%) !default;
$cg-color-primary-light-6: color.mix($cg-color-white, $cg-color-primary, 60%) !default;
$cg-color-primary-light-7: color.mix($cg-color-white, $cg-color-primary, 70%) !default;
$cg-color-primary-light-8: color.mix($cg-color-white, $cg-color-primary, 80%) !default;
$cg-color-primary-light-9: color.mix($cg-color-white, $cg-color-primary, 90%) !default;
$cg-font-family-base:
  Inter,
  PingFangSC-Regular,
  PingFang SC,
  -apple-system,
  BlinkMacSystemFont,
  Segoe UI,
  Hiragino Sans GB,
  Microsoft YaHei,
  Helvetica Neue,
  Helvetica,
  Arial,
  sans-serif,
  Apple Color Emoji,
  Segoe UI Emoji,
  Segoe UI Symbol;
$cg-font-size-base: 16px !default;
$cg-font-size-medium: 14px !default;
$cg-font-size-large: 18px !default;
$cg-font-size-thin: 12px !default;
$cg-font-size-extra-large: 20px !default;

$--border-radius-base: 0px !default;

/* Link
-------------------------- */
$cg-link-color: $cg-color-primary !default;
$cg-link-hover-color: $cg-color-primary-light-3 !default;
$cg-border-radius-base: $--border-radius-base !default;

// layout
$cg-layout-right-bgcolor: #f0f2f5 !default;
$cg-layout-right-padding: 0 12px 10px 12px;
// header
$cg-header-height: 38px !default;
// $cg-header-box-shadow:0px 4px 8px rgba(0, 0, 0, 0.12)  !default;
$cg-header-bg-color: var(--cg-header-bg-color) !default;

// sidebar
$cg-sidebar-width: 56px !default;
$cg-sidebar-text-color: var(--cg-sidebar-text-color) !default;
$cg-sidebar-bgcolor: var(--cg-sidebar-bgcolor) !default;
$cg-sidebar-font-size: 14px !default;
$cg-sidebar-padding-top: 22px !default;
$cg-sidebar-item-height: 64px !default;
$cg-sidebar-active-font-size: 14px !default;
// 菜单选中后的字体颜色
$cg-sidebar-current-color: var(--cg-sidebar-current-color) !default;
// 菜单选中后的背景色
$cg-sidebar-current-bgcolor: var(--cg-sidebar-current-bgcolor) !default;
// 菜单选中后字体粗细
$cg-sidebar-active-font-weight: 500 !default;
// 菜单激活的文本颜色
// $cg-sidebar-active-color:$cg-text-color-primary  !default;
// 菜单栏阴影
// $cg-sidebar-box-shadow:4px 0px 8px rgba(54, 54, 54, 0.15)  !default;
$cg-sidebar-submenu-font-size: 14px !default;
$cg-sidebar-submenu-color: var(--cg-sidebar-submenu-color) !default;
$cg-sidebar-submenu-text-align: left !default;
$cg-sidebar-submenu-bgcolor: var(--cg-sidebar-submenu-bgcolor) !default;
$cg-sidebar-submenu-title-color: var(--cg-sidebar-submenu-title-color) !default;
$cg-sidebar-submenu-width: 180px !default;
// $cg-sidebar-submenu-padding-left:15px  !default;
$cg-sidebar-submenu-hover-bgcolor: $cg-color-primary-light-9 !default;
$cg-sidebar-submenu-hover-color: var(--cg-sidebar-submenu-hover-color) !default;
$cg-sidebar-submenu-item-border-color: var(--cg-sidebar-submenu-item-border-color) !default;
$cg-sidebar-submenu-item-border-left: 1px $cg-sidebar-submenu-item-border-color solid !default;
$cg-sidebar-submenu-active-color: var(--cg-sidebar-submenu-active-color) !default;
// tagview
$cg-tagview-bgcolor: $cg-header-bg-color !default;
$cg-tagview--full-bgcolor: #e1e1e6 !default;
$cg-tagview-more-color: $cg-sidebar-text-color !default;
$cg-tagview-line-color: var(--cg-tagview-line-color) !default;
$cg-tagview-line-height: 20px !default;
$cg-tagview-item-color: $cg-sidebar-text-color !default;
$cg-tagview-item-font-size: 12px !default;

// $cg-tagview-item-border-radius:10px 10px 0px 0px  !default;
$cg-tagview-item-active-bg-color: var(--cg-tagview-item-active-bg-color) !default;
$cg-tagview-item-active-font-size: 12px !default;
$cg-tagview-item-active-color: var(--cg-tagview-item-active-color) !default;
$cg-tagview--full-item-active-color: #373742 !default;
// page-layout
// $cg-page-layout-box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.16) !default;
$cg-page-layout-padding: 10px !default;
$cg-page-layout-border-radius: 6px !default;
$cg-page-layout-bgcolor: #fff !default;
// grid
$cg-grid-empty-color: #888 !default;
$cg-grid-footer-bgcolor: #f9f9f9 !default;
$cg-grid-footer-height: 40px !default;
$cg-grid-complex-special-row-bgcolor: #f0f0f0 !default;
$cg-grid-complex-special-row-border: 1px solid $cg-color-primary-light-5 !default;
$cg-grid-complex-special-row-height: 40px !default;
$cg-grid-complex-special-row-vertical-align: middle !default;
$cg-grid-complex-loading-row-height: 18px !default;
$cg-grid-complex-more-bgcolor: $cg-color-primary-light-9 !default;

// messagebox
$cg-messagebox-border-radius: 5px !default;
$cg-messagebox-header-border-bottom: 1px solid #f2f2f2 !default;
$cg-messagebox-header-font-size: 16px !default;
$cg-messagebox-header-text-align: left !default;

$border-color: #e8eaec;
