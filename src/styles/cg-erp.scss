@use './cg-variables.scss' as *;

body,
body .vxe-table {
  font-family: $cg-font-family-base;
  overflow-x: auto;
}

.cg-tab-container {
  display: flex;
  flex-direction: column;
  height: 100%;

  .cg-tab-content {
    flex: 1;
  }

  .el-tabs .el-tabs__header {
    margin: 0;
    padding: 0 20px 0 20px;
  }
  .el-tabs--border-card {
    border: 0;
  }
  .el-tabs--border-card > .el-tabs__content {
    padding: 0;
  }
  .el-tabs--border-card > .el-tabs__header .el-tabs__item {
    font-weight: bold;
    color: #303133;
  }
}

.el-tabs__nav-wrap::after {
  height: 0px !important;
}

.el-tabs__header {
  border-bottom: 1px solid #e6e8eb;
}

.el-tabs__active-bar {
  height: 0px;
  border-bottom: 2px solid $cg-color-primary;
}

.el-textarea__inner {
  padding: 5px;
}
