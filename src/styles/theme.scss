:root {
  /* Element Plus 变量 */
  --el-text-color-regular: #424242;
  --el-font-size-base: 12px;
  --el-dialog-title-font-size: 16px;
  --el-checkbox-font-size: 12px;
  --el-text-color-placeholder: #a6abb4;

  /* VXE UI 变量 */
  --vxe-ui-table-column-padding-default: 3px 0;
  --vxe-ui-font-primary-color: #399e96;

  /* Caguuu 自定义变量 */
  /* 头部 */
  --cg-header-bg-color: #37474f;

  /* 侧边栏 */
  --cg-sidebar-text-color: #f0f0f0;
  --cg-sidebar-bgcolor: #263238;
  --cg-sidebar-current-color: #ffffff;
  --cg-sidebar-current-bgcolor: rgba(57, 158, 150, 0.2);

  /* 子菜单 */
  --cg-sidebar-submenu-color: #555555;
  --cg-sidebar-submenu-bgcolor: #ffffff;
  --cg-sidebar-submenu-title-color: #2c3e50;
  --cg-sidebar-submenu-hover-color: #399e96;
  --cg-sidebar-submenu-item-border-color: #eaedf0;
  --cg-sidebar-submenu-active-color: #399e96;

  /* 标签视图 */
  --cg-tagview-line-color: #dddfe08f;
  --cg-tagview-item-active-bg-color: #ffffff;
  --cg-tagview-item-active-color: #399e96;
}

.vxe-table {
  color: #0b1019;
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  width: 100%;
  font-size: 12px
}

.vxe-table .vxe-table--header-wrapper {
  color: #0b1019;
}


.vxe-table th {
  font-weight: 600;
  padding: unset !important;
}

.vxe-table .vxe-table--header-wrapper .vxe-table--header .vxe-header--row th {
  height: 40px;
}

.tox .tox-edit-area::before {
  border: 2px solid #399e96 !important;
}

.oneLine {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--el-input-text-color, var(--el-text-color-regular));
}

.color-placeholder {
  color: var(--el-text-color-placeholder);
}