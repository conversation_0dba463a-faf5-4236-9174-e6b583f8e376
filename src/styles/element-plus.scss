// src/styles/element/index.scss
/* 只需要重写你需要的即可 */
@forward 'element-plus/theme-chalk/src/common/var.scss' with ($colors: ('primary': ('base': #399e96,
    ),
  ));

// 如果只是按需导入，则可以忽略以下内容。
// 如果你想导入所有样式:
@use 'element-plus/theme-chalk/src/index.scss' as *;

.el-button--small {
  padding: 9px 12px;
  min-width: 32px;
  height: 32px
}

.cg-layout__main {
  .el-tabs__item {
    padding: 10px 16px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    line-height: 16px;
    height: unset;
    display: inline-block;
    list-style: none;
    font-size: 12px;
    font-weight: 400;
    color: #0b1019;
    position: relative;
    vertical-align: middle;
  }
}

.el-select-dropdown__item {
  font-size: 12px;
  padding: 0 12px;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #33363C;
  height: 32px;
  line-height: 32px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
}

.el-select__wrapper {
  font-size: 12px !important;
}