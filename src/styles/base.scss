@use './cg-variables.scss' as *;
html {
  font-weight: 100;
  overflow: hidden;
}

html,
body,
#app {
  height: 100%;
  font-weight: 400;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
  font-size: $cg-font-size-base;
  min-width: 1200px;
}

body {
  * {
    font-size: $cg-font-size-thin;
  }
}

/* 响应式调整 - 小屏幕 */
@media screen and (max-width: 1440px) {
  html {
    font-size: 14px;
  }
}

@media screen and (max-width: 1280px) {
  html {
    font-size: 13px;
  }
}

@media screen and (max-width: 1024px) {
  html {
    font-size: 12px;
    overflow: auto;
  }
  
  body, #app {
    overflow-y: auto;
  }
}

p {
  margin: 0;
}

a {
  text-decoration: none;
  color: $cg-color-primary;
}

img {
  vertical-align: middle;
  border-style: none;
}

* {
  box-sizing: border-box;
}

ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

/////// 滚动条样式 ///////
/* 滚动条整体部分 */
::-webkit-scrollbar {
  width: 9px;
  height: 9px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: rgba(240, 240, 240, 0.8);
  border-radius: 4px;
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
  background-color: rgba(150, 150, 150, 0.5);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
  transition: all 0.3s ease;
}

/* 滚动条滑块悬停和激活状态 */
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(130, 130, 130, 0.7);
}

::-webkit-scrollbar-thumb:active {
  background-color: rgba(100, 100, 100, 0.9);
}

/* 滚动条交汇处 */
::-webkit-scrollbar-corner {
  background-color: transparent;
}

.dark-theme {
  ::-webkit-scrollbar-track {
    background-color: rgba(30, 30, 30, 0.6);
  }
  
  ::-webkit-scrollbar-thumb {
    background-color: rgba(170, 170, 170, 0.5);
  }
  
  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(190, 190, 190, 0.7);
  }
}

/* Chrome, Safari, Edge, Opera 浏览器 */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox 浏览器 */
input[type="number"] {
  -moz-appearance: textfield;
}