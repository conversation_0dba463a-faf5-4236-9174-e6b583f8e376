import { useAxiosCancelTokenStore } from '@/stores/AxiosCancelToken'
import { useUserStore } from '@/stores/User'
import axios from 'axios'

const axiosInstance = axios.create({
  timeout: 150000,
  headers: { 'content-type': 'application/json' },
  withCredentials: false,
})

axiosInstance.interceptors.request.use(
  (config) => {
    if (!config.headers)
      config.headers = new axios.AxiosHeaders()

    const axiosCancelTokenStore = useAxiosCancelTokenStore()
    config.cancelToken = new axios.CancelToken((cancel) => {
      // 在发送请求设置cancel token
      config.url && axiosCancelTokenStore.addCancel({ url: config.url, cancel })
    })
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)
/**
  axios响应时拦截处理
 */
axiosInstance.interceptors.response.use(
  async (response) => {
    const axiosCancelTokenStore = useAxiosCancelTokenStore()
    axiosCancelTokenStore.deleteCancel(response.config.url || '')

    if (response.data.code === 401) {
      const userStore = useUserStore()
      userStore.logout()

      const [{ default: router }] = await Promise.all([import('@/router')])
      const currentRoute = router.currentRoute.value
      router.replace({ name: 'login', query: { redirect: currentRoute.fullPath } })

      return Promise.reject(response)
    }

    if (response.data.code === 200) {
      // 统一处理状态
      return Promise.resolve(response)
    }
    else {
      ElMessage({
        message: response.data.message,
        type: 'error',
        grouping: true,
      })
      return Promise.reject(response)
    }
  },
  // 错误处理
  (error) => {
    if (axios.isCancel(error)) {
      // 请求主动取消
      return Promise.reject(error)
    }
    const axiosCancelTokenStore = useAxiosCancelTokenStore()
    if (error?.response) {
      axiosCancelTokenStore.deleteCancel(error.response.config.url || '')
      switch (error.response.status) {
        case 400:
          error.message = '错误请求'
          break
        case 401:
          error.message = '登录已过期，请重新登录'
          break
        case 403:
          error.message = '拒绝访问'
          break
        case 404:
          error.message = '请求错误,未找到该资源'
          break
        case 500:
          error.message = '服务器端出错'
          break
        default:
          error.message = `未知错误${error.response.code}`
      }
    }
    else {
      axiosCancelTokenStore.clearCancel()
      error.message = '连接到服务器失败'
    }
    ElMessage({
      message: error.message,
      type: 'error',
      grouping: true,
    })
    return Promise.reject(error)
  },
)

export default axiosInstance
