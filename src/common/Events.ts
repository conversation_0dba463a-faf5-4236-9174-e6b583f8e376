/**
 * 添加事件监听
 * @param element 元素
 * @param event 事件名
 * @param handler 处理函数
 * @param options 选项
 */
export function addEvent(
  element: HTMLElement | Document | Window,
  event: string,
  handler: EventListenerOrEventListenerObject,
  options?: boolean | AddEventListenerOptions
) {
  if (element && event && handler) {
    element.addEventListener(event, handler, options)
  }
}

/**
 * 移除事件监听
 * @param element 元素
 * @param event 事件名
 * @param handler 处理函数
 * @param options 选项
 */
export function removeEvent(
  element: HTMLElement | Document | Window,
  event: string,
  handler: EventListenerOrEventListenerObject,
  options?: boolean | EventListenerOptions
) {
  if (element && event && handler) {
    element.removeEventListener(event, handler, options)
  }
}

export default {
  addEvent,
  removeEvent
}
