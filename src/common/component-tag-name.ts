import { ElInput } from 'element-plus'
import { markRaw } from 'vue'

export const ComponentTagName = {
  Input: {
    component: markRaw(ElInput),
    props: {
      class: 'w-200',
    },
  },
  Select: 'CgSelect',
  SelectV2: 'CgSelectV2',
  DatePicker: {
    component: 'CgDatePicker',
    props: {
      clearable: false,
      persistent: true,
    },
  },
  ComplexInput: {
    component: 'CgComplexInput',
    props: {
      defaultSelect: true,
      persistent: true,
    },
  },
  ComplexInputV2: 'complexInputV2',
  Dropdown: 'CgDropdown',
  Button: 'CgButton',
  InputNumber: 'CgInputNumber',
}
export default ComponentTagName
