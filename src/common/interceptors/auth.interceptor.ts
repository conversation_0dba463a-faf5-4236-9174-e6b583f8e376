import type { AxiosInstance } from 'axios'
import { useUserStore } from '@/stores/User'

export function setupAuthInterceptor(axiosInstance: AxiosInstance) {
  axiosInstance.interceptors.request.use(
    (config) => {
      const userStore = useUserStore()
      if (userStore.token)
        config.headers.Authorization = `Bearer ${userStore.token}`

      return config
    },
    (error) => {
      return Promise.reject(error)
    },
  )

  axiosInstance.interceptors.response.use(
    response => response,
    async (error) => {
      // 检查是否为401错误，并且不是登出接口
      if (error.response?.status === 401 && !error.config?.url?.includes('/api/users/logout')) {
        const userStore = useUserStore()
        await userStore.logout()
      }
      return Promise.reject(error)
    },
  )
}
