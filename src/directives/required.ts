import type { Directive, DirectiveBinding } from 'vue'

const required: Directive = {
  mounted(el: HTMLElement, _binding: DirectiveBinding) {
    // 创建星号元素
    const star = document.createElement('span')
    star.innerHTML = '* '
    star.style.color = 'red'
    // star.style.marginRight = '2px'

    // 如果元素是input,则将星号添加到父元素前
    if (el.tagName.toLowerCase() === 'input') {
      el.parentElement?.insertBefore(star, el)
    }
    else {
      // 否则直接在元素内容前添加星号
      el.insertBefore(star, el.firstChild)
    }
  },
}

export default required
