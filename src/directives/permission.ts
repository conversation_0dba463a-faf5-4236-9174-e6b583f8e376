import type { Directive, DirectiveBinding } from 'vue'
import { usePermissionStore } from '@/stores/Permission'

export const permission: Directive = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const { value } = binding
    const permissionStore = usePermissionStore()
    const roles = permissionStore.roles

    if (value && value.length > 0) {
      // 如果传入的是数组，检查是否有交集
      if (Array.isArray(value)) {
        // 如果包含 '*'，表示不需要权限
        if (value.includes('*')) {
          return
        }
        
        // 检查用户角色是否与所需角色有交集
        const hasPermission = roles.some(role => value.includes(role))
        
        if (!hasPermission) {
          // 如果没有权限，则隐藏元素
          el.style.display = 'none'
        }
      }
      // 如果传入的是字符串，直接检查是否包含
      else if (typeof value === 'string') {
        if (value === '*') {
          return
        }
        
        if (!roles.includes(value)) {
          el.style.display = 'none'
        }
      }
    }
  },
}

export default permission
