import type { Directive, DirectiveBinding } from 'vue'
import { nextTick } from 'vue'

interface ExHTMLElement extends HTMLElement {
  resizeListener: EventListener
}

export const adaptive: Directive = {
  mounted: (el: ExHTMLElement, binding: DirectiveBinding) => {
    el.resizeListener = () => {
      nextTick(() => {
        setHeight(el, binding)
      })
    }
    nextTick(() => {
      setHeight(el, binding)
    })
    window.addEventListener('resize', el.resizeListener)
  },
  unmounted(el: ExHTMLElement) {
    window.removeEventListener('resize', el.resizeListener)
  },
  updated(el: ExHTMLElement, binding: DirectiveBinding) {
    nextTick(() => {
      setHeight(el, binding)
    })
  },
}

// 设置表格高度
function setHeight(el: ExHTMLElement, binding: DirectiveBinding) {
  // 获取前一个元素的高度（如工具栏等）
  const prevSiblingHeight = el.previousElementSibling ? el.previousElementSibling.getBoundingClientRect().height : 0
  // 获取顶部高度，默认为88px（布局中的header和tags高度）
  const top = prevSiblingHeight + (binding.value?.top || 88)
  // 获取底部高度，默认为48px（分页器高度）
  const bottom = binding.value?.bottom || 48
  // 获取整个屏幕的高度
  const pageHeight = window.innerHeight

  // 判断表格是否在模态框中使用
  if (el.closest('.vxe-modal--content')) {
    // 模态框中的表格高度计算
    const modalContent = el.closest('.vxe-modal--content') as HTMLElement
    if (modalContent) {
      el.style.height = `${modalContent.clientHeight - top - bottom - 14}px` // 14px是vxe-model的默认padding
    }
  }
  else {
    // 普通页面中的表格高度计算
    console.log('height: ', `${pageHeight - top - bottom}px`)
    el.style.height = `${pageHeight - top - bottom}px`
  }

  el.style.overflowY = 'auto'
}
