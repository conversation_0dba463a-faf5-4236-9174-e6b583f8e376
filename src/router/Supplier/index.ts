import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout/Main/index.vue'

const routes: Array<RouteRecordRaw> = [
  {
    // 供应链
    component: Layout,
    path: '/supplier',
    name: 'supplier',
    children: [
      {
        path: 'list',
        name: 'SupplierList',
        meta: { title: '供应商资料', KeepAlive: true },
        component: () => import('@/views/Supplier/SupplierList.vue'),
      },
    ],
  },
]
export default routes
