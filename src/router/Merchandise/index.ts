import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout/Main/index.vue'

const routes: Array<RouteRecordRaw> = [
  {
    // 运营管理
    component: Layout,
    path: '/operation',
    name: 'operation',
    children: [
      {
        path: 'list',
        name: 'MerchandiseList',
        meta: { title: '上架运营', KeepAlive: true },
        component: () => import('@/views/Operation/Merchandise/MerchandiseList.vue'),
      },
    ],
  },
]
export default routes
