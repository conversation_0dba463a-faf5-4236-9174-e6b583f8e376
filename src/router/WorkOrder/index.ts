import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout/Main/index.vue'

const routes: Array<RouteRecordRaw> = [
  {
    // 运营管理
    component: Layout,
    path: '/work-order',
    name: 'workOrder',
    children: [
      {
        path: 'list',
        name: 'WorkOrderList',
        meta: { title: '工单管理', KeepAlive: true },
        component: () => import('@/views/WorkOrder/WorkOrderList.vue'),
      },
      {
        path: 'create',
        name: 'WorkOrderCreate',
        meta: { title: '创建工单' },
        component: () => import('@/views/WorkOrder/WorkOrderCreate.vue'),
      },
    ],
  },
]

export default routes
