import type { RouteRecordRaw } from 'vue-router'
import Layout from '@/components/Layout/Main/index.vue'
import { createRouter, createWebHashHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Login from '../views/Login.vue'
import Redirect from '../views/Redirect.vue'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/home',
    component: Layout,
    name: 'root',
    children: [
      {
        path: 'home',
        name: 'home',
        meta: { affix: true, title: '首页' },
        component: Home,
      },
    ],
  },
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: { requiresAuth: false },
  },
  {
    path: '/test',
    name: 'test',
    component: () => import('../views/demo/Test.vue'),
  },
  {
    path: '/',
    name: 'layout',
    component: Layout,
    children: [
      // 刷新专用路由，放在最前面
      {
        path: 'redirect/:path(.*)',
        name: 'Redirect',
        component: Redirect,
        meta: { hidden: true },
      },
      {
        path: 'form-demo',
        name: 'form-demo',
        meta: { title: '表单示例' },
        component: () => import('../views/demo/form-demo.vue'),
      },
      {
        path: 'link-button-demo',
        name: 'LinkButtonDemo',
        meta: { title: '链接按钮示例' },
        component: () => import('../views/demo/LinkButtonDemo.vue'),
      },
    ],
  },
]

// 自动导入路由文件
const modules = import.meta.glob<any>('./**/index.ts', { eager: true })
Object.keys(modules).forEach((key) => {
  routes.push(...modules[key].default)
})
// routes.push({ path: '/:pathMatch(.*)', redirect: '/404' })

const router = createRouter({
  history: createWebHashHistory(),
  routes,
})
export default router
