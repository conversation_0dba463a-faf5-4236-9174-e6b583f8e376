import type { Router } from 'vue-router'
import { useMenuStore } from '@/stores/Menu'
import { useUserStore } from '@/stores/User'

const whiteList = ['/login']

export function setupRouterGuard(router: Router) {
  router.beforeEach((to, _from, next) => {
    const userStore = useUserStore()

    if (userStore.isLoggedIn()) {
      if (to.path === '/login') {
        if (to.query.redirect) {
          next({ path: to.query.redirect as string })
        }
        else {
          next({ path: '/' })
        }
      }
      else {
        next()
      }
    }
    else {
      if (whiteList.includes(to.path)) {
        next()
      }
      else {
        next({
          path: '/login',
          query: {
            redirect: to.fullPath,
          },
        })
        ElMessage.warning('请先登录')
      }
    }
  })

  router.afterEach((to) => {
    const menuStore = useMenuStore()
    menuStore.syncWithRoute(to)
  })
}
