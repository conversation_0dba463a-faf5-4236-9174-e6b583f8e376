import type { App } from 'vue'
import { library } from '@fortawesome/fontawesome-svg-core'
import { faFacebookF, faInstagram, faLinkedinIn, faTwitter } from '@fortawesome/free-brands-svg-icons'
import { faCircleDown as farCircleDown } from '@fortawesome/free-regular-svg-icons'
import {
  faBarcode,
  faBox,
  faBoxes,
  faCalendarDays,
  faChartLine,
  faCheckCircle,
  faCheckSquare,
  faClock,
  faCogs,
  faCouch,
  faDotCircle,
  faDownload,
  faEllipsisV,
  faExclamationTriangle,
  faEye,
  faEyeSlash,
  faFileInvoiceDollar,
  faFilter,
  faGlobe,
  faGripVertical,
  faImage,
  faImages,
  faKeyboard,
  faListAlt,
  faListUl,
  faLock,
  faMagnifyingGlass,
  faMinusCircle,
  faMoneyBillWave,
  faPaperclip,
  faParagraph,
  faPencilAlt,
  faPlus,
  faPlusCircle,
  faQuestion,
  faQuestionCircle,
  faRuler,
  faRulerCombined,
  faShoppingCart,
  faSquarePlus,
  faStore,
  faSync,
  faTag,
  faTags,
  faTicket,
  faTimes,
  faTrash,
  faTrashAlt,
  faTruck,
  faUpload,
  faUser,
  faUsers,
  faWeight,
  faXmark,
} from '@fortawesome/free-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'

library.add(
  faChartLine,
  faBoxes,
  faGlobe,
  faUsers,
  faCouch,
  faEye,
  faEyeSlash,
  faFacebookF,
  faTwitter,
  faLinkedinIn,
  faInstagram,
  faShoppingCart,
  faFileInvoiceDollar,
  faCogs,
  faBox,
  faListAlt,
  faMoneyBillWave,
  faDownload,
  faKeyboard,
  faLock,
  faQuestion,
  faQuestionCircle,
  faUser,
  faXmark,
  faFilter,
  faPlus,
  faSquarePlus,
  faPaperclip,
  faTimes,
  faUpload,
  faImage,
  faTag,
  faTags,
  faExclamationTriangle,
  faTrashAlt,
  faRulerCombined,
  faWeight,
  faRuler,
  faMinusCircle,
  faPlusCircle,
  faStore,
  faBarcode,
  faCheckCircle,
  faClock,
  faPencilAlt,
  faImages,
  faGripVertical,
  faParagraph,
  faCheckSquare,
  faDotCircle,
  faCalendarDays,
  faListUl,
  faTrash,
  faTicket,
  faMagnifyingGlass,
  faEllipsisV,
  faSync,
  faTruck,
  farCircleDown,
)

export default {
  install: (app: App) => {
    app.component('font-awesome-icon', FontAwesomeIcon)
  },
}
