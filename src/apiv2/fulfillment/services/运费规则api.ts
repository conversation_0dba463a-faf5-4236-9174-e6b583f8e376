/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { HeadPriceRuleRequest } from '../models';
// @ts-ignore
import type { ResultVOHeadPriceRuleDetailVO } from '../models';
// @ts-ignore
import type { ResultVOPageVOHeadPriceRuleVO } from '../models';
// @ts-ignore
import type { ResultVOPageVOTailPriceRuleVO } from '../models';
// @ts-ignore
import type { ResultVOTailPriceRuleDetailVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
// @ts-ignore
import type { TailPriceRuleRequest } from '../models';
/**
 * 运费规则Api - axios parameter creator
 * @export
 */
export const 运费规则ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 创建新的头程运费规则
         * @summary 创建头程运费规则
         * @param {HeadPriceRuleRequest} headPriceRuleRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createHeadPriceRule: async (headPriceRuleRequest: HeadPriceRuleRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'headPriceRuleRequest' is not null or undefined
            assertParamExists('createHeadPriceRule', 'headPriceRuleRequest', headPriceRuleRequest)
            const localVarPath = `/freight-rules/head`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(headPriceRuleRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 创建新的尾程运费规则
         * @summary 创建尾程运费规则
         * @param {TailPriceRuleRequest} tailPriceRuleRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createTailPriceRule: async (tailPriceRuleRequest: TailPriceRuleRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'tailPriceRuleRequest' is not null or undefined
            assertParamExists('createTailPriceRule', 'tailPriceRuleRequest', tailPriceRuleRequest)
            const localVarPath = `/freight-rules/tail`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(tailPriceRuleRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 根据ID删除头程运费规则
         * @summary 删除头程运费规则
         * @param {number} id 头程运费规则ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteHeadPriceRule: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteHeadPriceRule', 'id', id)
            const localVarPath = `/freight-rules/head/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 根据ID删除尾程运费规则
         * @summary 删除尾程运费规则
         * @param {number} id 尾程运费规则ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteTailPriceRule: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteTailPriceRule', 'id', id)
            const localVarPath = `/freight-rules/tail/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 根据ID获取头程运费规则详细信息
         * @summary 获取头程运费规则详情
         * @param {number} id 头程运费规则ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getHeadPriceRuleDetail: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getHeadPriceRuleDetail', 'id', id)
            const localVarPath = `/freight-rules/head/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 根据ID获取尾程运费规则详细信息
         * @summary 获取尾程运费规则详情
         * @param {number} id 尾程运费规则ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTailPriceRuleDetail: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getTailPriceRuleDetail', 'id', id)
            const localVarPath = `/freight-rules/tail/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 头程规则分页
         * @param {number} page 页码
         * @param {number} size 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headPriceRulePage: async (page: number, size: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'page' is not null or undefined
            assertParamExists('headPriceRulePage', 'page', page)
            // verify required parameter 'size' is not null or undefined
            assertParamExists('headPriceRulePage', 'size', size)
            const localVarPath = `/freight-rules/head/_pagination`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 尾程规则分页
         * @param {number} page 页码
         * @param {number} size 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        tailPriceRulePage: async (page: number, size: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'page' is not null or undefined
            assertParamExists('tailPriceRulePage', 'page', page)
            // verify required parameter 'size' is not null or undefined
            assertParamExists('tailPriceRulePage', 'size', size)
            const localVarPath = `/freight-rules/tail/_pagination`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 根据ID更新头程运费规则信息
         * @summary 更新头程运费规则
         * @param {number} id 头程运费规则ID
         * @param {HeadPriceRuleRequest} headPriceRuleRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateHeadPriceRule: async (id: number, headPriceRuleRequest: HeadPriceRuleRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateHeadPriceRule', 'id', id)
            // verify required parameter 'headPriceRuleRequest' is not null or undefined
            assertParamExists('updateHeadPriceRule', 'headPriceRuleRequest', headPriceRuleRequest)
            const localVarPath = `/freight-rules/head/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(headPriceRuleRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 根据ID更新尾程运费规则信息
         * @summary 更新尾程运费规则
         * @param {number} id 尾程运费规则ID
         * @param {TailPriceRuleRequest} tailPriceRuleRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateTailPriceRule: async (id: number, tailPriceRuleRequest: TailPriceRuleRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateTailPriceRule', 'id', id)
            // verify required parameter 'tailPriceRuleRequest' is not null or undefined
            assertParamExists('updateTailPriceRule', 'tailPriceRuleRequest', tailPriceRuleRequest)
            const localVarPath = `/freight-rules/tail/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(tailPriceRuleRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 运费规则Api - functional programming interface
 * @export
 */
export const 运费规则ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 运费规则ApiAxiosParamCreator(configuration)
    return {
        /**
         * 创建新的头程运费规则
         * @summary 创建头程运费规则
         * @param {HeadPriceRuleRequest} headPriceRuleRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createHeadPriceRule(headPriceRuleRequest: HeadPriceRuleRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createHeadPriceRule(headPriceRuleRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费规则Api.createHeadPriceRule']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 创建新的尾程运费规则
         * @summary 创建尾程运费规则
         * @param {TailPriceRuleRequest} tailPriceRuleRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createTailPriceRule(tailPriceRuleRequest: TailPriceRuleRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createTailPriceRule(tailPriceRuleRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费规则Api.createTailPriceRule']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 根据ID删除头程运费规则
         * @summary 删除头程运费规则
         * @param {number} id 头程运费规则ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteHeadPriceRule(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteHeadPriceRule(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费规则Api.deleteHeadPriceRule']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 根据ID删除尾程运费规则
         * @summary 删除尾程运费规则
         * @param {number} id 尾程运费规则ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteTailPriceRule(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteTailPriceRule(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费规则Api.deleteTailPriceRule']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 根据ID获取头程运费规则详细信息
         * @summary 获取头程运费规则详情
         * @param {number} id 头程运费规则ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getHeadPriceRuleDetail(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOHeadPriceRuleDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getHeadPriceRuleDetail(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费规则Api.getHeadPriceRuleDetail']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 根据ID获取尾程运费规则详细信息
         * @summary 获取尾程运费规则详情
         * @param {number} id 尾程运费规则ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getTailPriceRuleDetail(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOTailPriceRuleDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getTailPriceRuleDetail(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费规则Api.getTailPriceRuleDetail']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 头程规则分页
         * @param {number} page 页码
         * @param {number} size 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async headPriceRulePage(page: number, size: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOHeadPriceRuleVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.headPriceRulePage(page, size, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费规则Api.headPriceRulePage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 尾程规则分页
         * @param {number} page 页码
         * @param {number} size 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async tailPriceRulePage(page: number, size: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOTailPriceRuleVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.tailPriceRulePage(page, size, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费规则Api.tailPriceRulePage']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 根据ID更新头程运费规则信息
         * @summary 更新头程运费规则
         * @param {number} id 头程运费规则ID
         * @param {HeadPriceRuleRequest} headPriceRuleRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateHeadPriceRule(id: number, headPriceRuleRequest: HeadPriceRuleRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateHeadPriceRule(id, headPriceRuleRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费规则Api.updateHeadPriceRule']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 根据ID更新尾程运费规则信息
         * @summary 更新尾程运费规则
         * @param {number} id 尾程运费规则ID
         * @param {TailPriceRuleRequest} tailPriceRuleRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateTailPriceRule(id: number, tailPriceRuleRequest: TailPriceRuleRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateTailPriceRule(id, tailPriceRuleRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费规则Api.updateTailPriceRule']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 运费规则Api - factory interface
 * @export
 */
export const 运费规则ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 运费规则ApiFp(configuration)
    return {
        /**
         * 创建新的头程运费规则
         * @summary 创建头程运费规则
         * @param {运费规则ApiCreateHeadPriceRuleRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createHeadPriceRule(requestParameters: 运费规则ApiCreateHeadPriceRuleRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.createHeadPriceRule(requestParameters.headPriceRuleRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 创建新的尾程运费规则
         * @summary 创建尾程运费规则
         * @param {运费规则ApiCreateTailPriceRuleRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createTailPriceRule(requestParameters: 运费规则ApiCreateTailPriceRuleRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.createTailPriceRule(requestParameters.tailPriceRuleRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 根据ID删除头程运费规则
         * @summary 删除头程运费规则
         * @param {运费规则ApiDeleteHeadPriceRuleRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteHeadPriceRule(requestParameters: 运费规则ApiDeleteHeadPriceRuleRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.deleteHeadPriceRule(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * 根据ID删除尾程运费规则
         * @summary 删除尾程运费规则
         * @param {运费规则ApiDeleteTailPriceRuleRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteTailPriceRule(requestParameters: 运费规则ApiDeleteTailPriceRuleRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.deleteTailPriceRule(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * 根据ID获取头程运费规则详细信息
         * @summary 获取头程运费规则详情
         * @param {运费规则ApiGetHeadPriceRuleDetailRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getHeadPriceRuleDetail(requestParameters: 运费规则ApiGetHeadPriceRuleDetailRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOHeadPriceRuleDetailVO> {
            return localVarFp.getHeadPriceRuleDetail(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * 根据ID获取尾程运费规则详细信息
         * @summary 获取尾程运费规则详情
         * @param {运费规则ApiGetTailPriceRuleDetailRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getTailPriceRuleDetail(requestParameters: 运费规则ApiGetTailPriceRuleDetailRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOTailPriceRuleDetailVO> {
            return localVarFp.getTailPriceRuleDetail(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 头程规则分页
         * @param {运费规则ApiHeadPriceRulePageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        headPriceRulePage(requestParameters: 运费规则ApiHeadPriceRulePageRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOHeadPriceRuleVO> {
            return localVarFp.headPriceRulePage(requestParameters.page, requestParameters.size, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 尾程规则分页
         * @param {运费规则ApiTailPriceRulePageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        tailPriceRulePage(requestParameters: 运费规则ApiTailPriceRulePageRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOTailPriceRuleVO> {
            return localVarFp.tailPriceRulePage(requestParameters.page, requestParameters.size, options).then((request) => request(axios, basePath));
        },
        /**
         * 根据ID更新头程运费规则信息
         * @summary 更新头程运费规则
         * @param {运费规则ApiUpdateHeadPriceRuleRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateHeadPriceRule(requestParameters: 运费规则ApiUpdateHeadPriceRuleRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.updateHeadPriceRule(requestParameters.id, requestParameters.headPriceRuleRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 根据ID更新尾程运费规则信息
         * @summary 更新尾程运费规则
         * @param {运费规则ApiUpdateTailPriceRuleRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateTailPriceRule(requestParameters: 运费规则ApiUpdateTailPriceRuleRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.updateTailPriceRule(requestParameters.id, requestParameters.tailPriceRuleRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createHeadPriceRule operation in 运费规则Api.
 * @export
 * @interface 运费规则ApiCreateHeadPriceRuleRequest
 */
export interface 运费规则ApiCreateHeadPriceRuleRequest {
    /**
     * 
     * @type {HeadPriceRuleRequest}
     * @memberof 运费规则ApiCreateHeadPriceRule
     */
    readonly headPriceRuleRequest: HeadPriceRuleRequest
}

/**
 * Request parameters for createTailPriceRule operation in 运费规则Api.
 * @export
 * @interface 运费规则ApiCreateTailPriceRuleRequest
 */
export interface 运费规则ApiCreateTailPriceRuleRequest {
    /**
     * 
     * @type {TailPriceRuleRequest}
     * @memberof 运费规则ApiCreateTailPriceRule
     */
    readonly tailPriceRuleRequest: TailPriceRuleRequest
}

/**
 * Request parameters for deleteHeadPriceRule operation in 运费规则Api.
 * @export
 * @interface 运费规则ApiDeleteHeadPriceRuleRequest
 */
export interface 运费规则ApiDeleteHeadPriceRuleRequest {
    /**
     * 头程运费规则ID
     * @type {number}
     * @memberof 运费规则ApiDeleteHeadPriceRule
     */
    readonly id: number
}

/**
 * Request parameters for deleteTailPriceRule operation in 运费规则Api.
 * @export
 * @interface 运费规则ApiDeleteTailPriceRuleRequest
 */
export interface 运费规则ApiDeleteTailPriceRuleRequest {
    /**
     * 尾程运费规则ID
     * @type {number}
     * @memberof 运费规则ApiDeleteTailPriceRule
     */
    readonly id: number
}

/**
 * Request parameters for getHeadPriceRuleDetail operation in 运费规则Api.
 * @export
 * @interface 运费规则ApiGetHeadPriceRuleDetailRequest
 */
export interface 运费规则ApiGetHeadPriceRuleDetailRequest {
    /**
     * 头程运费规则ID
     * @type {number}
     * @memberof 运费规则ApiGetHeadPriceRuleDetail
     */
    readonly id: number
}

/**
 * Request parameters for getTailPriceRuleDetail operation in 运费规则Api.
 * @export
 * @interface 运费规则ApiGetTailPriceRuleDetailRequest
 */
export interface 运费规则ApiGetTailPriceRuleDetailRequest {
    /**
     * 尾程运费规则ID
     * @type {number}
     * @memberof 运费规则ApiGetTailPriceRuleDetail
     */
    readonly id: number
}

/**
 * Request parameters for headPriceRulePage operation in 运费规则Api.
 * @export
 * @interface 运费规则ApiHeadPriceRulePageRequest
 */
export interface 运费规则ApiHeadPriceRulePageRequest {
    /**
     * 页码
     * @type {number}
     * @memberof 运费规则ApiHeadPriceRulePage
     */
    readonly page: number

    /**
     * 分页大小
     * @type {number}
     * @memberof 运费规则ApiHeadPriceRulePage
     */
    readonly size: number
}

/**
 * Request parameters for tailPriceRulePage operation in 运费规则Api.
 * @export
 * @interface 运费规则ApiTailPriceRulePageRequest
 */
export interface 运费规则ApiTailPriceRulePageRequest {
    /**
     * 页码
     * @type {number}
     * @memberof 运费规则ApiTailPriceRulePage
     */
    readonly page: number

    /**
     * 分页大小
     * @type {number}
     * @memberof 运费规则ApiTailPriceRulePage
     */
    readonly size: number
}

/**
 * Request parameters for updateHeadPriceRule operation in 运费规则Api.
 * @export
 * @interface 运费规则ApiUpdateHeadPriceRuleRequest
 */
export interface 运费规则ApiUpdateHeadPriceRuleRequest {
    /**
     * 头程运费规则ID
     * @type {number}
     * @memberof 运费规则ApiUpdateHeadPriceRule
     */
    readonly id: number

    /**
     * 
     * @type {HeadPriceRuleRequest}
     * @memberof 运费规则ApiUpdateHeadPriceRule
     */
    readonly headPriceRuleRequest: HeadPriceRuleRequest
}

/**
 * Request parameters for updateTailPriceRule operation in 运费规则Api.
 * @export
 * @interface 运费规则ApiUpdateTailPriceRuleRequest
 */
export interface 运费规则ApiUpdateTailPriceRuleRequest {
    /**
     * 尾程运费规则ID
     * @type {number}
     * @memberof 运费规则ApiUpdateTailPriceRule
     */
    readonly id: number

    /**
     * 
     * @type {TailPriceRuleRequest}
     * @memberof 运费规则ApiUpdateTailPriceRule
     */
    readonly tailPriceRuleRequest: TailPriceRuleRequest
}

/**
 * 运费规则Api - object-oriented interface
 * @export
 * @class 运费规则Api
 * @extends {BaseAPI}
 */
export class 运费规则Api extends BaseAPI {
    /**
     * 创建新的头程运费规则
     * @summary 创建头程运费规则
     * @param {运费规则ApiCreateHeadPriceRuleRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费规则Api
     */
    public createHeadPriceRule(requestParameters: 运费规则ApiCreateHeadPriceRuleRequest, options?: RawAxiosRequestConfig) {
        return 运费规则ApiFp(this.configuration).createHeadPriceRule(requestParameters.headPriceRuleRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 创建新的尾程运费规则
     * @summary 创建尾程运费规则
     * @param {运费规则ApiCreateTailPriceRuleRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费规则Api
     */
    public createTailPriceRule(requestParameters: 运费规则ApiCreateTailPriceRuleRequest, options?: RawAxiosRequestConfig) {
        return 运费规则ApiFp(this.configuration).createTailPriceRule(requestParameters.tailPriceRuleRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 根据ID删除头程运费规则
     * @summary 删除头程运费规则
     * @param {运费规则ApiDeleteHeadPriceRuleRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费规则Api
     */
    public deleteHeadPriceRule(requestParameters: 运费规则ApiDeleteHeadPriceRuleRequest, options?: RawAxiosRequestConfig) {
        return 运费规则ApiFp(this.configuration).deleteHeadPriceRule(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 根据ID删除尾程运费规则
     * @summary 删除尾程运费规则
     * @param {运费规则ApiDeleteTailPriceRuleRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费规则Api
     */
    public deleteTailPriceRule(requestParameters: 运费规则ApiDeleteTailPriceRuleRequest, options?: RawAxiosRequestConfig) {
        return 运费规则ApiFp(this.configuration).deleteTailPriceRule(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 根据ID获取头程运费规则详细信息
     * @summary 获取头程运费规则详情
     * @param {运费规则ApiGetHeadPriceRuleDetailRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费规则Api
     */
    public getHeadPriceRuleDetail(requestParameters: 运费规则ApiGetHeadPriceRuleDetailRequest, options?: RawAxiosRequestConfig) {
        return 运费规则ApiFp(this.configuration).getHeadPriceRuleDetail(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 根据ID获取尾程运费规则详细信息
     * @summary 获取尾程运费规则详情
     * @param {运费规则ApiGetTailPriceRuleDetailRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费规则Api
     */
    public getTailPriceRuleDetail(requestParameters: 运费规则ApiGetTailPriceRuleDetailRequest, options?: RawAxiosRequestConfig) {
        return 运费规则ApiFp(this.configuration).getTailPriceRuleDetail(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 头程规则分页
     * @param {运费规则ApiHeadPriceRulePageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费规则Api
     */
    public headPriceRulePage(requestParameters: 运费规则ApiHeadPriceRulePageRequest, options?: RawAxiosRequestConfig) {
        return 运费规则ApiFp(this.configuration).headPriceRulePage(requestParameters.page, requestParameters.size, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 尾程规则分页
     * @param {运费规则ApiTailPriceRulePageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费规则Api
     */
    public tailPriceRulePage(requestParameters: 运费规则ApiTailPriceRulePageRequest, options?: RawAxiosRequestConfig) {
        return 运费规则ApiFp(this.configuration).tailPriceRulePage(requestParameters.page, requestParameters.size, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 根据ID更新头程运费规则信息
     * @summary 更新头程运费规则
     * @param {运费规则ApiUpdateHeadPriceRuleRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费规则Api
     */
    public updateHeadPriceRule(requestParameters: 运费规则ApiUpdateHeadPriceRuleRequest, options?: RawAxiosRequestConfig) {
        return 运费规则ApiFp(this.configuration).updateHeadPriceRule(requestParameters.id, requestParameters.headPriceRuleRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 根据ID更新尾程运费规则信息
     * @summary 更新尾程运费规则
     * @param {运费规则ApiUpdateTailPriceRuleRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费规则Api
     */
    public updateTailPriceRule(requestParameters: 运费规则ApiUpdateTailPriceRuleRequest, options?: RawAxiosRequestConfig) {
        return 运费规则ApiFp(this.configuration).updateTailPriceRule(requestParameters.id, requestParameters.tailPriceRuleRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

