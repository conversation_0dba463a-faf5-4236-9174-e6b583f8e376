/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOPageVOShippingMethodVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
// @ts-ignore
import type { ShippingMethodRequest } from '../models';
/**
 * 运输方式管理Api - axios parameter creator
 * @export
 */
export const 运输方式管理ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 删除运输方式
         * @param {number} id 运输方式ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        _delete: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('_delete', 'id', id)
            const localVarPath = `/shipping-methods/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 创建运输方式
         * @param {ShippingMethodRequest} shippingMethodRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create: async (shippingMethodRequest: ShippingMethodRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'shippingMethodRequest' is not null or undefined
            assertParamExists('create', 'shippingMethodRequest', shippingMethodRequest)
            const localVarPath = `/shipping-methods`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(shippingMethodRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询运输方式
         * @param {number} [page] 页码
         * @param {number} [size] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        page: async (page?: number, size?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/shipping-methods`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新运输方式
         * @param {number} id 运输方式ID
         * @param {ShippingMethodRequest} shippingMethodRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        update: async (id: number, shippingMethodRequest: ShippingMethodRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('update', 'id', id)
            // verify required parameter 'shippingMethodRequest' is not null or undefined
            assertParamExists('update', 'shippingMethodRequest', shippingMethodRequest)
            const localVarPath = `/shipping-methods/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(shippingMethodRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 运输方式管理Api - functional programming interface
 * @export
 */
export const 运输方式管理ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 运输方式管理ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 删除运输方式
         * @param {number} id 运输方式ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async _delete(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator._delete(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运输方式管理Api._delete']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 创建运输方式
         * @param {ShippingMethodRequest} shippingMethodRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async create(shippingMethodRequest: ShippingMethodRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.create(shippingMethodRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运输方式管理Api.create']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 分页查询运输方式
         * @param {number} [page] 页码
         * @param {number} [size] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async page(page?: number, size?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOShippingMethodVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.page(page, size, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运输方式管理Api.page']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 更新运输方式
         * @param {number} id 运输方式ID
         * @param {ShippingMethodRequest} shippingMethodRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async update(id: number, shippingMethodRequest: ShippingMethodRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.update(id, shippingMethodRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运输方式管理Api.update']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 运输方式管理Api - factory interface
 * @export
 */
export const 运输方式管理ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 运输方式管理ApiFp(configuration)
    return {
        /**
         * 
         * @summary 删除运输方式
         * @param {运输方式管理ApiDeleteRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        _delete(requestParameters: 运输方式管理ApiDeleteRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp._delete(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 创建运输方式
         * @param {运输方式管理ApiCreateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create(requestParameters: 运输方式管理ApiCreateRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.create(requestParameters.shippingMethodRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询运输方式
         * @param {运输方式管理ApiPageRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        page(requestParameters: 运输方式管理ApiPageRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOShippingMethodVO> {
            return localVarFp.page(requestParameters.page, requestParameters.size, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新运输方式
         * @param {运输方式管理ApiUpdateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        update(requestParameters: 运输方式管理ApiUpdateRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.update(requestParameters.id, requestParameters.shippingMethodRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for _delete operation in 运输方式管理Api.
 * @export
 * @interface 运输方式管理ApiDeleteRequest
 */
export interface 运输方式管理ApiDeleteRequest {
    /**
     * 运输方式ID
     * @type {number}
     * @memberof 运输方式管理ApiDelete
     */
    readonly id: number
}

/**
 * Request parameters for create operation in 运输方式管理Api.
 * @export
 * @interface 运输方式管理ApiCreateRequest
 */
export interface 运输方式管理ApiCreateRequest {
    /**
     * 
     * @type {ShippingMethodRequest}
     * @memberof 运输方式管理ApiCreate
     */
    readonly shippingMethodRequest: ShippingMethodRequest
}

/**
 * Request parameters for page operation in 运输方式管理Api.
 * @export
 * @interface 运输方式管理ApiPageRequest
 */
export interface 运输方式管理ApiPageRequest {
    /**
     * 页码
     * @type {number}
     * @memberof 运输方式管理ApiPage
     */
    readonly page?: number

    /**
     * 分页大小
     * @type {number}
     * @memberof 运输方式管理ApiPage
     */
    readonly size?: number
}

/**
 * Request parameters for update operation in 运输方式管理Api.
 * @export
 * @interface 运输方式管理ApiUpdateRequest
 */
export interface 运输方式管理ApiUpdateRequest {
    /**
     * 运输方式ID
     * @type {number}
     * @memberof 运输方式管理ApiUpdate
     */
    readonly id: number

    /**
     * 
     * @type {ShippingMethodRequest}
     * @memberof 运输方式管理ApiUpdate
     */
    readonly shippingMethodRequest: ShippingMethodRequest
}

/**
 * 运输方式管理Api - object-oriented interface
 * @export
 * @class 运输方式管理Api
 * @extends {BaseAPI}
 */
export class 运输方式管理Api extends BaseAPI {
    /**
     * 
     * @summary 删除运输方式
     * @param {运输方式管理ApiDeleteRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运输方式管理Api
     */
    public _delete(requestParameters: 运输方式管理ApiDeleteRequest, options?: RawAxiosRequestConfig) {
        return 运输方式管理ApiFp(this.configuration)._delete(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 创建运输方式
     * @param {运输方式管理ApiCreateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运输方式管理Api
     */
    public create(requestParameters: 运输方式管理ApiCreateRequest, options?: RawAxiosRequestConfig) {
        return 运输方式管理ApiFp(this.configuration).create(requestParameters.shippingMethodRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 分页查询运输方式
     * @param {运输方式管理ApiPageRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运输方式管理Api
     */
    public page(requestParameters: 运输方式管理ApiPageRequest = {}, options?: RawAxiosRequestConfig) {
        return 运输方式管理ApiFp(this.configuration).page(requestParameters.page, requestParameters.size, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 更新运输方式
     * @param {运输方式管理ApiUpdateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运输方式管理Api
     */
    public update(requestParameters: 运输方式管理ApiUpdateRequest, options?: RawAxiosRequestConfig) {
        return 运输方式管理ApiFp(this.configuration).update(requestParameters.id, requestParameters.shippingMethodRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

