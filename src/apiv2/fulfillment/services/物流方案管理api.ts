/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { LogisticsChannelRequest } from '../models';
// @ts-ignore
import type { ResultVOPageVOLogisticsChannelVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
/**
 * 物流方案管理Api - axios parameter creator
 * @export
 */
export const 物流方案管理ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 创建物流方案
         * @param {LogisticsChannelRequest} logisticsChannelRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create2: async (logisticsChannelRequest: LogisticsChannelRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'logisticsChannelRequest' is not null or undefined
            assertParamExists('create2', 'logisticsChannelRequest', logisticsChannelRequest)
            const localVarPath = `/logistics-channels`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(logisticsChannelRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除物流方案
         * @param {number} id 物流方案ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        delete2: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('delete2', 'id', id)
            const localVarPath = `/logistics-channels/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询物流方案
         * @param {number} [page] 页码
         * @param {number} [size] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        page2: async (page?: number, size?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/logistics-channels`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新物流方案
         * @param {number} id 物流方案ID
         * @param {LogisticsChannelRequest} logisticsChannelRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        update2: async (id: number, logisticsChannelRequest: LogisticsChannelRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('update2', 'id', id)
            // verify required parameter 'logisticsChannelRequest' is not null or undefined
            assertParamExists('update2', 'logisticsChannelRequest', logisticsChannelRequest)
            const localVarPath = `/logistics-channels/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(logisticsChannelRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 物流方案管理Api - functional programming interface
 * @export
 */
export const 物流方案管理ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 物流方案管理ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 创建物流方案
         * @param {LogisticsChannelRequest} logisticsChannelRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async create2(logisticsChannelRequest: LogisticsChannelRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.create2(logisticsChannelRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['物流方案管理Api.create2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 删除物流方案
         * @param {number} id 物流方案ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async delete2(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.delete2(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['物流方案管理Api.delete2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 分页查询物流方案
         * @param {number} [page] 页码
         * @param {number} [size] 分页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async page2(page?: number, size?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOLogisticsChannelVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.page2(page, size, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['物流方案管理Api.page2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 更新物流方案
         * @param {number} id 物流方案ID
         * @param {LogisticsChannelRequest} logisticsChannelRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async update2(id: number, logisticsChannelRequest: LogisticsChannelRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.update2(id, logisticsChannelRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['物流方案管理Api.update2']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 物流方案管理Api - factory interface
 * @export
 */
export const 物流方案管理ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 物流方案管理ApiFp(configuration)
    return {
        /**
         * 
         * @summary 创建物流方案
         * @param {物流方案管理ApiCreate2Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create2(requestParameters: 物流方案管理ApiCreate2Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.create2(requestParameters.logisticsChannelRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除物流方案
         * @param {物流方案管理ApiDelete2Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        delete2(requestParameters: 物流方案管理ApiDelete2Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.delete2(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询物流方案
         * @param {物流方案管理ApiPage2Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        page2(requestParameters: 物流方案管理ApiPage2Request = {}, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOLogisticsChannelVO> {
            return localVarFp.page2(requestParameters.page, requestParameters.size, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新物流方案
         * @param {物流方案管理ApiUpdate2Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        update2(requestParameters: 物流方案管理ApiUpdate2Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.update2(requestParameters.id, requestParameters.logisticsChannelRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for create2 operation in 物流方案管理Api.
 * @export
 * @interface 物流方案管理ApiCreate2Request
 */
export interface 物流方案管理ApiCreate2Request {
    /**
     * 
     * @type {LogisticsChannelRequest}
     * @memberof 物流方案管理ApiCreate2
     */
    readonly logisticsChannelRequest: LogisticsChannelRequest
}

/**
 * Request parameters for delete2 operation in 物流方案管理Api.
 * @export
 * @interface 物流方案管理ApiDelete2Request
 */
export interface 物流方案管理ApiDelete2Request {
    /**
     * 物流方案ID
     * @type {number}
     * @memberof 物流方案管理ApiDelete2
     */
    readonly id: number
}

/**
 * Request parameters for page2 operation in 物流方案管理Api.
 * @export
 * @interface 物流方案管理ApiPage2Request
 */
export interface 物流方案管理ApiPage2Request {
    /**
     * 页码
     * @type {number}
     * @memberof 物流方案管理ApiPage2
     */
    readonly page?: number

    /**
     * 分页大小
     * @type {number}
     * @memberof 物流方案管理ApiPage2
     */
    readonly size?: number
}

/**
 * Request parameters for update2 operation in 物流方案管理Api.
 * @export
 * @interface 物流方案管理ApiUpdate2Request
 */
export interface 物流方案管理ApiUpdate2Request {
    /**
     * 物流方案ID
     * @type {number}
     * @memberof 物流方案管理ApiUpdate2
     */
    readonly id: number

    /**
     * 
     * @type {LogisticsChannelRequest}
     * @memberof 物流方案管理ApiUpdate2
     */
    readonly logisticsChannelRequest: LogisticsChannelRequest
}

/**
 * 物流方案管理Api - object-oriented interface
 * @export
 * @class 物流方案管理Api
 * @extends {BaseAPI}
 */
export class 物流方案管理Api extends BaseAPI {
    /**
     * 
     * @summary 创建物流方案
     * @param {物流方案管理ApiCreate2Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 物流方案管理Api
     */
    public create2(requestParameters: 物流方案管理ApiCreate2Request, options?: RawAxiosRequestConfig) {
        return 物流方案管理ApiFp(this.configuration).create2(requestParameters.logisticsChannelRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 删除物流方案
     * @param {物流方案管理ApiDelete2Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 物流方案管理Api
     */
    public delete2(requestParameters: 物流方案管理ApiDelete2Request, options?: RawAxiosRequestConfig) {
        return 物流方案管理ApiFp(this.configuration).delete2(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 分页查询物流方案
     * @param {物流方案管理ApiPage2Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 物流方案管理Api
     */
    public page2(requestParameters: 物流方案管理ApiPage2Request = {}, options?: RawAxiosRequestConfig) {
        return 物流方案管理ApiFp(this.configuration).page2(requestParameters.page, requestParameters.size, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 更新物流方案
     * @param {物流方案管理ApiUpdate2Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 物流方案管理Api
     */
    public update2(requestParameters: 物流方案管理ApiUpdate2Request, options?: RawAxiosRequestConfig) {
        return 物流方案管理ApiFp(this.configuration).update2(requestParameters.id, requestParameters.logisticsChannelRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

