/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOListTransportationTimelinessVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
/**
 * 运输时效Api - axios parameter creator
 * @export
 */
export const 运输时效ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 所有运输时效的列表
         * @summary 运输时效列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        list: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/transportation-timeliness/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 运输时效Api - functional programming interface
 * @export
 */
export const 运输时效ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 运输时效ApiAxiosParamCreator(configuration)
    return {
        /**
         * 所有运输时效的列表
         * @summary 运输时效列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async list(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListTransportationTimelinessVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.list(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运输时效Api.list']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 运输时效Api - factory interface
 * @export
 */
export const 运输时效ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 运输时效ApiFp(configuration)
    return {
        /**
         * 所有运输时效的列表
         * @summary 运输时效列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        list(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListTransportationTimelinessVO> {
            return localVarFp.list(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * 运输时效Api - object-oriented interface
 * @export
 * @class 运输时效Api
 * @extends {BaseAPI}
 */
export class 运输时效Api extends BaseAPI {
    /**
     * 所有运输时效的列表
     * @summary 运输时效列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运输时效Api
     */
    public list(options?: RawAxiosRequestConfig) {
        return 运输时效ApiFp(this.configuration).list(options).then((request) => request(this.axios, this.basePath));
    }
}

