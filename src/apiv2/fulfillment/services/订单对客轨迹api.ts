/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOOrderTrackVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
/**
 * 订单对客轨迹Api - axios parameter creator
 * @export
 */
export const 订单对客轨迹ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 订单轨迹查询
         * @param {string} params 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderTrackByOrderSku: async (params: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'params' is not null or undefined
            assertParamExists('getOrderTrackByOrderSku', 'params', params)
            const localVarPath = `/order-track-for-customer`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (params !== undefined) {
                localVarQueryParameter['params'] = params;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 订单对客轨迹Api - functional programming interface
 * @export
 */
export const 订单对客轨迹ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 订单对客轨迹ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 订单轨迹查询
         * @param {string} params 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOrderTrackByOrderSku(params: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOOrderTrackVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOrderTrackByOrderSku(params, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['订单对客轨迹Api.getOrderTrackByOrderSku']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 订单对客轨迹Api - factory interface
 * @export
 */
export const 订单对客轨迹ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 订单对客轨迹ApiFp(configuration)
    return {
        /**
         * 
         * @summary 订单轨迹查询
         * @param {订单对客轨迹ApiGetOrderTrackByOrderSkuRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOrderTrackByOrderSku(requestParameters: 订单对客轨迹ApiGetOrderTrackByOrderSkuRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOOrderTrackVO> {
            return localVarFp.getOrderTrackByOrderSku(requestParameters.params, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getOrderTrackByOrderSku operation in 订单对客轨迹Api.
 * @export
 * @interface 订单对客轨迹ApiGetOrderTrackByOrderSkuRequest
 */
export interface 订单对客轨迹ApiGetOrderTrackByOrderSkuRequest {
    /**
     * 
     * @type {string}
     * @memberof 订单对客轨迹ApiGetOrderTrackByOrderSku
     */
    readonly params: string
}

/**
 * 订单对客轨迹Api - object-oriented interface
 * @export
 * @class 订单对客轨迹Api
 * @extends {BaseAPI}
 */
export class 订单对客轨迹Api extends BaseAPI {
    /**
     * 
     * @summary 订单轨迹查询
     * @param {订单对客轨迹ApiGetOrderTrackByOrderSkuRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 订单对客轨迹Api
     */
    public getOrderTrackByOrderSku(requestParameters: 订单对客轨迹ApiGetOrderTrackByOrderSkuRequest, options?: RawAxiosRequestConfig) {
        return 订单对客轨迹ApiFp(this.configuration).getOrderTrackByOrderSku(requestParameters.params, options).then((request) => request(this.axios, this.basePath));
    }
}

