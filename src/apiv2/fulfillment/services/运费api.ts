/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { FreightCalculationRequest } from '../models';
// @ts-ignore
import type { FreightMatchRequest } from '../models';
// @ts-ignore
import type { ResultVOFreightCalculationVO } from '../models';
// @ts-ignore
import type { ResultVOFreightMatchVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
/**
 * 运费Api - axios parameter creator
 * @export
 */
export const 运费ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 根据物流渠道和包裹信息计算运费
         * @summary 计算运费
         * @param {FreightCalculationRequest} freightCalculationRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        calculateFreight: async (freightCalculationRequest: FreightCalculationRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'freightCalculationRequest' is not null or undefined
            assertParamExists('calculateFreight', 'freightCalculationRequest', freightCalculationRequest)
            const localVarPath = `/freights/calculate`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(freightCalculationRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 根据包裹信息匹配最低运费方案
         * @summary 匹配运费
         * @param {FreightMatchRequest} freightMatchRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        matchFreight: async (freightMatchRequest: FreightMatchRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'freightMatchRequest' is not null or undefined
            assertParamExists('matchFreight', 'freightMatchRequest', freightMatchRequest)
            const localVarPath = `/freights/match`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(freightMatchRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 运费Api - functional programming interface
 * @export
 */
export const 运费ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 运费ApiAxiosParamCreator(configuration)
    return {
        /**
         * 根据物流渠道和包裹信息计算运费
         * @summary 计算运费
         * @param {FreightCalculationRequest} freightCalculationRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async calculateFreight(freightCalculationRequest: FreightCalculationRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOFreightCalculationVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.calculateFreight(freightCalculationRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费Api.calculateFreight']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 根据包裹信息匹配最低运费方案
         * @summary 匹配运费
         * @param {FreightMatchRequest} freightMatchRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async matchFreight(freightMatchRequest: FreightMatchRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOFreightMatchVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.matchFreight(freightMatchRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['运费Api.matchFreight']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 运费Api - factory interface
 * @export
 */
export const 运费ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 运费ApiFp(configuration)
    return {
        /**
         * 根据物流渠道和包裹信息计算运费
         * @summary 计算运费
         * @param {运费ApiCalculateFreightRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        calculateFreight(requestParameters: 运费ApiCalculateFreightRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOFreightCalculationVO> {
            return localVarFp.calculateFreight(requestParameters.freightCalculationRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 根据包裹信息匹配最低运费方案
         * @summary 匹配运费
         * @param {运费ApiMatchFreightRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        matchFreight(requestParameters: 运费ApiMatchFreightRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOFreightMatchVO> {
            return localVarFp.matchFreight(requestParameters.freightMatchRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for calculateFreight operation in 运费Api.
 * @export
 * @interface 运费ApiCalculateFreightRequest
 */
export interface 运费ApiCalculateFreightRequest {
    /**
     * 
     * @type {FreightCalculationRequest}
     * @memberof 运费ApiCalculateFreight
     */
    readonly freightCalculationRequest: FreightCalculationRequest
}

/**
 * Request parameters for matchFreight operation in 运费Api.
 * @export
 * @interface 运费ApiMatchFreightRequest
 */
export interface 运费ApiMatchFreightRequest {
    /**
     * 
     * @type {FreightMatchRequest}
     * @memberof 运费ApiMatchFreight
     */
    readonly freightMatchRequest: FreightMatchRequest
}

/**
 * 运费Api - object-oriented interface
 * @export
 * @class 运费Api
 * @extends {BaseAPI}
 */
export class 运费Api extends BaseAPI {
    /**
     * 根据物流渠道和包裹信息计算运费
     * @summary 计算运费
     * @param {运费ApiCalculateFreightRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费Api
     */
    public calculateFreight(requestParameters: 运费ApiCalculateFreightRequest, options?: RawAxiosRequestConfig) {
        return 运费ApiFp(this.configuration).calculateFreight(requestParameters.freightCalculationRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 根据包裹信息匹配最低运费方案
     * @summary 匹配运费
     * @param {运费ApiMatchFreightRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 运费Api
     */
    public matchFreight(requestParameters: 运费ApiMatchFreightRequest, options?: RawAxiosRequestConfig) {
        return 运费ApiFp(this.configuration).matchFreight(requestParameters.freightMatchRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

