/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { LogisticsProviderRequest } from '../models';
// @ts-ignore
import type { ResultVOListLogisticsProviderVO } from '../models';
// @ts-ignore
import type { ResultVOPageVOLogisticsProviderVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
/**
 * 物流供应商管理Api - axios parameter creator
 * @export
 */
export const 物流供应商管理ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 创建物流供应商
         * @param {LogisticsProviderRequest} logisticsProviderRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create1: async (logisticsProviderRequest: LogisticsProviderRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'logisticsProviderRequest' is not null or undefined
            assertParamExists('create1', 'logisticsProviderRequest', logisticsProviderRequest)
            const localVarPath = `/logistics-providers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(logisticsProviderRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 删除物流供应商
         * @param {number} id 物流供应商ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        delete1: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('delete1', 'id', id)
            const localVarPath = `/logistics-providers/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 主要用于下拉列表
         * @summary 查询物流供应商列表
         * @param {ListAllStageEnum} stage 头程/尾程
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listAll: async (stage: ListAllStageEnum, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'stage' is not null or undefined
            assertParamExists('listAll', 'stage', stage)
            const localVarPath = `/logistics-providers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (stage !== undefined) {
                localVarQueryParameter['stage'] = stage;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 分页查询物流供应商
         * @param {number} [page] 页码
         * @param {number} [size] 页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        page1: async (page?: number, size?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/logistics-providers/_pagination`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (page !== undefined) {
                localVarQueryParameter['page'] = page;
            }

            if (size !== undefined) {
                localVarQueryParameter['size'] = size;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新物流供应商
         * @param {number} id 物流供应商ID
         * @param {LogisticsProviderRequest} logisticsProviderRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        update1: async (id: number, logisticsProviderRequest: LogisticsProviderRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('update1', 'id', id)
            // verify required parameter 'logisticsProviderRequest' is not null or undefined
            assertParamExists('update1', 'logisticsProviderRequest', logisticsProviderRequest)
            const localVarPath = `/logistics-providers/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(logisticsProviderRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 物流供应商管理Api - functional programming interface
 * @export
 */
export const 物流供应商管理ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 物流供应商管理ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 创建物流供应商
         * @param {LogisticsProviderRequest} logisticsProviderRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async create1(logisticsProviderRequest: LogisticsProviderRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.create1(logisticsProviderRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['物流供应商管理Api.create1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 删除物流供应商
         * @param {number} id 物流供应商ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async delete1(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.delete1(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['物流供应商管理Api.delete1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 主要用于下拉列表
         * @summary 查询物流供应商列表
         * @param {ListAllStageEnum} stage 头程/尾程
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listAll(stage: ListAllStageEnum, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListLogisticsProviderVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listAll(stage, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['物流供应商管理Api.listAll']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 分页查询物流供应商
         * @param {number} [page] 页码
         * @param {number} [size] 页大小
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async page1(page?: number, size?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOLogisticsProviderVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.page1(page, size, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['物流供应商管理Api.page1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 更新物流供应商
         * @param {number} id 物流供应商ID
         * @param {LogisticsProviderRequest} logisticsProviderRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async update1(id: number, logisticsProviderRequest: LogisticsProviderRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.update1(id, logisticsProviderRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['物流供应商管理Api.update1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 物流供应商管理Api - factory interface
 * @export
 */
export const 物流供应商管理ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 物流供应商管理ApiFp(configuration)
    return {
        /**
         * 
         * @summary 创建物流供应商
         * @param {物流供应商管理ApiCreate1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create1(requestParameters: 物流供应商管理ApiCreate1Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.create1(requestParameters.logisticsProviderRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 删除物流供应商
         * @param {物流供应商管理ApiDelete1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        delete1(requestParameters: 物流供应商管理ApiDelete1Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.delete1(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * 主要用于下拉列表
         * @summary 查询物流供应商列表
         * @param {物流供应商管理ApiListAllRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listAll(requestParameters: 物流供应商管理ApiListAllRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListLogisticsProviderVO> {
            return localVarFp.listAll(requestParameters.stage, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 分页查询物流供应商
         * @param {物流供应商管理ApiPage1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        page1(requestParameters: 物流供应商管理ApiPage1Request = {}, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOLogisticsProviderVO> {
            return localVarFp.page1(requestParameters.page, requestParameters.size, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新物流供应商
         * @param {物流供应商管理ApiUpdate1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        update1(requestParameters: 物流供应商管理ApiUpdate1Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.update1(requestParameters.id, requestParameters.logisticsProviderRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for create1 operation in 物流供应商管理Api.
 * @export
 * @interface 物流供应商管理ApiCreate1Request
 */
export interface 物流供应商管理ApiCreate1Request {
    /**
     * 
     * @type {LogisticsProviderRequest}
     * @memberof 物流供应商管理ApiCreate1
     */
    readonly logisticsProviderRequest: LogisticsProviderRequest
}

/**
 * Request parameters for delete1 operation in 物流供应商管理Api.
 * @export
 * @interface 物流供应商管理ApiDelete1Request
 */
export interface 物流供应商管理ApiDelete1Request {
    /**
     * 物流供应商ID
     * @type {number}
     * @memberof 物流供应商管理ApiDelete1
     */
    readonly id: number
}

/**
 * Request parameters for listAll operation in 物流供应商管理Api.
 * @export
 * @interface 物流供应商管理ApiListAllRequest
 */
export interface 物流供应商管理ApiListAllRequest {
    /**
     * 头程/尾程
     * @type {'HEAD' | 'TAIL' | 'ALL'}
     * @memberof 物流供应商管理ApiListAll
     */
    readonly stage: ListAllStageEnum
}

/**
 * Request parameters for page1 operation in 物流供应商管理Api.
 * @export
 * @interface 物流供应商管理ApiPage1Request
 */
export interface 物流供应商管理ApiPage1Request {
    /**
     * 页码
     * @type {number}
     * @memberof 物流供应商管理ApiPage1
     */
    readonly page?: number

    /**
     * 页大小
     * @type {number}
     * @memberof 物流供应商管理ApiPage1
     */
    readonly size?: number
}

/**
 * Request parameters for update1 operation in 物流供应商管理Api.
 * @export
 * @interface 物流供应商管理ApiUpdate1Request
 */
export interface 物流供应商管理ApiUpdate1Request {
    /**
     * 物流供应商ID
     * @type {number}
     * @memberof 物流供应商管理ApiUpdate1
     */
    readonly id: number

    /**
     * 
     * @type {LogisticsProviderRequest}
     * @memberof 物流供应商管理ApiUpdate1
     */
    readonly logisticsProviderRequest: LogisticsProviderRequest
}

/**
 * 物流供应商管理Api - object-oriented interface
 * @export
 * @class 物流供应商管理Api
 * @extends {BaseAPI}
 */
export class 物流供应商管理Api extends BaseAPI {
    /**
     * 
     * @summary 创建物流供应商
     * @param {物流供应商管理ApiCreate1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 物流供应商管理Api
     */
    public create1(requestParameters: 物流供应商管理ApiCreate1Request, options?: RawAxiosRequestConfig) {
        return 物流供应商管理ApiFp(this.configuration).create1(requestParameters.logisticsProviderRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 删除物流供应商
     * @param {物流供应商管理ApiDelete1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 物流供应商管理Api
     */
    public delete1(requestParameters: 物流供应商管理ApiDelete1Request, options?: RawAxiosRequestConfig) {
        return 物流供应商管理ApiFp(this.configuration).delete1(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 主要用于下拉列表
     * @summary 查询物流供应商列表
     * @param {物流供应商管理ApiListAllRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 物流供应商管理Api
     */
    public listAll(requestParameters: 物流供应商管理ApiListAllRequest, options?: RawAxiosRequestConfig) {
        return 物流供应商管理ApiFp(this.configuration).listAll(requestParameters.stage, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 分页查询物流供应商
     * @param {物流供应商管理ApiPage1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 物流供应商管理Api
     */
    public page1(requestParameters: 物流供应商管理ApiPage1Request = {}, options?: RawAxiosRequestConfig) {
        return 物流供应商管理ApiFp(this.configuration).page1(requestParameters.page, requestParameters.size, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 更新物流供应商
     * @param {物流供应商管理ApiUpdate1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 物流供应商管理Api
     */
    public update1(requestParameters: 物流供应商管理ApiUpdate1Request, options?: RawAxiosRequestConfig) {
        return 物流供应商管理ApiFp(this.configuration).update1(requestParameters.id, requestParameters.logisticsProviderRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const ListAllStageEnum = {
    HEAD: 'HEAD',
    TAIL: 'TAIL',
    ALL: 'ALL'
} as const;
export type ListAllStageEnum = typeof ListAllStageEnum[keyof typeof ListAllStageEnum];
