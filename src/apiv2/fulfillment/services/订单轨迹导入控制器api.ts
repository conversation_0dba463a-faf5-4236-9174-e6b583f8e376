/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOVoid } from '../models';
/**
 * 订单轨迹导入控制器Api - axios parameter creator
 * @export
 */
export const 订单轨迹导入控制器ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        importLocalTemplate: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/files/import/original-order/local`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        importOriginalOrder: async (file: File, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'file' is not null or undefined
            assertParamExists('importOriginalOrder', 'file', file)
            const localVarPath = `/files/import/original-order`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;
            const localVarFormParams = new ((configuration && configuration.formDataCtor) || FormData)();


            if (file !== undefined) { 
                localVarFormParams.append('file', file as any);
            }
    
    
            localVarHeaderParameter['Content-Type'] = 'multipart/form-data';
    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = localVarFormParams;

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 订单轨迹导入控制器Api - functional programming interface
 * @export
 */
export const 订单轨迹导入控制器ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 订单轨迹导入控制器ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async importLocalTemplate(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.importLocalTemplate(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['订单轨迹导入控制器Api.importLocalTemplate']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {File} file 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async importOriginalOrder(file: File, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.importOriginalOrder(file, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['订单轨迹导入控制器Api.importOriginalOrder']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 订单轨迹导入控制器Api - factory interface
 * @export
 */
export const 订单轨迹导入控制器ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 订单轨迹导入控制器ApiFp(configuration)
    return {
        /**
         * 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        importLocalTemplate(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.importLocalTemplate(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {订单轨迹导入控制器ApiImportOriginalOrderRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        importOriginalOrder(requestParameters: 订单轨迹导入控制器ApiImportOriginalOrderRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.importOriginalOrder(requestParameters.file, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for importOriginalOrder operation in 订单轨迹导入控制器Api.
 * @export
 * @interface 订单轨迹导入控制器ApiImportOriginalOrderRequest
 */
export interface 订单轨迹导入控制器ApiImportOriginalOrderRequest {
    /**
     * 
     * @type {File}
     * @memberof 订单轨迹导入控制器ApiImportOriginalOrder
     */
    readonly file: File
}

/**
 * 订单轨迹导入控制器Api - object-oriented interface
 * @export
 * @class 订单轨迹导入控制器Api
 * @extends {BaseAPI}
 */
export class 订单轨迹导入控制器Api extends BaseAPI {
    /**
     * 
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof 订单轨迹导入控制器Api
     */
    public importLocalTemplate(options?: RawAxiosRequestConfig) {
        return 订单轨迹导入控制器ApiFp(this.configuration).importLocalTemplate(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {订单轨迹导入控制器ApiImportOriginalOrderRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof 订单轨迹导入控制器Api
     */
    public importOriginalOrder(requestParameters: 订单轨迹导入控制器ApiImportOriginalOrderRequest, options?: RawAxiosRequestConfig) {
        return 订单轨迹导入控制器ApiFp(this.configuration).importOriginalOrder(requestParameters.file, options).then((request) => request(this.axios, this.basePath));
    }
}

