/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { SurchargeDetailVO } from './surcharge-detail-vo';

/**
 * 运费计算结果视图对象
 * @export
 * @interface FreightCalculationVO
 */
export interface FreightCalculationVO {
    /**
     * 渠道ID
     * @type {number}
     * @memberof FreightCalculationVO
     */
    'channelId'?: number;
    /**
     * 渠道名称
     * @type {string}
     * @memberof FreightCalculationVO
     */
    'channelName'?: string;
    /**
     * 计费重量(kg)
     * @type {number}
     * @memberof FreightCalculationVO
     */
    'chargeableWeight'?: number;
    /**
     * 头程费用
     * @type {number}
     * @memberof FreightCalculationVO
     */
    'headFreight'?: number;
    /**
     * 头程货币单位
     * @type {string}
     * @memberof FreightCalculationVO
     */
    'headCurrency'?: string;
    /**
     * 尾程费用
     * @type {number}
     * @memberof FreightCalculationVO
     */
    'tailFreight'?: number;
    /**
     * 尾程货币单位
     * @type {string}
     * @memberof FreightCalculationVO
     */
    'tailCurrency'?: string;
    /**
     * 附加费明细
     * @type {Array<SurchargeDetailVO>}
     * @memberof FreightCalculationVO
     */
    'surcharges'?: Array<SurchargeDetailVO>;
}

