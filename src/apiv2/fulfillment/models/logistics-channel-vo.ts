/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 物流渠道视图对象
 * @export
 * @interface LogisticsChannelVO
 */
export interface LogisticsChannelVO {
    /**
     * 渠道ID
     * @type {number}
     * @memberof LogisticsChannelVO
     */
    'id'?: number;
    /**
     * 头程价格规则ID
     * @type {number}
     * @memberof LogisticsChannelVO
     */
    'headRuleId'?: number;
    /**
     * 尾程价格规则ID
     * @type {number}
     * @memberof LogisticsChannelVO
     */
    'tailRuleId'?: number;
    /**
     * 渠道编码
     * @type {string}
     * @memberof LogisticsChannelVO
     */
    'channelCode'?: string;
    /**
     * 渠道名称
     * @type {string}
     * @memberof LogisticsChannelVO
     */
    'channelName'?: string;
    /**
     * 运输方式
     * @type {string}
     * @memberof LogisticsChannelVO
     */
    'shippingMethod'?: string;
    /**
     * 状态：0-禁用，1-启用
     * @type {boolean}
     * @memberof LogisticsChannelVO
     */
    'status'?: boolean;
    /**
     * 最小运输天数
     * @type {number}
     * @memberof LogisticsChannelVO
     */
    'minDays'?: number;
    /**
     * 最大运输天数
     * @type {number}
     * @memberof LogisticsChannelVO
     */
    'maxDays'?: number;
    /**
     * 创建人ID
     * @type {number}
     * @memberof LogisticsChannelVO
     */
    'creatorId'?: number;
    /**
     * 更新人ID
     * @type {number}
     * @memberof LogisticsChannelVO
     */
    'updaterId'?: number;
    /**
     * 是否有转单号：0-否，1-是
     * @type {boolean}
     * @memberof LogisticsChannelVO
     */
    'hasTransNo'?: boolean;
    /**
     * 创建时间
     * @type {string}
     * @memberof LogisticsChannelVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof LogisticsChannelVO
     */
    'updateTime'?: string;
}

