/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Dimension } from './dimension';

/**
 * 运费匹配请求模型
 * @export
 * @interface FreightMatchRequest
 */
export interface FreightMatchRequest {
    /**
     * 实际重量(g)
     * @type {number}
     * @memberof FreightMatchRequest
     */
    'actualWeight': number;
    /**
     * 尺寸列表，每个元素包含长宽高三元组
     * @type {Array<Dimension>}
     * @memberof FreightMatchRequest
     */
    'dimensions': Array<Dimension>;
    /**
     * 件数
     * @type {number}
     * @memberof FreightMatchRequest
     */
    'quantity': number;
    /**
     * 目的地邮编
     * @type {string}
     * @memberof FreightMatchRequest
     */
    'postalCode'?: string;
    /**
     * 发货时效id
     * @type {number}
     * @memberof FreightMatchRequest
     */
    'timelinessId': number;
    /**
     * 目标币种
     * @type {string}
     * @memberof FreightMatchRequest
     */
    'currency'?: FreightMatchRequestCurrencyEnum;
}

export const FreightMatchRequestCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type FreightMatchRequestCurrencyEnum = typeof FreightMatchRequestCurrencyEnum[keyof typeof FreightMatchRequestCurrencyEnum];


