/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PriceDetailVO } from './price-detail-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SurchargeVO } from './surcharge-vo';

/**
 * 头程运费规则详细响应VO
 * @export
 * @interface HeadPriceRuleDetailVO
 */
export interface HeadPriceRuleDetailVO {
    /**
     * 规则ID
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'id'?: number;
    /**
     * 供应商ID
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'providerId'?: number;
    /**
     * 规则名称
     * @type {string}
     * @memberof HeadPriceRuleDetailVO
     */
    'ruleName'?: string;
    /**
     * 计费方式
     * @type {string}
     * @memberof HeadPriceRuleDetailVO
     */
    'billingMethod'?: HeadPriceRuleDetailVOBillingMethodEnum;
    /**
     * 计价方式
     * @type {string}
     * @memberof HeadPriceRuleDetailVO
     */
    'pricingMethod'?: HeadPriceRuleDetailVOPricingMethodEnum;
    /**
     * 首重
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'firstWeight'?: number;
    /**
     * 首重费用
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'firstFee'?: number;
    /**
     * 续重单位
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'additionalWeight'?: number;
    /**
     * 续重费用
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'additionalFee'?: number;
    /**
     * 货币单位：CNY/USD/JPY等
     * @type {string}
     * @memberof HeadPriceRuleDetailVO
     */
    'currency'?: HeadPriceRuleDetailVOCurrencyEnum;
    /**
     * 重量单位：KG/LB
     * @type {string}
     * @memberof HeadPriceRuleDetailVO
     */
    'weightUnit'?: HeadPriceRuleDetailVOWeightUnitEnum;
    /**
     * 最小计费重量
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'minWeight'?: number;
    /**
     * 最大计费重量
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'maxWeight'?: number;
    /**
     * 最大三边和，单位：厘米
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'maxDimension'?: number;
    /**
     * 基础费用(当pricing_method为UNIT/FIXED时)
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'baseFee'?: number;
    /**
     * 单价(当pricing_method为UNIT时)
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'unitPrice'?: number;
    /**
     * 体积系数
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'volumeFactor'?: number;
    /**
     * 体积单位
     * @type {string}
     * @memberof HeadPriceRuleDetailVO
     */
    'volumeUnit'?: string;
    /**
     * 规则特定抛货系数
     * @type {number}
     * @memberof HeadPriceRuleDetailVO
     */
    'chargeableFactor'?: number;
    /**
     * 目的地：JP/US/EU
     * @type {string}
     * @memberof HeadPriceRuleDetailVO
     */
    'destination'?: string;
    /**
     * 状态：0-禁用，1-启用
     * @type {boolean}
     * @memberof HeadPriceRuleDetailVO
     */
    'status'?: boolean;
    /**
     * 创建人姓名
     * @type {string}
     * @memberof HeadPriceRuleDetailVO
     */
    'creator'?: string;
    /**
     * 更新人姓名
     * @type {string}
     * @memberof HeadPriceRuleDetailVO
     */
    'updator'?: string;
    /**
     * 创建时间
     * @type {string}
     * @memberof HeadPriceRuleDetailVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof HeadPriceRuleDetailVO
     */
    'updateTime'?: string;
    /**
     * 阶梯价格明细列表
     * @type {Array<PriceDetailVO>}
     * @memberof HeadPriceRuleDetailVO
     */
    'priceDetails'?: Array<PriceDetailVO>;
    /**
     * 附加费列表
     * @type {Array<SurchargeVO>}
     * @memberof HeadPriceRuleDetailVO
     */
    'surcharges'?: Array<SurchargeVO>;
}

export const HeadPriceRuleDetailVOBillingMethodEnum = {
    ACTUAL_WEIGHT: 'ACTUAL_WEIGHT',
    VOLUME_WEIGHT: 'VOLUME_WEIGHT',
    PER_ITEM: 'PER_ITEM',
    CALCULATE_WEIGHT: 'CALCULATE_WEIGHT'
} as const;

export type HeadPriceRuleDetailVOBillingMethodEnum = typeof HeadPriceRuleDetailVOBillingMethodEnum[keyof typeof HeadPriceRuleDetailVOBillingMethodEnum];
export const HeadPriceRuleDetailVOPricingMethodEnum = {
    FIRST_ADDITIONAL: 'FIRST_ADDITIONAL',
    TIERED: 'TIERED',
    UNIT: 'UNIT',
    FIXED: 'FIXED'
} as const;

export type HeadPriceRuleDetailVOPricingMethodEnum = typeof HeadPriceRuleDetailVOPricingMethodEnum[keyof typeof HeadPriceRuleDetailVOPricingMethodEnum];
export const HeadPriceRuleDetailVOCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type HeadPriceRuleDetailVOCurrencyEnum = typeof HeadPriceRuleDetailVOCurrencyEnum[keyof typeof HeadPriceRuleDetailVOCurrencyEnum];
export const HeadPriceRuleDetailVOWeightUnitEnum = {
    KG: 'KG',
    LB: 'LB'
} as const;

export type HeadPriceRuleDetailVOWeightUnitEnum = typeof HeadPriceRuleDetailVOWeightUnitEnum[keyof typeof HeadPriceRuleDetailVOWeightUnitEnum];


