/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 价格明细请求
 * @export
 * @interface PriceDetailRequest
 */
export interface PriceDetailRequest {
    /**
     * 价格ID
     * @type {number}
     * @memberof PriceDetailRequest
     */
    'id'?: number;
    /**
     * 开始重量
     * @type {number}
     * @memberof PriceDetailRequest
     */
    'startWeight'?: number;
    /**
     * 结束重量
     * @type {number}
     * @memberof PriceDetailRequest
     */
    'endWeight'?: number;
    /**
     * 重量单位
     * @type {string}
     * @memberof PriceDetailRequest
     */
    'weightUnit'?: PriceDetailRequestWeightUnitEnum;
    /**
     * 起始三边和
     * @type {number}
     * @memberof PriceDetailRequest
     */
    'startDimension'?: number;
    /**
     * 结束三边和
     * @type {number}
     * @memberof PriceDetailRequest
     */
    'endDimension'?: number;
    /**
     * 三边和单位
     * @type {string}
     * @memberof PriceDetailRequest
     */
    'dimensionUnit'?: PriceDetailRequestDimensionUnitEnum;
    /**
     * 价格
     * @type {number}
     * @memberof PriceDetailRequest
     */
    'price'?: number;
}

export const PriceDetailRequestWeightUnitEnum = {
    KG: 'KG',
    LB: 'LB'
} as const;

export type PriceDetailRequestWeightUnitEnum = typeof PriceDetailRequestWeightUnitEnum[keyof typeof PriceDetailRequestWeightUnitEnum];
export const PriceDetailRequestDimensionUnitEnum = {
    CM: 'CM',
    INCH: 'INCH'
} as const;

export type PriceDetailRequestDimensionUnitEnum = typeof PriceDetailRequestDimensionUnitEnum[keyof typeof PriceDetailRequestDimensionUnitEnum];


