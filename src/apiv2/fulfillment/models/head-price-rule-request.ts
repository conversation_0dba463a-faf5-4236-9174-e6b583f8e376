/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PriceDetailRequest } from './price-detail-request';
// May contain unused imports in some cases
// @ts-ignore
import type { SurchargeRequest } from './surcharge-request';

/**
 * 头程运费规则请求DTO
 * @export
 * @interface HeadPriceRuleRequest
 */
export interface HeadPriceRuleRequest {
    /**
     * 规则名称
     * @type {string}
     * @memberof HeadPriceRuleRequest
     */
    'ruleName'?: string;
    /**
     * 供应商ID
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'providerId'?: number;
    /**
     * 计费方式
     * @type {string}
     * @memberof HeadPriceRuleRequest
     */
    'billingMethod'?: HeadPriceRuleRequestBillingMethodEnum;
    /**
     * 计价方式
     * @type {string}
     * @memberof HeadPriceRuleRequest
     */
    'pricingMethod'?: HeadPriceRuleRequestPricingMethodEnum;
    /**
     * 首重
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'firstWeight'?: number;
    /**
     * 首重费用
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'firstFee'?: number;
    /**
     * 续重单位
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'additionalWeight'?: number;
    /**
     * 续重费用
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'additionalFee'?: number;
    /**
     * 货币单位：CNY/USD/JPY等
     * @type {string}
     * @memberof HeadPriceRuleRequest
     */
    'currency'?: HeadPriceRuleRequestCurrencyEnum;
    /**
     * 重量单位：KG/LB
     * @type {string}
     * @memberof HeadPriceRuleRequest
     */
    'weightUnit'?: HeadPriceRuleRequestWeightUnitEnum;
    /**
     * 最小计费重量
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'minWeight'?: number;
    /**
     * 最大计费重量
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'maxWeight'?: number;
    /**
     * 最大三边和，单位：厘米
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'maxDimension'?: number;
    /**
     * 基础费用(当pricing_method为UNIT/FIXED时)
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'baseFee'?: number;
    /**
     * 单价(当pricing_method为UNIT时)
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'unitPrice'?: number;
    /**
     * 体积系数
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'volumeFactor'?: number;
    /**
     * 体积单位
     * @type {string}
     * @memberof HeadPriceRuleRequest
     */
    'volumeUnit'?: string;
    /**
     * 规则特定抛货系数
     * @type {number}
     * @memberof HeadPriceRuleRequest
     */
    'chargeableFactor'?: number;
    /**
     * 目的地：JP/US/EU
     * @type {string}
     * @memberof HeadPriceRuleRequest
     */
    'destination'?: string;
    /**
     * 状态：0-禁用，1-启用
     * @type {boolean}
     * @memberof HeadPriceRuleRequest
     */
    'status'?: boolean;
    /**
     * 阶梯价格明细列表
     * @type {Array<PriceDetailRequest>}
     * @memberof HeadPriceRuleRequest
     */
    'priceDetails'?: Array<PriceDetailRequest>;
    /**
     * 附加费列表
     * @type {Array<SurchargeRequest>}
     * @memberof HeadPriceRuleRequest
     */
    'surcharges'?: Array<SurchargeRequest>;
}

export const HeadPriceRuleRequestBillingMethodEnum = {
    ACTUAL_WEIGHT: 'ACTUAL_WEIGHT',
    VOLUME_WEIGHT: 'VOLUME_WEIGHT',
    PER_ITEM: 'PER_ITEM',
    CALCULATE_WEIGHT: 'CALCULATE_WEIGHT'
} as const;

export type HeadPriceRuleRequestBillingMethodEnum = typeof HeadPriceRuleRequestBillingMethodEnum[keyof typeof HeadPriceRuleRequestBillingMethodEnum];
export const HeadPriceRuleRequestPricingMethodEnum = {
    FIRST_ADDITIONAL: 'FIRST_ADDITIONAL',
    TIERED: 'TIERED',
    UNIT: 'UNIT',
    FIXED: 'FIXED'
} as const;

export type HeadPriceRuleRequestPricingMethodEnum = typeof HeadPriceRuleRequestPricingMethodEnum[keyof typeof HeadPriceRuleRequestPricingMethodEnum];
export const HeadPriceRuleRequestCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type HeadPriceRuleRequestCurrencyEnum = typeof HeadPriceRuleRequestCurrencyEnum[keyof typeof HeadPriceRuleRequestCurrencyEnum];
export const HeadPriceRuleRequestWeightUnitEnum = {
    KG: 'KG',
    LB: 'LB'
} as const;

export type HeadPriceRuleRequestWeightUnitEnum = typeof HeadPriceRuleRequestWeightUnitEnum[keyof typeof HeadPriceRuleRequestWeightUnitEnum];


