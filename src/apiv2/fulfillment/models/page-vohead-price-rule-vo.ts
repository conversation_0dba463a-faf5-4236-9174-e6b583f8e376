/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { HeadPriceRuleVO } from './head-price-rule-vo';

/**
 * 数据
 * @export
 * @interface PageVOHeadPriceRuleVO
 */
export interface PageVOHeadPriceRuleVO {
    /**
     * 
     * @type {number}
     * @memberof PageVOHeadPriceRuleVO
     */
    'pageNum'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOHeadPriceRuleVO
     */
    'pageSize'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOHeadPriceRuleVO
     */
    'total'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOHeadPriceRuleVO
     */
    'pages'?: number;
    /**
     * 
     * @type {Array<HeadPriceRuleVO>}
     * @memberof PageVOHeadPriceRuleVO
     */
    'records'?: Array<HeadPriceRuleVO>;
}

