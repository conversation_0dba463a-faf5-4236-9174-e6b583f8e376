/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 物流服务商视图对象
 * @export
 * @interface LogisticsProviderVO
 */
export interface LogisticsProviderVO {
    /**
     * ID
     * @type {number}
     * @memberof LogisticsProviderVO
     */
    'id'?: number;
    /**
     * 名称
     * @type {string}
     * @memberof LogisticsProviderVO
     */
    'name'?: string;
    /**
     * 编码
     * @type {string}
     * @memberof LogisticsProviderVO
     */
    'code'?: string;
    /**
     * 状态
     * @type {boolean}
     * @memberof LogisticsProviderVO
     */
    'status'?: boolean;
    /**
     * 物流阶段
     * @type {string}
     * @memberof LogisticsProviderVO
     */
    'logisticsStage'?: LogisticsProviderVOLogisticsStageEnum;
    /**
     * 创建时间
     * @type {string}
     * @memberof LogisticsProviderVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof LogisticsProviderVO
     */
    'updateTime'?: string;
}

export const LogisticsProviderVOLogisticsStageEnum = {
    HEAD: 'HEAD',
    TAIL: 'TAIL',
    ALL: 'ALL'
} as const;

export type LogisticsProviderVOLogisticsStageEnum = typeof LogisticsProviderVOLogisticsStageEnum[keyof typeof LogisticsProviderVOLogisticsStageEnum];


