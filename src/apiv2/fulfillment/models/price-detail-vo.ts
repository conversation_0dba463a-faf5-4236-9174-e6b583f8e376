/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 价格明细VO
 * @export
 * @interface PriceDetailVO
 */
export interface PriceDetailVO {
    /**
     * 价格ID
     * @type {number}
     * @memberof PriceDetailVO
     */
    'id'?: number;
    /**
     * 开始重量
     * @type {number}
     * @memberof PriceDetailVO
     */
    'startWeight'?: number;
    /**
     * 结束重量
     * @type {number}
     * @memberof PriceDetailVO
     */
    'endWeight'?: number;
    /**
     * 重量单位
     * @type {string}
     * @memberof PriceDetailVO
     */
    'weightUnit'?: PriceDetailVOWeightUnitEnum;
    /**
     * 起始三边和
     * @type {number}
     * @memberof PriceDetailVO
     */
    'startDimension'?: number;
    /**
     * 结束三边和
     * @type {number}
     * @memberof PriceDetailVO
     */
    'endDimension'?: number;
    /**
     * 三边和单位
     * @type {string}
     * @memberof PriceDetailVO
     */
    'dimensionUnit'?: PriceDetailVODimensionUnitEnum;
    /**
     * 价格
     * @type {number}
     * @memberof PriceDetailVO
     */
    'price'?: number;
}

export const PriceDetailVOWeightUnitEnum = {
    KG: 'KG',
    LB: 'LB'
} as const;

export type PriceDetailVOWeightUnitEnum = typeof PriceDetailVOWeightUnitEnum[keyof typeof PriceDetailVOWeightUnitEnum];
export const PriceDetailVODimensionUnitEnum = {
    CM: 'CM',
    INCH: 'INCH'
} as const;

export type PriceDetailVODimensionUnitEnum = typeof PriceDetailVODimensionUnitEnum[keyof typeof PriceDetailVODimensionUnitEnum];


