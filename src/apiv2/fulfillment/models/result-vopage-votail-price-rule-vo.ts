/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PageVOTailPriceRuleVO } from './page-votail-price-rule-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOPageVOTailPriceRuleVO
 */
export interface ResultVOPageVOTailPriceRuleVO {
    /**
     * 状态码
     * @type {number}
     * @memberof ResultVOPageVOTailPriceRuleVO
     */
    'code'?: number;
    /**
     * 消息
     * @type {string}
     * @memberof ResultVOPageVOTailPriceRuleVO
     */
    'message'?: string;
    /**
     * 
     * @type {PageVOTailPriceRuleVO}
     * @memberof ResultVOPageVOTailPriceRuleVO
     */
    'data'?: PageVOTailPriceRuleVO;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOPageVOTailPriceRuleVO
     */
    'success'?: boolean;
}

