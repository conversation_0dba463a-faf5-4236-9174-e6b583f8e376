/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 运输方式请求对象
 * @export
 * @interface ShippingMethodRequest
 */
export interface ShippingMethodRequest {
    /**
     * 名称
     * @type {string}
     * @memberof ShippingMethodRequest
     */
    'name': string;
    /**
     * 头程运输方式
     * @type {string}
     * @memberof ShippingMethodRequest
     */
    'headShippingMethod'?: ShippingMethodRequestHeadShippingMethodEnum;
    /**
     * 尾程运输方式
     * @type {string}
     * @memberof ShippingMethodRequest
     */
    'tailShippingMethod'?: ShippingMethodRequestTailShippingMethodEnum;
    /**
     * 状态
     * @type {boolean}
     * @memberof ShippingMethodRequest
     */
    'status'?: boolean;
    /**
     * 描述
     * @type {string}
     * @memberof ShippingMethodRequest
     */
    'desc'?: string;
}

export const ShippingMethodRequestHeadShippingMethodEnum = {
    SEA: 'SEA',
    AIR: 'AIR'
} as const;

export type ShippingMethodRequestHeadShippingMethodEnum = typeof ShippingMethodRequestHeadShippingMethodEnum[keyof typeof ShippingMethodRequestHeadShippingMethodEnum];
export const ShippingMethodRequestTailShippingMethodEnum = {
    EXPRESS: 'EXPRESS',
    TRUCK: 'TRUCK',
    LOCAL: 'LOCAL'
} as const;

export type ShippingMethodRequestTailShippingMethodEnum = typeof ShippingMethodRequestTailShippingMethodEnum[keyof typeof ShippingMethodRequestTailShippingMethodEnum];


