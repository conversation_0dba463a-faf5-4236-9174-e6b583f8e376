/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 头程运费规则响应VO
 * @export
 * @interface HeadPriceRuleVO
 */
export interface HeadPriceRuleVO {
    /**
     * 规则ID
     * @type {number}
     * @memberof HeadPriceRuleVO
     */
    'id'?: number;
    /**
     * 供应商名称
     * @type {string}
     * @memberof HeadPriceRuleVO
     */
    'providerName'?: string;
    /**
     * 规则名称
     * @type {string}
     * @memberof HeadPriceRuleVO
     */
    'ruleName'?: string;
    /**
     * 创建人姓名
     * @type {string}
     * @memberof HeadPriceRuleVO
     */
    'creator'?: string;
    /**
     * 创建时间
     * @type {string}
     * @memberof HeadPriceRuleVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof HeadPriceRuleVO
     */
    'updateTime'?: string;
}

