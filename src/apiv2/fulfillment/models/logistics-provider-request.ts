/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 物流供应商请求对象
 * @export
 * @interface LogisticsProviderRequest
 */
export interface LogisticsProviderRequest {
    /**
     * 名称
     * @type {string}
     * @memberof LogisticsProviderRequest
     */
    'name': string;
    /**
     * 编码
     * @type {string}
     * @memberof LogisticsProviderRequest
     */
    'code': string;
    /**
     * 状态
     * @type {boolean}
     * @memberof LogisticsProviderRequest
     */
    'status'?: boolean;
    /**
     * 物流阶段
     * @type {string}
     * @memberof LogisticsProviderRequest
     */
    'logisticsStage'?: LogisticsProviderRequestLogisticsStageEnum;
}

export const LogisticsProviderRequestLogisticsStageEnum = {
    HEAD: 'HEAD',
    TAIL: 'TAIL',
    ALL: 'ALL'
} as const;

export type LogisticsProviderRequestLogisticsStageEnum = typeof LogisticsProviderRequestLogisticsStageEnum[keyof typeof LogisticsProviderRequestLogisticsStageEnum];


