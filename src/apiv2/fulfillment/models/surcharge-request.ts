/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 附加费请求
 * @export
 * @interface SurchargeRequest
 */
export interface SurchargeRequest {
    /**
     * 附加费ID
     * @type {number}
     * @memberof SurchargeRequest
     */
    'id'?: number;
    /**
     * 序号
     * @type {number}
     * @memberof SurchargeRequest
     */
    'orderNum'?: number;
    /**
     * 附加费名称
     * @type {string}
     * @memberof SurchargeRequest
     */
    'name'?: string;
    /**
     * 费用类型
     * @type {string}
     * @memberof SurchargeRequest
     */
    'feeType'?: SurchargeRequestFeeTypeEnum;
    /**
     * 费用
     * @type {number}
     * @memberof SurchargeRequest
     */
    'amount'?: number;
    /**
     * 收费方式：FIXED(固定金额)/PERCENTAGE(百分比)
     * @type {string}
     * @memberof SurchargeRequest
     */
    'chargeMethod'?: SurchargeRequestChargeMethodEnum;
    /**
     * 货币单位：CNY/USD/JPY等
     * @type {string}
     * @memberof SurchargeRequest
     */
    'currency'?: SurchargeRequestCurrencyEnum;
    /**
     * 适用条件
     * @type {string}
     * @memberof SurchargeRequest
     */
    'condition'?: string;
    /**
     * 状态：0-禁用，1-启用
     * @type {boolean}
     * @memberof SurchargeRequest
     */
    'status'?: boolean;
}

export const SurchargeRequestFeeTypeEnum = {
    FUEL: 'FUEL',
    REMOTE: 'REMOTE',
    OVERSIZE: 'OVERSIZE',
    CUSTOMS: 'CUSTOMS',
    SIGNATURE: 'SIGNATURE',
    INSURANCE: 'INSURANCE',
    PACKAGING: 'PACKAGING',
    CONSUMPTION_TAX: 'CONSUMPTION_TAX'
} as const;

export type SurchargeRequestFeeTypeEnum = typeof SurchargeRequestFeeTypeEnum[keyof typeof SurchargeRequestFeeTypeEnum];
export const SurchargeRequestChargeMethodEnum = {
    FIXED: 'FIXED',
    PERCENTAGE: 'PERCENTAGE'
} as const;

export type SurchargeRequestChargeMethodEnum = typeof SurchargeRequestChargeMethodEnum[keyof typeof SurchargeRequestChargeMethodEnum];
export const SurchargeRequestCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type SurchargeRequestCurrencyEnum = typeof SurchargeRequestCurrencyEnum[keyof typeof SurchargeRequestCurrencyEnum];


