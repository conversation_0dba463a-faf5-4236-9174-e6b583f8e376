/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 物流渠道请求数据传输对象
 * @export
 * @interface LogisticsChannelRequest
 */
export interface LogisticsChannelRequest {
    /**
     * 渠道编码
     * @type {string}
     * @memberof LogisticsChannelRequest
     */
    'channelCode': string;
    /**
     * 渠道名称
     * @type {string}
     * @memberof LogisticsChannelRequest
     */
    'channelName': string;
    /**
     * 运输方式
     * @type {string}
     * @memberof LogisticsChannelRequest
     */
    'shippingMethod': string;
    /**
     * 头程价格规则ID
     * @type {number}
     * @memberof LogisticsChannelRequest
     */
    'headRuleId'?: number;
    /**
     * 尾程价格规则ID
     * @type {number}
     * @memberof LogisticsChannelRequest
     */
    'tailRuleId'?: number;
    /**
     * 状态：0-禁用，1-启用
     * @type {boolean}
     * @memberof LogisticsChannelRequest
     */
    'status'?: boolean;
    /**
     * 最小运输天数
     * @type {number}
     * @memberof LogisticsChannelRequest
     */
    'minDays'?: number;
    /**
     * 最大运输天数
     * @type {number}
     * @memberof LogisticsChannelRequest
     */
    'maxDays'?: number;
    /**
     * 是否有转单号：0-否，1-是
     * @type {boolean}
     * @memberof LogisticsChannelRequest
     */
    'hasTransNo'?: boolean;
}

