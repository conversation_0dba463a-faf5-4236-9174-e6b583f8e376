/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 运费匹配结果视图对象
 * @export
 * @interface FreightMatchVO
 */
export interface FreightMatchVO {
    /**
     * 渠道id
     * @type {number}
     * @memberof FreightMatchVO
     */
    'channelId'?: number;
    /**
     * 头程费用
     * @type {number}
     * @memberof FreightMatchVO
     */
    'headFreight'?: number;
    /**
     * 头程货币单位
     * @type {string}
     * @memberof FreightMatchVO
     */
    'headCurrency'?: FreightMatchVOHeadCurrencyEnum;
    /**
     * 尾程费用
     * @type {number}
     * @memberof FreightMatchVO
     */
    'tailFreight'?: number;
    /**
     * 尾程货币单位
     * @type {string}
     * @memberof FreightMatchVO
     */
    'tailCurrency'?: FreightMatchVOTailCurrencyEnum;
    /**
     * 运输方式
     * @type {number}
     * @memberof FreightMatchVO
     */
    'shippingMethod'?: number;
    /**
     * 尾程渠道编码
     * @type {string}
     * @memberof FreightMatchVO
     */
    'tailChannelCode'?: string;
    /**
     * 整体时效
     * @type {string}
     * @memberof FreightMatchVO
     */
    'totalTimeliness'?: string;
    /**
     * 所有结果
     * @type {Array<FreightMatchVO>}
     * @memberof FreightMatchVO
     */
    'allResults'?: Array<FreightMatchVO>;
}

export const FreightMatchVOHeadCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type FreightMatchVOHeadCurrencyEnum = typeof FreightMatchVOHeadCurrencyEnum[keyof typeof FreightMatchVOHeadCurrencyEnum];
export const FreightMatchVOTailCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type FreightMatchVOTailCurrencyEnum = typeof FreightMatchVOTailCurrencyEnum[keyof typeof FreightMatchVOTailCurrencyEnum];


