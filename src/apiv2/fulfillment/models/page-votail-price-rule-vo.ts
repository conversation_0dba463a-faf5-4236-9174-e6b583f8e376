/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { TailPriceRuleVO } from './tail-price-rule-vo';

/**
 * 数据
 * @export
 * @interface PageVOTailPriceRuleVO
 */
export interface PageVOTailPriceRuleVO {
    /**
     * 
     * @type {number}
     * @memberof PageVOTailPriceRuleVO
     */
    'pageNum'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOTailPriceRuleVO
     */
    'pageSize'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOTailPriceRuleVO
     */
    'total'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOTailPriceRuleVO
     */
    'pages'?: number;
    /**
     * 
     * @type {Array<TailPriceRuleVO>}
     * @memberof PageVOTailPriceRuleVO
     */
    'records'?: Array<TailPriceRuleVO>;
}

