/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { LogisticsProviderVO } from './logistics-provider-vo';

/**
 * 数据
 * @export
 * @interface PageVOLogisticsProviderVO
 */
export interface PageVOLogisticsProviderVO {
    /**
     * 
     * @type {number}
     * @memberof PageVOLogisticsProviderVO
     */
    'pageNum'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOLogisticsProviderVO
     */
    'pageSize'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOLogisticsProviderVO
     */
    'total'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOLogisticsProviderVO
     */
    'pages'?: number;
    /**
     * 
     * @type {Array<LogisticsProviderVO>}
     * @memberof PageVOLogisticsProviderVO
     */
    'records'?: Array<LogisticsProviderVO>;
}

