/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 附加费VO
 * @export
 * @interface SurchargeVO
 */
export interface SurchargeVO {
    /**
     * 附加费ID
     * @type {number}
     * @memberof SurchargeVO
     */
    'id'?: number;
    /**
     * 费用类型
     * @type {string}
     * @memberof SurchargeVO
     */
    'feeType'?: SurchargeVOFeeTypeEnum;
    /**
     * 费用
     * @type {number}
     * @memberof SurchargeVO
     */
    'amount'?: number;
    /**
     * 收费方式：FIXED(固定金额)/PERCENTAGE(百分比)
     * @type {string}
     * @memberof SurchargeVO
     */
    'chargeMethod'?: SurchargeVOChargeMethodEnum;
    /**
     * 货币单位：CNY/USD/JPY等
     * @type {string}
     * @memberof SurchargeVO
     */
    'currency'?: SurchargeVOCurrencyEnum;
}

export const SurchargeVOFeeTypeEnum = {
    FUEL: 'FUEL',
    REMOTE: 'REMOTE',
    OVERSIZE: 'OVERSIZE',
    CUSTOMS: 'CUSTOMS',
    SIGNATURE: 'SIGNATURE',
    INSURANCE: 'INSURANCE',
    PACKAGING: 'PACKAGING',
    CONSUMPTION_TAX: 'CONSUMPTION_TAX'
} as const;

export type SurchargeVOFeeTypeEnum = typeof SurchargeVOFeeTypeEnum[keyof typeof SurchargeVOFeeTypeEnum];
export const SurchargeVOChargeMethodEnum = {
    FIXED: 'FIXED',
    PERCENTAGE: 'PERCENTAGE'
} as const;

export type SurchargeVOChargeMethodEnum = typeof SurchargeVOChargeMethodEnum[keyof typeof SurchargeVOChargeMethodEnum];
export const SurchargeVOCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type SurchargeVOCurrencyEnum = typeof SurchargeVOCurrencyEnum[keyof typeof SurchargeVOCurrencyEnum];


