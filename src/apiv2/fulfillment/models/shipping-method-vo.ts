/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 运输方式视图对象
 * @export
 * @interface ShippingMethodVO
 */
export interface ShippingMethodVO {
    /**
     * ID
     * @type {number}
     * @memberof ShippingMethodVO
     */
    'id'?: number;
    /**
     * 名称
     * @type {string}
     * @memberof ShippingMethodVO
     */
    'name'?: string;
    /**
     * 头程运输方式
     * @type {string}
     * @memberof ShippingMethodVO
     */
    'headShippingMethod'?: ShippingMethodVOHeadShippingMethodEnum;
    /**
     * 尾程运输方式
     * @type {string}
     * @memberof ShippingMethodVO
     */
    'tailShippingMethod'?: ShippingMethodVOTailShippingMethodEnum;
    /**
     * 状态
     * @type {boolean}
     * @memberof ShippingMethodVO
     */
    'status'?: boolean;
    /**
     * 描述
     * @type {string}
     * @memberof ShippingMethodVO
     */
    'desc'?: string;
    /**
     * 创建时间
     * @type {string}
     * @memberof ShippingMethodVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof ShippingMethodVO
     */
    'updateTime'?: string;
}

export const ShippingMethodVOHeadShippingMethodEnum = {
    SEA: 'SEA',
    AIR: 'AIR'
} as const;

export type ShippingMethodVOHeadShippingMethodEnum = typeof ShippingMethodVOHeadShippingMethodEnum[keyof typeof ShippingMethodVOHeadShippingMethodEnum];
export const ShippingMethodVOTailShippingMethodEnum = {
    EXPRESS: 'EXPRESS',
    TRUCK: 'TRUCK',
    LOCAL: 'LOCAL'
} as const;

export type ShippingMethodVOTailShippingMethodEnum = typeof ShippingMethodVOTailShippingMethodEnum[keyof typeof ShippingMethodVOTailShippingMethodEnum];


