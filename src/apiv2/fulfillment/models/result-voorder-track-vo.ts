/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { OrderTrackVO } from './order-track-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOOrderTrackVO
 */
export interface ResultVOOrderTrackVO {
    /**
     * 状态码
     * @type {number}
     * @memberof ResultVOOrderTrackVO
     */
    'code'?: number;
    /**
     * 消息
     * @type {string}
     * @memberof ResultVOOrderTrackVO
     */
    'message'?: string;
    /**
     * 
     * @type {OrderTrackVO}
     * @memberof ResultVOOrderTrackVO
     */
    'data'?: OrderTrackVO;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOOrderTrackVO
     */
    'success'?: boolean;
}

