/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PriceDetailVO } from './price-detail-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SurchargeVO } from './surcharge-vo';

/**
 * 尾程运费规则详细响应VO
 * @export
 * @interface TailPriceRuleDetailVO
 */
export interface TailPriceRuleDetailVO {
    /**
     * 规则ID
     * @type {number}
     * @memberof TailPriceRuleDetailVO
     */
    'id'?: number;
    /**
     * 物流服务商ID
     * @type {number}
     * @memberof TailPriceRuleDetailVO
     */
    'providerId'?: number;
    /**
     * 规则名称
     * @type {string}
     * @memberof TailPriceRuleDetailVO
     */
    'ruleName'?: string;
    /**
     * 计价方式
     * @type {string}
     * @memberof TailPriceRuleDetailVO
     */
    'pricingMethod'?: TailPriceRuleDetailVOPricingMethodEnum;
    /**
     * 货币单位：CNY/USD/JPY等
     * @type {string}
     * @memberof TailPriceRuleDetailVO
     */
    'currency'?: TailPriceRuleDetailVOCurrencyEnum;
    /**
     * 重量单位：KG/LB
     * @type {string}
     * @memberof TailPriceRuleDetailVO
     */
    'weightUnit'?: TailPriceRuleDetailVOWeightUnitEnum;
    /**
     * 首重
     * @type {number}
     * @memberof TailPriceRuleDetailVO
     */
    'firstWeight'?: number;
    /**
     * 首重费用
     * @type {number}
     * @memberof TailPriceRuleDetailVO
     */
    'firstFee'?: number;
    /**
     * 续重单位
     * @type {number}
     * @memberof TailPriceRuleDetailVO
     */
    'additionalWeight'?: number;
    /**
     * 续重费用
     * @type {number}
     * @memberof TailPriceRuleDetailVO
     */
    'additionalFee'?: number;
    /**
     * 基础费用(当pricing_method为UNIT/FIXED时)
     * @type {number}
     * @memberof TailPriceRuleDetailVO
     */
    'baseFee'?: number;
    /**
     * 体积系数
     * @type {number}
     * @memberof TailPriceRuleDetailVO
     */
    'volumeFactor'?: number;
    /**
     * 体积单位
     * @type {string}
     * @memberof TailPriceRuleDetailVO
     */
    'volumeUnit'?: string;
    /**
     * 状态：0-禁用，1-启用
     * @type {boolean}
     * @memberof TailPriceRuleDetailVO
     */
    'status'?: boolean;
    /**
     * 创建人姓名
     * @type {string}
     * @memberof TailPriceRuleDetailVO
     */
    'creator'?: string;
    /**
     * 更新人姓名
     * @type {string}
     * @memberof TailPriceRuleDetailVO
     */
    'updator'?: string;
    /**
     * 创建时间
     * @type {string}
     * @memberof TailPriceRuleDetailVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof TailPriceRuleDetailVO
     */
    'updateTime'?: string;
    /**
     * 价格明细列表
     * @type {Array<PriceDetailVO>}
     * @memberof TailPriceRuleDetailVO
     */
    'priceDetails'?: Array<PriceDetailVO>;
    /**
     * 附加费列表
     * @type {Array<SurchargeVO>}
     * @memberof TailPriceRuleDetailVO
     */
    'surcharges'?: Array<SurchargeVO>;
}

export const TailPriceRuleDetailVOPricingMethodEnum = {
    WEIGHT_TIERED: 'WEIGHT_TIERED',
    DIMENSION_WEIGHT_TIERED: 'DIMENSION_WEIGHT_TIERED'
} as const;

export type TailPriceRuleDetailVOPricingMethodEnum = typeof TailPriceRuleDetailVOPricingMethodEnum[keyof typeof TailPriceRuleDetailVOPricingMethodEnum];
export const TailPriceRuleDetailVOCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type TailPriceRuleDetailVOCurrencyEnum = typeof TailPriceRuleDetailVOCurrencyEnum[keyof typeof TailPriceRuleDetailVOCurrencyEnum];
export const TailPriceRuleDetailVOWeightUnitEnum = {
    KG: 'KG',
    LB: 'LB'
} as const;

export type TailPriceRuleDetailVOWeightUnitEnum = typeof TailPriceRuleDetailVOWeightUnitEnum[keyof typeof TailPriceRuleDetailVOWeightUnitEnum];


