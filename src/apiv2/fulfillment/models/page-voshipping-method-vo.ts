/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ShippingMethodVO } from './shipping-method-vo';

/**
 * 数据
 * @export
 * @interface PageVOShippingMethodVO
 */
export interface PageVOShippingMethodVO {
    /**
     * 
     * @type {number}
     * @memberof PageVOShippingMethodVO
     */
    'pageNum'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOShippingMethodVO
     */
    'pageSize'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOShippingMethodVO
     */
    'total'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOShippingMethodVO
     */
    'pages'?: number;
    /**
     * 
     * @type {Array<ShippingMethodVO>}
     * @memberof PageVOShippingMethodVO
     */
    'records'?: Array<ShippingMethodVO>;
}

