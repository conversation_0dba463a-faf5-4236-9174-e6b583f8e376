/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { LogisticsChannelContactVO } from './logistics-channel-contact-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { OrderTrackSkuRecordVO } from './order-track-sku-record-vo';

/**
 * 订单轨迹sku视图对象
 * @export
 * @interface OrderTrackSkuVO
 */
export interface OrderTrackSkuVO {
    /**
     * sku编号
     * @type {string}
     * @memberof OrderTrackSkuVO
     */
    'skuCode'?: string;
    /**
     * sku名称
     * @type {string}
     * @memberof OrderTrackSkuVO
     */
    'skuDescription'?: string;
    /**
     * sku数量
     * @type {number}
     * @memberof OrderTrackSkuVO
     */
    'skuCount'?: number;
    /**
     * sku图片
     * @type {string}
     * @memberof OrderTrackSkuVO
     */
    'skuImage'?: string;
    /**
     * 价格
     * @type {number}
     * @memberof OrderTrackSkuVO
     */
    'price'?: number;
    /**
     * 币种
     * @type {string}
     * @memberof OrderTrackSkuVO
     */
    'priceCurrency'?: OrderTrackSkuVOPriceCurrencyEnum;
    /**
     * 所有规格
     * @type {string}
     * @memberof OrderTrackSkuVO
     */
    'options'?: string;
    /**
     * 运单号
     * @type {string}
     * @memberof OrderTrackSkuVO
     */
    'expressNo'?: string;
    /**
     * 发货类型
     * @type {string}
     * @memberof OrderTrackSkuVO
     */
    'shippingType'?: string;
    /**
     * channel编码
     * @type {string}
     * @memberof OrderTrackSkuVO
     */
    'channelCode'?: string;
    /**
     * 官网地址
     * @type {string}
     * @memberof OrderTrackSkuVO
     */
    'channelLink'?: string;
    /**
     * 联系电话
     * @type {Array<LogisticsChannelContactVO>}
     * @memberof OrderTrackSkuVO
     */
    'channelContacts'?: Array<LogisticsChannelContactVO>;
    /**
     * (全部节点完成了的话)返回空,(进行中的话)返回预计完成时间
     * @type {string}
     * @memberof OrderTrackSkuVO
     */
    'dateStr'?: string;
    /**
     * 所有进度
     * @type {Array<OrderTrackSkuRecordVO>}
     * @memberof OrderTrackSkuVO
     */
    'records'?: Array<OrderTrackSkuRecordVO>;
}

export const OrderTrackSkuVOPriceCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD'
} as const;

export type OrderTrackSkuVOPriceCurrencyEnum = typeof OrderTrackSkuVOPriceCurrencyEnum[keyof typeof OrderTrackSkuVOPriceCurrencyEnum];


