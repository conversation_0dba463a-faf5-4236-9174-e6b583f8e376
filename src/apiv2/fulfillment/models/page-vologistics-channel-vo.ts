/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { LogisticsChannelVO } from './logistics-channel-vo';

/**
 * 数据
 * @export
 * @interface PageVOLogisticsChannelVO
 */
export interface PageVOLogisticsChannelVO {
    /**
     * 
     * @type {number}
     * @memberof PageVOLogisticsChannelVO
     */
    'pageNum'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOLogisticsChannelVO
     */
    'pageSize'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOLogisticsChannelVO
     */
    'total'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOLogisticsChannelVO
     */
    'pages'?: number;
    /**
     * 
     * @type {Array<LogisticsChannelVO>}
     * @memberof PageVOLogisticsChannelVO
     */
    'records'?: Array<LogisticsChannelVO>;
}

