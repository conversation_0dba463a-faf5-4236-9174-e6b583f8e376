/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 订单轨迹sku所有记录视图对象
 * @export
 * @interface OrderTrackSkuRecordVO
 */
export interface OrderTrackSkuRecordVO {
    /**
     * 该节点key
     * @type {string}
     * @memberof OrderTrackSkuRecordVO
     */
    'nodeKey'?: string;
    /**
     * 该节点描述 description
     * @type {string}
     * @memberof OrderTrackSkuRecordVO
     */
    'nodeDescription'?: string;
    /**
     * 节点状态(按时间轴)
     * @type {string}
     * @memberof OrderTrackSkuRecordVO
     */
    'nodeStatus'?: OrderTrackSkuRecordVONodeStatusEnum;
    /**
     * (完成了的话)完成时间,(进行中的话)预计完成时间
     * @type {string}
     * @memberof OrderTrackSkuRecordVO
     */
    'dateStr'?: string;
}

export const OrderTrackSkuRecordVONodeStatusEnum = {
    SOLVED: 'SOLVED',
    PROCESSING: 'PROCESSING',
    PENDING: 'PENDING'
} as const;

export type OrderTrackSkuRecordVONodeStatusEnum = typeof OrderTrackSkuRecordVONodeStatusEnum[keyof typeof OrderTrackSkuRecordVONodeStatusEnum];


