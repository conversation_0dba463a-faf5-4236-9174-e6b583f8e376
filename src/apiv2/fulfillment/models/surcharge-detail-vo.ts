/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 附加费明细视图对象
 * @export
 * @interface SurchargeDetailVO
 */
export interface SurchargeDetailVO {
    /**
     * 附加费名称
     * @type {string}
     * @memberof SurchargeDetailVO
     */
    'name'?: string;
    /**
     * 附加费类型
     * @type {string}
     * @memberof SurchargeDetailVO
     */
    'feeType'?: SurchargeDetailVOFeeTypeEnum;
    /**
     * 附加费金额
     * @type {number}
     * @memberof SurchargeDetailVO
     */
    'amount'?: number;
    /**
     * 货币单位
     * @type {string}
     * @memberof SurchargeDetailVO
     */
    'currency'?: SurchargeDetailVOCurrencyEnum;
    /**
     * 适用阶段(头程/尾程)
     * @type {string}
     * @memberof SurchargeDetailVO
     */
    'stage'?: SurchargeDetailVOStageEnum;
}

export const SurchargeDetailVOFeeTypeEnum = {
    FUEL: 'FUEL',
    REMOTE: 'REMOTE',
    OVERSIZE: 'OVERSIZE',
    CUSTOMS: 'CUSTOMS',
    SIGNATURE: 'SIGNATURE',
    INSURANCE: 'INSURANCE',
    PACKAGING: 'PACKAGING',
    CONSUMPTION_TAX: 'CONSUMPTION_TAX'
} as const;

export type SurchargeDetailVOFeeTypeEnum = typeof SurchargeDetailVOFeeTypeEnum[keyof typeof SurchargeDetailVOFeeTypeEnum];
export const SurchargeDetailVOCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type SurchargeDetailVOCurrencyEnum = typeof SurchargeDetailVOCurrencyEnum[keyof typeof SurchargeDetailVOCurrencyEnum];
export const SurchargeDetailVOStageEnum = {
    HEAD: 'HEAD',
    TAIL: 'TAIL',
    ALL: 'ALL'
} as const;

export type SurchargeDetailVOStageEnum = typeof SurchargeDetailVOStageEnum[keyof typeof SurchargeDetailVOStageEnum];


