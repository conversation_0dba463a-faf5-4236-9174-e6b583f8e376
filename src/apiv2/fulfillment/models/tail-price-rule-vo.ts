/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 尾程运费规则响应VO
 * @export
 * @interface TailPriceRuleVO
 */
export interface TailPriceRuleVO {
    /**
     * 规则ID
     * @type {number}
     * @memberof TailPriceRuleVO
     */
    'id'?: number;
    /**
     * 物流服务商名称
     * @type {string}
     * @memberof TailPriceRuleVO
     */
    'providerName'?: string;
    /**
     * 规则名称
     * @type {string}
     * @memberof TailPriceRuleVO
     */
    'ruleName'?: string;
    /**
     * 创建人姓名
     * @type {string}
     * @memberof TailPriceRuleVO
     */
    'creator'?: string;
    /**
     * 创建时间
     * @type {string}
     * @memberof TailPriceRuleVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof TailPriceRuleVO
     */
    'updateTime'?: string;
}

