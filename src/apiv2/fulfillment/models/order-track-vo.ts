/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { OrderTrackSkuVO } from './order-track-sku-vo';

/**
 * 订单轨迹视图对象
 * @export
 * @interface OrderTrackVO
 */
export interface OrderTrackVO {
    /**
     * 订单号
     * @type {string}
     * @memberof OrderTrackVO
     */
    'orderNo'?: string;
    /**
     * 订单轨迹所有sku记录
     * @type {Array<OrderTrackSkuVO>}
     * @memberof OrderTrackVO
     */
    'skus'?: Array<OrderTrackSkuVO>;
}

