/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { Dimension } from './dimension';

/**
 * 运费计算请求模型
 * @export
 * @interface FreightCalculationRequest
 */
export interface FreightCalculationRequest {
    /**
     * 渠道ID
     * @type {number}
     * @memberof FreightCalculationRequest
     */
    'channelId'?: number;
    /**
     * 实际重量(g)
     * @type {number}
     * @memberof FreightCalculationRequest
     */
    'actualWeight': number;
    /**
     * 尺寸列表，每个元素包含长宽高三元组
     * @type {Array<Dimension>}
     * @memberof FreightCalculationRequest
     */
    'dimensions': Array<Dimension>;
    /**
     * 件数
     * @type {number}
     * @memberof FreightCalculationRequest
     */
    'quantity': number;
    /**
     * 目的地邮编
     * @type {string}
     * @memberof FreightCalculationRequest
     */
    'postalCode'?: string;
}

