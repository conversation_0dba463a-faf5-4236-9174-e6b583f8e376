/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PageVOHeadPriceRuleVO } from './page-vohead-price-rule-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOPageVOHeadPriceRuleVO
 */
export interface ResultVOPageVOHeadPriceRuleVO {
    /**
     * 状态码
     * @type {number}
     * @memberof ResultVOPageVOHeadPriceRuleVO
     */
    'code'?: number;
    /**
     * 消息
     * @type {string}
     * @memberof ResultVOPageVOHeadPriceRuleVO
     */
    'message'?: string;
    /**
     * 
     * @type {PageVOHeadPriceRuleVO}
     * @memberof ResultVOPageVOHeadPriceRuleVO
     */
    'data'?: PageVOHeadPriceRuleVO;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOPageVOHeadPriceRuleVO
     */
    'success'?: boolean;
}

