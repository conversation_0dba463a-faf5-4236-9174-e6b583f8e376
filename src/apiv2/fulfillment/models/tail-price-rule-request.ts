/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PriceDetailRequest } from './price-detail-request';
// May contain unused imports in some cases
// @ts-ignore
import type { SurchargeRequest } from './surcharge-request';

/**
 * 尾程运费规则请求DTO
 * @export
 * @interface TailPriceRuleRequest
 */
export interface TailPriceRuleRequest {
    /**
     * 规则名称
     * @type {string}
     * @memberof TailPriceRuleRequest
     */
    'ruleName'?: string;
    /**
     * 物流服务商ID
     * @type {number}
     * @memberof TailPriceRuleRequest
     */
    'providerId'?: number;
    /**
     * 计价方式
     * @type {string}
     * @memberof TailPriceRuleRequest
     */
    'pricingMethod'?: TailPriceRuleRequestPricingMethodEnum;
    /**
     * 首重
     * @type {number}
     * @memberof TailPriceRuleRequest
     */
    'firstWeight'?: number;
    /**
     * 首重费用
     * @type {number}
     * @memberof TailPriceRuleRequest
     */
    'firstFee'?: number;
    /**
     * 续重单位
     * @type {number}
     * @memberof TailPriceRuleRequest
     */
    'additionalWeight'?: number;
    /**
     * 续重费用
     * @type {number}
     * @memberof TailPriceRuleRequest
     */
    'additionalFee'?: number;
    /**
     * 货币单位：CNY/USD/JPY等
     * @type {string}
     * @memberof TailPriceRuleRequest
     */
    'currency'?: TailPriceRuleRequestCurrencyEnum;
    /**
     * 重量单位：KG/LB
     * @type {string}
     * @memberof TailPriceRuleRequest
     */
    'weightUnit'?: TailPriceRuleRequestWeightUnitEnum;
    /**
     * 基础费用(当pricing_method为UNIT/FIXED时)
     * @type {number}
     * @memberof TailPriceRuleRequest
     */
    'baseFee'?: number;
    /**
     * 单价(当pricing_method为UNIT时)
     * @type {number}
     * @memberof TailPriceRuleRequest
     */
    'unitPrice'?: number;
    /**
     * 体积系数
     * @type {number}
     * @memberof TailPriceRuleRequest
     */
    'volumeFactor'?: number;
    /**
     * 体积单位
     * @type {string}
     * @memberof TailPriceRuleRequest
     */
    'volumeUnit'?: string;
    /**
     * 状态：0-禁用，1-启用
     * @type {boolean}
     * @memberof TailPriceRuleRequest
     */
    'status'?: boolean;
    /**
     * 价格明细列表
     * @type {Array<PriceDetailRequest>}
     * @memberof TailPriceRuleRequest
     */
    'priceDetails'?: Array<PriceDetailRequest>;
    /**
     * 附加费列表
     * @type {Array<SurchargeRequest>}
     * @memberof TailPriceRuleRequest
     */
    'surcharges'?: Array<SurchargeRequest>;
}

export const TailPriceRuleRequestPricingMethodEnum = {
    WEIGHT_TIERED: 'WEIGHT_TIERED',
    DIMENSION_WEIGHT_TIERED: 'DIMENSION_WEIGHT_TIERED'
} as const;

export type TailPriceRuleRequestPricingMethodEnum = typeof TailPriceRuleRequestPricingMethodEnum[keyof typeof TailPriceRuleRequestPricingMethodEnum];
export const TailPriceRuleRequestCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type TailPriceRuleRequestCurrencyEnum = typeof TailPriceRuleRequestCurrencyEnum[keyof typeof TailPriceRuleRequestCurrencyEnum];
export const TailPriceRuleRequestWeightUnitEnum = {
    KG: 'KG',
    LB: 'LB'
} as const;

export type TailPriceRuleRequestWeightUnitEnum = typeof TailPriceRuleRequestWeightUnitEnum[keyof typeof TailPriceRuleRequestWeightUnitEnum];


