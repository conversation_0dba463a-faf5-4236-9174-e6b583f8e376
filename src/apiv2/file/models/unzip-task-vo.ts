/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { UnzipResultVO } from './unzip-result-vo';

/**
 * 数据
 * @export
 * @interface UnzipTaskVO
 */
export interface UnzipTaskVO {
    /**
     * 任务ID
     * @type {string}
     * @memberof UnzipTaskVO
     */
    'taskId'?: string;
    /**
     * 任务状态
     * @type {number}
     * @memberof UnzipTaskVO
     */
    'status'?: UnzipTaskVOStatusEnum;
    /**
     * 
     * @type {Array<UnzipResultVO>}
     * @memberof UnzipTaskVO
     */
    'result'?: Array<UnzipResultVO>;
}

export const UnzipTaskVOStatusEnum = {
    WAITING: 0,
    PROCESSING: 1,
    SUCCESS: 2,
    FAILED: 3,
    PARTIAL_SUCCESS: 4
} as const;

export type UnzipTaskVOStatusEnum = typeof UnzipTaskVOStatusEnum[keyof typeof UnzipTaskVOStatusEnum];


