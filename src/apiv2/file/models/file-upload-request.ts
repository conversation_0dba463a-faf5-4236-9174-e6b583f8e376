/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 文件上传请求
 * @export
 * @interface FileUploadRequest
 */
export interface FileUploadRequest {
    /**
     * 文件内容
     * @type {Array<string>}
     * @memberof FileUploadRequest
     */
    'fileData'?: Array<string>;
    /**
     * 访问权限类型
     * @type {string}
     * @memberof FileUploadRequest
     */
    'ossAccessType': string;
    /**
     * 上传文件类型枚举，包含在路径中
     * @type {string}
     * @memberof FileUploadRequest
     */
    'uploadFileType': string;
    /**
     * 是否需要异步处理，默认false
     * @type {boolean}
     * @memberof FileUploadRequest
     */
    'isAsync'?: boolean;
    /**
     * 内容类型
     * @type {string}
     * @memberof FileUploadRequest
     */
    'contentType'?: string;
    /**
     * 文件扩展名,如\'.txt\',\'.xlsx\' 
     * @type {string}
     * @memberof FileUploadRequest
     */
    'extension'?: string;
}

