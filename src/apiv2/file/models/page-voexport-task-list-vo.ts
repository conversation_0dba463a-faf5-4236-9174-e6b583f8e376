/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ExportTaskListVO } from './export-task-list-vo';

/**
 * API分页结果
 * @export
 * @interface PageVOExportTaskListVO
 */
export interface PageVOExportTaskListVO {
    /**
     * 当前页码
     * @type {number}
     * @memberof PageVOExportTaskListVO
     */
    'pageNum'?: number;
    /**
     * 每页大小
     * @type {number}
     * @memberof PageVOExportTaskListVO
     */
    'pageSize'?: number;
    /**
     * 总记录数
     * @type {number}
     * @memberof PageVOExportTaskListVO
     */
    'total'?: number;
    /**
     * 总页数
     * @type {number}
     * @memberof PageVOExportTaskListVO
     */
    'pages'?: number;
    /**
     * 当前页结果集
     * @type {Array<ExportTaskListVO>}
     * @memberof PageVOExportTaskListVO
     */
    'records'?: Array<ExportTaskListVO>;
}

