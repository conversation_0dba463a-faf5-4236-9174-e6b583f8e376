/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 导出任务列表视图
 * @export
 * @interface ExportTaskListVO
 */
export interface ExportTaskListVO {
    /**
     * 
     * @type {number}
     * @memberof ExportTaskListVO
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof ExportTaskListVO
     */
    'exportBizType'?: ExportTaskListVOExportBizTypeEnum;
    /**
     * 
     * @type {number}
     * @memberof ExportTaskListVO
     */
    'status'?: ExportTaskListVOStatusEnum;
    /**
     * 
     * @type {string}
     * @memberof ExportTaskListVO
     */
    'exportParams'?: string;
    /**
     * 
     * @type {string}
     * @memberof ExportTaskListVO
     */
    'fileName'?: string;
    /**
     * 
     * @type {string}
     * @memberof ExportTaskListVO
     */
    'exportPath'?: string;
    /**
     * 
     * @type {string}
     * @memberof ExportTaskListVO
     */
    'failedReason'?: string;
    /**
     * 
     * @type {number}
     * @memberof ExportTaskListVO
     */
    'userId'?: number;
    /**
     * 
     * @type {string}
     * @memberof ExportTaskListVO
     */
    'createTime'?: string;
    /**
     * 
     * @type {string}
     * @memberof ExportTaskListVO
     */
    'finishTime'?: string;
}

export const ExportTaskListVOExportBizTypeEnum = {
    PRODUCT_EXPORT: 0
} as const;

export type ExportTaskListVOExportBizTypeEnum = typeof ExportTaskListVOExportBizTypeEnum[keyof typeof ExportTaskListVOExportBizTypeEnum];
export const ExportTaskListVOStatusEnum = {
    FAILURE: -1,
    PENDING: 0,
    PROCESSING: 1,
    COMPLETED: 2
} as const;

export type ExportTaskListVOStatusEnum = typeof ExportTaskListVOStatusEnum[keyof typeof ExportTaskListVOStatusEnum];


