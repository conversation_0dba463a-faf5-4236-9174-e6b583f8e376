/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 分页查询请求对象
 * @export
 * @interface ExportTaskPageReq
 */
export interface ExportTaskPageReq {
    /**
     * 当前页码
     * @type {number}
     * @memberof ExportTaskPageReq
     */
    'pageNum'?: number;
    /**
     * 分页大小
     * @type {number}
     * @memberof ExportTaskPageReq
     */
    'pageSize'?: number;
    /**
     * 排序规则
     * @type {string}
     * @memberof ExportTaskPageReq
     */
    'orderBy'?: string;
    /**
     * 主键id
     * @type {number}
     * @memberof ExportTaskPageReq
     */
    'id'?: number;
    /**
     * 状态 -1 处理失败, 0 待处理, 1 处理中, 2 已完成
     * @type {number}
     * @memberof ExportTaskPageReq
     */
    'status'?: ExportTaskPageReqStatusEnum;
    /**
     * 报告名称
     * @type {string}
     * @memberof ExportTaskPageReq
     */
    'fileName'?: string;
    /**
     * 生成开始时间
     * @type {string}
     * @memberof ExportTaskPageReq
     */
    'finishStartTime'?: string;
    /**
     * 生成结束时间
     * @type {string}
     * @memberof ExportTaskPageReq
     */
    'finishEndTime'?: string;
    /**
     * 创建开始时间
     * @type {string}
     * @memberof ExportTaskPageReq
     */
    'createStartTime'?: string;
    /**
     * 创建结束时间
     * @type {string}
     * @memberof ExportTaskPageReq
     */
    'createEndTime'?: string;
    /**
     * 操作人账号
     * @type {number}
     * @memberof ExportTaskPageReq
     */
    'createUserCode'?: number;
}

export const ExportTaskPageReqStatusEnum = {
    FAILURE: -1,
    PENDING: 0,
    PROCESSING: 1,
    COMPLETED: 2
} as const;

export type ExportTaskPageReqStatusEnum = typeof ExportTaskPageReqStatusEnum[keyof typeof ExportTaskPageReqStatusEnum];


