/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { FileVO } from './file-vo';

/**
 * 
 * @export
 * @interface UnzipResultVO
 */
export interface UnzipResultVO {
    /**
     * 分类名称
     * @type {number}
     * @memberof UnzipResultVO
     */
    'classifyName'?: UnzipResultVOClassifyNameEnum;
    /**
     * 二级分类
     * @type {string}
     * @memberof UnzipResultVO
     */
    'secondName'?: string;
    /**
     * 文件url
     * @type {Array<FileVO>}
     * @memberof UnzipResultVO
     */
    'fileUrl'?: Array<FileVO>;
}

export const UnzipResultVOClassifyNameEnum = {
    PRODUCT_MAIN_IMAGE: 0,
    PRODUCT_SKU_IMAGE: 1,
    PRODUCT_DETAIL_IMAGE: 2,
    PRODUCT_3D_FILE: 3,
    PRODUCT_MANUAL: 4,
    PRODUCT_CERTIFICATION: 5,
    PRODUCT_SEO_VIDEO: 6,
    PRODUCT_ATTACHMENT: 7
} as const;

export type UnzipResultVOClassifyNameEnum = typeof UnzipResultVOClassifyNameEnum[keyof typeof UnzipResultVOClassifyNameEnum];


