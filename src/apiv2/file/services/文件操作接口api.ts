/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { FileUploadRequest } from '../models';
// @ts-ignore
import type { ResultVOFileUploadVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
/**
 * 文件操作接口Api - axios parameter creator
 * @export
 */
export const 文件操作接口ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 删除文件到OSS
         * @summary 删除文件
         * @param {string} filePath 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteFile: async (filePath: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'filePath' is not null or undefined
            assertParamExists('deleteFile', 'filePath', filePath)
            const localVarPath = `/api/v1/files/file`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (filePath !== undefined) {
                localVarQueryParameter['filePath'] = filePath;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 上传文件到OSS
         * @summary 上传文件
         * @param {FileUploadRequest} fileUploadRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadFile: async (fileUploadRequest: FileUploadRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'fileUploadRequest' is not null or undefined
            assertParamExists('uploadFile', 'fileUploadRequest', fileUploadRequest)
            const localVarPath = `/api/v1/files/upload-file`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(fileUploadRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 文件操作接口Api - functional programming interface
 * @export
 */
export const 文件操作接口ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 文件操作接口ApiAxiosParamCreator(configuration)
    return {
        /**
         * 删除文件到OSS
         * @summary 删除文件
         * @param {string} filePath 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteFile(filePath: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteFile(filePath, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['文件操作接口Api.deleteFile']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 上传文件到OSS
         * @summary 上传文件
         * @param {FileUploadRequest} fileUploadRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async uploadFile(fileUploadRequest: FileUploadRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOFileUploadVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.uploadFile(fileUploadRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['文件操作接口Api.uploadFile']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 文件操作接口Api - factory interface
 * @export
 */
export const 文件操作接口ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 文件操作接口ApiFp(configuration)
    return {
        /**
         * 删除文件到OSS
         * @summary 删除文件
         * @param {文件操作接口ApiDeleteFileRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteFile(requestParameters: 文件操作接口ApiDeleteFileRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.deleteFile(requestParameters.filePath, options).then((request) => request(axios, basePath));
        },
        /**
         * 上传文件到OSS
         * @summary 上传文件
         * @param {文件操作接口ApiUploadFileRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        uploadFile(requestParameters: 文件操作接口ApiUploadFileRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOFileUploadVO> {
            return localVarFp.uploadFile(requestParameters.fileUploadRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for deleteFile operation in 文件操作接口Api.
 * @export
 * @interface 文件操作接口ApiDeleteFileRequest
 */
export interface 文件操作接口ApiDeleteFileRequest {
    /**
     * 
     * @type {string}
     * @memberof 文件操作接口ApiDeleteFile
     */
    readonly filePath: string
}

/**
 * Request parameters for uploadFile operation in 文件操作接口Api.
 * @export
 * @interface 文件操作接口ApiUploadFileRequest
 */
export interface 文件操作接口ApiUploadFileRequest {
    /**
     * 
     * @type {FileUploadRequest}
     * @memberof 文件操作接口ApiUploadFile
     */
    readonly fileUploadRequest: FileUploadRequest
}

/**
 * 文件操作接口Api - object-oriented interface
 * @export
 * @class 文件操作接口Api
 * @extends {BaseAPI}
 */
export class 文件操作接口Api extends BaseAPI {
    /**
     * 删除文件到OSS
     * @summary 删除文件
     * @param {文件操作接口ApiDeleteFileRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 文件操作接口Api
     */
    public deleteFile(requestParameters: 文件操作接口ApiDeleteFileRequest, options?: RawAxiosRequestConfig) {
        return 文件操作接口ApiFp(this.configuration).deleteFile(requestParameters.filePath, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 上传文件到OSS
     * @summary 上传文件
     * @param {文件操作接口ApiUploadFileRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 文件操作接口Api
     */
    public uploadFile(requestParameters: 文件操作接口ApiUploadFileRequest, options?: RawAxiosRequestConfig) {
        return 文件操作接口ApiFp(this.configuration).uploadFile(requestParameters.fileUploadRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

