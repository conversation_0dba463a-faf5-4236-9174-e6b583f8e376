/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOUnzipTaskVO } from '../models';
// @ts-ignore
import type { ResultVOZipTaskVO } from '../models';
// @ts-ignore
import type { UnzipRequest } from '../models';
/**
 * 压缩文件的任务Api - axios parameter creator
 * @export
 */
export const 压缩文件的任务ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 查询解压缩结果
         * @param {string} taskId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryUnzipResult: async (taskId: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'taskId' is not null or undefined
            assertParamExists('queryUnzipResult', 'taskId', taskId)
            const localVarPath = `/api/v1/task/pollUnzipResult/{taskId}`
                .replace(`{${"taskId"}}`, encodeURIComponent(String(taskId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 提交解压缩任务
         * @summary 提交解压缩任务
         * @param {UnzipRequest} unzipRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submitZipTask: async (unzipRequest: UnzipRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'unzipRequest' is not null or undefined
            assertParamExists('submitZipTask', 'unzipRequest', unzipRequest)
            const localVarPath = `/api/v1/task/_zip`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(unzipRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 压缩文件的任务Api - functional programming interface
 * @export
 */
export const 压缩文件的任务ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 压缩文件的任务ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 查询解压缩结果
         * @param {string} taskId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async queryUnzipResult(taskId: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOUnzipTaskVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.queryUnzipResult(taskId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['压缩文件的任务Api.queryUnzipResult']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 提交解压缩任务
         * @summary 提交解压缩任务
         * @param {UnzipRequest} unzipRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async submitZipTask(unzipRequest: UnzipRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOZipTaskVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.submitZipTask(unzipRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['压缩文件的任务Api.submitZipTask']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 压缩文件的任务Api - factory interface
 * @export
 */
export const 压缩文件的任务ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 压缩文件的任务ApiFp(configuration)
    return {
        /**
         * 
         * @summary 查询解压缩结果
         * @param {压缩文件的任务ApiQueryUnzipResultRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryUnzipResult(requestParameters: 压缩文件的任务ApiQueryUnzipResultRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOUnzipTaskVO> {
            return localVarFp.queryUnzipResult(requestParameters.taskId, options).then((request) => request(axios, basePath));
        },
        /**
         * 提交解压缩任务
         * @summary 提交解压缩任务
         * @param {压缩文件的任务ApiSubmitZipTaskRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        submitZipTask(requestParameters: 压缩文件的任务ApiSubmitZipTaskRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOZipTaskVO> {
            return localVarFp.submitZipTask(requestParameters.unzipRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for queryUnzipResult operation in 压缩文件的任务Api.
 * @export
 * @interface 压缩文件的任务ApiQueryUnzipResultRequest
 */
export interface 压缩文件的任务ApiQueryUnzipResultRequest {
    /**
     * 
     * @type {string}
     * @memberof 压缩文件的任务ApiQueryUnzipResult
     */
    readonly taskId: string
}

/**
 * Request parameters for submitZipTask operation in 压缩文件的任务Api.
 * @export
 * @interface 压缩文件的任务ApiSubmitZipTaskRequest
 */
export interface 压缩文件的任务ApiSubmitZipTaskRequest {
    /**
     * 
     * @type {UnzipRequest}
     * @memberof 压缩文件的任务ApiSubmitZipTask
     */
    readonly unzipRequest: UnzipRequest
}

/**
 * 压缩文件的任务Api - object-oriented interface
 * @export
 * @class 压缩文件的任务Api
 * @extends {BaseAPI}
 */
export class 压缩文件的任务Api extends BaseAPI {
    /**
     * 
     * @summary 查询解压缩结果
     * @param {压缩文件的任务ApiQueryUnzipResultRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 压缩文件的任务Api
     */
    public queryUnzipResult(requestParameters: 压缩文件的任务ApiQueryUnzipResultRequest, options?: RawAxiosRequestConfig) {
        return 压缩文件的任务ApiFp(this.configuration).queryUnzipResult(requestParameters.taskId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 提交解压缩任务
     * @summary 提交解压缩任务
     * @param {压缩文件的任务ApiSubmitZipTaskRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 压缩文件的任务Api
     */
    public submitZipTask(requestParameters: 压缩文件的任务ApiSubmitZipTaskRequest, options?: RawAxiosRequestConfig) {
        return 压缩文件的任务ApiFp(this.configuration).submitZipTask(requestParameters.unzipRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

