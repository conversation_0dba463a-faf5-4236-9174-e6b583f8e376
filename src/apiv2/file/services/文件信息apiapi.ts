/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOFileContentVO } from '../models';
// @ts-ignore
import type { ResultVOFileImageVO } from '../models';
// @ts-ignore
import type { ResultVOFileMetadataVO } from '../models';
// @ts-ignore
import type { ResultVOOssUploadCredentialVO } from '../models';
// @ts-ignore
import type { ResultVOSignedUrlVO } from '../models';
/**
 * 文件信息APIApi - axios parameter creator
 * @export
 */
export const 文件信息APIApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 获取整个文件。慎用，遇到大文件时会产生巨大IO，后续应该抽取为断点续传解决该问题
         * @summary 获取文件
         * @param {string} filePath 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFile: async (filePath: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'filePath' is not null or undefined
            assertParamExists('getFile', 'filePath', filePath)
            const localVarPath = `/api/v1/files/_download`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (filePath !== undefined) {
                localVarQueryParameter['filePath'] = filePath;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取已经上传到OSS的文件的元数据
         * @summary 获取文件元数据
         * @param {string} filePath 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFileMetadata: async (filePath: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'filePath' is not null or undefined
            assertParamExists('getFileMetadata', 'filePath', filePath)
            const localVarPath = `/api/v1/files/metadata`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (filePath !== undefined) {
                localVarQueryParameter['filePath'] = filePath;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取图片文件的信息，宽高尺寸等
         * @summary 获取图片文件的信息
         * @param {string} filePath 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getImageInfo: async (filePath: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'filePath' is not null or undefined
            assertParamExists('getImageInfo', 'filePath', filePath)
            const localVarPath = `/api/v1/files/image-info`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (filePath !== undefined) {
                localVarQueryParameter['filePath'] = filePath;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @param {string} fileName 
         * @param {string} [uploadFileType] 
         * @param {string} [accessType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOssUploadCredential: async (fileName: string, uploadFileType?: string, accessType?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'fileName' is not null or undefined
            assertParamExists('getOssUploadCredential', 'fileName', fileName)
            const localVarPath = `/api/v1/files/upload-credential`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (fileName !== undefined) {
                localVarQueryParameter['fileName'] = fileName;
            }

            if (uploadFileType !== undefined) {
                localVarQueryParameter['uploadFileType'] = uploadFileType;
            }

            if (accessType !== undefined) {
                localVarQueryParameter['accessType'] = accessType;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 通过文件路径获取该文件的url，必要时带签名
         * @summary 获取带签名的URL
         * @param {string} filePath 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSignedUrl: async (filePath: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'filePath' is not null or undefined
            assertParamExists('getSignedUrl', 'filePath', filePath)
            const localVarPath = `/api/v1/files/signed-url`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (filePath !== undefined) {
                localVarQueryParameter['filePath'] = filePath;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 文件信息APIApi - functional programming interface
 * @export
 */
export const 文件信息APIApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 文件信息APIApiAxiosParamCreator(configuration)
    return {
        /**
         * 获取整个文件。慎用，遇到大文件时会产生巨大IO，后续应该抽取为断点续传解决该问题
         * @summary 获取文件
         * @param {string} filePath 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getFile(filePath: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOFileContentVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getFile(filePath, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['文件信息APIApi.getFile']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 获取已经上传到OSS的文件的元数据
         * @summary 获取文件元数据
         * @param {string} filePath 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getFileMetadata(filePath: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOFileMetadataVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getFileMetadata(filePath, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['文件信息APIApi.getFileMetadata']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 获取图片文件的信息，宽高尺寸等
         * @summary 获取图片文件的信息
         * @param {string} filePath 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getImageInfo(filePath: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOFileImageVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getImageInfo(filePath, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['文件信息APIApi.getImageInfo']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @param {string} fileName 
         * @param {string} [uploadFileType] 
         * @param {string} [accessType] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOssUploadCredential(fileName: string, uploadFileType?: string, accessType?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOOssUploadCredentialVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOssUploadCredential(fileName, uploadFileType, accessType, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['文件信息APIApi.getOssUploadCredential']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 通过文件路径获取该文件的url，必要时带签名
         * @summary 获取带签名的URL
         * @param {string} filePath 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getSignedUrl(filePath: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOSignedUrlVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getSignedUrl(filePath, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['文件信息APIApi.getSignedUrl']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 文件信息APIApi - factory interface
 * @export
 */
export const 文件信息APIApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 文件信息APIApiFp(configuration)
    return {
        /**
         * 获取整个文件。慎用，遇到大文件时会产生巨大IO，后续应该抽取为断点续传解决该问题
         * @summary 获取文件
         * @param {文件信息APIApiGetFileRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFile(requestParameters: 文件信息APIApiGetFileRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOFileContentVO> {
            return localVarFp.getFile(requestParameters.filePath, options).then((request) => request(axios, basePath));
        },
        /**
         * 获取已经上传到OSS的文件的元数据
         * @summary 获取文件元数据
         * @param {文件信息APIApiGetFileMetadataRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getFileMetadata(requestParameters: 文件信息APIApiGetFileMetadataRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOFileMetadataVO> {
            return localVarFp.getFileMetadata(requestParameters.filePath, options).then((request) => request(axios, basePath));
        },
        /**
         * 获取图片文件的信息，宽高尺寸等
         * @summary 获取图片文件的信息
         * @param {文件信息APIApiGetImageInfoRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getImageInfo(requestParameters: 文件信息APIApiGetImageInfoRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOFileImageVO> {
            return localVarFp.getImageInfo(requestParameters.filePath, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @param {文件信息APIApiGetOssUploadCredentialRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOssUploadCredential(requestParameters: 文件信息APIApiGetOssUploadCredentialRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOOssUploadCredentialVO> {
            return localVarFp.getOssUploadCredential(requestParameters.fileName, requestParameters.uploadFileType, requestParameters.accessType, options).then((request) => request(axios, basePath));
        },
        /**
         * 通过文件路径获取该文件的url，必要时带签名
         * @summary 获取带签名的URL
         * @param {文件信息APIApiGetSignedUrlRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getSignedUrl(requestParameters: 文件信息APIApiGetSignedUrlRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOSignedUrlVO> {
            return localVarFp.getSignedUrl(requestParameters.filePath, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getFile operation in 文件信息APIApi.
 * @export
 * @interface 文件信息APIApiGetFileRequest
 */
export interface 文件信息APIApiGetFileRequest {
    /**
     * 
     * @type {string}
     * @memberof 文件信息APIApiGetFile
     */
    readonly filePath: string
}

/**
 * Request parameters for getFileMetadata operation in 文件信息APIApi.
 * @export
 * @interface 文件信息APIApiGetFileMetadataRequest
 */
export interface 文件信息APIApiGetFileMetadataRequest {
    /**
     * 
     * @type {string}
     * @memberof 文件信息APIApiGetFileMetadata
     */
    readonly filePath: string
}

/**
 * Request parameters for getImageInfo operation in 文件信息APIApi.
 * @export
 * @interface 文件信息APIApiGetImageInfoRequest
 */
export interface 文件信息APIApiGetImageInfoRequest {
    /**
     * 
     * @type {string}
     * @memberof 文件信息APIApiGetImageInfo
     */
    readonly filePath: string
}

/**
 * Request parameters for getOssUploadCredential operation in 文件信息APIApi.
 * @export
 * @interface 文件信息APIApiGetOssUploadCredentialRequest
 */
export interface 文件信息APIApiGetOssUploadCredentialRequest {
    /**
     * 
     * @type {string}
     * @memberof 文件信息APIApiGetOssUploadCredential
     */
    readonly fileName: string

    /**
     * 
     * @type {string}
     * @memberof 文件信息APIApiGetOssUploadCredential
     */
    readonly uploadFileType?: string

    /**
     * 
     * @type {string}
     * @memberof 文件信息APIApiGetOssUploadCredential
     */
    readonly accessType?: string
}

/**
 * Request parameters for getSignedUrl operation in 文件信息APIApi.
 * @export
 * @interface 文件信息APIApiGetSignedUrlRequest
 */
export interface 文件信息APIApiGetSignedUrlRequest {
    /**
     * 
     * @type {string}
     * @memberof 文件信息APIApiGetSignedUrl
     */
    readonly filePath: string
}

/**
 * 文件信息APIApi - object-oriented interface
 * @export
 * @class 文件信息APIApi
 * @extends {BaseAPI}
 */
export class 文件信息APIApi extends BaseAPI {
    /**
     * 获取整个文件。慎用，遇到大文件时会产生巨大IO，后续应该抽取为断点续传解决该问题
     * @summary 获取文件
     * @param {文件信息APIApiGetFileRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 文件信息APIApi
     */
    public getFile(requestParameters: 文件信息APIApiGetFileRequest, options?: RawAxiosRequestConfig) {
        return 文件信息APIApiFp(this.configuration).getFile(requestParameters.filePath, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 获取已经上传到OSS的文件的元数据
     * @summary 获取文件元数据
     * @param {文件信息APIApiGetFileMetadataRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 文件信息APIApi
     */
    public getFileMetadata(requestParameters: 文件信息APIApiGetFileMetadataRequest, options?: RawAxiosRequestConfig) {
        return 文件信息APIApiFp(this.configuration).getFileMetadata(requestParameters.filePath, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 获取图片文件的信息，宽高尺寸等
     * @summary 获取图片文件的信息
     * @param {文件信息APIApiGetImageInfoRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 文件信息APIApi
     */
    public getImageInfo(requestParameters: 文件信息APIApiGetImageInfoRequest, options?: RawAxiosRequestConfig) {
        return 文件信息APIApiFp(this.configuration).getImageInfo(requestParameters.filePath, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @param {文件信息APIApiGetOssUploadCredentialRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 文件信息APIApi
     */
    public getOssUploadCredential(requestParameters: 文件信息APIApiGetOssUploadCredentialRequest, options?: RawAxiosRequestConfig) {
        return 文件信息APIApiFp(this.configuration).getOssUploadCredential(requestParameters.fileName, requestParameters.uploadFileType, requestParameters.accessType, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 通过文件路径获取该文件的url，必要时带签名
     * @summary 获取带签名的URL
     * @param {文件信息APIApiGetSignedUrlRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 文件信息APIApi
     */
    public getSignedUrl(requestParameters: 文件信息APIApiGetSignedUrlRequest, options?: RawAxiosRequestConfig) {
        return 文件信息APIApiFp(this.configuration).getSignedUrl(requestParameters.filePath, options).then((request) => request(this.axios, this.basePath));
    }
}

