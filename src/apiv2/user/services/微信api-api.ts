/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { BindWXIdToUserReq } from '../models';
// @ts-ignore
import type { ResultVOInteger } from '../models';
// @ts-ignore
import type { ResultVOListString } from '../models';
// @ts-ignore
import type { ResultVOWeChatSendMessageVO } from '../models';
// @ts-ignore
import type { SendStringMessageByUserReq } from '../models';
/**
 * 微信ApiApi - axios parameter creator
 * @export
 */
export const 微信ApiApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 绑定企业微信Id给用户
         * @param {BindWXIdToUserReq} bindWXIdToUserReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        bindWXIdToUser: async (bindWXIdToUserReq: BindWXIdToUserReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'bindWXIdToUserReq' is not null or undefined
            assertParamExists('bindWXIdToUser', 'bindWXIdToUserReq', bindWXIdToUserReq)
            const localVarPath = `/api/wx/bindWXIdToUser`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(bindWXIdToUserReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取企业微信所有成员Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getMembersIdList: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/wx/wx-get-member-id-list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 发送文本消息给指定的企业微信成员
         * @param {SendStringMessageByUserReq} sendStringMessageByUserReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        sendStringMessageByUser: async (sendStringMessageByUserReq: SendStringMessageByUserReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'sendStringMessageByUserReq' is not null or undefined
            assertParamExists('sendStringMessageByUser', 'sendStringMessageByUserReq', sendStringMessageByUserReq)
            const localVarPath = `/api/wx/sendMessageByUser`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(sendStringMessageByUserReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 微信ApiApi - functional programming interface
 * @export
 */
export const 微信ApiApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 微信ApiApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 绑定企业微信Id给用户
         * @param {BindWXIdToUserReq} bindWXIdToUserReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async bindWXIdToUser(bindWXIdToUserReq: BindWXIdToUserReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOInteger>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.bindWXIdToUser(bindWXIdToUserReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['微信ApiApi.bindWXIdToUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 获取企业微信所有成员Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getMembersIdList(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListString>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getMembersIdList(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['微信ApiApi.getMembersIdList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 发送文本消息给指定的企业微信成员
         * @param {SendStringMessageByUserReq} sendStringMessageByUserReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async sendStringMessageByUser(sendStringMessageByUserReq: SendStringMessageByUserReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOWeChatSendMessageVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.sendStringMessageByUser(sendStringMessageByUserReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['微信ApiApi.sendStringMessageByUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 微信ApiApi - factory interface
 * @export
 */
export const 微信ApiApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 微信ApiApiFp(configuration)
    return {
        /**
         * 
         * @summary 绑定企业微信Id给用户
         * @param {微信ApiApiBindWXIdToUserRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        bindWXIdToUser(requestParameters: 微信ApiApiBindWXIdToUserRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOInteger> {
            return localVarFp.bindWXIdToUser(requestParameters.bindWXIdToUserReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取企业微信所有成员Id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getMembersIdList(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListString> {
            return localVarFp.getMembersIdList(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 发送文本消息给指定的企业微信成员
         * @param {微信ApiApiSendStringMessageByUserRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        sendStringMessageByUser(requestParameters: 微信ApiApiSendStringMessageByUserRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOWeChatSendMessageVO> {
            return localVarFp.sendStringMessageByUser(requestParameters.sendStringMessageByUserReq, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for bindWXIdToUser operation in 微信ApiApi.
 * @export
 * @interface 微信ApiApiBindWXIdToUserRequest
 */
export interface 微信ApiApiBindWXIdToUserRequest {
    /**
     * 
     * @type {BindWXIdToUserReq}
     * @memberof 微信ApiApiBindWXIdToUser
     */
    readonly bindWXIdToUserReq: BindWXIdToUserReq
}

/**
 * Request parameters for sendStringMessageByUser operation in 微信ApiApi.
 * @export
 * @interface 微信ApiApiSendStringMessageByUserRequest
 */
export interface 微信ApiApiSendStringMessageByUserRequest {
    /**
     * 
     * @type {SendStringMessageByUserReq}
     * @memberof 微信ApiApiSendStringMessageByUser
     */
    readonly sendStringMessageByUserReq: SendStringMessageByUserReq
}

/**
 * 微信ApiApi - object-oriented interface
 * @export
 * @class 微信ApiApi
 * @extends {BaseAPI}
 */
export class 微信ApiApi extends BaseAPI {
    /**
     * 
     * @summary 绑定企业微信Id给用户
     * @param {微信ApiApiBindWXIdToUserRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 微信ApiApi
     */
    public bindWXIdToUser(requestParameters: 微信ApiApiBindWXIdToUserRequest, options?: RawAxiosRequestConfig) {
        return 微信ApiApiFp(this.configuration).bindWXIdToUser(requestParameters.bindWXIdToUserReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 获取企业微信所有成员Id
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 微信ApiApi
     */
    public getMembersIdList(options?: RawAxiosRequestConfig) {
        return 微信ApiApiFp(this.configuration).getMembersIdList(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 发送文本消息给指定的企业微信成员
     * @param {微信ApiApiSendStringMessageByUserRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 微信ApiApi
     */
    public sendStringMessageByUser(requestParameters: 微信ApiApiSendStringMessageByUserRequest, options?: RawAxiosRequestConfig) {
        return 微信ApiApiFp(this.configuration).sendStringMessageByUser(requestParameters.sendStringMessageByUserReq, options).then((request) => request(this.axios, this.basePath));
    }
}

