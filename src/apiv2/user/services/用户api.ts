/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { PasswordUpdateReq } from '../models';
// @ts-ignore
import type { ResultVO } from '../models';
// @ts-ignore
import type { ResultVOCaptchaValueVO } from '../models';
// @ts-ignore
import type { ResultVOInteger } from '../models';
// @ts-ignore
import type { ResultVOListUserDetailVO } from '../models';
// @ts-ignore
import type { ResultVOPageVOUserWithDeptInfoVO } from '../models';
// @ts-ignore
import type { ResultVOString } from '../models';
// @ts-ignore
import type { ResultVOUserDetailVO } from '../models';
// @ts-ignore
import type { ResultVOUserLoginDetailVO } from '../models';
// @ts-ignore
import type { UserCreateReq } from '../models';
// @ts-ignore
import type { UserLoginReq } from '../models';
// @ts-ignore
import type { UserQueryReq } from '../models';
// @ts-ignore
import type { UserSearchReq } from '../models';
/**
 * 用户Api - axios parameter creator
 * @export
 */
export const 用户ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取微信应用级API Ticket
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        acquireWeixinAppApiTicket: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/users/wx-app-api-ticket/get`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取微信企业级API Ticket
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        acquireWeixinCorpApiTicket: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/users/wx-corp-api-ticket/get`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 添加用户
         * @param {UserCreateReq} userCreateReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create: async (userCreateReq: UserCreateReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userCreateReq' is not null or undefined
            assertParamExists('create', 'userCreateReq', userCreateReq)
            const localVarPath = `/api/users`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(userCreateReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 禁用用户
         * @param {number} userId 用户ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        disableUser: async (userId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('disableUser', 'userId', userId)
            const localVarPath = `/api/users/{userId}/disable`
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 启用用户
         * @param {number} userId 用户ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        enableUser: async (userId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('enableUser', 'userId', userId)
            const localVarPath = `/api/users/{userId}/enable`
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 生成登录校验码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        generateCaptcha: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/users/generate-captcha`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取单个用户信息
         * @param {number} userId 用户ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        get: async (userId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userId' is not null or undefined
            assertParamExists('get', 'userId', userId)
            const localVarPath = `/api/users/{userId}`
                .replace(`{${"userId"}}`, encodeURIComponent(String(userId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 批量获取用户信息
         * @param {Array<number>} userIds 用户ID集合
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUsersByIds: async (userIds: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userIds' is not null or undefined
            assertParamExists('getUsersByIds', 'userIds', userIds)
            const localVarPath = `/api/users`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (userIds) {
                localVarQueryParameter['userIds'] = userIds;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 用于刷新会话防止超时自动退出
         * @summary 保持登录
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        keepLoggedIn: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/users/keep-logged-in`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 用户登录
         * @param {UserLoginReq} userLoginReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        login: async (userLoginReq: UserLoginReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userLoginReq' is not null or undefined
            assertParamExists('login', 'userLoginReq', userLoginReq)
            const localVarPath = `/api/users/login`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(userLoginReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 用户退出
         * @param {string} jwt JWT
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        logout: async (jwt: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'jwt' is not null or undefined
            assertParamExists('logout', 'jwt', jwt)
            const localVarPath = `/api/users/logout`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (jwt !== undefined) {
                localVarQueryParameter['jwt'] = jwt;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary OAuth登录回调接口
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        oauthCallback: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/users/oauth-callback`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 根据条件查询用户
         * @param {UserQueryReq} userQueryReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        query: async (userQueryReq: UserQueryReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userQueryReq' is not null or undefined
            assertParamExists('query', 'userQueryReq', userQueryReq)
            const localVarPath = `/api/users/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(userQueryReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 搜索用户信息
         * @param {UserSearchReq} userSearchReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        search: async (userSearchReq: UserSearchReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userSearchReq' is not null or undefined
            assertParamExists('search', 'userSearchReq', userSearchReq)
            const localVarPath = `/api/users/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(userSearchReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 修改用户密码
         * @param {PasswordUpdateReq} passwordUpdateReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updatePassword: async (passwordUpdateReq: PasswordUpdateReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'passwordUpdateReq' is not null or undefined
            assertParamExists('updatePassword', 'passwordUpdateReq', passwordUpdateReq)
            const localVarPath = `/api/users/update-self-password`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(passwordUpdateReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 用户Api - functional programming interface
 * @export
 */
export const 用户ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 用户ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 获取微信应用级API Ticket
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async acquireWeixinAppApiTicket(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOString>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.acquireWeixinAppApiTicket(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.acquireWeixinAppApiTicket']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 获取微信企业级API Ticket
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async acquireWeixinCorpApiTicket(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOString>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.acquireWeixinCorpApiTicket(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.acquireWeixinCorpApiTicket']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 添加用户
         * @param {UserCreateReq} userCreateReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async create(userCreateReq: UserCreateReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOInteger>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.create(userCreateReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.create']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 禁用用户
         * @param {number} userId 用户ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async disableUser(userId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.disableUser(userId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.disableUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 启用用户
         * @param {number} userId 用户ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async enableUser(userId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.enableUser(userId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.enableUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 生成登录校验码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async generateCaptcha(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOCaptchaValueVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.generateCaptcha(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.generateCaptcha']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 获取单个用户信息
         * @param {number} userId 用户ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async get(userId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOUserDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.get(userId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.get']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 批量获取用户信息
         * @param {Array<number>} userIds 用户ID集合
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getUsersByIds(userIds: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListUserDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getUsersByIds(userIds, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.getUsersByIds']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 用于刷新会话防止超时自动退出
         * @summary 保持登录
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async keepLoggedIn(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.keepLoggedIn(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.keepLoggedIn']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 用户登录
         * @param {UserLoginReq} userLoginReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async login(userLoginReq: UserLoginReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOUserLoginDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.login(userLoginReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.login']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 用户退出
         * @param {string} jwt JWT
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async logout(jwt: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.logout(jwt, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.logout']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary OAuth登录回调接口
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async oauthCallback(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.oauthCallback(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.oauthCallback']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 根据条件查询用户
         * @param {UserQueryReq} userQueryReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async query(userQueryReq: UserQueryReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOUserWithDeptInfoVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.query(userQueryReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.query']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 搜索用户信息
         * @param {UserSearchReq} userSearchReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async search(userSearchReq: UserSearchReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOUserWithDeptInfoVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.search(userSearchReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.search']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 修改用户密码
         * @param {PasswordUpdateReq} passwordUpdateReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updatePassword(passwordUpdateReq: PasswordUpdateReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updatePassword(passwordUpdateReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['用户Api.updatePassword']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 用户Api - factory interface
 * @export
 */
export const 用户ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 用户ApiFp(configuration)
    return {
        /**
         * 
         * @summary 获取微信应用级API Ticket
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        acquireWeixinAppApiTicket(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOString> {
            return localVarFp.acquireWeixinAppApiTicket(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取微信企业级API Ticket
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        acquireWeixinCorpApiTicket(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOString> {
            return localVarFp.acquireWeixinCorpApiTicket(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 添加用户
         * @param {用户ApiCreateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create(requestParameters: 用户ApiCreateRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOInteger> {
            return localVarFp.create(requestParameters.userCreateReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 禁用用户
         * @param {用户ApiDisableUserRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        disableUser(requestParameters: 用户ApiDisableUserRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVO> {
            return localVarFp.disableUser(requestParameters.userId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 启用用户
         * @param {用户ApiEnableUserRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        enableUser(requestParameters: 用户ApiEnableUserRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVO> {
            return localVarFp.enableUser(requestParameters.userId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 生成登录校验码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        generateCaptcha(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOCaptchaValueVO> {
            return localVarFp.generateCaptcha(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取单个用户信息
         * @param {用户ApiGetRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        get(requestParameters: 用户ApiGetRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOUserDetailVO> {
            return localVarFp.get(requestParameters.userId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 批量获取用户信息
         * @param {用户ApiGetUsersByIdsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getUsersByIds(requestParameters: 用户ApiGetUsersByIdsRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListUserDetailVO> {
            return localVarFp.getUsersByIds(requestParameters.userIds, options).then((request) => request(axios, basePath));
        },
        /**
         * 用于刷新会话防止超时自动退出
         * @summary 保持登录
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        keepLoggedIn(options?: RawAxiosRequestConfig): AxiosPromise<ResultVO> {
            return localVarFp.keepLoggedIn(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 用户登录
         * @param {用户ApiLoginRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        login(requestParameters: 用户ApiLoginRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOUserLoginDetailVO> {
            return localVarFp.login(requestParameters.userLoginReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 用户退出
         * @param {用户ApiLogoutRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        logout(requestParameters: 用户ApiLogoutRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVO> {
            return localVarFp.logout(requestParameters.jwt, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary OAuth登录回调接口
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        oauthCallback(options?: RawAxiosRequestConfig): AxiosPromise<ResultVO> {
            return localVarFp.oauthCallback(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 根据条件查询用户
         * @param {用户ApiQueryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        query(requestParameters: 用户ApiQueryRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOUserWithDeptInfoVO> {
            return localVarFp.query(requestParameters.userQueryReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 搜索用户信息
         * @param {用户ApiSearchRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        search(requestParameters: 用户ApiSearchRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOUserWithDeptInfoVO> {
            return localVarFp.search(requestParameters.userSearchReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 修改用户密码
         * @param {用户ApiUpdatePasswordRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updatePassword(requestParameters: 用户ApiUpdatePasswordRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVO> {
            return localVarFp.updatePassword(requestParameters.passwordUpdateReq, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for create operation in 用户Api.
 * @export
 * @interface 用户ApiCreateRequest
 */
export interface 用户ApiCreateRequest {
    /**
     * 
     * @type {UserCreateReq}
     * @memberof 用户ApiCreate
     */
    readonly userCreateReq: UserCreateReq
}

/**
 * Request parameters for disableUser operation in 用户Api.
 * @export
 * @interface 用户ApiDisableUserRequest
 */
export interface 用户ApiDisableUserRequest {
    /**
     * 用户ID
     * @type {number}
     * @memberof 用户ApiDisableUser
     */
    readonly userId: number
}

/**
 * Request parameters for enableUser operation in 用户Api.
 * @export
 * @interface 用户ApiEnableUserRequest
 */
export interface 用户ApiEnableUserRequest {
    /**
     * 用户ID
     * @type {number}
     * @memberof 用户ApiEnableUser
     */
    readonly userId: number
}

/**
 * Request parameters for get operation in 用户Api.
 * @export
 * @interface 用户ApiGetRequest
 */
export interface 用户ApiGetRequest {
    /**
     * 用户ID
     * @type {number}
     * @memberof 用户ApiGet
     */
    readonly userId: number
}

/**
 * Request parameters for getUsersByIds operation in 用户Api.
 * @export
 * @interface 用户ApiGetUsersByIdsRequest
 */
export interface 用户ApiGetUsersByIdsRequest {
    /**
     * 用户ID集合
     * @type {Array<number>}
     * @memberof 用户ApiGetUsersByIds
     */
    readonly userIds: Array<number>
}

/**
 * Request parameters for login operation in 用户Api.
 * @export
 * @interface 用户ApiLoginRequest
 */
export interface 用户ApiLoginRequest {
    /**
     * 
     * @type {UserLoginReq}
     * @memberof 用户ApiLogin
     */
    readonly userLoginReq: UserLoginReq
}

/**
 * Request parameters for logout operation in 用户Api.
 * @export
 * @interface 用户ApiLogoutRequest
 */
export interface 用户ApiLogoutRequest {
    /**
     * JWT
     * @type {string}
     * @memberof 用户ApiLogout
     */
    readonly jwt: string
}

/**
 * Request parameters for query operation in 用户Api.
 * @export
 * @interface 用户ApiQueryRequest
 */
export interface 用户ApiQueryRequest {
    /**
     * 
     * @type {UserQueryReq}
     * @memberof 用户ApiQuery
     */
    readonly userQueryReq: UserQueryReq
}

/**
 * Request parameters for search operation in 用户Api.
 * @export
 * @interface 用户ApiSearchRequest
 */
export interface 用户ApiSearchRequest {
    /**
     * 
     * @type {UserSearchReq}
     * @memberof 用户ApiSearch
     */
    readonly userSearchReq: UserSearchReq
}

/**
 * Request parameters for updatePassword operation in 用户Api.
 * @export
 * @interface 用户ApiUpdatePasswordRequest
 */
export interface 用户ApiUpdatePasswordRequest {
    /**
     * 
     * @type {PasswordUpdateReq}
     * @memberof 用户ApiUpdatePassword
     */
    readonly passwordUpdateReq: PasswordUpdateReq
}

/**
 * 用户Api - object-oriented interface
 * @export
 * @class 用户Api
 * @extends {BaseAPI}
 */
export class 用户Api extends BaseAPI {
    /**
     * 
     * @summary 获取微信应用级API Ticket
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public acquireWeixinAppApiTicket(options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).acquireWeixinAppApiTicket(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 获取微信企业级API Ticket
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public acquireWeixinCorpApiTicket(options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).acquireWeixinCorpApiTicket(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 添加用户
     * @param {用户ApiCreateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public create(requestParameters: 用户ApiCreateRequest, options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).create(requestParameters.userCreateReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 禁用用户
     * @param {用户ApiDisableUserRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public disableUser(requestParameters: 用户ApiDisableUserRequest, options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).disableUser(requestParameters.userId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 启用用户
     * @param {用户ApiEnableUserRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public enableUser(requestParameters: 用户ApiEnableUserRequest, options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).enableUser(requestParameters.userId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 生成登录校验码
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public generateCaptcha(options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).generateCaptcha(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 获取单个用户信息
     * @param {用户ApiGetRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public get(requestParameters: 用户ApiGetRequest, options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).get(requestParameters.userId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 批量获取用户信息
     * @param {用户ApiGetUsersByIdsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public getUsersByIds(requestParameters: 用户ApiGetUsersByIdsRequest, options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).getUsersByIds(requestParameters.userIds, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 用于刷新会话防止超时自动退出
     * @summary 保持登录
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public keepLoggedIn(options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).keepLoggedIn(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 用户登录
     * @param {用户ApiLoginRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public login(requestParameters: 用户ApiLoginRequest, options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).login(requestParameters.userLoginReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 用户退出
     * @param {用户ApiLogoutRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public logout(requestParameters: 用户ApiLogoutRequest, options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).logout(requestParameters.jwt, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary OAuth登录回调接口
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public oauthCallback(options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).oauthCallback(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 根据条件查询用户
     * @param {用户ApiQueryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public query(requestParameters: 用户ApiQueryRequest, options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).query(requestParameters.userQueryReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 搜索用户信息
     * @param {用户ApiSearchRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public search(requestParameters: 用户ApiSearchRequest, options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).search(requestParameters.userSearchReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 修改用户密码
     * @param {用户ApiUpdatePasswordRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 用户Api
     */
    public updatePassword(requestParameters: 用户ApiUpdatePasswordRequest, options?: RawAxiosRequestConfig) {
        return 用户ApiFp(this.configuration).updatePassword(requestParameters.passwordUpdateReq, options).then((request) => request(this.axios, this.basePath));
    }
}

