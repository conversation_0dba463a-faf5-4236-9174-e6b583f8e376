/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVO } from '../models';
// @ts-ignore
import type { ResultVOListRoleListItemVO } from '../models';
// @ts-ignore
import type { ResultVOPageVORoleListItemVO } from '../models';
// @ts-ignore
import type { ResultVORoleDetailVO } from '../models';
// @ts-ignore
import type { RoleDTOReq } from '../models';
// @ts-ignore
import type { RoleQueryReq } from '../models';
// @ts-ignore
import type { UserRoleSetReq } from '../models';
/**
 * 角色Api - axios parameter creator
 * @export
 */
export const 角色ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 添加角色
         * @param {RoleDTOReq} roleDTOReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create1: async (roleDTOReq: RoleDTOReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'roleDTOReq' is not null or undefined
            assertParamExists('create1', 'roleDTOReq', roleDTOReq)
            const localVarPath = `/api/roles`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(roleDTOReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 查询角色
         * @param {number} roleId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        get1: async (roleId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'roleId' is not null or undefined
            assertParamExists('get1', 'roleId', roleId)
            const localVarPath = `/api/roles/{roleId}`
                .replace(`{${"roleId"}}`, encodeURIComponent(String(roleId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 角色列表
         * @param {RoleQueryReq} roleQueryReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        list: async (roleQueryReq: RoleQueryReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'roleQueryReq' is not null or undefined
            assertParamExists('list', 'roleQueryReq', roleQueryReq)
            const localVarPath = `/api/roles/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(roleQueryReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 角色列表(不分页)
         * @param {RoleQueryReq} roleQueryReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        query1: async (roleQueryReq: RoleQueryReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'roleQueryReq' is not null or undefined
            assertParamExists('query1', 'roleQueryReq', roleQueryReq)
            const localVarPath = `/api/roles/non-paginated-list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(roleQueryReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 设置用户角色
         * @param {UserRoleSetReq} userRoleSetReq 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        setRoleOfUser: async (userRoleSetReq: UserRoleSetReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'userRoleSetReq' is not null or undefined
            assertParamExists('setRoleOfUser', 'userRoleSetReq', userRoleSetReq)
            const localVarPath = `/api/roles/user-relation/set`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(userRoleSetReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 编辑角色
         * @param {RoleDTOReq} roleDTOReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        update: async (roleDTOReq: RoleDTOReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'roleDTOReq' is not null or undefined
            assertParamExists('update', 'roleDTOReq', roleDTOReq)
            const localVarPath = `/api/roles/update`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(roleDTOReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 角色Api - functional programming interface
 * @export
 */
export const 角色ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 角色ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 添加角色
         * @param {RoleDTOReq} roleDTOReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async create1(roleDTOReq: RoleDTOReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.create1(roleDTOReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['角色Api.create1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 查询角色
         * @param {number} roleId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async get1(roleId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVORoleDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.get1(roleId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['角色Api.get1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 角色列表
         * @param {RoleQueryReq} roleQueryReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async list(roleQueryReq: RoleQueryReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVORoleListItemVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.list(roleQueryReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['角色Api.list']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 角色列表(不分页)
         * @param {RoleQueryReq} roleQueryReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async query1(roleQueryReq: RoleQueryReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListRoleListItemVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.query1(roleQueryReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['角色Api.query1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 设置用户角色
         * @param {UserRoleSetReq} userRoleSetReq 
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        async setRoleOfUser(userRoleSetReq: UserRoleSetReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.setRoleOfUser(userRoleSetReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['角色Api.setRoleOfUser']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 编辑角色
         * @param {RoleDTOReq} roleDTOReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async update(roleDTOReq: RoleDTOReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.update(roleDTOReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['角色Api.update']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 角色Api - factory interface
 * @export
 */
export const 角色ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 角色ApiFp(configuration)
    return {
        /**
         * 
         * @summary 添加角色
         * @param {角色ApiCreate1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        create1(requestParameters: 角色ApiCreate1Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVO> {
            return localVarFp.create1(requestParameters.roleDTOReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 查询角色
         * @param {角色ApiGet1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        get1(requestParameters: 角色ApiGet1Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVORoleDetailVO> {
            return localVarFp.get1(requestParameters.roleId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 角色列表
         * @param {角色ApiListRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        list(requestParameters: 角色ApiListRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVORoleListItemVO> {
            return localVarFp.list(requestParameters.roleQueryReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 角色列表(不分页)
         * @param {角色ApiQuery1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        query1(requestParameters: 角色ApiQuery1Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListRoleListItemVO> {
            return localVarFp.query1(requestParameters.roleQueryReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 设置用户角色
         * @param {角色ApiSetRoleOfUserRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @deprecated
         * @throws {RequiredError}
         */
        setRoleOfUser(requestParameters: 角色ApiSetRoleOfUserRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVO> {
            return localVarFp.setRoleOfUser(requestParameters.userRoleSetReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 编辑角色
         * @param {角色ApiUpdateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        update(requestParameters: 角色ApiUpdateRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVO> {
            return localVarFp.update(requestParameters.roleDTOReq, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for create1 operation in 角色Api.
 * @export
 * @interface 角色ApiCreate1Request
 */
export interface 角色ApiCreate1Request {
    /**
     * 
     * @type {RoleDTOReq}
     * @memberof 角色ApiCreate1
     */
    readonly roleDTOReq: RoleDTOReq
}

/**
 * Request parameters for get1 operation in 角色Api.
 * @export
 * @interface 角色ApiGet1Request
 */
export interface 角色ApiGet1Request {
    /**
     * 
     * @type {number}
     * @memberof 角色ApiGet1
     */
    readonly roleId: number
}

/**
 * Request parameters for list operation in 角色Api.
 * @export
 * @interface 角色ApiListRequest
 */
export interface 角色ApiListRequest {
    /**
     * 
     * @type {RoleQueryReq}
     * @memberof 角色ApiList
     */
    readonly roleQueryReq: RoleQueryReq
}

/**
 * Request parameters for query1 operation in 角色Api.
 * @export
 * @interface 角色ApiQuery1Request
 */
export interface 角色ApiQuery1Request {
    /**
     * 
     * @type {RoleQueryReq}
     * @memberof 角色ApiQuery1
     */
    readonly roleQueryReq: RoleQueryReq
}

/**
 * Request parameters for setRoleOfUser operation in 角色Api.
 * @export
 * @interface 角色ApiSetRoleOfUserRequest
 */
export interface 角色ApiSetRoleOfUserRequest {
    /**
     * 
     * @type {UserRoleSetReq}
     * @memberof 角色ApiSetRoleOfUser
     */
    readonly userRoleSetReq: UserRoleSetReq
}

/**
 * Request parameters for update operation in 角色Api.
 * @export
 * @interface 角色ApiUpdateRequest
 */
export interface 角色ApiUpdateRequest {
    /**
     * 
     * @type {RoleDTOReq}
     * @memberof 角色ApiUpdate
     */
    readonly roleDTOReq: RoleDTOReq
}

/**
 * 角色Api - object-oriented interface
 * @export
 * @class 角色Api
 * @extends {BaseAPI}
 */
export class 角色Api extends BaseAPI {
    /**
     * 
     * @summary 添加角色
     * @param {角色ApiCreate1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 角色Api
     */
    public create1(requestParameters: 角色ApiCreate1Request, options?: RawAxiosRequestConfig) {
        return 角色ApiFp(this.configuration).create1(requestParameters.roleDTOReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 查询角色
     * @param {角色ApiGet1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 角色Api
     */
    public get1(requestParameters: 角色ApiGet1Request, options?: RawAxiosRequestConfig) {
        return 角色ApiFp(this.configuration).get1(requestParameters.roleId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 角色列表
     * @param {角色ApiListRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 角色Api
     */
    public list(requestParameters: 角色ApiListRequest, options?: RawAxiosRequestConfig) {
        return 角色ApiFp(this.configuration).list(requestParameters.roleQueryReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 角色列表(不分页)
     * @param {角色ApiQuery1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 角色Api
     */
    public query1(requestParameters: 角色ApiQuery1Request, options?: RawAxiosRequestConfig) {
        return 角色ApiFp(this.configuration).query1(requestParameters.roleQueryReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 设置用户角色
     * @param {角色ApiSetRoleOfUserRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @deprecated
     * @throws {RequiredError}
     * @memberof 角色Api
     */
    public setRoleOfUser(requestParameters: 角色ApiSetRoleOfUserRequest, options?: RawAxiosRequestConfig) {
        return 角色ApiFp(this.configuration).setRoleOfUser(requestParameters.userRoleSetReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 编辑角色
     * @param {角色ApiUpdateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 角色Api
     */
    public update(requestParameters: 角色ApiUpdateRequest, options?: RawAxiosRequestConfig) {
        return 角色ApiFp(this.configuration).update(requestParameters.roleDTOReq, options).then((request) => request(this.axios, this.basePath));
    }
}

