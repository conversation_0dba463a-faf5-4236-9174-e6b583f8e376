/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 发送消息到微信
 * @export
 * @interface WeChatSendMessageVO
 */
export interface WeChatSendMessageVO {
    /**
     * 微信返回的code
     * @type {number}
     * @memberof WeChatSendMessageVO
     */
    'errcode'?: number;
    /**
     * 微信返回错误信息
     * @type {string}
     * @memberof WeChatSendMessageVO
     */
    'errmsg'?: string;
    /**
     * 不合法的userid，不区分大小写，统一转为小写
     * @type {string}
     * @memberof WeChatSendMessageVO
     */
    'invaliduser'?: string;
    /**
     * 不合法的partyid(部门id)
     * @type {string}
     * @memberof WeChatSendMessageVO
     */
    'invalidparty'?: string;
    /**
     * 不合法的标签id
     * @type {string}
     * @memberof WeChatSendMessageVO
     */
    'invalidtag'?: string;
    /**
     * 没有基础接口许可(包含已过期)的userid
     * @type {string}
     * @memberof WeChatSendMessageVO
     */
    'unlicenseduser'?: string;
    /**
     * 
     * @type {string}
     * @memberof WeChatSendMessageVO
     */
    'msgid'?: string;
    /**
     * 
     * @type {string}
     * @memberof WeChatSendMessageVO
     */
    'response_code'?: string;
}

