/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 用户信息（包括部门）
 * @export
 * @interface UserWithDeptInfoVO
 */
export interface UserWithDeptInfoVO {
    /**
     * 用户ID
     * @type {number}
     * @memberof UserWithDeptInfoVO
     */
    'id'?: number;
    /**
     * 用户名称
     * @type {string}
     * @memberof UserWithDeptInfoVO
     */
    'userName'?: string;
    /**
     * 姓名
     * @type {string}
     * @memberof UserWithDeptInfoVO
     */
    'name'?: string;
    /**
     * 电话
     * @type {string}
     * @memberof UserWithDeptInfoVO
     */
    'phone'?: string;
    /**
     * 邮箱
     * @type {string}
     * @memberof UserWithDeptInfoVO
     */
    'email'?: string;
    /**
     * 描述
     * @type {string}
     * @memberof UserWithDeptInfoVO
     */
    'userDescription'?: string;
    /**
     * 是否被禁用，0-否，1-是
     * @type {number}
     * @memberof UserWithDeptInfoVO
     */
    'isDisabled'?: number;
    /**
     * 是否删除，0-否，1-是
     * @type {number}
     * @memberof UserWithDeptInfoVO
     */
    'isDeleted'?: number;
    /**
     * 创建时间
     * @type {string}
     * @memberof UserWithDeptInfoVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof UserWithDeptInfoVO
     */
    'updateTime'?: string;
    /**
     * 所属部门ID
     * @type {number}
     * @memberof UserWithDeptInfoVO
     */
    'departmentId'?: number;
    /**
     * 所属部门名称
     * @type {string}
     * @memberof UserWithDeptInfoVO
     */
    'departmentName'?: string;
    /**
     * 所属部门ID路径
     * @type {string}
     * @memberof UserWithDeptInfoVO
     */
    'departmentPath'?: string;
}

