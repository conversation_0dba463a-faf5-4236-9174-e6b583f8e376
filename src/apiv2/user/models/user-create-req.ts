/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 用户创建请求
 * @export
 * @interface UserCreateReq
 */
export interface UserCreateReq {
    /**
     * 用户名称
     * @type {string}
     * @memberof UserCreateReq
     */
    'userName': string;
    /**
     * 用户密码，使用RSA公钥对密码加密后的字符串
     * @type {string}
     * @memberof UserCreateReq
     */
    'userPassword': string;
    /**
     * 姓名
     * @type {string}
     * @memberof UserCreateReq
     */
    'name'?: string;
    /**
     * 电话
     * @type {string}
     * @memberof UserCreateReq
     */
    'phone'?: string;
    /**
     * 邮箱
     * @type {string}
     * @memberof UserCreateReq
     */
    'email'?: string;
    /**
     * 用户描述
     * @type {string}
     * @memberof UserCreateReq
     */
    'userDescription'?: string;
    /**
     * 关联的角色ID
     * @type {Array<number>}
     * @memberof UserCreateReq
     */
    'roleIds': Array<number>;
}

