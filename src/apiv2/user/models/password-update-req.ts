/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 用户密码更新请求
 * @export
 * @interface PasswordUpdateReq
 */
export interface PasswordUpdateReq {
    /**
     * 用户旧密码，使用RSA公钥对密码加密后的字符串
     * @type {string}
     * @memberof PasswordUpdateReq
     */
    'oldPassword': string;
    /**
     * 用户密码，使用RSA公钥对密码加密后的字符串
     * @type {string}
     * @memberof PasswordUpdateReq
     */
    'newPassword': string;
}

