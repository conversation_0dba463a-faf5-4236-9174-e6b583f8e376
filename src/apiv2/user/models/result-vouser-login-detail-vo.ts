/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { UserLoginDetailVO } from './user-login-detail-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOUserLoginDetailVO
 */
export interface ResultVOUserLoginDetailVO {
    /**
     * 错误码
     * @type {number}
     * @memberof ResultVOUserLoginDetailVO
     */
    'code'?: number;
    /**
     * 错误消息
     * @type {string}
     * @memberof ResultVOUserLoginDetailVO
     */
    'message'?: string;
    /**
     * 
     * @type {UserLoginDetailVO}
     * @memberof ResultVOUserLoginDetailVO
     */
    'data'?: UserLoginDetailVO;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOUserLoginDetailVO
     */
    'success'?: boolean;
}

