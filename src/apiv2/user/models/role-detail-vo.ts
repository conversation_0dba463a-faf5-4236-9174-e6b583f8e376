/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 角色详情
 * @export
 * @interface RoleDetailVO
 */
export interface RoleDetailVO {
    /**
     * 角色id
     * @type {number}
     * @memberof RoleDetailVO
     */
    'id'?: number;
    /**
     * 角色名称
     * @type {string}
     * @memberof RoleDetailVO
     */
    'roleName'?: string;
    /**
     * 包含的权限点ID
     * @type {Array<number>}
     * @memberof RoleDetailVO
     */
    'permissionIds'?: Array<number>;
    /**
     * 角色类别
     * @type {number}
     * @memberof RoleDetailVO
     */
    'roleType'?: number;
    /**
     * 角色描述
     * @type {string}
     * @memberof RoleDetailVO
     */
    'roleDesc'?: string;
}

