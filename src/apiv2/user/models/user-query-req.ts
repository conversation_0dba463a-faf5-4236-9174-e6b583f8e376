/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 用户查询条件
 * @export
 * @interface UserQueryReq
 */
export interface UserQueryReq {
    /**
     * 当前页码
     * @type {number}
     * @memberof UserQueryReq
     */
    'pageNum'?: number;
    /**
     * 分页大小
     * @type {number}
     * @memberof UserQueryReq
     */
    'pageSize'?: number;
    /**
     * 排序规则
     * @type {string}
     * @memberof UserQueryReq
     */
    'orderBy'?: string;
    /**
     * 用户ID
     * @type {number}
     * @memberof UserQueryReq
     */
    'id'?: number;
    /**
     * 用户名称
     * @type {string}
     * @memberof UserQueryReq
     */
    'userName'?: string;
    /**
     * 姓名
     * @type {string}
     * @memberof UserQueryReq
     */
    'name'?: string;
    /**
     * 角色id
     * @type {number}
     * @memberof UserQueryReq
     */
    'roleId'?: number;
    /**
     * 部门path
     * @type {string}
     * @memberof UserQueryReq
     */
    'departmentPath'?: string;
    /**
     * 部门名称
     * @type {string}
     * @memberof UserQueryReq
     */
    'departmentName'?: string;
    /**
     * 是否包含下属部门
     * @type {boolean}
     * @memberof UserQueryReq
     */
    'includeChildren'?: boolean;
    /**
     * 是否禁用
     * @type {boolean}
     * @memberof UserQueryReq
     */
    'disabled'?: boolean;
}

