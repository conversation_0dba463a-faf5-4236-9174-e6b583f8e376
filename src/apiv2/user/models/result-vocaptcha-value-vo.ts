/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CaptchaValueVO } from './captcha-value-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOCaptchaValueVO
 */
export interface ResultVOCaptchaValueVO {
    /**
     * 错误码
     * @type {number}
     * @memberof ResultVOCaptchaValueVO
     */
    'code'?: number;
    /**
     * 错误消息
     * @type {string}
     * @memberof ResultVOCaptchaValueVO
     */
    'message'?: string;
    /**
     * 
     * @type {CaptchaValueVO}
     * @memberof ResultVOCaptchaValueVO
     */
    'data'?: CaptchaValueVO;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOCaptchaValueVO
     */
    'success'?: boolean;
}

