/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 角色列表项
 * @export
 * @interface RoleListItemVO
 */
export interface RoleListItemVO {
    /**
     * 角色id
     * @type {number}
     * @memberof RoleListItemVO
     */
    'id'?: number;
    /**
     * 角色名称
     * @type {string}
     * @memberof RoleListItemVO
     */
    'roleName'?: string;
    /**
     * 角色编码
     * @type {string}
     * @memberof RoleListItemVO
     */
    'roleCode'?: string;
    /**
     * 角色类型，1：系统预置，2：自定义
     * @type {number}
     * @memberof RoleListItemVO
     */
    'roleType'?: number;
    /**
     * 角色描述
     * @type {string}
     * @memberof RoleListItemVO
     */
    'roleDescription'?: string;
    /**
     * 创建时间
     * @type {string}
     * @memberof RoleListItemVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof RoleListItemVO
     */
    'updateTime'?: string;
}

