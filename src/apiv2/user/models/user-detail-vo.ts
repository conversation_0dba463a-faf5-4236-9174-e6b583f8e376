/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 用户详情
 * @export
 * @interface UserDetailVO
 */
export interface UserDetailVO {
    /**
     * 用户id
     * @type {number}
     * @memberof UserDetailVO
     */
    'userId'?: number;
    /**
     * 用户名称
     * @type {string}
     * @memberof UserDetailVO
     */
    'userName'?: string;
    /**
     * 姓名
     * @type {string}
     * @memberof UserDetailVO
     */
    'name'?: string;
    /**
     * 电话
     * @type {string}
     * @memberof UserDetailVO
     */
    'phone'?: string;
    /**
     * 邮箱
     * @type {string}
     * @memberof UserDetailVO
     */
    'email'?: string;
    /**
     * 用户描述
     * @type {string}
     * @memberof UserDetailVO
     */
    'userDescription'?: string;
    /**
     * 角色id
     * @type {Array<number>}
     * @memberof UserDetailVO
     */
    'roleList'?: Array<number>;
    /**
     * 用户具有的权限点编码
     * @type {Array<string>}
     * @memberof UserDetailVO
     */
    'permissionCodes'?: Array<string>;
    /**
     * 是否被禁用，0-否，1-是
     * @type {number}
     * @memberof UserDetailVO
     */
    'isDisabled'?: number;
    /**
     * 创建时间
     * @type {string}
     * @memberof UserDetailVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof UserDetailVO
     */
    'updateTime'?: string;
}

