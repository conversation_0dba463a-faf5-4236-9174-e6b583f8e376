/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { SupplierDetailResponse } from '../models';
/**
 * 供应商对内服务接口Api - axios parameter creator
 * @export
 */
export const 供应商对内服务接口ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 一次最多查询200个供应商
         * @summary 批量查询供应商详情
         * @param {Array<number>} requestBody 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        querySupplierDetail: async (requestBody: Array<number>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'requestBody' is not null or undefined
            assertParamExists('querySupplierDetail', 'requestBody', requestBody)
            const localVarPath = `/api/suppliers/internal/querySupplier`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(requestBody, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 供应商对内服务接口Api - functional programming interface
 * @export
 */
export const 供应商对内服务接口ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 供应商对内服务接口ApiAxiosParamCreator(configuration)
    return {
        /**
         * 一次最多查询200个供应商
         * @summary 批量查询供应商详情
         * @param {Array<number>} requestBody 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async querySupplierDetail(requestBody: Array<number>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<Array<SupplierDetailResponse>>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.querySupplierDetail(requestBody, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['供应商对内服务接口Api.querySupplierDetail']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 供应商对内服务接口Api - factory interface
 * @export
 */
export const 供应商对内服务接口ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 供应商对内服务接口ApiFp(configuration)
    return {
        /**
         * 一次最多查询200个供应商
         * @summary 批量查询供应商详情
         * @param {供应商对内服务接口ApiQuerySupplierDetailRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        querySupplierDetail(requestParameters: 供应商对内服务接口ApiQuerySupplierDetailRequest, options?: RawAxiosRequestConfig): AxiosPromise<Array<SupplierDetailResponse>> {
            return localVarFp.querySupplierDetail(requestParameters.requestBody, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for querySupplierDetail operation in 供应商对内服务接口Api.
 * @export
 * @interface 供应商对内服务接口ApiQuerySupplierDetailRequest
 */
export interface 供应商对内服务接口ApiQuerySupplierDetailRequest {
    /**
     * 
     * @type {Array<number>}
     * @memberof 供应商对内服务接口ApiQuerySupplierDetail
     */
    readonly requestBody: Array<number>
}

/**
 * 供应商对内服务接口Api - object-oriented interface
 * @export
 * @class 供应商对内服务接口Api
 * @extends {BaseAPI}
 */
export class 供应商对内服务接口Api extends BaseAPI {
    /**
     * 一次最多查询200个供应商
     * @summary 批量查询供应商详情
     * @param {供应商对内服务接口ApiQuerySupplierDetailRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 供应商对内服务接口Api
     */
    public querySupplierDetail(requestParameters: 供应商对内服务接口ApiQuerySupplierDetailRequest, options?: RawAxiosRequestConfig) {
        return 供应商对内服务接口ApiFp(this.configuration).querySupplierDetail(requestParameters.requestBody, options).then((request) => request(this.axios, this.basePath));
    }
}

