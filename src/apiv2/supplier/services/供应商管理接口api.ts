/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOBoolean } from '../models';
// @ts-ignore
import type { ResultVOPageVOSupplierListVO } from '../models';
// @ts-ignore
import type { ResultVOString } from '../models';
// @ts-ignore
import type { ResultVOSupplierDetailVO } from '../models';
// @ts-ignore
import type { SupplierCreateRequest } from '../models';
// @ts-ignore
import type { SupplierPageRequest } from '../models';
// @ts-ignore
import type { SupplierUpdateRequest } from '../models';
/**
 * 供应商管理接口Api - axios parameter creator
 * @export
 */
export const 供应商管理接口ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 编辑供应商详情
         * @param {SupplierCreateRequest} supplierCreateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSupplier: async (supplierCreateRequest: SupplierCreateRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'supplierCreateRequest' is not null or undefined
            assertParamExists('createSupplier', 'supplierCreateRequest', supplierCreateRequest)
            const localVarPath = `/api/suppliers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(supplierCreateRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 搜索供应商列表
         * @param {SupplierPageRequest} supplierPageRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listSuppliers: async (supplierPageRequest: SupplierPageRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'supplierPageRequest' is not null or undefined
            assertParamExists('listSuppliers', 'supplierPageRequest', supplierPageRequest)
            const localVarPath = `/api/suppliers/list`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(supplierPageRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 查询供应商详情
         * @param {number} supplierId 供应商id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        querySupplierDetail1: async (supplierId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'supplierId' is not null or undefined
            assertParamExists('querySupplierDetail1', 'supplierId', supplierId)
            const localVarPath = `/api/suppliers/{supplierId}`
                .replace(`{${"supplierId"}}`, encodeURIComponent(String(supplierId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新供应商信息
         * @param {SupplierUpdateRequest} supplierUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSupplier: async (supplierUpdateRequest: SupplierUpdateRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'supplierUpdateRequest' is not null or undefined
            assertParamExists('updateSupplier', 'supplierUpdateRequest', supplierUpdateRequest)
            const localVarPath = `/api/suppliers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(supplierUpdateRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 供应商管理接口Api - functional programming interface
 * @export
 */
export const 供应商管理接口ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 供应商管理接口ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 编辑供应商详情
         * @param {SupplierCreateRequest} supplierCreateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createSupplier(supplierCreateRequest: SupplierCreateRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOString>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createSupplier(supplierCreateRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['供应商管理接口Api.createSupplier']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 搜索供应商列表
         * @param {SupplierPageRequest} supplierPageRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listSuppliers(supplierPageRequest: SupplierPageRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOSupplierListVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listSuppliers(supplierPageRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['供应商管理接口Api.listSuppliers']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 查询供应商详情
         * @param {number} supplierId 供应商id
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async querySupplierDetail1(supplierId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOSupplierDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.querySupplierDetail1(supplierId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['供应商管理接口Api.querySupplierDetail1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 更新供应商信息
         * @param {SupplierUpdateRequest} supplierUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateSupplier(supplierUpdateRequest: SupplierUpdateRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOBoolean>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateSupplier(supplierUpdateRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['供应商管理接口Api.updateSupplier']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 供应商管理接口Api - factory interface
 * @export
 */
export const 供应商管理接口ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 供应商管理接口ApiFp(configuration)
    return {
        /**
         * 
         * @summary 编辑供应商详情
         * @param {供应商管理接口ApiCreateSupplierRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createSupplier(requestParameters: 供应商管理接口ApiCreateSupplierRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOString> {
            return localVarFp.createSupplier(requestParameters.supplierCreateRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 搜索供应商列表
         * @param {供应商管理接口ApiListSuppliersRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listSuppliers(requestParameters: 供应商管理接口ApiListSuppliersRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOSupplierListVO> {
            return localVarFp.listSuppliers(requestParameters.supplierPageRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 查询供应商详情
         * @param {供应商管理接口ApiQuerySupplierDetail1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        querySupplierDetail1(requestParameters: 供应商管理接口ApiQuerySupplierDetail1Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOSupplierDetailVO> {
            return localVarFp.querySupplierDetail1(requestParameters.supplierId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新供应商信息
         * @param {供应商管理接口ApiUpdateSupplierRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateSupplier(requestParameters: 供应商管理接口ApiUpdateSupplierRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOBoolean> {
            return localVarFp.updateSupplier(requestParameters.supplierUpdateRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createSupplier operation in 供应商管理接口Api.
 * @export
 * @interface 供应商管理接口ApiCreateSupplierRequest
 */
export interface 供应商管理接口ApiCreateSupplierRequest {
    /**
     * 
     * @type {SupplierCreateRequest}
     * @memberof 供应商管理接口ApiCreateSupplier
     */
    readonly supplierCreateRequest: SupplierCreateRequest
}

/**
 * Request parameters for listSuppliers operation in 供应商管理接口Api.
 * @export
 * @interface 供应商管理接口ApiListSuppliersRequest
 */
export interface 供应商管理接口ApiListSuppliersRequest {
    /**
     * 
     * @type {SupplierPageRequest}
     * @memberof 供应商管理接口ApiListSuppliers
     */
    readonly supplierPageRequest: SupplierPageRequest
}

/**
 * Request parameters for querySupplierDetail1 operation in 供应商管理接口Api.
 * @export
 * @interface 供应商管理接口ApiQuerySupplierDetail1Request
 */
export interface 供应商管理接口ApiQuerySupplierDetail1Request {
    /**
     * 供应商id
     * @type {number}
     * @memberof 供应商管理接口ApiQuerySupplierDetail1
     */
    readonly supplierId: number
}

/**
 * Request parameters for updateSupplier operation in 供应商管理接口Api.
 * @export
 * @interface 供应商管理接口ApiUpdateSupplierRequest
 */
export interface 供应商管理接口ApiUpdateSupplierRequest {
    /**
     * 
     * @type {SupplierUpdateRequest}
     * @memberof 供应商管理接口ApiUpdateSupplier
     */
    readonly supplierUpdateRequest: SupplierUpdateRequest
}

/**
 * 供应商管理接口Api - object-oriented interface
 * @export
 * @class 供应商管理接口Api
 * @extends {BaseAPI}
 */
export class 供应商管理接口Api extends BaseAPI {
    /**
     * 
     * @summary 编辑供应商详情
     * @param {供应商管理接口ApiCreateSupplierRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 供应商管理接口Api
     */
    public createSupplier(requestParameters: 供应商管理接口ApiCreateSupplierRequest, options?: RawAxiosRequestConfig) {
        return 供应商管理接口ApiFp(this.configuration).createSupplier(requestParameters.supplierCreateRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 搜索供应商列表
     * @param {供应商管理接口ApiListSuppliersRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 供应商管理接口Api
     */
    public listSuppliers(requestParameters: 供应商管理接口ApiListSuppliersRequest, options?: RawAxiosRequestConfig) {
        return 供应商管理接口ApiFp(this.configuration).listSuppliers(requestParameters.supplierPageRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 查询供应商详情
     * @param {供应商管理接口ApiQuerySupplierDetail1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 供应商管理接口Api
     */
    public querySupplierDetail1(requestParameters: 供应商管理接口ApiQuerySupplierDetail1Request, options?: RawAxiosRequestConfig) {
        return 供应商管理接口ApiFp(this.configuration).querySupplierDetail1(requestParameters.supplierId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 更新供应商信息
     * @param {供应商管理接口ApiUpdateSupplierRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 供应商管理接口Api
     */
    public updateSupplier(requestParameters: 供应商管理接口ApiUpdateSupplierRequest, options?: RawAxiosRequestConfig) {
        return 供应商管理接口ApiFp(this.configuration).updateSupplier(requestParameters.supplierUpdateRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

