/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 供应商列表VO
 * @export
 * @interface SupplierListVO
 */
export interface SupplierListVO {
    /**
     * 供应商ID
     * @type {number}
     * @memberof SupplierListVO
     */
    'id'?: number;
    /**
     * 供应商编码
     * @type {string}
     * @memberof SupplierListVO
     */
    'supplierCode'?: string;
    /**
     * 供应商简称
     * @type {string}
     * @memberof SupplierListVO
     */
    'shortName'?: string;
    /**
     * 供应商状态
     * @type {number}
     * @memberof SupplierListVO
     */
    'status'?: SupplierListVOStatusEnum;
    /**
     * 一级分类
     * @type {number}
     * @memberof SupplierListVO
     */
    'firstCategory'?: SupplierListVOFirstCategoryEnum;
    /**
     * 二级分类
     * @type {number}
     * @memberof SupplierListVO
     */
    'secondCategory'?: number;
    /**
     * 合作模式
     * @type {number}
     * @memberof SupplierListVO
     */
    'cooperationMode'?: SupplierListVOCooperationModeEnum;
    /**
     * 供应商评级
     * @type {number}
     * @memberof SupplierListVO
     */
    'rating'?: SupplierListVORatingEnum;
    /**
     * 跟进对接人ID
     * @type {number}
     * @memberof SupplierListVO
     */
    'responsibleUserId'?: number;
    /**
     * 是否有品牌
     * @type {boolean}
     * @memberof SupplierListVO
     */
    'brandFlag'?: boolean;
    /**
     * 更新时间
     * @type {string}
     * @memberof SupplierListVO
     */
    'updatedTime'?: string;
}

export const SupplierListVOStatusEnum = {
    PUBLISHED: 0,
    PENDING_PUBLISH: 1,
    INTRODUCING: 2,
    NEWLY_DEVELOPED: 3,
    PENDING_COMMUNICATION: 4,
    SUSPENDED: 5
} as const;

export type SupplierListVOStatusEnum = typeof SupplierListVOStatusEnum[keyof typeof SupplierListVOStatusEnum];
export const SupplierListVOFirstCategoryEnum = {
    ONLINE: 0,
    OFFLINE: 1
} as const;

export type SupplierListVOFirstCategoryEnum = typeof SupplierListVOFirstCategoryEnum[keyof typeof SupplierListVOFirstCategoryEnum];
export const SupplierListVOCooperationModeEnum = {
    DROP_SHIPPING: 0,
    WHOLESALE: 1
} as const;

export type SupplierListVOCooperationModeEnum = typeof SupplierListVOCooperationModeEnum[keyof typeof SupplierListVOCooperationModeEnum];
export const SupplierListVORatingEnum = {
    S: 0,
    A: 1,
    B: 2,
    C: 3
} as const;

export type SupplierListVORatingEnum = typeof SupplierListVORatingEnum[keyof typeof SupplierListVORatingEnum];


