/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { SupplierListVO } from './supplier-list-vo';

/**
 * API分页结果
 * @export
 * @interface PageVOSupplierListVO
 */
export interface PageVOSupplierListVO {
    /**
     * 当前页码
     * @type {number}
     * @memberof PageVOSupplierListVO
     */
    'pageNum'?: number;
    /**
     * 每页大小
     * @type {number}
     * @memberof PageVOSupplierListVO
     */
    'pageSize'?: number;
    /**
     * 总记录数
     * @type {number}
     * @memberof PageVOSupplierListVO
     */
    'total'?: number;
    /**
     * 总页数
     * @type {number}
     * @memberof PageVOSupplierListVO
     */
    'pages'?: number;
    /**
     * 当前页结果集
     * @type {Array<SupplierListVO>}
     * @memberof PageVOSupplierListVO
     */
    'records'?: Array<SupplierListVO>;
}

