/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 资质文件列表
 * @export
 * @interface SupplierDocumentCreateRequest
 */
export interface SupplierDocumentCreateRequest {
    /**
     * 文件类型
     * @type {number}
     * @memberof SupplierDocumentCreateRequest
     */
    'documentType'?: SupplierDocumentCreateRequestDocumentTypeEnum;
    /**
     * 文件URL
     * @type {string}
     * @memberof SupplierDocumentCreateRequest
     */
    'filePath'?: string;
    /**
     * 文件名称
     * @type {string}
     * @memberof SupplierDocumentCreateRequest
     */
    'fileName'?: string;
}

export const SupplierDocumentCreateRequestDocumentTypeEnum = {
    BUSINESS_LICENSE: 0,
    TAX_REGISTRATION: 1,
    QUALITY_INSPECTION_REPORT: 2
} as const;

export type SupplierDocumentCreateRequestDocumentTypeEnum = typeof SupplierDocumentCreateRequestDocumentTypeEnum[keyof typeof SupplierDocumentCreateRequestDocumentTypeEnum];


