/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { SupplierAddressUpdateRequest } from './supplier-address-update-request';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierContactUpdateRequest } from './supplier-contact-update-request';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierDocumentUpdateRequest } from './supplier-document-update-request';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierFinanceUpdateRequest } from './supplier-finance-update-request';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierStoreUpdateRequest } from './supplier-store-update-request';

/**
 * 供应商信息
 * @export
 * @interface SupplierUpdateRequest
 */
export interface SupplierUpdateRequest {
    /**
     * 供应商编码
     * @type {string}
     * @memberof SupplierUpdateRequest
     */
    'supplierCode'?: string;
    /**
     * 公司全称
     * @type {string}
     * @memberof SupplierUpdateRequest
     */
    'supplierName'?: string;
    /**
     * 供应商简称
     * @type {string}
     * @memberof SupplierUpdateRequest
     */
    'shortName'?: string;
    /**
     * 状态
     * @type {number}
     * @memberof SupplierUpdateRequest
     */
    'status'?: SupplierUpdateRequestStatusEnum;
    /**
     * 一级分类ID
     * @type {number}
     * @memberof SupplierUpdateRequest
     */
    'firstCategoryId'?: SupplierUpdateRequestFirstCategoryIdEnum;
    /**
     * 二级分类ID
     * @type {number}
     * @memberof SupplierUpdateRequest
     */
    'secondCategoryId'?: number;
    /**
     * 合作模式
     * @type {number}
     * @memberof SupplierUpdateRequest
     */
    'cooperationMode'?: SupplierUpdateRequestCooperationModeEnum;
    /**
     * 评级(S/A/B/C/D)
     * @type {number}
     * @memberof SupplierUpdateRequest
     */
    'rating'?: SupplierUpdateRequestRatingEnum;
    /**
     * 结算方式
     * @type {number}
     * @memberof SupplierUpdateRequest
     */
    'settlementMethod'?: SupplierUpdateRequestSettlementMethodEnum;
    /**
     * 支付方式
     * @type {number}
     * @memberof SupplierUpdateRequest
     */
    'paymentMethod'?: SupplierUpdateRequestPaymentMethodEnum;
    /**
     * 对接人用户ID
     * @type {number}
     * @memberof SupplierUpdateRequest
     */
    'responsibleUserId'?: number;
    /**
     * 是否品牌商
     * @type {boolean}
     * @memberof SupplierUpdateRequest
     */
    'brandFlag'?: boolean;
    /**
     * 备注
     * @type {string}
     * @memberof SupplierUpdateRequest
     */
    'remark'?: string;
    /**
     * 供应商id
     * @type {number}
     * @memberof SupplierUpdateRequest
     */
    'id'?: number;
    /**
     * 联系人列表
     * @type {Array<SupplierContactUpdateRequest>}
     * @memberof SupplierUpdateRequest
     */
    'contacts'?: Array<SupplierContactUpdateRequest>;
    /**
     * 资质文件列表
     * @type {Array<SupplierDocumentUpdateRequest>}
     * @memberof SupplierUpdateRequest
     */
    'documents'?: Array<SupplierDocumentUpdateRequest>;
    /**
     * 财务信息
     * @type {Array<SupplierFinanceUpdateRequest>}
     * @memberof SupplierUpdateRequest
     */
    'finance'?: Array<SupplierFinanceUpdateRequest>;
    /**
     * 店铺信息列表
     * @type {Array<SupplierStoreUpdateRequest>}
     * @memberof SupplierUpdateRequest
     */
    'stores'?: Array<SupplierStoreUpdateRequest>;
    /**
     * 地址信息列表
     * @type {Array<SupplierAddressUpdateRequest>}
     * @memberof SupplierUpdateRequest
     */
    'addresses'?: Array<SupplierAddressUpdateRequest>;
    /**
     * 删除标记
     * @type {boolean}
     * @memberof SupplierUpdateRequest
     */
    'deletedFlag'?: boolean;
}

export const SupplierUpdateRequestStatusEnum = {
    PUBLISHED: 0,
    PENDING_PUBLISH: 1,
    INTRODUCING: 2,
    NEWLY_DEVELOPED: 3,
    PENDING_COMMUNICATION: 4,
    SUSPENDED: 5
} as const;

export type SupplierUpdateRequestStatusEnum = typeof SupplierUpdateRequestStatusEnum[keyof typeof SupplierUpdateRequestStatusEnum];
export const SupplierUpdateRequestFirstCategoryIdEnum = {
    ONLINE: 0,
    OFFLINE: 1
} as const;

export type SupplierUpdateRequestFirstCategoryIdEnum = typeof SupplierUpdateRequestFirstCategoryIdEnum[keyof typeof SupplierUpdateRequestFirstCategoryIdEnum];
export const SupplierUpdateRequestCooperationModeEnum = {
    DROP_SHIPPING: 0,
    WHOLESALE: 1
} as const;

export type SupplierUpdateRequestCooperationModeEnum = typeof SupplierUpdateRequestCooperationModeEnum[keyof typeof SupplierUpdateRequestCooperationModeEnum];
export const SupplierUpdateRequestRatingEnum = {
    S: 0,
    A: 1,
    B: 2,
    C: 3
} as const;

export type SupplierUpdateRequestRatingEnum = typeof SupplierUpdateRequestRatingEnum[keyof typeof SupplierUpdateRequestRatingEnum];
export const SupplierUpdateRequestSettlementMethodEnum = {
    MONTHLY_SETTLEMENT: 0,
    BIWEEKLY_SETTLEMENT: 1,
    WEEKLY_SETTLEMENT: 2,
    IMMEDIATE_SETTLEMENT: 3
} as const;

export type SupplierUpdateRequestSettlementMethodEnum = typeof SupplierUpdateRequestSettlementMethodEnum[keyof typeof SupplierUpdateRequestSettlementMethodEnum];
export const SupplierUpdateRequestPaymentMethodEnum = {
    ONLINE_BANK_TRANSFER: 0,
    ONLINE_PAYMENT: 1
} as const;

export type SupplierUpdateRequestPaymentMethodEnum = typeof SupplierUpdateRequestPaymentMethodEnum[keyof typeof SupplierUpdateRequestPaymentMethodEnum];


