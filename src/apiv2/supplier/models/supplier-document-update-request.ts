/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 供应商文件更新参数
 * @export
 * @interface SupplierDocumentUpdateRequest
 */
export interface SupplierDocumentUpdateRequest {
    /**
     * 文件类型
     * @type {number}
     * @memberof SupplierDocumentUpdateRequest
     */
    'documentType'?: SupplierDocumentUpdateRequestDocumentTypeEnum;
    /**
     * 文件URL
     * @type {string}
     * @memberof SupplierDocumentUpdateRequest
     */
    'filePath'?: string;
    /**
     * 文件名称
     * @type {string}
     * @memberof SupplierDocumentUpdateRequest
     */
    'fileName'?: string;
    /**
     * 文件ID
     * @type {number}
     * @memberof SupplierDocumentUpdateRequest
     */
    'id'?: number;
    /**
     * 删除标识
     * @type {boolean}
     * @memberof SupplierDocumentUpdateRequest
     */
    'deletedFlag'?: boolean;
}

export const SupplierDocumentUpdateRequestDocumentTypeEnum = {
    BUSINESS_LICENSE: 0,
    TAX_REGISTRATION: 1,
    QUALITY_INSPECTION_REPORT: 2
} as const;

export type SupplierDocumentUpdateRequestDocumentTypeEnum = typeof SupplierDocumentUpdateRequestDocumentTypeEnum[keyof typeof SupplierDocumentUpdateRequestDocumentTypeEnum];


