/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 地址信息列表
 * @export
 * @interface SupplierAddressCreateRequest
 */
export interface SupplierAddressCreateRequest {
    /**
     * 地址类型
     * @type {number}
     * @memberof SupplierAddressCreateRequest
     */
    'addressType'?: SupplierAddressCreateRequestAddressTypeEnum;
    /**
     * 国家
     * @type {string}
     * @memberof SupplierAddressCreateRequest
     */
    'country'?: string;
    /**
     * 省份
     * @type {string}
     * @memberof SupplierAddressCreateRequest
     */
    'province'?: string;
    /**
     * 城市
     * @type {string}
     * @memberof SupplierAddressCreateRequest
     */
    'city'?: string;
    /**
     * 区县
     * @type {string}
     * @memberof SupplierAddressCreateRequest
     */
    'district'?: string;
    /**
     * 详细地址
     * @type {string}
     * @memberof SupplierAddressCreateRequest
     */
    'detailedAddress'?: string;
    /**
     * 是否为默认地址
     * @type {boolean}
     * @memberof SupplierAddressCreateRequest
     */
    'defaultAddress'?: boolean;
}

export const SupplierAddressCreateRequestAddressTypeEnum = {
    COMPANY: 0,
    FACTORY: 1,
    SHOWROOM: 2
} as const;

export type SupplierAddressCreateRequestAddressTypeEnum = typeof SupplierAddressCreateRequestAddressTypeEnum[keyof typeof SupplierAddressCreateRequestAddressTypeEnum];


