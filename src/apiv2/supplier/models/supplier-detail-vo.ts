/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AddressVO } from './address-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { ContactVO } from './contact-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { DocumentVO } from './document-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { FinanceVO } from './finance-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { StoreVO } from './store-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierBaseVO } from './supplier-base-vo';

/**
 * 供应商详情响应视图
 * @export
 * @interface SupplierDetailVO
 */
export interface SupplierDetailVO {
    /**
     * 
     * @type {SupplierBaseVO}
     * @memberof SupplierDetailVO
     */
    'baseInfo'?: SupplierBaseVO;
    /**
     * 联系人列表
     * @type {Array<ContactVO>}
     * @memberof SupplierDetailVO
     */
    'contacts'?: Array<ContactVO>;
    /**
     * 资质文件列表
     * @type {Array<DocumentVO>}
     * @memberof SupplierDetailVO
     */
    'documents'?: Array<DocumentVO>;
    /**
     * 财务信息
     * @type {Array<FinanceVO>}
     * @memberof SupplierDetailVO
     */
    'finance'?: Array<FinanceVO>;
    /**
     * 店铺信息列表
     * @type {Array<StoreVO>}
     * @memberof SupplierDetailVO
     */
    'stores'?: Array<StoreVO>;
    /**
     * 地址信息列表
     * @type {Array<AddressVO>}
     * @memberof SupplierDetailVO
     */
    'addresses'?: Array<AddressVO>;
}

