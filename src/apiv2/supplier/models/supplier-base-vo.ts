/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 供应商基础信息视图
 * @export
 * @interface SupplierBaseVO
 */
export interface SupplierBaseVO {
    /**
     * 供应商ID
     * @type {number}
     * @memberof SupplierBaseVO
     */
    'id'?: number;
    /**
     * 供应商编码
     * @type {string}
     * @memberof SupplierBaseVO
     */
    'supplierCode'?: string;
    /**
     * 公司全称
     * @type {string}
     * @memberof SupplierBaseVO
     */
    'supplierName'?: string;
    /**
     * 供应商简称
     * @type {string}
     * @memberof SupplierBaseVO
     */
    'shortName'?: string;
    /**
     * 状态
     * @type {number}
     * @memberof SupplierBaseVO
     */
    'status'?: SupplierBaseVOStatusEnum;
    /**
     * 一级分类
     * @type {number}
     * @memberof SupplierBaseVO
     */
    'firstCategory'?: SupplierBaseVOFirstCategoryEnum;
    /**
     * 二级分类
     * @type {number}
     * @memberof SupplierBaseVO
     */
    'secondCategory'?: number;
    /**
     * 合作模式
     * @type {number}
     * @memberof SupplierBaseVO
     */
    'cooperationMode'?: SupplierBaseVOCooperationModeEnum;
    /**
     * 评级(S/A/B/C/D)
     * @type {number}
     * @memberof SupplierBaseVO
     */
    'rating'?: SupplierBaseVORatingEnum;
    /**
     * 结算方式
     * @type {number}
     * @memberof SupplierBaseVO
     */
    'settlementMethod'?: SupplierBaseVOSettlementMethodEnum;
    /**
     * 支付方式
     * @type {number}
     * @memberof SupplierBaseVO
     */
    'paymentMethod'?: SupplierBaseVOPaymentMethodEnum;
    /**
     * 对接人用户ID
     * @type {number}
     * @memberof SupplierBaseVO
     */
    'responsibleUserId'?: number;
    /**
     * 是否品牌商(0:否 1:是)
     * @type {number}
     * @memberof SupplierBaseVO
     */
    'brandFlag'?: number;
    /**
     * 备注
     * @type {string}
     * @memberof SupplierBaseVO
     */
    'remark'?: string;
}

export const SupplierBaseVOStatusEnum = {
    PUBLISHED: 0,
    PENDING_PUBLISH: 1,
    INTRODUCING: 2,
    NEWLY_DEVELOPED: 3,
    PENDING_COMMUNICATION: 4,
    SUSPENDED: 5
} as const;

export type SupplierBaseVOStatusEnum = typeof SupplierBaseVOStatusEnum[keyof typeof SupplierBaseVOStatusEnum];
export const SupplierBaseVOFirstCategoryEnum = {
    ONLINE: 0,
    OFFLINE: 1
} as const;

export type SupplierBaseVOFirstCategoryEnum = typeof SupplierBaseVOFirstCategoryEnum[keyof typeof SupplierBaseVOFirstCategoryEnum];
export const SupplierBaseVOCooperationModeEnum = {
    DROP_SHIPPING: 0,
    WHOLESALE: 1
} as const;

export type SupplierBaseVOCooperationModeEnum = typeof SupplierBaseVOCooperationModeEnum[keyof typeof SupplierBaseVOCooperationModeEnum];
export const SupplierBaseVORatingEnum = {
    S: 0,
    A: 1,
    B: 2,
    C: 3
} as const;

export type SupplierBaseVORatingEnum = typeof SupplierBaseVORatingEnum[keyof typeof SupplierBaseVORatingEnum];
export const SupplierBaseVOSettlementMethodEnum = {
    MONTHLY_SETTLEMENT: 0,
    BIWEEKLY_SETTLEMENT: 1,
    WEEKLY_SETTLEMENT: 2,
    IMMEDIATE_SETTLEMENT: 3
} as const;

export type SupplierBaseVOSettlementMethodEnum = typeof SupplierBaseVOSettlementMethodEnum[keyof typeof SupplierBaseVOSettlementMethodEnum];
export const SupplierBaseVOPaymentMethodEnum = {
    ONLINE_BANK_TRANSFER: 0,
    ONLINE_PAYMENT: 1
} as const;

export type SupplierBaseVOPaymentMethodEnum = typeof SupplierBaseVOPaymentMethodEnum[keyof typeof SupplierBaseVOPaymentMethodEnum];


