/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 供应链联系人更新参数
 * @export
 * @interface SupplierContactUpdateRequest
 */
export interface SupplierContactUpdateRequest {
    /**
     * 联系人姓名
     * @type {string}
     * @memberof SupplierContactUpdateRequest
     */
    'contactName'?: string;
    /**
     * 职位
     * @type {string}
     * @memberof SupplierContactUpdateRequest
     */
    'position'?: string;
    /**
     * 手机号
     * @type {string}
     * @memberof SupplierContactUpdateRequest
     */
    'mobile'?: string;
    /**
     * 微信
     * @type {string}
     * @memberof SupplierContactUpdateRequest
     */
    'wechat'?: string;
    /**
     * QQ
     * @type {string}
     * @memberof SupplierContactUpdateRequest
     */
    'qq'?: string;
    /**
     * 邮箱
     * @type {string}
     * @memberof SupplierContactUpdateRequest
     */
    'email'?: string;
    /**
     * 联系人ID
     * @type {number}
     * @memberof SupplierContactUpdateRequest
     */
    'id'?: number;
    /**
     * 联系人ID
     * @type {boolean}
     * @memberof SupplierContactUpdateRequest
     */
    'deletedFlag'?: boolean;
}

