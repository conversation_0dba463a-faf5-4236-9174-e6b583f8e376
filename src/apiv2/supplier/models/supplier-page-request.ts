/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 分页搜索供应商列表
 * @export
 * @interface SupplierPageRequest
 */
export interface SupplierPageRequest {
    /**
     * 
     * @type {number}
     * @memberof SupplierPageRequest
     */
    'pageNum'?: number;
    /**
     * 
     * @type {number}
     * @memberof SupplierPageRequest
     */
    'pageSize'?: number;
    /**
     * 
     * @type {string}
     * @memberof SupplierPageRequest
     */
    'sortColumn'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof SupplierPageRequest
     */
    'descFlag'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof SupplierPageRequest
     */
    'count'?: boolean;
    /**
     * 状态
     * @type {Array<number>}
     * @memberof SupplierPageRequest
     */
    'status'?: Array<SupplierPageRequestStatusEnum>;
    /**
     * 合作模式
     * @type {number}
     * @memberof SupplierPageRequest
     */
    'cooperationMode'?: SupplierPageRequestCooperationModeEnum;
    /**
     * 是否品牌商
     * @type {boolean}
     * @memberof SupplierPageRequest
     */
    'brandFlag'?: boolean;
    /**
     * 一级分类
     * @type {number}
     * @memberof SupplierPageRequest
     */
    'firstCategory'?: SupplierPageRequestFirstCategoryEnum;
    /**
     * 供应商简称
     * @type {string}
     * @memberof SupplierPageRequest
     */
    'shortName'?: string;
    /**
     * 联系人姓名
     * @type {string}
     * @memberof SupplierPageRequest
     */
    'contactName'?: string;
    /**
     * 手机号
     * @type {string}
     * @memberof SupplierPageRequest
     */
    'mobile'?: string;
    /**
     * 
     * @type {number}
     * @memberof SupplierPageRequest
     */
    'offset'?: number;
    /**
     * 
     * @type {string}
     * @memberof SupplierPageRequest
     */
    'orderBy'?: string;
}

export const SupplierPageRequestStatusEnum = {
    PUBLISHED: 0,
    PENDING_PUBLISH: 1,
    INTRODUCING: 2,
    NEWLY_DEVELOPED: 3,
    PENDING_COMMUNICATION: 4,
    SUSPENDED: 5
} as const;

export type SupplierPageRequestStatusEnum = typeof SupplierPageRequestStatusEnum[keyof typeof SupplierPageRequestStatusEnum];
export const SupplierPageRequestCooperationModeEnum = {
    DROP_SHIPPING: 0,
    WHOLESALE: 1
} as const;

export type SupplierPageRequestCooperationModeEnum = typeof SupplierPageRequestCooperationModeEnum[keyof typeof SupplierPageRequestCooperationModeEnum];
export const SupplierPageRequestFirstCategoryEnum = {
    ONLINE: 0,
    OFFLINE: 1
} as const;

export type SupplierPageRequestFirstCategoryEnum = typeof SupplierPageRequestFirstCategoryEnum[keyof typeof SupplierPageRequestFirstCategoryEnum];


