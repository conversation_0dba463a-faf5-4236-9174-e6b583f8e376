/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 供应商店铺更新参数
 * @export
 * @interface SupplierStoreUpdateRequest
 */
export interface SupplierStoreUpdateRequest {
    /**
     * 店铺名称
     * @type {string}
     * @memberof SupplierStoreUpdateRequest
     */
    'storeName'?: string;
    /**
     * 平台名称
     * @type {number}
     * @memberof SupplierStoreUpdateRequest
     */
    'storePlatform'?: SupplierStoreUpdateRequestStorePlatformEnum;
    /**
     * 店铺URL
     * @type {string}
     * @memberof SupplierStoreUpdateRequest
     */
    'storeUrl'?: string;
    /**
     * 店铺ID
     * @type {number}
     * @memberof SupplierStoreUpdateRequest
     */
    'id'?: number;
    /**
     * 删除标识
     * @type {boolean}
     * @memberof SupplierStoreUpdateRequest
     */
    'deletedFlag'?: boolean;
}

export const SupplierStoreUpdateRequestStorePlatformEnum = {
    TAOBAO: 0,
    JD: 1,
    PINDUODUO: 2,
    AMAZON: 3
} as const;

export type SupplierStoreUpdateRequestStorePlatformEnum = typeof SupplierStoreUpdateRequestStorePlatformEnum[keyof typeof SupplierStoreUpdateRequestStorePlatformEnum];


