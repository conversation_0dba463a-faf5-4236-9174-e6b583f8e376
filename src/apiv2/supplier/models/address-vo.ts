/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 供应商地址信息视图
 * @export
 * @interface AddressVO
 */
export interface AddressVO {
    /**
     * 地址ID
     * @type {number}
     * @memberof AddressVO
     */
    'id'?: number;
    /**
     * 地址类型
     * @type {number}
     * @memberof AddressVO
     */
    'addressType'?: AddressVOAddressTypeEnum;
    /**
     * 国家
     * @type {string}
     * @memberof AddressVO
     */
    'country'?: string;
    /**
     * 省份
     * @type {string}
     * @memberof AddressVO
     */
    'province'?: string;
    /**
     * 城市
     * @type {string}
     * @memberof AddressVO
     */
    'city'?: string;
    /**
     * 区县
     * @type {string}
     * @memberof AddressVO
     */
    'district'?: string;
    /**
     * 详细地址
     * @type {string}
     * @memberof AddressVO
     */
    'detailedAddress'?: string;
    /**
     * 是否为默认地址
     * @type {boolean}
     * @memberof AddressVO
     */
    'defaultAddress'?: boolean;
}

export const AddressVOAddressTypeEnum = {
    COMPANY: 0,
    FACTORY: 1,
    SHOWROOM: 2
} as const;

export type AddressVOAddressTypeEnum = typeof AddressVOAddressTypeEnum[keyof typeof AddressVOAddressTypeEnum];


