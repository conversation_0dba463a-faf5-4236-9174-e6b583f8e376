/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 店铺信息列表
 * @export
 * @interface SupplierStoreCreateRequest
 */
export interface SupplierStoreCreateRequest {
    /**
     * 店铺名称
     * @type {string}
     * @memberof SupplierStoreCreateRequest
     */
    'storeName'?: string;
    /**
     * 平台名称
     * @type {number}
     * @memberof SupplierStoreCreateRequest
     */
    'storePlatform'?: SupplierStoreCreateRequestStorePlatformEnum;
    /**
     * 店铺URL
     * @type {string}
     * @memberof SupplierStoreCreateRequest
     */
    'storeUrl'?: string;
}

export const SupplierStoreCreateRequestStorePlatformEnum = {
    TAOBAO: 0,
    JD: 1,
    PINDUODUO: 2,
    AMAZON: 3
} as const;

export type SupplierStoreCreateRequestStorePlatformEnum = typeof SupplierStoreCreateRequestStorePlatformEnum[keyof typeof SupplierStoreCreateRequestStorePlatformEnum];


