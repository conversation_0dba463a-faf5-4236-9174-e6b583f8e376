/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 供应商店铺信息视图
 * @export
 * @interface StoreVO
 */
export interface StoreVO {
    /**
     * 店铺ID
     * @type {number}
     * @memberof StoreVO
     */
    'id'?: number;
    /**
     * 店铺名称
     * @type {string}
     * @memberof StoreVO
     */
    'storeName'?: string;
    /**
     * 平台名称
     * @type {number}
     * @memberof StoreVO
     */
    'storePlatform'?: StoreVOStorePlatformEnum;
    /**
     * 店铺URL
     * @type {string}
     * @memberof StoreVO
     */
    'storeUrl'?: string;
}

export const StoreVOStorePlatformEnum = {
    TAOBAO: 0,
    JD: 1,
    PINDUODUO: 2,
    AMAZON: 3
} as const;

export type StoreVOStorePlatformEnum = typeof StoreVOStorePlatformEnum[keyof typeof StoreVOStorePlatformEnum];


