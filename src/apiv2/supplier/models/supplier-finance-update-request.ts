/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 供应商财务更新参数
 * @export
 * @interface SupplierFinanceUpdateRequest
 */
export interface SupplierFinanceUpdateRequest {
    /**
     * 账户名
     * @type {string}
     * @memberof SupplierFinanceUpdateRequest
     */
    'accountName'?: string;
    /**
     * 开户行
     * @type {string}
     * @memberof SupplierFinanceUpdateRequest
     */
    'bankName'?: string;
    /**
     * 银行账号
     * @type {string}
     * @memberof SupplierFinanceUpdateRequest
     */
    'bankAccount'?: string;
    /**
     * 财务信息ID
     * @type {number}
     * @memberof SupplierFinanceUpdateRequest
     */
    'id'?: number;
    /**
     * 删除标识
     * @type {boolean}
     * @memberof SupplierFinanceUpdateRequest
     */
    'deletedFlag'?: boolean;
}

