/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 文档信息视图
 * @export
 * @interface DocumentVO
 */
export interface DocumentVO {
    /**
     * 文件ID
     * @type {number}
     * @memberof DocumentVO
     */
    'id'?: number;
    /**
     * 文件类型
     * @type {number}
     * @memberof DocumentVO
     */
    'documentType'?: DocumentVODocumentTypeEnum;
    /**
     * 文件URL
     * @type {string}
     * @memberof DocumentVO
     */
    'filePath'?: string;
    /**
     * 文件名称
     * @type {string}
     * @memberof DocumentVO
     */
    'fileName'?: string;
}

export const DocumentVODocumentTypeEnum = {
    BUSINESS_LICENSE: 0,
    TAX_REGISTRATION: 1,
    QUALITY_INSPECTION_REPORT: 2
} as const;

export type DocumentVODocumentTypeEnum = typeof DocumentVODocumentTypeEnum[keyof typeof DocumentVODocumentTypeEnum];


