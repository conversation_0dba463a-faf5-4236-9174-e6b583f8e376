/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 供应商地址更新参数
 * @export
 * @interface SupplierAddressUpdateRequest
 */
export interface SupplierAddressUpdateRequest {
    /**
     * 地址类型
     * @type {number}
     * @memberof SupplierAddressUpdateRequest
     */
    'addressType'?: SupplierAddressUpdateRequestAddressTypeEnum;
    /**
     * 国家
     * @type {string}
     * @memberof SupplierAddressUpdateRequest
     */
    'country'?: string;
    /**
     * 省份
     * @type {string}
     * @memberof SupplierAddressUpdateRequest
     */
    'province'?: string;
    /**
     * 城市
     * @type {string}
     * @memberof SupplierAddressUpdateRequest
     */
    'city'?: string;
    /**
     * 区县
     * @type {string}
     * @memberof SupplierAddressUpdateRequest
     */
    'district'?: string;
    /**
     * 详细地址
     * @type {string}
     * @memberof SupplierAddressUpdateRequest
     */
    'detailedAddress'?: string;
    /**
     * 是否为默认地址
     * @type {boolean}
     * @memberof SupplierAddressUpdateRequest
     */
    'defaultAddress'?: boolean;
    /**
     * 地址ID
     * @type {number}
     * @memberof SupplierAddressUpdateRequest
     */
    'id'?: number;
    /**
     * 删除标识
     * @type {boolean}
     * @memberof SupplierAddressUpdateRequest
     */
    'deletedFlag'?: boolean;
}

export const SupplierAddressUpdateRequestAddressTypeEnum = {
    COMPANY: 0,
    FACTORY: 1,
    SHOWROOM: 2
} as const;

export type SupplierAddressUpdateRequestAddressTypeEnum = typeof SupplierAddressUpdateRequestAddressTypeEnum[keyof typeof SupplierAddressUpdateRequestAddressTypeEnum];


