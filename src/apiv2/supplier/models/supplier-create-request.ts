/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { SupplierAddressCreateRequest } from './supplier-address-create-request';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierContactCreateRequest } from './supplier-contact-create-request';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierDocumentCreateRequest } from './supplier-document-create-request';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierFinanceCreateRequest } from './supplier-finance-create-request';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierStoreCreateRequest } from './supplier-store-create-request';

/**
 * 供应商信息
 * @export
 * @interface SupplierCreateRequest
 */
export interface SupplierCreateRequest {
    /**
     * 供应商编码
     * @type {string}
     * @memberof SupplierCreateRequest
     */
    'supplierCode'?: string;
    /**
     * 公司全称
     * @type {string}
     * @memberof SupplierCreateRequest
     */
    'supplierName'?: string;
    /**
     * 供应商简称
     * @type {string}
     * @memberof SupplierCreateRequest
     */
    'shortName'?: string;
    /**
     * 状态
     * @type {number}
     * @memberof SupplierCreateRequest
     */
    'status'?: SupplierCreateRequestStatusEnum;
    /**
     * 一级分类ID
     * @type {number}
     * @memberof SupplierCreateRequest
     */
    'firstCategoryId'?: SupplierCreateRequestFirstCategoryIdEnum;
    /**
     * 二级分类ID
     * @type {number}
     * @memberof SupplierCreateRequest
     */
    'secondCategoryId'?: number;
    /**
     * 合作模式
     * @type {number}
     * @memberof SupplierCreateRequest
     */
    'cooperationMode'?: SupplierCreateRequestCooperationModeEnum;
    /**
     * 评级(S/A/B/C/D)
     * @type {number}
     * @memberof SupplierCreateRequest
     */
    'rating'?: SupplierCreateRequestRatingEnum;
    /**
     * 结算方式
     * @type {number}
     * @memberof SupplierCreateRequest
     */
    'settlementMethod'?: SupplierCreateRequestSettlementMethodEnum;
    /**
     * 支付方式
     * @type {number}
     * @memberof SupplierCreateRequest
     */
    'paymentMethod'?: SupplierCreateRequestPaymentMethodEnum;
    /**
     * 对接人用户ID
     * @type {number}
     * @memberof SupplierCreateRequest
     */
    'responsibleUserId'?: number;
    /**
     * 是否品牌商
     * @type {boolean}
     * @memberof SupplierCreateRequest
     */
    'brandFlag'?: boolean;
    /**
     * 备注
     * @type {string}
     * @memberof SupplierCreateRequest
     */
    'remark'?: string;
    /**
     * 联系人列表
     * @type {Array<SupplierContactCreateRequest>}
     * @memberof SupplierCreateRequest
     */
    'contacts'?: Array<SupplierContactCreateRequest>;
    /**
     * 资质文件列表
     * @type {Array<SupplierDocumentCreateRequest>}
     * @memberof SupplierCreateRequest
     */
    'documents'?: Array<SupplierDocumentCreateRequest>;
    /**
     * 财务信息
     * @type {Array<SupplierFinanceCreateRequest>}
     * @memberof SupplierCreateRequest
     */
    'finance'?: Array<SupplierFinanceCreateRequest>;
    /**
     * 店铺信息列表
     * @type {Array<SupplierStoreCreateRequest>}
     * @memberof SupplierCreateRequest
     */
    'stores'?: Array<SupplierStoreCreateRequest>;
    /**
     * 地址信息列表
     * @type {Array<SupplierAddressCreateRequest>}
     * @memberof SupplierCreateRequest
     */
    'addresses'?: Array<SupplierAddressCreateRequest>;
}

export const SupplierCreateRequestStatusEnum = {
    PUBLISHED: 0,
    PENDING_PUBLISH: 1,
    INTRODUCING: 2,
    NEWLY_DEVELOPED: 3,
    PENDING_COMMUNICATION: 4,
    SUSPENDED: 5
} as const;

export type SupplierCreateRequestStatusEnum = typeof SupplierCreateRequestStatusEnum[keyof typeof SupplierCreateRequestStatusEnum];
export const SupplierCreateRequestFirstCategoryIdEnum = {
    ONLINE: 0,
    OFFLINE: 1
} as const;

export type SupplierCreateRequestFirstCategoryIdEnum = typeof SupplierCreateRequestFirstCategoryIdEnum[keyof typeof SupplierCreateRequestFirstCategoryIdEnum];
export const SupplierCreateRequestCooperationModeEnum = {
    DROP_SHIPPING: 0,
    WHOLESALE: 1
} as const;

export type SupplierCreateRequestCooperationModeEnum = typeof SupplierCreateRequestCooperationModeEnum[keyof typeof SupplierCreateRequestCooperationModeEnum];
export const SupplierCreateRequestRatingEnum = {
    S: 0,
    A: 1,
    B: 2,
    C: 3
} as const;

export type SupplierCreateRequestRatingEnum = typeof SupplierCreateRequestRatingEnum[keyof typeof SupplierCreateRequestRatingEnum];
export const SupplierCreateRequestSettlementMethodEnum = {
    MONTHLY_SETTLEMENT: 0,
    BIWEEKLY_SETTLEMENT: 1,
    WEEKLY_SETTLEMENT: 2,
    IMMEDIATE_SETTLEMENT: 3
} as const;

export type SupplierCreateRequestSettlementMethodEnum = typeof SupplierCreateRequestSettlementMethodEnum[keyof typeof SupplierCreateRequestSettlementMethodEnum];
export const SupplierCreateRequestPaymentMethodEnum = {
    ONLINE_BANK_TRANSFER: 0,
    ONLINE_PAYMENT: 1
} as const;

export type SupplierCreateRequestPaymentMethodEnum = typeof SupplierCreateRequestPaymentMethodEnum[keyof typeof SupplierCreateRequestPaymentMethodEnum];


