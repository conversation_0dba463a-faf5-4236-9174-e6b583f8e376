/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { BatchCommodityPlatformStatusRequest } from '../models';
// @ts-ignore
import type { BatchCommoditySelectionRequest } from '../models';
// @ts-ignore
import type { CommoditySearchParam } from '../models';
// @ts-ignore
import type { CommodityUpdateRequest } from '../models';
// @ts-ignore
import type { ResultVOCommodityDetailVO } from '../models';
// @ts-ignore
import type { ResultVOListPlatformVO } from '../models';
// @ts-ignore
import type { ResultVOPageVOCommodityListVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
// @ts-ignore
import type { SkuPriceParams } from '../models';
/**
 * 商品Api - axios parameter creator
 * @export
 */
export const 商品ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 批量更新多个商品在指定平台的上架状态
         * @summary 批量更新平台商品状态
         * @param {BatchCommodityPlatformStatusRequest} batchCommodityPlatformStatusRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchUpdateCommodityPlatformStatus: async (batchCommodityPlatformStatusRequest: BatchCommodityPlatformStatusRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'batchCommodityPlatformStatusRequest' is not null or undefined
            assertParamExists('batchUpdateCommodityPlatformStatus', 'batchCommodityPlatformStatusRequest', batchCommodityPlatformStatusRequest)
            const localVarPath = `/api/commodities/platform-status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(batchCommodityPlatformStatusRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 批量更新多个商品的选品状态，如果选品不通过需要填写原因
         * @summary 批量更新商品选品状态
         * @param {BatchCommoditySelectionRequest} batchCommoditySelectionRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchUpdateCommoditySelectionStatus: async (batchCommoditySelectionRequest: BatchCommoditySelectionRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'batchCommoditySelectionRequest' is not null or undefined
            assertParamExists('batchUpdateCommoditySelectionStatus', 'batchCommoditySelectionRequest', batchCommoditySelectionRequest)
            const localVarPath = `/api/commodities/selection-status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(batchCommoditySelectionRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 批量设置SKU的系数、市场价、折扣率和促销价
         * @summary 批量设置商品SKU价格参数
         * @param {Array<SkuPriceParams>} skuPriceParams 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchUpdateSkuPriceParams: async (skuPriceParams: Array<SkuPriceParams>, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'skuPriceParams' is not null or undefined
            assertParamExists('batchUpdateSkuPriceParams', 'skuPriceParams', skuPriceParams)
            const localVarPath = `/api/commodities/skus/prices`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PATCH', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(skuPriceParams, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取商品的详细信息，包括基本信息、SKU、图片和文件
         * @summary 获取商品详情
         * @param {number} commodityId 商品ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCommodityDetail: async (commodityId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'commodityId' is not null or undefined
            assertParamExists('getCommodityDetail', 'commodityId', commodityId)
            const localVarPath = `/api/commodities/{commodityId}`
                .replace(`{${"commodityId"}}`, encodeURIComponent(String(commodityId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 搜索商品列表
         * @param {CommoditySearchParam} commoditySearchParam 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listCommodities: async (commoditySearchParam: CommoditySearchParam, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'commoditySearchParam' is not null or undefined
            assertParamExists('listCommodities', 'commoditySearchParam', commoditySearchParam)
            const localVarPath = `/api/commodities/_search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(commoditySearchParam, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取所有可用的平台列表
         * @summary 获取平台列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listPlatforms: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/commodities/platforms`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 将所有商品同步到 Typesense 搜索引擎
         * @summary 全量同步商品到搜索引擎
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        syncCommodities: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/commodities/_sync`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 更新商品基本信息、SKU、图片和文件
         * @summary 更新商品信息
         * @param {number} commodityId 商品ID
         * @param {CommodityUpdateRequest} commodityUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCommodity: async (commodityId: number, commodityUpdateRequest: CommodityUpdateRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'commodityId' is not null or undefined
            assertParamExists('updateCommodity', 'commodityId', commodityId)
            // verify required parameter 'commodityUpdateRequest' is not null or undefined
            assertParamExists('updateCommodity', 'commodityUpdateRequest', commodityUpdateRequest)
            const localVarPath = `/api/commodities/{commodityId}`
                .replace(`{${"commodityId"}}`, encodeURIComponent(String(commodityId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(commodityUpdateRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 商品Api - functional programming interface
 * @export
 */
export const 商品ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 商品ApiAxiosParamCreator(configuration)
    return {
        /**
         * 批量更新多个商品在指定平台的上架状态
         * @summary 批量更新平台商品状态
         * @param {BatchCommodityPlatformStatusRequest} batchCommodityPlatformStatusRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async batchUpdateCommodityPlatformStatus(batchCommodityPlatformStatusRequest: BatchCommodityPlatformStatusRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.batchUpdateCommodityPlatformStatus(batchCommodityPlatformStatusRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['商品Api.batchUpdateCommodityPlatformStatus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 批量更新多个商品的选品状态，如果选品不通过需要填写原因
         * @summary 批量更新商品选品状态
         * @param {BatchCommoditySelectionRequest} batchCommoditySelectionRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async batchUpdateCommoditySelectionStatus(batchCommoditySelectionRequest: BatchCommoditySelectionRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.batchUpdateCommoditySelectionStatus(batchCommoditySelectionRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['商品Api.batchUpdateCommoditySelectionStatus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 批量设置SKU的系数、市场价、折扣率和促销价
         * @summary 批量设置商品SKU价格参数
         * @param {Array<SkuPriceParams>} skuPriceParams 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async batchUpdateSkuPriceParams(skuPriceParams: Array<SkuPriceParams>, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.batchUpdateSkuPriceParams(skuPriceParams, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['商品Api.batchUpdateSkuPriceParams']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 获取商品的详细信息，包括基本信息、SKU、图片和文件
         * @summary 获取商品详情
         * @param {number} commodityId 商品ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCommodityDetail(commodityId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOCommodityDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCommodityDetail(commodityId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['商品Api.getCommodityDetail']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 搜索商品列表
         * @param {CommoditySearchParam} commoditySearchParam 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listCommodities(commoditySearchParam: CommoditySearchParam, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOCommodityListVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listCommodities(commoditySearchParam, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['商品Api.listCommodities']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 获取所有可用的平台列表
         * @summary 获取平台列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listPlatforms(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListPlatformVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listPlatforms(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['商品Api.listPlatforms']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 将所有商品同步到 Typesense 搜索引擎
         * @summary 全量同步商品到搜索引擎
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async syncCommodities(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.syncCommodities(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['商品Api.syncCommodities']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 更新商品基本信息、SKU、图片和文件
         * @summary 更新商品信息
         * @param {number} commodityId 商品ID
         * @param {CommodityUpdateRequest} commodityUpdateRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateCommodity(commodityId: number, commodityUpdateRequest: CommodityUpdateRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateCommodity(commodityId, commodityUpdateRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['商品Api.updateCommodity']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 商品Api - factory interface
 * @export
 */
export const 商品ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 商品ApiFp(configuration)
    return {
        /**
         * 批量更新多个商品在指定平台的上架状态
         * @summary 批量更新平台商品状态
         * @param {商品ApiBatchUpdateCommodityPlatformStatusRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchUpdateCommodityPlatformStatus(requestParameters: 商品ApiBatchUpdateCommodityPlatformStatusRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.batchUpdateCommodityPlatformStatus(requestParameters.batchCommodityPlatformStatusRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 批量更新多个商品的选品状态，如果选品不通过需要填写原因
         * @summary 批量更新商品选品状态
         * @param {商品ApiBatchUpdateCommoditySelectionStatusRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchUpdateCommoditySelectionStatus(requestParameters: 商品ApiBatchUpdateCommoditySelectionStatusRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.batchUpdateCommoditySelectionStatus(requestParameters.batchCommoditySelectionRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 批量设置SKU的系数、市场价、折扣率和促销价
         * @summary 批量设置商品SKU价格参数
         * @param {商品ApiBatchUpdateSkuPriceParamsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        batchUpdateSkuPriceParams(requestParameters: 商品ApiBatchUpdateSkuPriceParamsRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.batchUpdateSkuPriceParams(requestParameters.skuPriceParams, options).then((request) => request(axios, basePath));
        },
        /**
         * 获取商品的详细信息，包括基本信息、SKU、图片和文件
         * @summary 获取商品详情
         * @param {商品ApiGetCommodityDetailRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCommodityDetail(requestParameters: 商品ApiGetCommodityDetailRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOCommodityDetailVO> {
            return localVarFp.getCommodityDetail(requestParameters.commodityId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 搜索商品列表
         * @param {商品ApiListCommoditiesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listCommodities(requestParameters: 商品ApiListCommoditiesRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOCommodityListVO> {
            return localVarFp.listCommodities(requestParameters.commoditySearchParam, options).then((request) => request(axios, basePath));
        },
        /**
         * 获取所有可用的平台列表
         * @summary 获取平台列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listPlatforms(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListPlatformVO> {
            return localVarFp.listPlatforms(options).then((request) => request(axios, basePath));
        },
        /**
         * 将所有商品同步到 Typesense 搜索引擎
         * @summary 全量同步商品到搜索引擎
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        syncCommodities(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.syncCommodities(options).then((request) => request(axios, basePath));
        },
        /**
         * 更新商品基本信息、SKU、图片和文件
         * @summary 更新商品信息
         * @param {商品ApiUpdateCommodityRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCommodity(requestParameters: 商品ApiUpdateCommodityRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.updateCommodity(requestParameters.commodityId, requestParameters.commodityUpdateRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for batchUpdateCommodityPlatformStatus operation in 商品Api.
 * @export
 * @interface 商品ApiBatchUpdateCommodityPlatformStatusRequest
 */
export interface 商品ApiBatchUpdateCommodityPlatformStatusRequest {
    /**
     * 
     * @type {BatchCommodityPlatformStatusRequest}
     * @memberof 商品ApiBatchUpdateCommodityPlatformStatus
     */
    readonly batchCommodityPlatformStatusRequest: BatchCommodityPlatformStatusRequest
}

/**
 * Request parameters for batchUpdateCommoditySelectionStatus operation in 商品Api.
 * @export
 * @interface 商品ApiBatchUpdateCommoditySelectionStatusRequest
 */
export interface 商品ApiBatchUpdateCommoditySelectionStatusRequest {
    /**
     * 
     * @type {BatchCommoditySelectionRequest}
     * @memberof 商品ApiBatchUpdateCommoditySelectionStatus
     */
    readonly batchCommoditySelectionRequest: BatchCommoditySelectionRequest
}

/**
 * Request parameters for batchUpdateSkuPriceParams operation in 商品Api.
 * @export
 * @interface 商品ApiBatchUpdateSkuPriceParamsRequest
 */
export interface 商品ApiBatchUpdateSkuPriceParamsRequest {
    /**
     * 
     * @type {Array<SkuPriceParams>}
     * @memberof 商品ApiBatchUpdateSkuPriceParams
     */
    readonly skuPriceParams: Array<SkuPriceParams>
}

/**
 * Request parameters for getCommodityDetail operation in 商品Api.
 * @export
 * @interface 商品ApiGetCommodityDetailRequest
 */
export interface 商品ApiGetCommodityDetailRequest {
    /**
     * 商品ID
     * @type {number}
     * @memberof 商品ApiGetCommodityDetail
     */
    readonly commodityId: number
}

/**
 * Request parameters for listCommodities operation in 商品Api.
 * @export
 * @interface 商品ApiListCommoditiesRequest
 */
export interface 商品ApiListCommoditiesRequest {
    /**
     * 
     * @type {CommoditySearchParam}
     * @memberof 商品ApiListCommodities
     */
    readonly commoditySearchParam: CommoditySearchParam
}

/**
 * Request parameters for updateCommodity operation in 商品Api.
 * @export
 * @interface 商品ApiUpdateCommodityRequest
 */
export interface 商品ApiUpdateCommodityRequest {
    /**
     * 商品ID
     * @type {number}
     * @memberof 商品ApiUpdateCommodity
     */
    readonly commodityId: number

    /**
     * 
     * @type {CommodityUpdateRequest}
     * @memberof 商品ApiUpdateCommodity
     */
    readonly commodityUpdateRequest: CommodityUpdateRequest
}

/**
 * 商品Api - object-oriented interface
 * @export
 * @class 商品Api
 * @extends {BaseAPI}
 */
export class 商品Api extends BaseAPI {
    /**
     * 批量更新多个商品在指定平台的上架状态
     * @summary 批量更新平台商品状态
     * @param {商品ApiBatchUpdateCommodityPlatformStatusRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 商品Api
     */
    public batchUpdateCommodityPlatformStatus(requestParameters: 商品ApiBatchUpdateCommodityPlatformStatusRequest, options?: RawAxiosRequestConfig) {
        return 商品ApiFp(this.configuration).batchUpdateCommodityPlatformStatus(requestParameters.batchCommodityPlatformStatusRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 批量更新多个商品的选品状态，如果选品不通过需要填写原因
     * @summary 批量更新商品选品状态
     * @param {商品ApiBatchUpdateCommoditySelectionStatusRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 商品Api
     */
    public batchUpdateCommoditySelectionStatus(requestParameters: 商品ApiBatchUpdateCommoditySelectionStatusRequest, options?: RawAxiosRequestConfig) {
        return 商品ApiFp(this.configuration).batchUpdateCommoditySelectionStatus(requestParameters.batchCommoditySelectionRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 批量设置SKU的系数、市场价、折扣率和促销价
     * @summary 批量设置商品SKU价格参数
     * @param {商品ApiBatchUpdateSkuPriceParamsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 商品Api
     */
    public batchUpdateSkuPriceParams(requestParameters: 商品ApiBatchUpdateSkuPriceParamsRequest, options?: RawAxiosRequestConfig) {
        return 商品ApiFp(this.configuration).batchUpdateSkuPriceParams(requestParameters.skuPriceParams, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 获取商品的详细信息，包括基本信息、SKU、图片和文件
     * @summary 获取商品详情
     * @param {商品ApiGetCommodityDetailRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 商品Api
     */
    public getCommodityDetail(requestParameters: 商品ApiGetCommodityDetailRequest, options?: RawAxiosRequestConfig) {
        return 商品ApiFp(this.configuration).getCommodityDetail(requestParameters.commodityId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 搜索商品列表
     * @param {商品ApiListCommoditiesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 商品Api
     */
    public listCommodities(requestParameters: 商品ApiListCommoditiesRequest, options?: RawAxiosRequestConfig) {
        return 商品ApiFp(this.configuration).listCommodities(requestParameters.commoditySearchParam, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 获取所有可用的平台列表
     * @summary 获取平台列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 商品Api
     */
    public listPlatforms(options?: RawAxiosRequestConfig) {
        return 商品ApiFp(this.configuration).listPlatforms(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 将所有商品同步到 Typesense 搜索引擎
     * @summary 全量同步商品到搜索引擎
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 商品Api
     */
    public syncCommodities(options?: RawAxiosRequestConfig) {
        return 商品ApiFp(this.configuration).syncCommodities(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 更新商品基本信息、SKU、图片和文件
     * @summary 更新商品信息
     * @param {商品ApiUpdateCommodityRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 商品Api
     */
    public updateCommodity(requestParameters: 商品ApiUpdateCommodityRequest, options?: RawAxiosRequestConfig) {
        return 商品ApiFp(this.configuration).updateCommodity(requestParameters.commodityId, requestParameters.commodityUpdateRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

