/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOVoid } from '../models';
/**
 * 系统服务Api - axios parameter creator
 * @export
 */
export const 系统服务ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 清空系统所有数据，此操作不可逆，需要特殊权限。
         * @summary 清除数据
         * @param {string} xACCESSCODE 访问安全码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cleanData: async (xACCESSCODE: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'xACCESSCODE' is not null or undefined
            assertParamExists('cleanData', 'xACCESSCODE', xACCESSCODE)
            const localVarPath = `/api/system/data`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            if (xACCESSCODE != null) {
                localVarHeaderParameter['X-ACCESS-CODE'] = String(xACCESSCODE);
            }
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 系统服务Api - functional programming interface
 * @export
 */
export const 系统服务ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 系统服务ApiAxiosParamCreator(configuration)
    return {
        /**
         * 清空系统所有数据，此操作不可逆，需要特殊权限。
         * @summary 清除数据
         * @param {string} xACCESSCODE 访问安全码
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async cleanData(xACCESSCODE: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.cleanData(xACCESSCODE, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['系统服务Api.cleanData']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 系统服务Api - factory interface
 * @export
 */
export const 系统服务ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 系统服务ApiFp(configuration)
    return {
        /**
         * 清空系统所有数据，此操作不可逆，需要特殊权限。
         * @summary 清除数据
         * @param {系统服务ApiCleanDataRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        cleanData(requestParameters: 系统服务ApiCleanDataRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.cleanData(requestParameters.xACCESSCODE, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for cleanData operation in 系统服务Api.
 * @export
 * @interface 系统服务ApiCleanDataRequest
 */
export interface 系统服务ApiCleanDataRequest {
    /**
     * 访问安全码
     * @type {string}
     * @memberof 系统服务ApiCleanData
     */
    readonly xACCESSCODE: string
}

/**
 * 系统服务Api - object-oriented interface
 * @export
 * @class 系统服务Api
 * @extends {BaseAPI}
 */
export class 系统服务Api extends BaseAPI {
    /**
     * 清空系统所有数据，此操作不可逆，需要特殊权限。
     * @summary 清除数据
     * @param {系统服务ApiCleanDataRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 系统服务Api
     */
    public cleanData(requestParameters: 系统服务ApiCleanDataRequest, options?: RawAxiosRequestConfig) {
        return 系统服务ApiFp(this.configuration).cleanData(requestParameters.xACCESSCODE, options).then((request) => request(this.axios, this.basePath));
    }
}

