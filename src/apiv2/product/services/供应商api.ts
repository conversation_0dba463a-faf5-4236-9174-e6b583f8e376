/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOListSupplierVO } from '../models';
/**
 * 供应商Api - axios parameter creator
 * @export
 */
export const 供应商ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 供应商列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listSuppliers: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/suppliers`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 供应商Api - functional programming interface
 * @export
 */
export const 供应商ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 供应商ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 供应商列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listSuppliers(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListSupplierVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listSuppliers(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['供应商Api.listSuppliers']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 供应商Api - factory interface
 * @export
 */
export const 供应商ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 供应商ApiFp(configuration)
    return {
        /**
         * 
         * @summary 供应商列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listSuppliers(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListSupplierVO> {
            return localVarFp.listSuppliers(options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * 供应商Api - object-oriented interface
 * @export
 * @class 供应商Api
 * @extends {BaseAPI}
 */
export class 供应商Api extends BaseAPI {
    /**
     * 
     * @summary 供应商列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 供应商Api
     */
    public listSuppliers(options?: RawAxiosRequestConfig) {
        return 供应商ApiFp(this.configuration).listSuppliers(options).then((request) => request(this.axios, this.basePath));
    }
}

