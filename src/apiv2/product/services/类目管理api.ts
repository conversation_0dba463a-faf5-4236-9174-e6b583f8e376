/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { CategoryRequest } from '../models';
// @ts-ignore
import type { ResultVOCategoryVO } from '../models';
// @ts-ignore
import type { ResultVOInteger } from '../models';
// @ts-ignore
import type { ResultVOListCategoryVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
/**
 * 类目管理Api - axios parameter creator
 * @export
 */
export const 类目管理ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 创建一个新的类目
         * @summary 创建类目
         * @param {CategoryRequest} categoryRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCategory: async (categoryRequest: CategoryRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'categoryRequest' is not null or undefined
            assertParamExists('createCategory', 'categoryRequest', categoryRequest)
            const localVarPath = `/api/categories`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(categoryRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 删除指定的类目，非叶子节点不允许删除，关联产品的类目不允许删除
         * @summary 删除类目
         * @param {number} id 类目ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCategory: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('deleteCategory', 'id', id)
            const localVarPath = `/api/categories/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'DELETE', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取类目信息
         * @param {number} id 类目ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCategory: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getCategory', 'id', id)
            const localVarPath = `/api/categories/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取类目的树形结构 (全量展示1-3级，第四级只展示最上面一个叶子节点的孩子节点)
         * @summary 获取类目树
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCategoryTree: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/categories/tree`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取指定类目的直接子类目
         * @summary 获取子类目
         * @param {number} id 父类目ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getChildCategories: async (id: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('getChildCategories', 'id', id)
            const localVarPath = `/api/categories/{id}/children`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 返回树形结构
         * @summary 搜索类目
         * @param {string} keyword 搜索关键词
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchCategories: async (keyword: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'keyword' is not null or undefined
            assertParamExists('searchCategories', 'keyword', keyword)
            const localVarPath = `/api/categories/search`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (keyword !== undefined) {
                localVarQueryParameter['keyword'] = keyword;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 全量同步所有类目数据到Typesense搜索引擎
         * @summary 同步类目到搜索引擎
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        syncCategories: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/categories/_sync`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 更新现有类目信息
         * @summary 更新类目
         * @param {number} id 类目ID
         * @param {CategoryRequest} categoryRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCategory: async (id: number, categoryRequest: CategoryRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'id' is not null or undefined
            assertParamExists('updateCategory', 'id', id)
            // verify required parameter 'categoryRequest' is not null or undefined
            assertParamExists('updateCategory', 'categoryRequest', categoryRequest)
            const localVarPath = `/api/categories/{id}`
                .replace(`{${"id"}}`, encodeURIComponent(String(id)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(categoryRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 类目管理Api - functional programming interface
 * @export
 */
export const 类目管理ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 类目管理ApiAxiosParamCreator(configuration)
    return {
        /**
         * 创建一个新的类目
         * @summary 创建类目
         * @param {CategoryRequest} categoryRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async createCategory(categoryRequest: CategoryRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOInteger>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.createCategory(categoryRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['类目管理Api.createCategory']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 删除指定的类目，非叶子节点不允许删除，关联产品的类目不允许删除
         * @summary 删除类目
         * @param {number} id 类目ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deleteCategory(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deleteCategory(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['类目管理Api.deleteCategory']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 获取类目信息
         * @param {number} id 类目ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCategory(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOCategoryVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCategory(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['类目管理Api.getCategory']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 获取类目的树形结构 (全量展示1-3级，第四级只展示最上面一个叶子节点的孩子节点)
         * @summary 获取类目树
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getCategoryTree(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListCategoryVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getCategoryTree(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['类目管理Api.getCategoryTree']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 获取指定类目的直接子类目
         * @summary 获取子类目
         * @param {number} id 父类目ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getChildCategories(id: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListCategoryVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getChildCategories(id, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['类目管理Api.getChildCategories']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 返回树形结构
         * @summary 搜索类目
         * @param {string} keyword 搜索关键词
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async searchCategories(keyword: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListCategoryVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.searchCategories(keyword, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['类目管理Api.searchCategories']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 全量同步所有类目数据到Typesense搜索引擎
         * @summary 同步类目到搜索引擎
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async syncCategories(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.syncCategories(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['类目管理Api.syncCategories']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 更新现有类目信息
         * @summary 更新类目
         * @param {number} id 类目ID
         * @param {CategoryRequest} categoryRequest 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async updateCategory(id: number, categoryRequest: CategoryRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.updateCategory(id, categoryRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['类目管理Api.updateCategory']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 类目管理Api - factory interface
 * @export
 */
export const 类目管理ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 类目管理ApiFp(configuration)
    return {
        /**
         * 创建一个新的类目
         * @summary 创建类目
         * @param {类目管理ApiCreateCategoryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        createCategory(requestParameters: 类目管理ApiCreateCategoryRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOInteger> {
            return localVarFp.createCategory(requestParameters.categoryRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 删除指定的类目，非叶子节点不允许删除，关联产品的类目不允许删除
         * @summary 删除类目
         * @param {类目管理ApiDeleteCategoryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deleteCategory(requestParameters: 类目管理ApiDeleteCategoryRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.deleteCategory(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取类目信息
         * @param {类目管理ApiGetCategoryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCategory(requestParameters: 类目管理ApiGetCategoryRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOCategoryVO> {
            return localVarFp.getCategory(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * 获取类目的树形结构 (全量展示1-3级，第四级只展示最上面一个叶子节点的孩子节点)
         * @summary 获取类目树
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getCategoryTree(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListCategoryVO> {
            return localVarFp.getCategoryTree(options).then((request) => request(axios, basePath));
        },
        /**
         * 获取指定类目的直接子类目
         * @summary 获取子类目
         * @param {类目管理ApiGetChildCategoriesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getChildCategories(requestParameters: 类目管理ApiGetChildCategoriesRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListCategoryVO> {
            return localVarFp.getChildCategories(requestParameters.id, options).then((request) => request(axios, basePath));
        },
        /**
         * 返回树形结构
         * @summary 搜索类目
         * @param {类目管理ApiSearchCategoriesRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        searchCategories(requestParameters: 类目管理ApiSearchCategoriesRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListCategoryVO> {
            return localVarFp.searchCategories(requestParameters.keyword, options).then((request) => request(axios, basePath));
        },
        /**
         * 全量同步所有类目数据到Typesense搜索引擎
         * @summary 同步类目到搜索引擎
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        syncCategories(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.syncCategories(options).then((request) => request(axios, basePath));
        },
        /**
         * 更新现有类目信息
         * @summary 更新类目
         * @param {类目管理ApiUpdateCategoryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        updateCategory(requestParameters: 类目管理ApiUpdateCategoryRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.updateCategory(requestParameters.id, requestParameters.categoryRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for createCategory operation in 类目管理Api.
 * @export
 * @interface 类目管理ApiCreateCategoryRequest
 */
export interface 类目管理ApiCreateCategoryRequest {
    /**
     * 
     * @type {CategoryRequest}
     * @memberof 类目管理ApiCreateCategory
     */
    readonly categoryRequest: CategoryRequest
}

/**
 * Request parameters for deleteCategory operation in 类目管理Api.
 * @export
 * @interface 类目管理ApiDeleteCategoryRequest
 */
export interface 类目管理ApiDeleteCategoryRequest {
    /**
     * 类目ID
     * @type {number}
     * @memberof 类目管理ApiDeleteCategory
     */
    readonly id: number
}

/**
 * Request parameters for getCategory operation in 类目管理Api.
 * @export
 * @interface 类目管理ApiGetCategoryRequest
 */
export interface 类目管理ApiGetCategoryRequest {
    /**
     * 类目ID
     * @type {number}
     * @memberof 类目管理ApiGetCategory
     */
    readonly id: number
}

/**
 * Request parameters for getChildCategories operation in 类目管理Api.
 * @export
 * @interface 类目管理ApiGetChildCategoriesRequest
 */
export interface 类目管理ApiGetChildCategoriesRequest {
    /**
     * 父类目ID
     * @type {number}
     * @memberof 类目管理ApiGetChildCategories
     */
    readonly id: number
}

/**
 * Request parameters for searchCategories operation in 类目管理Api.
 * @export
 * @interface 类目管理ApiSearchCategoriesRequest
 */
export interface 类目管理ApiSearchCategoriesRequest {
    /**
     * 搜索关键词
     * @type {string}
     * @memberof 类目管理ApiSearchCategories
     */
    readonly keyword: string
}

/**
 * Request parameters for updateCategory operation in 类目管理Api.
 * @export
 * @interface 类目管理ApiUpdateCategoryRequest
 */
export interface 类目管理ApiUpdateCategoryRequest {
    /**
     * 类目ID
     * @type {number}
     * @memberof 类目管理ApiUpdateCategory
     */
    readonly id: number

    /**
     * 
     * @type {CategoryRequest}
     * @memberof 类目管理ApiUpdateCategory
     */
    readonly categoryRequest: CategoryRequest
}

/**
 * 类目管理Api - object-oriented interface
 * @export
 * @class 类目管理Api
 * @extends {BaseAPI}
 */
export class 类目管理Api extends BaseAPI {
    /**
     * 创建一个新的类目
     * @summary 创建类目
     * @param {类目管理ApiCreateCategoryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 类目管理Api
     */
    public createCategory(requestParameters: 类目管理ApiCreateCategoryRequest, options?: RawAxiosRequestConfig) {
        return 类目管理ApiFp(this.configuration).createCategory(requestParameters.categoryRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 删除指定的类目，非叶子节点不允许删除，关联产品的类目不允许删除
     * @summary 删除类目
     * @param {类目管理ApiDeleteCategoryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 类目管理Api
     */
    public deleteCategory(requestParameters: 类目管理ApiDeleteCategoryRequest, options?: RawAxiosRequestConfig) {
        return 类目管理ApiFp(this.configuration).deleteCategory(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 获取类目信息
     * @param {类目管理ApiGetCategoryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 类目管理Api
     */
    public getCategory(requestParameters: 类目管理ApiGetCategoryRequest, options?: RawAxiosRequestConfig) {
        return 类目管理ApiFp(this.configuration).getCategory(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 获取类目的树形结构 (全量展示1-3级，第四级只展示最上面一个叶子节点的孩子节点)
     * @summary 获取类目树
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 类目管理Api
     */
    public getCategoryTree(options?: RawAxiosRequestConfig) {
        return 类目管理ApiFp(this.configuration).getCategoryTree(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 获取指定类目的直接子类目
     * @summary 获取子类目
     * @param {类目管理ApiGetChildCategoriesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 类目管理Api
     */
    public getChildCategories(requestParameters: 类目管理ApiGetChildCategoriesRequest, options?: RawAxiosRequestConfig) {
        return 类目管理ApiFp(this.configuration).getChildCategories(requestParameters.id, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 返回树形结构
     * @summary 搜索类目
     * @param {类目管理ApiSearchCategoriesRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 类目管理Api
     */
    public searchCategories(requestParameters: 类目管理ApiSearchCategoriesRequest, options?: RawAxiosRequestConfig) {
        return 类目管理ApiFp(this.configuration).searchCategories(requestParameters.keyword, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 全量同步所有类目数据到Typesense搜索引擎
     * @summary 同步类目到搜索引擎
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 类目管理Api
     */
    public syncCategories(options?: RawAxiosRequestConfig) {
        return 类目管理ApiFp(this.configuration).syncCategories(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 更新现有类目信息
     * @summary 更新类目
     * @param {类目管理ApiUpdateCategoryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 类目管理Api
     */
    public updateCategory(requestParameters: 类目管理ApiUpdateCategoryRequest, options?: RawAxiosRequestConfig) {
        return 类目管理ApiFp(this.configuration).updateCategory(requestParameters.id, requestParameters.categoryRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

