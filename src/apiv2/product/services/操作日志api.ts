/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { AuditLogPageReq } from '../models';
// @ts-ignore
import type { ResultVOPageVOAggregatedLogDTO } from '../models';
/**
 * 操作日志Api - axios parameter creator
 * @export
 */
export const 操作日志ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 分页查询日志列表
         * @param {AuditLogPageReq} auditLogPageReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryLog: async (auditLogPageReq: AuditLogPageReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'auditLogPageReq' is not null or undefined
            assertParamExists('queryLog', 'auditLogPageReq', auditLogPageReq)
            const localVarPath = `/api/log`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(auditLogPageReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 操作日志Api - functional programming interface
 * @export
 */
export const 操作日志ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 操作日志ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 分页查询日志列表
         * @param {AuditLogPageReq} auditLogPageReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async queryLog(auditLogPageReq: AuditLogPageReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOAggregatedLogDTO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.queryLog(auditLogPageReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['操作日志Api.queryLog']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 操作日志Api - factory interface
 * @export
 */
export const 操作日志ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 操作日志ApiFp(configuration)
    return {
        /**
         * 
         * @summary 分页查询日志列表
         * @param {操作日志ApiQueryLogRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryLog(requestParameters: 操作日志ApiQueryLogRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOAggregatedLogDTO> {
            return localVarFp.queryLog(requestParameters.auditLogPageReq, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for queryLog operation in 操作日志Api.
 * @export
 * @interface 操作日志ApiQueryLogRequest
 */
export interface 操作日志ApiQueryLogRequest {
    /**
     * 
     * @type {AuditLogPageReq}
     * @memberof 操作日志ApiQueryLog
     */
    readonly auditLogPageReq: AuditLogPageReq
}

/**
 * 操作日志Api - object-oriented interface
 * @export
 * @class 操作日志Api
 * @extends {BaseAPI}
 */
export class 操作日志Api extends BaseAPI {
    /**
     * 
     * @summary 分页查询日志列表
     * @param {操作日志ApiQueryLogRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 操作日志Api
     */
    public queryLog(requestParameters: 操作日志ApiQueryLogRequest, options?: RawAxiosRequestConfig) {
        return 操作日志ApiFp(this.configuration).queryLog(requestParameters.auditLogPageReq, options).then((request) => request(this.axios, this.basePath));
    }
}

