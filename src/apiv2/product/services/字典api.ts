/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOListDictVO } from '../models';
// @ts-ignore
import type { ResultVOListString } from '../models';
/**
 * 字典Api - axios parameter creator
 * @export
 */
export const 字典ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 字典code列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listDictCodes: async (options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/dicts/codes`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 字典列表
         * @param {string} [codes] 字典code列表，多个code用逗号分隔
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listDicts: async (codes?: string, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/dicts`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (codes !== undefined) {
                localVarQueryParameter['codes'] = codes;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 字典Api - functional programming interface
 * @export
 */
export const 字典ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 字典ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 字典code列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listDictCodes(options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListString>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listDictCodes(options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['字典Api.listDictCodes']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 字典列表
         * @param {string} [codes] 字典code列表，多个code用逗号分隔
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async listDicts(codes?: string, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListDictVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.listDicts(codes, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['字典Api.listDicts']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 字典Api - factory interface
 * @export
 */
export const 字典ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 字典ApiFp(configuration)
    return {
        /**
         * 
         * @summary 字典code列表
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listDictCodes(options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListString> {
            return localVarFp.listDictCodes(options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 字典列表
         * @param {字典ApiListDictsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        listDicts(requestParameters: 字典ApiListDictsRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListDictVO> {
            return localVarFp.listDicts(requestParameters.codes, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for listDicts operation in 字典Api.
 * @export
 * @interface 字典ApiListDictsRequest
 */
export interface 字典ApiListDictsRequest {
    /**
     * 字典code列表，多个code用逗号分隔
     * @type {string}
     * @memberof 字典ApiListDicts
     */
    readonly codes?: string
}

/**
 * 字典Api - object-oriented interface
 * @export
 * @class 字典Api
 * @extends {BaseAPI}
 */
export class 字典Api extends BaseAPI {
    /**
     * 
     * @summary 字典code列表
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 字典Api
     */
    public listDictCodes(options?: RawAxiosRequestConfig) {
        return 字典ApiFp(this.configuration).listDictCodes(options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 字典列表
     * @param {字典ApiListDictsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 字典Api
     */
    public listDicts(requestParameters: 字典ApiListDictsRequest = {}, options?: RawAxiosRequestConfig) {
        return 字典ApiFp(this.configuration).listDicts(requestParameters.codes, options).then((request) => request(this.axios, this.basePath));
    }
}

