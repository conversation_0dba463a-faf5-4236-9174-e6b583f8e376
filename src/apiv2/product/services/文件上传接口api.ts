/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ExportRequest } from '../models';
// @ts-ignore
import type { ResultVOOssUploadCredentialVO } from '../models';
/**
 * 文件上传接口Api - axios parameter creator
 * @export
 */
export const 文件上传接口ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @param {ExportRequest} [exportRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        _export: async (exportRequest?: ExportRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/v1/files/export`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(exportRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 获取阿里云OSS的上传凭证，用于前端直传文件到OSS
         * @summary 获取OSS上传凭证
         * @param {string} fileName 文件名称，用于生成唯一的文件路径
         * @param {GetOssUploadCredentialUploadFileTypeEnum} [uploadFileType] 用途，用于确定存储路径
         * @param {GetOssUploadCredentialAccessTypeEnum} [accessType] 访问权限类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOssUploadCredential: async (fileName: string, uploadFileType?: GetOssUploadCredentialUploadFileTypeEnum, accessType?: GetOssUploadCredentialAccessTypeEnum, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'fileName' is not null or undefined
            assertParamExists('getOssUploadCredential', 'fileName', fileName)
            const localVarPath = `/api/v1/files/upload-credential`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (fileName !== undefined) {
                localVarQueryParameter['fileName'] = fileName;
            }

            if (uploadFileType !== undefined) {
                localVarQueryParameter['uploadFileType'] = uploadFileType;
            }

            if (accessType !== undefined) {
                localVarQueryParameter['accessType'] = accessType;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 文件上传接口Api - functional programming interface
 * @export
 */
export const 文件上传接口ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 文件上传接口ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @param {ExportRequest} [exportRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async _export(exportRequest?: ExportRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<void>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator._export(exportRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['文件上传接口Api._export']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 获取阿里云OSS的上传凭证，用于前端直传文件到OSS
         * @summary 获取OSS上传凭证
         * @param {string} fileName 文件名称，用于生成唯一的文件路径
         * @param {GetOssUploadCredentialUploadFileTypeEnum} [uploadFileType] 用途，用于确定存储路径
         * @param {GetOssUploadCredentialAccessTypeEnum} [accessType] 访问权限类型
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getOssUploadCredential(fileName: string, uploadFileType?: GetOssUploadCredentialUploadFileTypeEnum, accessType?: GetOssUploadCredentialAccessTypeEnum, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOOssUploadCredentialVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getOssUploadCredential(fileName, uploadFileType, accessType, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['文件上传接口Api.getOssUploadCredential']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 文件上传接口Api - factory interface
 * @export
 */
export const 文件上传接口ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 文件上传接口ApiFp(configuration)
    return {
        /**
         * 
         * @param {文件上传接口ApiExportRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        _export(requestParameters: 文件上传接口ApiExportRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<void> {
            return localVarFp._export(requestParameters.exportRequest, options).then((request) => request(axios, basePath));
        },
        /**
         * 获取阿里云OSS的上传凭证，用于前端直传文件到OSS
         * @summary 获取OSS上传凭证
         * @param {文件上传接口ApiGetOssUploadCredentialRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getOssUploadCredential(requestParameters: 文件上传接口ApiGetOssUploadCredentialRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOOssUploadCredentialVO> {
            return localVarFp.getOssUploadCredential(requestParameters.fileName, requestParameters.uploadFileType, requestParameters.accessType, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for _export operation in 文件上传接口Api.
 * @export
 * @interface 文件上传接口ApiExportRequest
 */
export interface 文件上传接口ApiExportRequest {
    /**
     * 
     * @type {ExportRequest}
     * @memberof 文件上传接口ApiExport
     */
    readonly exportRequest?: ExportRequest
}

/**
 * Request parameters for getOssUploadCredential operation in 文件上传接口Api.
 * @export
 * @interface 文件上传接口ApiGetOssUploadCredentialRequest
 */
export interface 文件上传接口ApiGetOssUploadCredentialRequest {
    /**
     * 文件名称，用于生成唯一的文件路径
     * @type {string}
     * @memberof 文件上传接口ApiGetOssUploadCredential
     */
    readonly fileName: string

    /**
     * 用途，用于确定存储路径
     * @type {'category/icon' | 'product/images/main' | 'product/images/sku' | 'product/images/detail' | 'product/files' | 'product/export'}
     * @memberof 文件上传接口ApiGetOssUploadCredential
     */
    readonly uploadFileType?: GetOssUploadCredentialUploadFileTypeEnum

    /**
     * 访问权限类型
     * @type {'public' | 'private'}
     * @memberof 文件上传接口ApiGetOssUploadCredential
     */
    readonly accessType?: GetOssUploadCredentialAccessTypeEnum
}

/**
 * 文件上传接口Api - object-oriented interface
 * @export
 * @class 文件上传接口Api
 * @extends {BaseAPI}
 */
export class 文件上传接口Api extends BaseAPI {
    /**
     * 
     * @param {文件上传接口ApiExportRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 文件上传接口Api
     */
    public _export(requestParameters: 文件上传接口ApiExportRequest = {}, options?: RawAxiosRequestConfig) {
        return 文件上传接口ApiFp(this.configuration)._export(requestParameters.exportRequest, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 获取阿里云OSS的上传凭证，用于前端直传文件到OSS
     * @summary 获取OSS上传凭证
     * @param {文件上传接口ApiGetOssUploadCredentialRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 文件上传接口Api
     */
    public getOssUploadCredential(requestParameters: 文件上传接口ApiGetOssUploadCredentialRequest, options?: RawAxiosRequestConfig) {
        return 文件上传接口ApiFp(this.configuration).getOssUploadCredential(requestParameters.fileName, requestParameters.uploadFileType, requestParameters.accessType, options).then((request) => request(this.axios, this.basePath));
    }
}

/**
 * @export
 */
export const GetOssUploadCredentialUploadFileTypeEnum = {
    CATEGORY_ICON: 'category/icon',
    PRODUCT_IMAGES_MAIN: 'product/images/main',
    PRODUCT_IMAGES_SKU: 'product/images/sku',
    PRODUCT_IMAGES_DETAIL: 'product/images/detail',
    PRODUCT_FILES: 'product/files',
    PRODUCT_EXPORT: 'product/export'
} as const;
export type GetOssUploadCredentialUploadFileTypeEnum = typeof GetOssUploadCredentialUploadFileTypeEnum[keyof typeof GetOssUploadCredentialUploadFileTypeEnum];
/**
 * @export
 */
export const GetOssUploadCredentialAccessTypeEnum = {
    PUBLIC: 'public',
    PRIVATE: 'private'
} as const;
export type GetOssUploadCredentialAccessTypeEnum = typeof GetOssUploadCredentialAccessTypeEnum[keyof typeof GetOssUploadCredentialAccessTypeEnum];
