/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { TaskStatusChangedCallbackReq } from '../models';
// @ts-ignore
import type { WorkflowInstStatusChangedCallbackReq } from '../models';
/**
 * 产品工单Api - axios parameter creator
 * @export
 */
export const 产品工单ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 工单中的任务处理状态发生变化时，工单系统会回调此接口
         * @summary 工单任务状态变化回调
         * @param {TaskStatusChangedCallbackReq} taskStatusChangedCallbackReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        callbackWhenTaskStatusChanged: async (taskStatusChangedCallbackReq: TaskStatusChangedCallbackReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'taskStatusChangedCallbackReq' is not null or undefined
            assertParamExists('callbackWhenTaskStatusChanged', 'taskStatusChangedCallbackReq', taskStatusChangedCallbackReq)
            const localVarPath = `/api/ticket/ticket-task-callback`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(taskStatusChangedCallbackReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 工单状态发生变化时，工单系统会回调此接口
         * @summary 工单状态变化回调
         * @param {WorkflowInstStatusChangedCallbackReq} workflowInstStatusChangedCallbackReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        callbackWhenWorkflowStatusChanged: async (workflowInstStatusChangedCallbackReq: WorkflowInstStatusChangedCallbackReq, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'workflowInstStatusChangedCallbackReq' is not null or undefined
            assertParamExists('callbackWhenWorkflowStatusChanged', 'workflowInstStatusChangedCallbackReq', workflowInstStatusChangedCallbackReq)
            const localVarPath = `/api/ticket/ticket-inst-callback`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(workflowInstStatusChangedCallbackReq, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 产品工单Api - functional programming interface
 * @export
 */
export const 产品工单ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 产品工单ApiAxiosParamCreator(configuration)
    return {
        /**
         * 工单中的任务处理状态发生变化时，工单系统会回调此接口
         * @summary 工单任务状态变化回调
         * @param {TaskStatusChangedCallbackReq} taskStatusChangedCallbackReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async callbackWhenTaskStatusChanged(taskStatusChangedCallbackReq: TaskStatusChangedCallbackReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<{ [key: string]: object; }>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.callbackWhenTaskStatusChanged(taskStatusChangedCallbackReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品工单Api.callbackWhenTaskStatusChanged']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 工单状态发生变化时，工单系统会回调此接口
         * @summary 工单状态变化回调
         * @param {WorkflowInstStatusChangedCallbackReq} workflowInstStatusChangedCallbackReq 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async callbackWhenWorkflowStatusChanged(workflowInstStatusChangedCallbackReq: WorkflowInstStatusChangedCallbackReq, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<{ [key: string]: object; }>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.callbackWhenWorkflowStatusChanged(workflowInstStatusChangedCallbackReq, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['产品工单Api.callbackWhenWorkflowStatusChanged']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 产品工单Api - factory interface
 * @export
 */
export const 产品工单ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 产品工单ApiFp(configuration)
    return {
        /**
         * 工单中的任务处理状态发生变化时，工单系统会回调此接口
         * @summary 工单任务状态变化回调
         * @param {产品工单ApiCallbackWhenTaskStatusChangedRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        callbackWhenTaskStatusChanged(requestParameters: 产品工单ApiCallbackWhenTaskStatusChangedRequest, options?: RawAxiosRequestConfig): AxiosPromise<{ [key: string]: object; }> {
            return localVarFp.callbackWhenTaskStatusChanged(requestParameters.taskStatusChangedCallbackReq, options).then((request) => request(axios, basePath));
        },
        /**
         * 工单状态发生变化时，工单系统会回调此接口
         * @summary 工单状态变化回调
         * @param {产品工单ApiCallbackWhenWorkflowStatusChangedRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        callbackWhenWorkflowStatusChanged(requestParameters: 产品工单ApiCallbackWhenWorkflowStatusChangedRequest, options?: RawAxiosRequestConfig): AxiosPromise<{ [key: string]: object; }> {
            return localVarFp.callbackWhenWorkflowStatusChanged(requestParameters.workflowInstStatusChangedCallbackReq, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for callbackWhenTaskStatusChanged operation in 产品工单Api.
 * @export
 * @interface 产品工单ApiCallbackWhenTaskStatusChangedRequest
 */
export interface 产品工单ApiCallbackWhenTaskStatusChangedRequest {
    /**
     * 
     * @type {TaskStatusChangedCallbackReq}
     * @memberof 产品工单ApiCallbackWhenTaskStatusChanged
     */
    readonly taskStatusChangedCallbackReq: TaskStatusChangedCallbackReq
}

/**
 * Request parameters for callbackWhenWorkflowStatusChanged operation in 产品工单Api.
 * @export
 * @interface 产品工单ApiCallbackWhenWorkflowStatusChangedRequest
 */
export interface 产品工单ApiCallbackWhenWorkflowStatusChangedRequest {
    /**
     * 
     * @type {WorkflowInstStatusChangedCallbackReq}
     * @memberof 产品工单ApiCallbackWhenWorkflowStatusChanged
     */
    readonly workflowInstStatusChangedCallbackReq: WorkflowInstStatusChangedCallbackReq
}

/**
 * 产品工单Api - object-oriented interface
 * @export
 * @class 产品工单Api
 * @extends {BaseAPI}
 */
export class 产品工单Api extends BaseAPI {
    /**
     * 工单中的任务处理状态发生变化时，工单系统会回调此接口
     * @summary 工单任务状态变化回调
     * @param {产品工单ApiCallbackWhenTaskStatusChangedRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品工单Api
     */
    public callbackWhenTaskStatusChanged(requestParameters: 产品工单ApiCallbackWhenTaskStatusChangedRequest, options?: RawAxiosRequestConfig) {
        return 产品工单ApiFp(this.configuration).callbackWhenTaskStatusChanged(requestParameters.taskStatusChangedCallbackReq, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 工单状态发生变化时，工单系统会回调此接口
     * @summary 工单状态变化回调
     * @param {产品工单ApiCallbackWhenWorkflowStatusChangedRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 产品工单Api
     */
    public callbackWhenWorkflowStatusChanged(requestParameters: 产品工单ApiCallbackWhenWorkflowStatusChangedRequest, options?: RawAxiosRequestConfig) {
        return 产品工单ApiFp(this.configuration).callbackWhenWorkflowStatusChanged(requestParameters.workflowInstStatusChangedCallbackReq, options).then((request) => request(this.axios, this.basePath));
    }
}

