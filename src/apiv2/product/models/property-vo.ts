/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PropertyValueVO } from './property-value-vo';

/**
 * 属性
 * @export
 * @interface PropertyVO
 */
export interface PropertyVO {
    /**
     * 属性ID
     * @type {number}
     * @memberof PropertyVO
     */
    'id'?: number;
    /**
     * 属性名称
     * @type {string}
     * @memberof PropertyVO
     */
    'propertyName'?: string;
    /**
     * 属性code
     * @type {string}
     * @memberof PropertyVO
     */
    'propertyCode'?: string;
    /**
     * 属性值列表
     * @type {Array<PropertyValueVO>}
     * @memberof PropertyVO
     */
    'values'?: Array<PropertyValueVO>;
}

