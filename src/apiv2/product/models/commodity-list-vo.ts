/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CommodityPlatformVO } from './commodity-platform-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { CommoditySkuVO } from './commodity-sku-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { DictValueVO } from './dict-value-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { PriceRange } from './price-range';
// May contain unused imports in some cases
// @ts-ignore
import type { PropertyValueVO } from './property-value-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SimpleCategoryVO } from './simple-category-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierVO } from './supplier-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { TicketInstListItemVO } from './ticket-inst-list-item-vo';

/**
 * 商品列表项
 * @export
 * @interface CommodityListVO
 */
export interface CommodityListVO {
    /**
     * 商品品ID
     * @type {number}
     * @memberof CommodityListVO
     */
    'id'?: number;
    /**
     * 产品ID
     * @type {number}
     * @memberof CommodityListVO
     */
    'productId'?: number;
    /**
     * 产品名称
     * @type {string}
     * @memberof CommodityListVO
     */
    'productName'?: string;
    /**
     * 商品名称
     * @type {string}
     * @memberof CommodityListVO
     */
    'commodityName'?: string;
    /**
     * 产品编码
     * @type {string}
     * @memberof CommodityListVO
     */
    'productCode'?: string;
    /**
     * 缩略图
     * @type {string}
     * @memberof CommodityListVO
     */
    'thumbnail'?: string;
    /**
     * 类目
     * @type {Array<SimpleCategoryVO>}
     * @memberof CommodityListVO
     */
    'categories'?: Array<SimpleCategoryVO>;
    /**
     * 
     * @type {PropertyValueVO}
     * @memberof CommodityListVO
     */
    'style'?: PropertyValueVO;
    /**
     * 
     * @type {SupplierVO}
     * @memberof CommodityListVO
     */
    'supplier'?: SupplierVO;
    /**
     * 
     * @type {DictValueVO}
     * @memberof CommodityListVO
     */
    'instrumentState'?: DictValueVO;
    /**
     * 
     * @type {DictValueVO}
     * @memberof CommodityListVO
     */
    'assembly'?: DictValueVO;
    /**
     * 选品状态
     * @type {number}
     * @memberof CommodityListVO
     */
    'selectionStatus'?: CommodityListVOSelectionStatusEnum;
    /**
     * 选品状态描述
     * @type {string}
     * @memberof CommodityListVO
     */
    'selectionReason'?: string | null;
    /**
     * 产品状态
     * @type {number}
     * @memberof CommodityListVO
     */
    'productStatus'?: CommodityListVOProductStatusEnum;
    /**
     * 平台商品状态
     * @type {Array<CommodityPlatformVO>}
     * @memberof CommodityListVO
     */
    'commodityPlatforms'?: Array<CommodityPlatformVO>;
    /**
     * 创建时间
     * @type {string}
     * @memberof CommodityListVO
     */
    'createTime'?: string;
    /**
     * 
     * @type {PriceRange}
     * @memberof CommodityListVO
     */
    'supplyPrice'?: PriceRange;
    /**
     * 
     * @type {PriceRange}
     * @memberof CommodityListVO
     */
    'marketPrice'?: PriceRange;
    /**
     * sku列表
     * @type {Array<CommoditySkuVO>}
     * @memberof CommodityListVO
     */
    'skus'?: Array<CommoditySkuVO>;
    /**
     * 关联的工单
     * @type {Array<TicketInstListItemVO>}
     * @memberof CommodityListVO
     */
    'ticketInsts'?: Array<TicketInstListItemVO>;
}

export const CommodityListVOSelectionStatusEnum = {
    PENDING: 0,
    APPROVED: 1,
    REJECTED: 2
} as const;

export type CommodityListVOSelectionStatusEnum = typeof CommodityListVOSelectionStatusEnum[keyof typeof CommodityListVOSelectionStatusEnum];
export const CommodityListVOProductStatusEnum = {
    DRAFT: 0,
    PENDING_REVIEW: 1,
    APPROVED: 2,
    ONLINE: 3,
    OFFLINE: 4,
    DELETED: 5
} as const;

export type CommodityListVOProductStatusEnum = typeof CommodityListVOProductStatusEnum[keyof typeof CommodityListVOProductStatusEnum];


