/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { OssUploadCredentialVO } from './oss-upload-credential-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOOssUploadCredentialVO
 */
export interface ResultVOOssUploadCredentialVO {
    /**
     * 状态码
     * @type {number}
     * @memberof ResultVOOssUploadCredentialVO
     */
    'code'?: number;
    /**
     * 消息
     * @type {string}
     * @memberof ResultVOOssUploadCredentialVO
     */
    'message'?: string;
    /**
     * 
     * @type {OssUploadCredentialVO}
     * @memberof ResultVOOssUploadCredentialVO
     */
    'data'?: OssUploadCredentialVO;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOOssUploadCredentialVO
     */
    'success'?: boolean;
}

