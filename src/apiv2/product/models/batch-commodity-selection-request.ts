/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 批量更新商品选品状态请求
 * @export
 * @interface BatchCommoditySelectionRequest
 */
export interface BatchCommoditySelectionRequest {
    /**
     * 商品ID列表
     * @type {Array<number>}
     * @memberof BatchCommoditySelectionRequest
     */
    'commodityIds': Array<number>;
    /**
     * 选品状态: 0-待审核 1-通过 2-不通过
     * @type {number}
     * @memberof BatchCommoditySelectionRequest
     */
    'selectionStatus': BatchCommoditySelectionRequestSelectionStatusEnum;
    /**
     * 选品不通过原因，当状态为不通过时必填
     * @type {string}
     * @memberof BatchCommoditySelectionRequest
     */
    'selectionReason'?: string;
}

export const BatchCommoditySelectionRequestSelectionStatusEnum = {
    PENDING: 0,
    APPROVED: 1,
    REJECTED: 2
} as const;

export type BatchCommoditySelectionRequestSelectionStatusEnum = typeof BatchCommoditySelectionRequestSelectionStatusEnum[keyof typeof BatchCommoditySelectionRequestSelectionStatusEnum];


