/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 产品图片更新参数
 * @export
 * @interface ProductImageUpdateRequest
 */
export interface ProductImageUpdateRequest {
    /**
     * 文件key
     * @type {string}
     * @memberof ProductImageUpdateRequest
     */
    'imageKey'?: string;
    /**
     * 图片类型
     * @type {number}
     * @memberof ProductImageUpdateRequest
     */
    'imageType'?: ProductImageUpdateRequestImageTypeEnum;
    /**
     * 图片名称
     * @type {string}
     * @memberof ProductImageUpdateRequest
     */
    'filename'?: string;
    /**
     * 是否封面
     * @type {boolean}
     * @memberof ProductImageUpdateRequest
     */
    'isCover'?: boolean;
    /**
     * 图片排序
     * @type {number}
     * @memberof ProductImageUpdateRequest
     */
    'order'?: number;
    /**
     * skuId，非sku图片的时候此值不传
     * @type {number}
     * @memberof ProductImageUpdateRequest
     */
    'skuId'?: number | null;
    /**
     * 图片ID，更新时必填，新增时不填
     * @type {number}
     * @memberof ProductImageUpdateRequest
     */
    'imageId'?: number;
}

export const ProductImageUpdateRequestImageTypeEnum = {
    PRODUCT_MAIN_IMAGE: 0,
    PRODUCT_SKU_IMAGE: 1,
    PRODUCT_DETAIL_IMAGE: 2
} as const;

export type ProductImageUpdateRequestImageTypeEnum = typeof ProductImageUpdateRequestImageTypeEnum[keyof typeof ProductImageUpdateRequestImageTypeEnum];


