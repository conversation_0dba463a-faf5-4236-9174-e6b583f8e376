/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 类目信息
 * @export
 * @interface CategoryVO
 */
export interface CategoryVO {
    /**
     * 类目ID
     * @type {number}
     * @memberof CategoryVO
     */
    'id'?: number;
    /**
     * 类目名称
     * @type {string}
     * @memberof CategoryVO
     */
    'categoryName'?: string;
    /**
     * 类目编码
     * @type {string}
     * @memberof CategoryVO
     */
    'categoryCode'?: string;
    /**
     * 类目图标
     * @type {string}
     * @memberof CategoryVO
     */
    'icon'?: string;
    /**
     * 类目描述
     * @type {string}
     * @memberof CategoryVO
     */
    'description'?: string;
    /**
     * 父类目ID
     * @type {number}
     * @memberof CategoryVO
     */
    'parentId'?: number;
    /**
     * 类目级别
     * @type {number}
     * @memberof CategoryVO
     */
    'level'?: number;
    /**
     * 是否叶子类目
     * @type {boolean}
     * @memberof CategoryVO
     */
    'isLeaf'?: boolean;
    /**
     * 排序序号
     * @type {number}
     * @memberof CategoryVO
     */
    'sortOrder'?: number;
    /**
     * 创建时间
     * @type {string}
     * @memberof CategoryVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof CategoryVO
     */
    'updateTime'?: string;
    /**
     * 子类目列表
     * @type {Array<CategoryVO>}
     * @memberof CategoryVO
     */
    'children'?: Array<CategoryVO>;
    /**
     * 祖先ID列表
     * @type {Array<number>}
     * @memberof CategoryVO
     */
    'ancestorIds'?: Array<number>;
    /**
     * 是否为搜索命中的结果
     * @type {boolean}
     * @memberof CategoryVO
     */
    'hint'?: boolean;
    /**
     * 搜索相关度得分
     * @type {number}
     * @memberof CategoryVO
     */
    'searchScore'?: number;
    /**
     * 匹配的字段
     * @type {Array<string>}
     * @memberof CategoryVO
     */
    'matchedFields'?: Array<string>;
    /**
     * 
     * @type {CategoryVO}
     * @memberof CategoryVO
     */
    'parent'?: CategoryVO;
}

