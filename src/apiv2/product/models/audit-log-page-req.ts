/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface AuditLogPageReq
 */
export interface AuditLogPageReq {
    /**
     * 
     * @type {number}
     * @memberof AuditLogPageReq
     */
    'pageNum'?: number;
    /**
     * 
     * @type {number}
     * @memberof AuditLogPageReq
     */
    'pageSize'?: number;
    /**
     * 
     * @type {string}
     * @memberof AuditLogPageReq
     */
    'sortColumn'?: string;
    /**
     * 
     * @type {boolean}
     * @memberof AuditLogPageReq
     */
    'descFlag'?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof AuditLogPageReq
     */
    'count'?: boolean;
    /**
     * 
     * @type {string}
     * @memberof AuditLogPageReq
     */
    'module': AuditLogPageReqModuleEnum;
    /**
     * 
     * @type {number}
     * @memberof AuditLogPageReq
     */
    'id'?: number;
    /**
     * 
     * @type {number}
     * @memberof AuditLogPageReq
     */
    'offset'?: number;
    /**
     * 
     * @type {string}
     * @memberof AuditLogPageReq
     */
    'orderBy'?: string;
}

export const AuditLogPageReqModuleEnum = {
    PRODUCT: 'PRODUCT',
    GOODS: 'GOODS'
} as const;

export type AuditLogPageReqModuleEnum = typeof AuditLogPageReqModuleEnum[keyof typeof AuditLogPageReqModuleEnum];


