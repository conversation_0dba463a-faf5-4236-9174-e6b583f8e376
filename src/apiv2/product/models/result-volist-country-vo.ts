/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CountryVO } from './country-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOListCountryVO
 */
export interface ResultVOListCountryVO {
    /**
     * 状态码
     * @type {number}
     * @memberof ResultVOListCountryVO
     */
    'code'?: number;
    /**
     * 消息
     * @type {string}
     * @memberof ResultVOListCountryVO
     */
    'message'?: string;
    /**
     * 数据
     * @type {Array<CountryVO>}
     * @memberof ResultVOListCountryVO
     */
    'data'?: Array<CountryVO>;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOListCountryVO
     */
    'success'?: boolean;
}

