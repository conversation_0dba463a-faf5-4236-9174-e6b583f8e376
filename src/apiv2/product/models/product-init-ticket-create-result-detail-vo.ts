/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ResultVO } from './result-vo';

/**
 * 产品初始化工单创建结果详情
 * @export
 * @interface ProductInitTicketCreateResultDetailVO
 */
export interface ProductInitTicketCreateResultDetailVO {
    /**
     * 成功的产品和工单映射关系，key为产品ID，value为工单ID
     * @type {{ [key: string]: string; }}
     * @memberof ProductInitTicketCreateResultDetailVO
     */
    'succeeded'?: { [key: string]: string; };
    /**
     * 失败的产品和错误原因，key为产品ID，value为结果对象
     * @type {{ [key: string]: ResultVO; }}
     * @memberof ProductInitTicketCreateResultDetailVO
     */
    'failed'?: { [key: string]: ResultVO; };
}

