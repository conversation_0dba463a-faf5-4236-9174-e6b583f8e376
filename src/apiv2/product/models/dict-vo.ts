/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { DictValueVO } from './dict-value-vo';

/**
 * 字典
 * @export
 * @interface DictVO
 */
export interface DictVO {
    /**
     * 字典ID
     * @type {number}
     * @memberof DictVO
     */
    'id'?: number;
    /**
     * 字典名称
     * @type {string}
     * @memberof DictVO
     */
    'dictName'?: string;
    /**
     * 字典code
     * @type {string}
     * @memberof DictVO
     */
    'dictCode'?: string;
    /**
     * 字典值列表
     * @type {Array<DictValueVO>}
     * @memberof DictVO
     */
    'values'?: Array<DictValueVO>;
}

