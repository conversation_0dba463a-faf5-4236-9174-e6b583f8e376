/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * sku物流信息
 * @export
 * @interface ProductSkuLogisticsVO
 */
export interface ProductSkuLogisticsVO {
    /**
     * 物流信息ID
     * @type {number}
     * @memberof ProductSkuLogisticsVO
     */
    'id'?: number;
    /**
     * 国家/地区
     * @type {string}
     * @memberof ProductSkuLogisticsVO
     */
    'locale'?: ProductSkuLogisticsVOLocaleEnum;
    /**
     * 运输方式
     * @type {number}
     * @memberof ProductSkuLogisticsVO
     */
    'transportMethod'?: number;
    /**
     * 物流渠道
     * @type {string}
     * @memberof ProductSkuLogisticsVO
     */
    'logisticsChannel'?: string;
    /**
     * 海运干线运费预估
     * @type {number}
     * @memberof ProductSkuLogisticsVO
     */
    'oceanFreightEstimate'?: number;
    /**
     * 海运运费预估-币种
     * @type {string}
     * @memberof ProductSkuLogisticsVO
     */
    'oceanFreightEstimateCurrency'?: ProductSkuLogisticsVOOceanFreightEstimateCurrencyEnum;
    /**
     * 尾程运费预估
     * @type {number}
     * @memberof ProductSkuLogisticsVO
     */
    'lastMileFreightEstimate'?: number;
    /**
     * 尾程运费预估-币种
     * @type {string}
     * @memberof ProductSkuLogisticsVO
     */
    'lastMileFreightEstimateCurrency'?: ProductSkuLogisticsVOLastMileFreightEstimateCurrencyEnum;
    /**
     * 预估运输天数
     * @type {string}
     * @memberof ProductSkuLogisticsVO
     */
    'estimatedDeliveryDays'?: string;
}

export const ProductSkuLogisticsVOLocaleEnum = {
    JAPAN: 'JP',
    KOREA: 'KR',
    UNITED_STATES: 'US',
    CHINA: 'CN',
    UNITED_KINGDOM: 'GB',
    FRANCE: 'FR',
    GERMANY: 'DE',
    ITALY: 'IT',
    SPAIN: 'ES',
    CANADA: 'CA',
    AUSTRALIA: 'AU',
    NEW_ZEALAND: 'NZ',
    SINGAPORE: 'SG',
    MALAYSIA: 'MY',
    THAILAND: 'TH',
    VIETNAM: 'VN'
} as const;

export type ProductSkuLogisticsVOLocaleEnum = typeof ProductSkuLogisticsVOLocaleEnum[keyof typeof ProductSkuLogisticsVOLocaleEnum];
export const ProductSkuLogisticsVOOceanFreightEstimateCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type ProductSkuLogisticsVOOceanFreightEstimateCurrencyEnum = typeof ProductSkuLogisticsVOOceanFreightEstimateCurrencyEnum[keyof typeof ProductSkuLogisticsVOOceanFreightEstimateCurrencyEnum];
export const ProductSkuLogisticsVOLastMileFreightEstimateCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type ProductSkuLogisticsVOLastMileFreightEstimateCurrencyEnum = typeof ProductSkuLogisticsVOLastMileFreightEstimateCurrencyEnum[keyof typeof ProductSkuLogisticsVOLastMileFreightEstimateCurrencyEnum];


