/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 产品文件更新参数
 * @export
 * @interface ProductFileUpdateRequest
 */
export interface ProductFileUpdateRequest {
    /**
     * 文件名称
     * @type {string}
     * @memberof ProductFileUpdateRequest
     */
    'filename': string;
    /**
     * 文件Path
     * @type {string}
     * @memberof ProductFileUpdateRequest
     */
    'filePath': string;
    /**
     * 文件类型
     * @type {number}
     * @memberof ProductFileUpdateRequest
     */
    'fileType': ProductFileUpdateRequestFileTypeEnum;
    /**
     * 文件说明
     * @type {string}
     * @memberof ProductFileUpdateRequest
     */
    'fileDesc'?: string;
    /**
     * 排序号
     * @type {number}
     * @memberof ProductFileUpdateRequest
     */
    'sortOrder'?: number;
    /**
     * 文件ID，更新时必填，新增时不填
     * @type {number}
     * @memberof ProductFileUpdateRequest
     */
    'fileId'?: number;
}

export const ProductFileUpdateRequestFileTypeEnum = {
    ATTACHMENT: 0,
    INSTRUMENT: 1,
    CERTIFICATION: 2,
    THREE_D: 3,
    SEO_VIDEO: 4
} as const;

export type ProductFileUpdateRequestFileTypeEnum = typeof ProductFileUpdateRequestFileTypeEnum[keyof typeof ProductFileUpdateRequestFileTypeEnum];


