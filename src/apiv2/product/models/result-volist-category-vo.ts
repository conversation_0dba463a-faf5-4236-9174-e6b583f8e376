/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CategoryVO } from './category-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOListCategoryVO
 */
export interface ResultVOListCategoryVO {
    /**
     * 状态码
     * @type {number}
     * @memberof ResultVOListCategoryVO
     */
    'code'?: number;
    /**
     * 消息
     * @type {string}
     * @memberof ResultVOListCategoryVO
     */
    'message'?: string;
    /**
     * 数据
     * @type {Array<CategoryVO>}
     * @memberof ResultVOListCategoryVO
     */
    'data'?: Array<CategoryVO>;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOListCategoryVO
     */
    'success'?: boolean;
}

