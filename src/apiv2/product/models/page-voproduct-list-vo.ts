/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ProductListVO } from './product-list-vo';

/**
 * 数据
 * @export
 * @interface PageVOProductListVO
 */
export interface PageVOProductListVO {
    /**
     * 
     * @type {number}
     * @memberof PageVOProductListVO
     */
    'pageNum'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOProductListVO
     */
    'pageSize'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOProductListVO
     */
    'total'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOProductListVO
     */
    'pages'?: number;
    /**
     * 
     * @type {Array<ProductListVO>}
     * @memberof PageVOProductListVO
     */
    'records'?: Array<ProductListVO>;
}

