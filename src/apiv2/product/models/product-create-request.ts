/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ProductFileCreateRequest } from './product-file-create-request';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductImageCreateRequest } from './product-image-create-request';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuCreateRequest } from './product-sku-create-request';

/**
 * 创建产品请求参数
 * @export
 * @interface ProductCreateRequest
 */
export interface ProductCreateRequest {
    /**
     * 产品名称
     * @type {string}
     * @memberof ProductCreateRequest
     */
    'productName': string;
    /**
     * 类目ID列表
     * @type {number}
     * @memberof ProductCreateRequest
     */
    'leafCategoryId': number;
    /**
     * 产品图像
     * @type {string}
     * @memberof ProductCreateRequest
     */
    'thumbnail'?: string;
    /**
     * 开发方式ID
     * @type {number}
     * @memberof ProductCreateRequest
     */
    'developMethod': number;
    /**
     * 产品链接
     * @type {string}
     * @memberof ProductCreateRequest
     */
    'productLink'?: string;
    /**
     * 供应商ID
     * @type {number}
     * @memberof ProductCreateRequest
     */
    'supplierId': number;
    /**
     * 发货时效
     * @type {number}
     * @memberof ProductCreateRequest
     */
    'shipTimeFrame'?: number;
    /**
     * 说明书状态ID
     * @type {number}
     * @memberof ProductCreateRequest
     */
    'instrumentState': number;
    /**
     * 实体检查ID，多选
     * @type {Array<number>}
     * @memberof ProductCreateRequest
     */
    'productChecks': Array<number>;
    /**
     * 组装ID
     * @type {number}
     * @memberof ProductCreateRequest
     */
    'assembly': number;
    /**
     * 支持定制ID，多选
     * @type {Array<number>}
     * @memberof ProductCreateRequest
     */
    'supportCustomizes': Array<number>;
    /**
     * 买手推荐(0:S, 1:A...)
     * @type {number}
     * @memberof ProductCreateRequest
     */
    'buyerRecommend'?: ProductCreateRequestBuyerRecommendEnum;
    /**
     * 维护者ID
     * @type {number}
     * @memberof ProductCreateRequest
     */
    'maintainerId': number;
    /**
     * 属性值ID列表
     * @type {Array<number>}
     * @memberof ProductCreateRequest
     */
    'properties'?: Array<number>;
    /**
     * 产品描述
     * @type {string}
     * @memberof ProductCreateRequest
     */
    'description'?: string;
    /**
     * 产品Code
     * @type {string}
     * @memberof ProductCreateRequest
     */
    'productCode'?: string;
    /**
     * SKU信息列表
     * @type {Array<ProductSkuCreateRequest>}
     * @memberof ProductCreateRequest
     */
    'skus'?: Array<ProductSkuCreateRequest>;
    /**
     * 产品图片列表
     * @type {Array<ProductImageCreateRequest>}
     * @memberof ProductCreateRequest
     */
    'images'?: Array<ProductImageCreateRequest>;
    /**
     * 产品文件列表
     * @type {Array<ProductFileCreateRequest>}
     * @memberof ProductCreateRequest
     */
    'files'?: Array<ProductFileCreateRequest>;
}

export const ProductCreateRequestBuyerRecommendEnum = {
    S: 0,
    A: 1,
    B: 2,
    C: 3
} as const;

export type ProductCreateRequestBuyerRecommendEnum = typeof ProductCreateRequestBuyerRecommendEnum[keyof typeof ProductCreateRequestBuyerRecommendEnum];


