/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 产品搜索参数
 * @export
 * @interface ProductSearchParam
 */
export interface ProductSearchParam {
    /**
     * 页码
     * @type {number}
     * @memberof ProductSearchParam
     */
    'page'?: number | null;
    /**
     * 每页条数
     * @type {number}
     * @memberof ProductSearchParam
     */
    'size'?: number | null;
    /**
     * 类目ID列表
     * @type {Array<number | null>}
     * @memberof ProductSearchParam
     */
    'categoryIds'?: Array<number | null> | null;
    /**
     * 供应商ID列表
     * @type {Array<number | null>}
     * @memberof ProductSearchParam
     */
    'supplierIds'?: Array<number | null> | null;
    /**
     * 属性值ID列表
     * @type {Array<number | null>}
     * @memberof ProductSearchParam
     */
    'propertyValueIds'?: Array<number | null> | null;
    /**
     * 创建时间开始
     * @type {string}
     * @memberof ProductSearchParam
     */
    'createTimeStart'?: string | null;
    /**
     * 创建时间结束
     * @type {string}
     * @memberof ProductSearchParam
     */
    'createTimeEnd'?: string | null;
    /**
     * 维护人ID列表
     * @type {Array<number | null>}
     * @memberof ProductSearchParam
     */
    'maintainerIds'?: Array<number | null> | null;
    /**
     * 组装ID列表
     * @type {Array<number | null>}
     * @memberof ProductSearchParam
     */
    'assemblyIds'?: Array<number | null> | null;
    /**
     * 产品状态
     * @type {number}
     * @memberof ProductSearchParam
     */
    'status'?: ProductSearchParamStatusEnum | null;
    /**
     * 带电状态
     * @type {boolean}
     * @memberof ProductSearchParam
     */
    'electronicState'?: boolean | null;
    /**
     * 产品名称（模糊搜索）
     * @type {string}
     * @memberof ProductSearchParam
     */
    'productName'?: string | null;
    /**
     * 产品code（批量精确搜索）
     * @type {Array<string | null>}
     * @memberof ProductSearchParam
     */
    'productCodes'?: Array<string | null> | null;
    /**
     * 产品code（单个模糊搜索）
     * @type {string}
     * @memberof ProductSearchParam
     */
    'productCode'?: string | null;
    /**
     * sku code（批量精确搜索）
     * @type {Array<string | null>}
     * @memberof ProductSearchParam
     */
    'skuCodes'?: Array<string | null> | null;
    /**
     * sku code（单个模糊搜索）
     * @type {string}
     * @memberof ProductSearchParam
     */
    'skuCode'?: string | null;
    /**
     * 材质
     * @type {Array<number>}
     * @memberof ProductSearchParam
     */
    'materialIds'?: Array<number>;
}

export const ProductSearchParamStatusEnum = {
    DRAFT: 0,
    PENDING_REVIEW: 1,
    APPROVED: 2,
    ONLINE: 3,
    OFFLINE: 4,
    DELETED: 5
} as const;

export type ProductSearchParamStatusEnum = typeof ProductSearchParamStatusEnum[keyof typeof ProductSearchParamStatusEnum];


