/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 属性值
 * @export
 * @interface PropertyValueVO
 */
export interface PropertyValueVO {
    /**
     * 属性值ID
     * @type {number}
     * @memberof PropertyValueVO
     */
    'id'?: number;
    /**
     * 属性值名称
     * @type {string}
     * @memberof PropertyValueVO
     */
    'valueName'?: string;
    /**
     * 国际化属性名称
     * @type {{ [key: string]: string; }}
     * @memberof PropertyValueVO
     */
    'i18nValueNames'?: { [key: string]: string; };
    /**
     * 属性值code
     * @type {string}
     * @memberof PropertyValueVO
     */
    'valueCode'?: string;
    /**
     * 属性值描述
     * @type {{ [key: string]: object; }}
     * @memberof PropertyValueVO
     */
    'attrs'?: { [key: string]: object; };
}

