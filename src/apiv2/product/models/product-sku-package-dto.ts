/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * SKU包装信息数据传输对象
 * @export
 * @interface ProductSkuPackageDTO
 */
export interface ProductSkuPackageDTO {
    /**
     * 包装信息ID（创建时不需要传）
     * @type {number}
     * @memberof ProductSkuPackageDTO
     */
    'id'?: number;
    /**
     * 包装长度(mm)
     * @type {number}
     * @memberof ProductSkuPackageDTO
     */
    'length': number;
    /**
     * 包装宽度(mm)
     * @type {number}
     * @memberof ProductSkuPackageDTO
     */
    'width': number;
    /**
     * 包装高度(mm)
     * @type {number}
     * @memberof ProductSkuPackageDTO
     */
    'height': number;
}

