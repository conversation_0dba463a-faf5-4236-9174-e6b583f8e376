/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuLogisticsDTO } from './product-sku-logistics-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuPackageDTO } from './product-sku-package-dto';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuSpecDTO } from './product-sku-spec-dto';

/**
 * 产品SKU更新参数
 * @export
 * @interface ProductSkuUpdateRequest
 */
export interface ProductSkuUpdateRequest {
    /**
     * SKU编码
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'skuCode': string;
    /**
     * SKU名称
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'skuName'?: string;
    /**
     * SKU英文名称
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'skuNameEn'?: string;
    /**
     * SKU状态
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'skuStatus': ProductSkuUpdateRequestSkuStatusEnum;
    /**
     * 缩略图OSS路径
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'thumbnail'?: string;
    /**
     * 标签
     * @type {Array<number>}
     * @memberof ProductSkuUpdateRequest
     */
    'properties'?: Array<number>;
    /**
     * 颜色（对应dict_value.id）
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'color'?: number;
    /**
     * 净重量（克）
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'netWeight': number;
    /**
     * 毛重量（克）
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'grossWeight': number;
    /**
     * 含税含运价格
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'purchasePrice': number;
    /**
     * 含税不含运价格
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'purchasePriceExFreight': number;
    /**
     * 税率百分比（如6.00表示6%）
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'taxRate': number;
    /**
     * 平台展示价格
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'platformPrice': number;
    /**
     * 采购备注
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'purchaseRemark'?: string;
    /**
     * SKU详细描述
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'description'?: string;
    /**
     * 附属品
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'appendant'?: string;
    /**
     * 安装难度（对应dict_value.id）
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'installDifficulty'?: number;
    /**
     * 电器配件类型（JSON数组）
     * @type {Array<number>}
     * @memberof ProductSkuUpdateRequest
     */
    'electricAccessory'?: Array<number>;
    /**
     * 是否带电：0=否 1=是
     * @type {boolean}
     * @memberof ProductSkuUpdateRequest
     */
    'electronicState'?: boolean;
    /**
     * 带电认证类型（对应dict_value.id）
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'electronicCertification'?: number;
    /**
     * 含电池（对应dict_value.id）
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'battery'?: number;
    /**
     * 易碎品（对应dict_value.id）
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'fragile'?: number;
    /**
     * 规格列表
     * @type {Array<ProductSkuSpecDTO>}
     * @memberof ProductSkuUpdateRequest
     */
    'specs'?: Array<ProductSkuSpecDTO>;
    /**
     * 包装信息列表
     * @type {Array<ProductSkuPackageDTO>}
     * @memberof ProductSkuUpdateRequest
     */
    'packages'?: Array<ProductSkuPackageDTO>;
    /**
     * 物流信息
     * @type {Array<ProductSkuLogisticsDTO>}
     * @memberof ProductSkuUpdateRequest
     */
    'logistics'?: Array<ProductSkuLogisticsDTO>;
    /**
     * 主材1级
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'priMatLv1': number;
    /**
     * 主材2级
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'priMatLv2'?: number;
    /**
     * 次材1级
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'secMatLv1'?: number;
    /**
     * 次材2级
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'secMatLv2'?: number;
    /**
     * 材质描述
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'materialDesc'?: string;
    /**
     * 规格1
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'spec1'?: string;
    /**
     * 规格2
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'spec2'?: string;
    /**
     * 规格3
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'spec3'?: string;
    /**
     * 规格4
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'spec4'?: string;
    /**
     * 规格5
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'spec5'?: string;
    /**
     * 安装说明书采集结果（对应dict_value.id）
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'installDocCollectionState'?: number;
    /**
     * 网采链接
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'onlinePurchaseLink'?: string;
    /**
     * 供应商发货编码
     * @type {string}
     * @memberof ProductSkuUpdateRequest
     */
    'supplierDeliveryCode'?: string;
    /**
     * SKU ID，更新时必填，新增时不填
     * @type {number}
     * @memberof ProductSkuUpdateRequest
     */
    'skuId'?: number;
}

export const ProductSkuUpdateRequestSkuStatusEnum = {
    DRAFT: 0,
    CHECKING: 1,
    ON_SHELF: 2,
    OFF_SHELVES: 3,
    FREEZE: 4,
    DELETED: 5
} as const;

export type ProductSkuUpdateRequestSkuStatusEnum = typeof ProductSkuUpdateRequestSkuStatusEnum[keyof typeof ProductSkuUpdateRequestSkuStatusEnum];


