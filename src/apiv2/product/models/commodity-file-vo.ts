/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 商品文件信息
 * @export
 * @interface CommodityFileVO
 */
export interface CommodityFileVO {
    /**
     * ID
     * @type {number}
     * @memberof CommodityFileVO
     */
    'id'?: number;
    /**
     * 文件类型
     * @type {number}
     * @memberof CommodityFileVO
     */
    'fileType'?: CommodityFileVOFileTypeEnum;
    /**
     * 文件名称
     * @type {string}
     * @memberof CommodityFileVO
     */
    'filename'?: string;
    /**
     * 文件路径
     * @type {string}
     * @memberof CommodityFileVO
     */
    'filePath'?: string;
    /**
     * 文件URL
     * @type {string}
     * @memberof CommodityFileVO
     */
    'fileUrl'?: string;
    /**
     * 描述
     * @type {string}
     * @memberof CommodityFileVO
     */
    'fileDesc'?: string;
    /**
     * 文件大小（字节）
     * @type {number}
     * @memberof CommodityFileVO
     */
    'fileSize'?: number;
}

export const CommodityFileVOFileTypeEnum = {
    ATTACHMENT: 0,
    INSTRUMENT: 1,
    CERTIFICATION: 2,
    THREE_D: 3,
    SEO_VIDEO: 4
} as const;

export type CommodityFileVOFileTypeEnum = typeof CommodityFileVOFileTypeEnum[keyof typeof CommodityFileVOFileTypeEnum];


