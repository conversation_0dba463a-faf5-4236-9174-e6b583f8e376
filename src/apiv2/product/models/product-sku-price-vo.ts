/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * sku产品定价信息
 * @export
 * @interface ProductSkuPriceVO
 */
export interface ProductSkuPriceVO {
    /**
     * id
     * @type {number}
     * @memberof ProductSkuPriceVO
     */
    'id'?: number;
    /**
     * skuId
     * @type {number}
     * @memberof ProductSkuPriceVO
     */
    'skuId'?: number;
    /**
     * 对应系数Id，目前没有设置，默认1
     * @type {number}
     * @memberof ProductSkuPriceVO
     */
    'coefficientId'?: number;
    /**
     * 含税含运价格
     * @type {number}
     * @memberof ProductSkuPriceVO
     */
    'purchasePrice'?: number;
    /**
     * 含税不含运价格
     * @type {number}
     * @memberof ProductSkuPriceVO
     */
    'purchasePriceExFreight'?: number;
    /**
     * sku物流信息对应国家
     * @type {string}
     * @memberof ProductSkuPriceVO
     */
    'country'?: ProductSkuPriceVOCountryEnum;
    /**
     * 海运干线运费预估
     * @type {number}
     * @memberof ProductSkuPriceVO
     */
    'oceanFreightEstimate'?: number;
    /**
     * 尾程运费预估
     * @type {number}
     * @memberof ProductSkuPriceVO
     */
    'lastMileFreightEstimate'?: number;
    /**
     * 基础币种（采购价对应币种）
     * @type {string}
     * @memberof ProductSkuPriceVO
     */
    'baseCurrency'?: string;
    /**
     * 目标币种（国家对应币种）
     * @type {string}
     * @memberof ProductSkuPriceVO
     */
    'targetCurrency'?: string;
    /**
     * 产品定价（sku当前物流信息对应的产品定价数据）
     * @type {number}
     * @memberof ProductSkuPriceVO
     */
    'calculatedPrice'?: number;
    /**
     * 定价时间
     * @type {string}
     * @memberof ProductSkuPriceVO
     */
    'priceTime'?: string;
}

export const ProductSkuPriceVOCountryEnum = {
    JAPAN: 'JP',
    KOREA: 'KR',
    UNITED_STATES: 'US',
    CHINA: 'CN',
    UNITED_KINGDOM: 'GB',
    FRANCE: 'FR',
    GERMANY: 'DE',
    ITALY: 'IT',
    SPAIN: 'ES',
    CANADA: 'CA',
    AUSTRALIA: 'AU',
    NEW_ZEALAND: 'NZ',
    SINGAPORE: 'SG',
    MALAYSIA: 'MY',
    THAILAND: 'TH',
    VIETNAM: 'VN'
} as const;

export type ProductSkuPriceVOCountryEnum = typeof ProductSkuPriceVOCountryEnum[keyof typeof ProductSkuPriceVOCountryEnum];


