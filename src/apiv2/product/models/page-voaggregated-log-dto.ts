/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { AggregatedLogDTO } from './aggregated-log-dto';

/**
 * 数据
 * @export
 * @interface PageVOAggregatedLogDTO
 */
export interface PageVOAggregatedLogDTO {
    /**
     * 
     * @type {number}
     * @memberof PageVOAggregatedLogDTO
     */
    'pageNum'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOAggregatedLogDTO
     */
    'pageSize'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOAggregatedLogDTO
     */
    'total'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOAggregatedLogDTO
     */
    'pages'?: number;
    /**
     * 
     * @type {Array<AggregatedLogDTO>}
     * @memberof PageVOAggregatedLogDTO
     */
    'records'?: Array<AggregatedLogDTO>;
}

