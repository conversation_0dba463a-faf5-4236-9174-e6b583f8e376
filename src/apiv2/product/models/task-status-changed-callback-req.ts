/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface TaskStatusChangedCallbackReq
 */
export interface TaskStatusChangedCallbackReq {
    /**
     * 
     * @type {string}
     * @memberof TaskStatusChangedCallbackReq
     */
    'eventType'?: TaskStatusChangedCallbackReqEventTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof TaskStatusChangedCallbackReq
     */
    'ticketDefCode'?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskStatusChangedCallbackReq
     */
    'relevantObjId'?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskStatusChangedCallbackReq
     */
    'nodeId'?: string;
    /**
     * 
     * @type {number}
     * @memberof TaskStatusChangedCallbackReq
     */
    'nodeType'?: TaskStatusChangedCallbackReqNodeTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof TaskStatusChangedCallbackReq
     */
    'nodeName'?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskStatusChangedCallbackReq
     */
    'resultVarName'?: string;
    /**
     * 
     * @type {string}
     * @memberof TaskStatusChangedCallbackReq
     */
    'processInstId'?: string;
}

export const TaskStatusChangedCallbackReqEventTypeEnum = {
    TASK_CREATED: 'TASK_CREATED',
    TASK_ASSIGNED: 'TASK_ASSIGNED',
    TASK_COMPLETED: 'TASK_COMPLETED'
} as const;

export type TaskStatusChangedCallbackReqEventTypeEnum = typeof TaskStatusChangedCallbackReqEventTypeEnum[keyof typeof TaskStatusChangedCallbackReqEventTypeEnum];
export const TaskStatusChangedCallbackReqNodeTypeEnum = {
    SUBMIT: 0,
    APPROVE: 1
} as const;

export type TaskStatusChangedCallbackReqNodeTypeEnum = typeof TaskStatusChangedCallbackReqNodeTypeEnum[keyof typeof TaskStatusChangedCallbackReqNodeTypeEnum];


