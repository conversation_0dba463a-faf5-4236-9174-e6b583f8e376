/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CommodityFileVO } from './commodity-file-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { CommodityImageVO } from './commodity-image-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { CommoditySkuVO } from './commodity-sku-vo';

/**
 * 商品详情
 * @export
 * @interface CommodityDetailVO
 */
export interface CommodityDetailVO {
    /**
     * 商品ID
     * @type {number}
     * @memberof CommodityDetailVO
     */
    'id'?: number;
    /**
     * 商品名称
     * @type {string}
     * @memberof CommodityDetailVO
     */
    'commodityName'?: string;
    /**
     * 产品状态
     * @type {number}
     * @memberof CommodityDetailVO
     */
    'productStatus'?: CommodityDetailVOProductStatusEnum;
    /**
     * 选品状态
     * @type {number}
     * @memberof CommodityDetailVO
     */
    'selectionStatus'?: CommodityDetailVOSelectionStatusEnum;
    /**
     * 选品原因
     * @type {string}
     * @memberof CommodityDetailVO
     */
    'selectionReason'?: string;
    /**
     * 创建时间
     * @type {string}
     * @memberof CommodityDetailVO
     */
    'createTime'?: string;
    /**
     * 商品SKU列表
     * @type {Array<CommoditySkuVO>}
     * @memberof CommodityDetailVO
     */
    'skus'?: Array<CommoditySkuVO>;
    /**
     * 商品图片列表
     * @type {Array<CommodityImageVO>}
     * @memberof CommodityDetailVO
     */
    'images'?: Array<CommodityImageVO>;
    /**
     * 商品文件列表
     * @type {Array<CommodityFileVO>}
     * @memberof CommodityDetailVO
     */
    'files'?: Array<CommodityFileVO>;
}

export const CommodityDetailVOProductStatusEnum = {
    DRAFT: 0,
    PENDING_REVIEW: 1,
    APPROVED: 2,
    ONLINE: 3,
    OFFLINE: 4,
    DELETED: 5
} as const;

export type CommodityDetailVOProductStatusEnum = typeof CommodityDetailVOProductStatusEnum[keyof typeof CommodityDetailVOProductStatusEnum];
export const CommodityDetailVOSelectionStatusEnum = {
    PENDING: 0,
    APPROVED: 1,
    REJECTED: 2
} as const;

export type CommodityDetailVOSelectionStatusEnum = typeof CommodityDetailVOSelectionStatusEnum[keyof typeof CommodityDetailVOSelectionStatusEnum];


