/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 平台商品状态
 * @export
 * @interface CommodityPlatformVO
 */
export interface CommodityPlatformVO {
    /**
     * 平台
     * @type {string}
     * @memberof CommodityPlatformVO
     */
    'platform'?: string;
    /**
     * 平台商品状态
     * @type {number}
     * @memberof CommodityPlatformVO
     */
    'listingStatus'?: CommodityPlatformVOListingStatusEnum;
}

export const CommodityPlatformVOListingStatusEnum = {
    PENDING: 0,
    PREPARING: 1,
    LISTED: 2,
    UNLISTED: 3
} as const;

export type CommodityPlatformVOListingStatusEnum = typeof CommodityPlatformVOListingStatusEnum[keyof typeof CommodityPlatformVOListingStatusEnum];


