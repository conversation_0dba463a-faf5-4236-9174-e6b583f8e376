/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuVO } from './product-sku-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { PropertyValueVO } from './property-value-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SimpleCategoryVO } from './simple-category-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierVO } from './supplier-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { TicketInstListItemVO } from './ticket-inst-list-item-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { UserVO } from './user-vo';

/**
 * 产品列表项
 * @export
 * @interface ProductListVO
 */
export interface ProductListVO {
    /**
     * 产品ID
     * @type {number}
     * @memberof ProductListVO
     */
    'id'?: number;
    /**
     * 产品名称
     * @type {string}
     * @memberof ProductListVO
     */
    'productName'?: string;
    /**
     * 产品编码
     * @type {string}
     * @memberof ProductListVO
     */
    'productCode'?: string;
    /**
     * 缩略图
     * @type {string}
     * @memberof ProductListVO
     */
    'thumbnail'?: string;
    /**
     * 类目
     * @type {Array<SimpleCategoryVO>}
     * @memberof ProductListVO
     */
    'categories'?: Array<SimpleCategoryVO>;
    /**
     * 
     * @type {SupplierVO}
     * @memberof ProductListVO
     */
    'supplier'?: SupplierVO;
    /**
     * 
     * @type {UserVO}
     * @memberof ProductListVO
     */
    'maintainer'?: UserVO;
    /**
     * 属性列表
     * @type {Array<PropertyValueVO>}
     * @memberof ProductListVO
     */
    'properties'?: Array<PropertyValueVO>;
    /**
     * 产品状态
     * @type {number}
     * @memberof ProductListVO
     */
    'status'?: ProductListVOStatusEnum;
    /**
     * sku列表
     * @type {Array<ProductSkuVO>}
     * @memberof ProductListVO
     */
    'skus'?: Array<ProductSkuVO> | null;
    /**
     * 创建时间
     * @type {string}
     * @memberof ProductListVO
     */
    'createTime'?: string;
    /**
     * 关联的工单
     * @type {Array<TicketInstListItemVO>}
     * @memberof ProductListVO
     */
    'ticketInsts'?: Array<TicketInstListItemVO>;
}

export const ProductListVOStatusEnum = {
    DRAFT: 0,
    PENDING_REVIEW: 1,
    APPROVED: 2,
    ONLINE: 3,
    OFFLINE: 4,
    DELETED: 5
} as const;

export type ProductListVOStatusEnum = typeof ProductListVOStatusEnum[keyof typeof ProductListVOStatusEnum];


