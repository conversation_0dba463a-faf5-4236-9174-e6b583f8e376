/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuSpecDTO } from './product-sku-spec-dto';

/**
 * 商品SKU更新参数
 * @export
 * @interface CommoditySkuUpdateRequest
 */
export interface CommoditySkuUpdateRequest {
    /**
     * SKU ID（更新时必填）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'skuId'?: number;
    /**
     * 属性列表
     * @type {Array<number>}
     * @memberof CommoditySkuUpdateRequest
     */
    'properties'?: Array<number>;
    /**
     * 颜色
     * @type {string}
     * @memberof CommoditySkuUpdateRequest
     */
    'color'?: string;
    /**
     * 供应价（元）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'supplyPrice'?: number;
    /**
     * 市场价（元）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'marketPrice'?: number;
    /**
     * 促销价（元）
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'promoPrice'?: number;
    /**
     * 折扣率
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'discountRate'?: number;
    /**
     * 价格系数
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'coefficient'?: number;
    /**
     * 安装服务费
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'installationFee'?: number;
    /**
     * 主材质（一级）
     * @type {string}
     * @memberof CommoditySkuUpdateRequest
     */
    'priMatLv1'?: string;
    /**
     * 主材质（二级）
     * @type {string}
     * @memberof CommoditySkuUpdateRequest
     */
    'priMatLv2'?: string;
    /**
     * 次材质（一级）
     * @type {string}
     * @memberof CommoditySkuUpdateRequest
     */
    'secMatLv1'?: string;
    /**
     * 次材质（二级）
     * @type {string}
     * @memberof CommoditySkuUpdateRequest
     */
    'secMatLv2'?: string;
    /**
     * 规格列表
     * @type {Array<ProductSkuSpecDTO>}
     * @memberof CommoditySkuUpdateRequest
     */
    'specs'?: Array<ProductSkuSpecDTO>;
    /**
     * 安装难易度
     * @type {number}
     * @memberof CommoditySkuUpdateRequest
     */
    'installDifficulty'?: number;
}

