/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 商品文件
 * @export
 * @interface ProductFileVO
 */
export interface ProductFileVO {
    /**
     * ID
     * @type {number}
     * @memberof ProductFileVO
     */
    'id'?: number;
    /**
     * 文件类型
     * @type {number}
     * @memberof ProductFileVO
     */
    'fileType'?: ProductFileVOFileTypeEnum;
    /**
     * 文件名称
     * @type {string}
     * @memberof ProductFileVO
     */
    'filename'?: string;
    /**
     * 文件路径
     * @type {string}
     * @memberof ProductFileVO
     */
    'filePath'?: string;
    /**
     * 文件URL
     * @type {string}
     * @memberof ProductFileVO
     */
    'fileUrl'?: string;
    /**
     * 描述
     * @type {string}
     * @memberof ProductFileVO
     */
    'fileDesc'?: string;
    /**
     * 文件大小
     * @type {number}
     * @memberof ProductFileVO
     */
    'fileSize'?: number;
    /**
     * 文件后缀
     * @type {string}
     * @memberof ProductFileVO
     */
    'extension'?: string;
}

export const ProductFileVOFileTypeEnum = {
    ATTACHMENT: 0,
    INSTRUMENT: 1,
    CERTIFICATION: 2,
    THREE_D: 3,
    SEO_VIDEO: 4
} as const;

export type ProductFileVOFileTypeEnum = typeof ProductFileVOFileTypeEnum[keyof typeof ProductFileVOFileTypeEnum];


