/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 产品图片创建参数
 * @export
 * @interface ProductImageCreateRequest
 */
export interface ProductImageCreateRequest {
    /**
     * 文件key
     * @type {string}
     * @memberof ProductImageCreateRequest
     */
    'imageKey'?: string;
    /**
     * 图片类型
     * @type {number}
     * @memberof ProductImageCreateRequest
     */
    'imageType'?: ProductImageCreateRequestImageTypeEnum;
    /**
     * 图片名称
     * @type {string}
     * @memberof ProductImageCreateRequest
     */
    'filename'?: string;
    /**
     * 是否封面
     * @type {boolean}
     * @memberof ProductImageCreateRequest
     */
    'isCover'?: boolean;
    /**
     * 图片排序
     * @type {number}
     * @memberof ProductImageCreateRequest
     */
    'order'?: number;
    /**
     * skuId，非sku图片的时候此值不传
     * @type {number}
     * @memberof ProductImageCreateRequest
     */
    'skuId'?: number | null;
}

export const ProductImageCreateRequestImageTypeEnum = {
    PRODUCT_MAIN_IMAGE: 0,
    PRODUCT_SKU_IMAGE: 1,
    PRODUCT_DETAIL_IMAGE: 2
} as const;

export type ProductImageCreateRequestImageTypeEnum = typeof ProductImageCreateRequestImageTypeEnum[keyof typeof ProductImageCreateRequestImageTypeEnum];


