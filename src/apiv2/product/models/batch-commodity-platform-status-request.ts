/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 批量更新平台商品状态请求
 * @export
 * @interface BatchCommodityPlatformStatusRequest
 */
export interface BatchCommodityPlatformStatusRequest {
    /**
     * 商品ID列表
     * @type {Array<number>}
     * @memberof BatchCommodityPlatformStatusRequest
     */
    'commodityIds': Array<number>;
    /**
     * 平台ID
     * @type {number}
     * @memberof BatchCommodityPlatformStatusRequest
     */
    'platformId': number;
    /**
     * 上架状态: 0-待启动 1-准备中 2-已上架 3-已下架
     * @type {number}
     * @memberof BatchCommodityPlatformStatusRequest
     */
    'listingStatus': BatchCommodityPlatformStatusRequestListingStatusEnum;
}

export const BatchCommodityPlatformStatusRequestListingStatusEnum = {
    PENDING: 0,
    PREPARING: 1,
    LISTED: 2,
    UNLISTED: 3
} as const;

export type BatchCommodityPlatformStatusRequestListingStatusEnum = typeof BatchCommodityPlatformStatusRequestListingStatusEnum[keyof typeof BatchCommodityPlatformStatusRequestListingStatusEnum];


