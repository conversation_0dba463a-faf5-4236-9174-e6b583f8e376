/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuPriceVO } from './product-sku-price-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOListProductSkuPriceVO
 */
export interface ResultVOListProductSkuPriceVO {
    /**
     * 状态码
     * @type {number}
     * @memberof ResultVOListProductSkuPriceVO
     */
    'code'?: number;
    /**
     * 消息
     * @type {string}
     * @memberof ResultVOListProductSkuPriceVO
     */
    'message'?: string;
    /**
     * 数据
     * @type {Array<ProductSkuPriceVO>}
     * @memberof ResultVOListProductSkuPriceVO
     */
    'data'?: Array<ProductSkuPriceVO>;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOListProductSkuPriceVO
     */
    'success'?: boolean;
}

