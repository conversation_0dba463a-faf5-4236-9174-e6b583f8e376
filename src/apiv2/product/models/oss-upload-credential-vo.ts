/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * OSS上传凭证信息
 * @export
 * @interface OssUploadCredentialVO
 */
export interface OssUploadCredentialVO {
    /**
     * 文件存储路径和名称，即文件在OSS中的完整路径
     * @type {string}
     * @memberof OssUploadCredentialVO
     */
    'key'?: string;
    /**
     * 上传域名，即OSS的访问域名
     * @type {string}
     * @memberof OssUploadCredentialVO
     */
    'host'?: string;
    /**
     * 访问凭证ID，用于OSS身份验证
     * @type {string}
     * @memberof OssUploadCredentialVO
     */
    'accessKeyId'?: string;
    /**
     * 上传策略，用于限制上传条件和内容
     * @type {string}
     * @memberof OssUploadCredentialVO
     */
    'policy'?: string;
    /**
     * 签名，用于验证上传请求的合法性
     * @type {string}
     * @memberof OssUploadCredentialVO
     */
    'signature'?: string;
    /**
     * 凭证过期时间戳，单位毫秒
     * @type {number}
     * @memberof OssUploadCredentialVO
     */
    'expireTime'?: number;
}

