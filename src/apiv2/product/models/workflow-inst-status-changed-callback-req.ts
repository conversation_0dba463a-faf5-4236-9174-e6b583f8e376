/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 
 * @export
 * @interface WorkflowInstStatusChangedCallbackReq
 */
export interface WorkflowInstStatusChangedCallbackReq {
    /**
     * 
     * @type {string}
     * @memberof WorkflowInstStatusChangedCallbackReq
     */
    'eventType'?: WorkflowInstStatusChangedCallbackReqEventTypeEnum;
    /**
     * 
     * @type {string}
     * @memberof WorkflowInstStatusChangedCallbackReq
     */
    'ticketDefCode'?: string;
    /**
     * 
     * @type {string}
     * @memberof WorkflowInstStatusChangedCallbackReq
     */
    'relevantObjId'?: string;
    /**
     * 
     * @type {string}
     * @memberof WorkflowInstStatusChangedCallbackReq
     */
    'processInstId'?: string;
    /**
     * 
     * @type {{ [key: string]: object; }}
     * @memberof WorkflowInstStatusChangedCallbackReq
     */
    'processInstVars'?: { [key: string]: object; };
    /**
     * 
     * @type {string}
     * @memberof WorkflowInstStatusChangedCallbackReq
     */
    'formData'?: string;
}

export const WorkflowInstStatusChangedCallbackReqEventTypeEnum = {
    PROCESS_STARTED: 'PROCESS_STARTED',
    PROCESS_COMPLETED: 'PROCESS_COMPLETED'
} as const;

export type WorkflowInstStatusChangedCallbackReqEventTypeEnum = typeof WorkflowInstStatusChangedCallbackReqEventTypeEnum[keyof typeof WorkflowInstStatusChangedCallbackReqEventTypeEnum];


