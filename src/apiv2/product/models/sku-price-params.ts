/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * SKU价格参数
 * @export
 * @interface SkuPriceParams
 */
export interface SkuPriceParams {
    /**
     * SKU ID
     * @type {number}
     * @memberof SkuPriceParams
     */
    'skuId': number;
    /**
     * 价格系数
     * @type {number}
     * @memberof SkuPriceParams
     */
    'coefficient'?: number;
    /**
     * 市场价（单位：分）
     * @type {number}
     * @memberof SkuPriceParams
     */
    'marketPrice'?: number;
    /**
     * 折扣率
     * @type {number}
     * @memberof SkuPriceParams
     */
    'discountRate'?: number;
    /**
     * 促销价（单位：分）
     * @type {number}
     * @memberof SkuPriceParams
     */
    'promoPrice'?: number;
}

