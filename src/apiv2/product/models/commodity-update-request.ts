/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CommodityFileUpdateRequest } from './commodity-file-update-request';
// May contain unused imports in some cases
// @ts-ignore
import type { CommodityImageUpdateRequest } from './commodity-image-update-request';
// May contain unused imports in some cases
// @ts-ignore
import type { CommoditySkuUpdateRequest } from './commodity-sku-update-request';

/**
 * 商品更新参数
 * @export
 * @interface CommodityUpdateRequest
 */
export interface CommodityUpdateRequest {
    /**
     * 商品ID
     * @type {number}
     * @memberof CommodityUpdateRequest
     */
    'commodityId': number;
    /**
     * 商品名称
     * @type {string}
     * @memberof CommodityUpdateRequest
     */
    'commodityName'?: string;
    /**
     * 商品SKU列表
     * @type {Array<CommoditySkuUpdateRequest>}
     * @memberof CommodityUpdateRequest
     */
    'skus'?: Array<CommoditySkuUpdateRequest>;
    /**
     * 商品图片列表
     * @type {Array<CommodityImageUpdateRequest>}
     * @memberof CommodityUpdateRequest
     */
    'images'?: Array<CommodityImageUpdateRequest>;
    /**
     * 商品文件列表
     * @type {Array<CommodityFileUpdateRequest>}
     * @memberof CommodityUpdateRequest
     */
    'files'?: Array<CommodityFileUpdateRequest>;
}

