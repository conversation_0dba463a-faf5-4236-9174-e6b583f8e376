/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 产品图片
 * @export
 * @interface ProductImageVO
 */
export interface ProductImageVO {
    /**
     * 图片ID
     * @type {number}
     * @memberof ProductImageVO
     */
    'id'?: number;
    /**
     * sku code
     * @type {string}
     * @memberof ProductImageVO
     */
    'skuCode'?: string | null;
    /**
     * 图片key
     * @type {string}
     * @memberof ProductImageVO
     */
    'imageKey'?: string;
    /**
     * 图片URL
     * @type {string}
     * @memberof ProductImageVO
     */
    'imageUrl'?: string;
    /**
     * 图片类型
     * @type {number}
     * @memberof ProductImageVO
     */
    'imageType'?: ProductImageVOImageTypeEnum;
    /**
     * 是否封面
     * @type {boolean}
     * @memberof ProductImageVO
     */
    'cover'?: boolean;
    /**
     * 图片名称
     * @type {string}
     * @memberof ProductImageVO
     */
    'filename'?: string;
    /**
     * 图片尺寸
     * @type {string}
     * @memberof ProductImageVO
     */
    'imageSize'?: string;
    /**
     * 图片大小
     * @type {number}
     * @memberof ProductImageVO
     */
    'imageSizeKb'?: number;
    /**
     * 图片排序
     * @type {number}
     * @memberof ProductImageVO
     */
    'order'?: number;
}

export const ProductImageVOImageTypeEnum = {
    PRODUCT_MAIN_IMAGE: 0,
    PRODUCT_SKU_IMAGE: 1,
    PRODUCT_DETAIL_IMAGE: 2
} as const;

export type ProductImageVOImageTypeEnum = typeof ProductImageVOImageTypeEnum[keyof typeof ProductImageVOImageTypeEnum];


