/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { PlatformVO } from './platform-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOListPlatformVO
 */
export interface ResultVOListPlatformVO {
    /**
     * 状态码
     * @type {number}
     * @memberof ResultVOListPlatformVO
     */
    'code'?: number;
    /**
     * 消息
     * @type {string}
     * @memberof ResultVOListPlatformVO
     */
    'message'?: string;
    /**
     * 数据
     * @type {Array<PlatformVO>}
     * @memberof ResultVOListPlatformVO
     */
    'data'?: Array<PlatformVO>;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOListPlatformVO
     */
    'success'?: boolean;
}

