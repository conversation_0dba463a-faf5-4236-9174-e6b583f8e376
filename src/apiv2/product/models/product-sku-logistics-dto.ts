/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * sku物流信息
 * @export
 * @interface ProductSkuLogisticsDTO
 */
export interface ProductSkuLogisticsDTO {
    /**
     * id(新增的时候可以不填)
     * @type {number}
     * @memberof ProductSkuLogisticsDTO
     */
    'id'?: number;
    /**
     * 国家
     * @type {string}
     * @memberof ProductSkuLogisticsDTO
     */
    'locale'?: ProductSkuLogisticsDTOLocaleEnum;
    /**
     * 运输方式
     * @type {number}
     * @memberof ProductSkuLogisticsDTO
     */
    'transportMethod'?: number;
    /**
     * 运输渠道
     * @type {string}
     * @memberof ProductSkuLogisticsDTO
     */
    'logisticsChannel'?: string;
    /**
     * 海运运费预估
     * @type {number}
     * @memberof ProductSkuLogisticsDTO
     */
    'oceanFreightEstimate'?: number;
    /**
     * 海运运费预估-币种
     * @type {string}
     * @memberof ProductSkuLogisticsDTO
     */
    'oceanFreightEstimateCurrency'?: ProductSkuLogisticsDTOOceanFreightEstimateCurrencyEnum;
    /**
     * 尾程运费预估
     * @type {number}
     * @memberof ProductSkuLogisticsDTO
     */
    'lastMileFreightEstimate'?: number;
    /**
     * 尾程运费预估-币种
     * @type {string}
     * @memberof ProductSkuLogisticsDTO
     */
    'lastMileFreightEstimateCurrency'?: ProductSkuLogisticsDTOLastMileFreightEstimateCurrencyEnum;
    /**
     * 运送时效
     * @type {string}
     * @memberof ProductSkuLogisticsDTO
     */
    'estimatedDeliveryDays'?: string;
}

export const ProductSkuLogisticsDTOLocaleEnum = {
    JAPAN: 'JP',
    KOREA: 'KR',
    UNITED_STATES: 'US',
    CHINA: 'CN',
    UNITED_KINGDOM: 'GB',
    FRANCE: 'FR',
    GERMANY: 'DE',
    ITALY: 'IT',
    SPAIN: 'ES',
    CANADA: 'CA',
    AUSTRALIA: 'AU',
    NEW_ZEALAND: 'NZ',
    SINGAPORE: 'SG',
    MALAYSIA: 'MY',
    THAILAND: 'TH',
    VIETNAM: 'VN'
} as const;

export type ProductSkuLogisticsDTOLocaleEnum = typeof ProductSkuLogisticsDTOLocaleEnum[keyof typeof ProductSkuLogisticsDTOLocaleEnum];
export const ProductSkuLogisticsDTOOceanFreightEstimateCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type ProductSkuLogisticsDTOOceanFreightEstimateCurrencyEnum = typeof ProductSkuLogisticsDTOOceanFreightEstimateCurrencyEnum[keyof typeof ProductSkuLogisticsDTOOceanFreightEstimateCurrencyEnum];
export const ProductSkuLogisticsDTOLastMileFreightEstimateCurrencyEnum = {
    CNY: 'CNY',
    JPY: 'JPY',
    USD: 'USD',
    EUR: 'EUR'
} as const;

export type ProductSkuLogisticsDTOLastMileFreightEstimateCurrencyEnum = typeof ProductSkuLogisticsDTOLastMileFreightEstimateCurrencyEnum[keyof typeof ProductSkuLogisticsDTOLastMileFreightEstimateCurrencyEnum];


