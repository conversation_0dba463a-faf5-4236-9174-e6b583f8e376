/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 产品批量状态更新请求
 * @export
 * @interface ProductBatchStatusUpdateRequest
 */
export interface ProductBatchStatusUpdateRequest {
    /**
     * 产品ID列表
     * @type {Array<number>}
     * @memberof ProductBatchStatusUpdateRequest
     */
    'productIds': Array<number>;
    /**
     * 目标状态
     * @type {number}
     * @memberof ProductBatchStatusUpdateRequest
     */
    'targetStatus': ProductBatchStatusUpdateRequestTargetStatusEnum;
}

export const ProductBatchStatusUpdateRequestTargetStatusEnum = {
    DRAFT: 0,
    PENDING_REVIEW: 1,
    APPROVED: 2,
    ONLINE: 3,
    OFFLINE: 4,
    DELETED: 5
} as const;

export type ProductBatchStatusUpdateRequestTargetStatusEnum = typeof ProductBatchStatusUpdateRequestTargetStatusEnum[keyof typeof ProductBatchStatusUpdateRequestTargetStatusEnum];


