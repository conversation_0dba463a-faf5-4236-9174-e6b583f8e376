/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 商品搜索参数
 * @export
 * @interface CommoditySearchParam
 */
export interface CommoditySearchParam {
    /**
     * 地区，默认值日本
     * @type {string}
     * @memberof CommoditySearchParam
     */
    'locale'?: CommoditySearchParamLocaleEnum;
    /**
     * 页码
     * @type {number}
     * @memberof CommoditySearchParam
     */
    'page'?: number | null;
    /**
     * 每页条数
     * @type {number}
     * @memberof CommoditySearchParam
     */
    'size'?: number | null;
    /**
     * 类目ID列表
     * @type {Array<number | null>}
     * @memberof CommoditySearchParam
     */
    'categoryIds'?: Array<number | null> | null;
    /**
     * SPU编码(精确搜索)
     * @type {Array<string>}
     * @memberof CommoditySearchParam
     */
    'productCodes'?: Array<string>;
    /**
     * SPU编码(模糊搜索)
     * @type {string}
     * @memberof CommoditySearchParam
     */
    'productCode'?: string;
    /**
     * 商品名称
     * @type {string}
     * @memberof CommoditySearchParam
     */
    'commodityName'?: string;
    /**
     * 产品状态
     * @type {number}
     * @memberof CommoditySearchParam
     */
    'productStatus'?: CommoditySearchParamProductStatusEnum;
    /**
     * 选品状态
     * @type {number}
     * @memberof CommoditySearchParam
     */
    'selectionStatus'?: CommoditySearchParamSelectionStatusEnum;
    /**
     * 创建时间开始
     * @type {string}
     * @memberof CommoditySearchParam
     */
    'createTimeStart'?: string | null;
    /**
     * 创建时间结束
     * @type {string}
     * @memberof CommoditySearchParam
     */
    'createTimeEnd'?: string | null;
    /**
     * 风格主题
     * @type {Array<number>}
     * @memberof CommoditySearchParam
     */
    'styles'?: Array<number>;
    /**
     * 平台列表
     * @type {Array<number>}
     * @memberof CommoditySearchParam
     */
    'platformIds'?: Array<number>;
    /**
     * 平台上架状态
     * @type {Array<number>}
     * @memberof CommoditySearchParam
     */
    'listingStatuses'?: Array<CommoditySearchParamListingStatusesEnum>;
}

export const CommoditySearchParamLocaleEnum = {
    JAPAN: 'JP',
    KOREA: 'KR',
    UNITED_STATES: 'US',
    CHINA: 'CN',
    UNITED_KINGDOM: 'GB',
    FRANCE: 'FR',
    GERMANY: 'DE',
    ITALY: 'IT',
    SPAIN: 'ES',
    CANADA: 'CA',
    AUSTRALIA: 'AU',
    NEW_ZEALAND: 'NZ',
    SINGAPORE: 'SG',
    MALAYSIA: 'MY',
    THAILAND: 'TH',
    VIETNAM: 'VN'
} as const;

export type CommoditySearchParamLocaleEnum = typeof CommoditySearchParamLocaleEnum[keyof typeof CommoditySearchParamLocaleEnum];
export const CommoditySearchParamProductStatusEnum = {
    DRAFT: 0,
    PENDING_REVIEW: 1,
    APPROVED: 2,
    ONLINE: 3,
    OFFLINE: 4,
    DELETED: 5
} as const;

export type CommoditySearchParamProductStatusEnum = typeof CommoditySearchParamProductStatusEnum[keyof typeof CommoditySearchParamProductStatusEnum];
export const CommoditySearchParamSelectionStatusEnum = {
    PENDING: 0,
    APPROVED: 1,
    REJECTED: 2
} as const;

export type CommoditySearchParamSelectionStatusEnum = typeof CommoditySearchParamSelectionStatusEnum[keyof typeof CommoditySearchParamSelectionStatusEnum];
export const CommoditySearchParamListingStatusesEnum = {
    PENDING: 0,
    PREPARING: 1,
    LISTED: 2,
    UNLISTED: 3
} as const;

export type CommoditySearchParamListingStatusesEnum = typeof CommoditySearchParamListingStatusesEnum[keyof typeof CommoditySearchParamListingStatusesEnum];


