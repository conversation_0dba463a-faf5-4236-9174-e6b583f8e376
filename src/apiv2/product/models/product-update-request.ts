/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ProductFileUpdateRequest } from './product-file-update-request';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductImageUpdateRequest } from './product-image-update-request';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuUpdateRequest } from './product-sku-update-request';

/**
 * 更新产品请求参数
 * @export
 * @interface ProductUpdateRequest
 */
export interface ProductUpdateRequest {
    /**
     * 产品名称
     * @type {string}
     * @memberof ProductUpdateRequest
     */
    'productName': string;
    /**
     * 类目ID列表
     * @type {number}
     * @memberof ProductUpdateRequest
     */
    'leafCategoryId': number;
    /**
     * 产品图像
     * @type {string}
     * @memberof ProductUpdateRequest
     */
    'thumbnail'?: string;
    /**
     * 开发方式ID
     * @type {number}
     * @memberof ProductUpdateRequest
     */
    'developMethod': number;
    /**
     * 产品链接
     * @type {string}
     * @memberof ProductUpdateRequest
     */
    'productLink'?: string;
    /**
     * 供应商ID
     * @type {number}
     * @memberof ProductUpdateRequest
     */
    'supplierId': number;
    /**
     * 发货时效
     * @type {number}
     * @memberof ProductUpdateRequest
     */
    'shipTimeFrame'?: number;
    /**
     * 说明书状态ID
     * @type {number}
     * @memberof ProductUpdateRequest
     */
    'instrumentState': number;
    /**
     * 实体检查ID，多选
     * @type {Array<number>}
     * @memberof ProductUpdateRequest
     */
    'productChecks': Array<number>;
    /**
     * 组装ID
     * @type {number}
     * @memberof ProductUpdateRequest
     */
    'assembly': number;
    /**
     * 支持定制ID，多选
     * @type {Array<number>}
     * @memberof ProductUpdateRequest
     */
    'supportCustomizes': Array<number>;
    /**
     * 买手推荐(0:S, 1:A...)
     * @type {number}
     * @memberof ProductUpdateRequest
     */
    'buyerRecommend'?: ProductUpdateRequestBuyerRecommendEnum;
    /**
     * 维护者ID
     * @type {number}
     * @memberof ProductUpdateRequest
     */
    'maintainerId': number;
    /**
     * 属性值ID列表
     * @type {Array<number>}
     * @memberof ProductUpdateRequest
     */
    'properties'?: Array<number>;
    /**
     * 产品描述
     * @type {string}
     * @memberof ProductUpdateRequest
     */
    'description'?: string;
    /**
     * 产品ID
     * @type {number}
     * @memberof ProductUpdateRequest
     */
    'productId': number;
    /**
     * SKU信息列表
     * @type {Array<ProductSkuUpdateRequest>}
     * @memberof ProductUpdateRequest
     */
    'skus'?: Array<ProductSkuUpdateRequest>;
    /**
     * 产品图片列表
     * @type {Array<ProductImageUpdateRequest>}
     * @memberof ProductUpdateRequest
     */
    'images'?: Array<ProductImageUpdateRequest>;
    /**
     * 产品文件列表
     * @type {Array<ProductFileUpdateRequest>}
     * @memberof ProductUpdateRequest
     */
    'files'?: Array<ProductFileUpdateRequest>;
}

export const ProductUpdateRequestBuyerRecommendEnum = {
    S: 0,
    A: 1,
    B: 2,
    C: 3
} as const;

export type ProductUpdateRequestBuyerRecommendEnum = typeof ProductUpdateRequestBuyerRecommendEnum[keyof typeof ProductUpdateRequestBuyerRecommendEnum];


