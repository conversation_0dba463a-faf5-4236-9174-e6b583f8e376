/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 产品文件创建参数
 * @export
 * @interface ProductFileCreateRequest
 */
export interface ProductFileCreateRequest {
    /**
     * 文件名称
     * @type {string}
     * @memberof ProductFileCreateRequest
     */
    'filename': string;
    /**
     * 文件Path
     * @type {string}
     * @memberof ProductFileCreateRequest
     */
    'filePath': string;
    /**
     * 文件类型
     * @type {number}
     * @memberof ProductFileCreateRequest
     */
    'fileType': ProductFileCreateRequestFileTypeEnum;
    /**
     * 文件说明
     * @type {string}
     * @memberof ProductFileCreateRequest
     */
    'fileDesc'?: string;
    /**
     * 排序号
     * @type {number}
     * @memberof ProductFileCreateRequest
     */
    'sortOrder'?: number;
}

export const ProductFileCreateRequestFileTypeEnum = {
    ATTACHMENT: 0,
    INSTRUMENT: 1,
    CERTIFICATION: 2,
    THREE_D: 3,
    SEO_VIDEO: 4
} as const;

export type ProductFileCreateRequestFileTypeEnum = typeof ProductFileCreateRequestFileTypeEnum[keyof typeof ProductFileCreateRequestFileTypeEnum];


