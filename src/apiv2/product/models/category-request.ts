/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 类目请求数据
 * @export
 * @interface CategoryRequest
 */
export interface CategoryRequest {
    /**
     * 类目ID
     * @type {number}
     * @memberof CategoryRequest
     */
    'id'?: number;
    /**
     * 类目名称
     * @type {string}
     * @memberof CategoryRequest
     */
    'categoryName': string;
    /**
     * 类目编码
     * @type {string}
     * @memberof CategoryRequest
     */
    'categoryCode'?: string;
    /**
     * 类目图标
     * @type {string}
     * @memberof CategoryRequest
     */
    'icon'?: string;
    /**
     * 类目描述
     * @type {string}
     * @memberof CategoryRequest
     */
    'description'?: string;
    /**
     * 父类目ID
     * @type {number}
     * @memberof CategoryRequest
     */
    'parentId'?: number;
}

