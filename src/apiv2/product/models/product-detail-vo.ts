/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { DictValueVO } from './dict-value-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductFileVO } from './product-file-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductImageVO } from './product-image-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuVO } from './product-sku-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { PropertyValueVO } from './property-value-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SimpleCategoryVO } from './simple-category-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SupplierVO } from './supplier-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { UserVO } from './user-vo';

/**
 * 产品视图
 * @export
 * @interface ProductDetailVO
 */
export interface ProductDetailVO {
    /**
     * 产品ID
     * @type {number}
     * @memberof ProductDetailVO
     */
    'id'?: number;
    /**
     * 产品名称
     * @type {string}
     * @memberof ProductDetailVO
     */
    'productName'?: string;
    /**
     * 产品编码
     * @type {string}
     * @memberof ProductDetailVO
     */
    'productCode'?: string;
    /**
     * 图片
     * @type {string}
     * @memberof ProductDetailVO
     */
    'thumbnail'?: string;
    /**
     * 类目
     * @type {Array<SimpleCategoryVO>}
     * @memberof ProductDetailVO
     */
    'categories'?: Array<SimpleCategoryVO>;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductDetailVO
     */
    'developMethod'?: DictValueVO;
    /**
     * 产品链接
     * @type {string}
     * @memberof ProductDetailVO
     */
    'productLink'?: string;
    /**
     * 
     * @type {SupplierVO}
     * @memberof ProductDetailVO
     */
    'supplier'?: SupplierVO;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductDetailVO
     */
    'instrumentState'?: DictValueVO;
    /**
     * 实体检查，多选
     * @type {Array<DictValueVO>}
     * @memberof ProductDetailVO
     */
    'productChecks'?: Array<DictValueVO>;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductDetailVO
     */
    'assembly'?: DictValueVO;
    /**
     * 支持定制，多选
     * @type {Array<DictValueVO>}
     * @memberof ProductDetailVO
     */
    'supportCustomizes'?: Array<DictValueVO>;
    /**
     * 买手推荐(0:S, 1:A...)
     * @type {number}
     * @memberof ProductDetailVO
     */
    'buyerRecommend'?: ProductDetailVOBuyerRecommendEnum;
    /**
     * 
     * @type {UserVO}
     * @memberof ProductDetailVO
     */
    'maintainer'?: UserVO;
    /**
     * 属性列表
     * @type {Array<PropertyValueVO>}
     * @memberof ProductDetailVO
     */
    'properties'?: Array<PropertyValueVO>;
    /**
     * 图片列表
     * @type {Array<ProductImageVO>}
     * @memberof ProductDetailVO
     */
    'images'?: Array<ProductImageVO>;
    /**
     * 文件列表
     * @type {Array<ProductFileVO>}
     * @memberof ProductDetailVO
     */
    'files'?: Array<ProductFileVO>;
    /**
     * 产品描述
     * @type {string}
     * @memberof ProductDetailVO
     */
    'description'?: string;
    /**
     * sku列表
     * @type {Array<ProductSkuVO>}
     * @memberof ProductDetailVO
     */
    'skus'?: Array<ProductSkuVO>;
    /**
     * 
     * @type {DictValueVO}
     * @memberof ProductDetailVO
     */
    'shipTimeFrame'?: DictValueVO;
    /**
     * 创建时间
     * @type {string}
     * @memberof ProductDetailVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof ProductDetailVO
     */
    'updateTime'?: string;
    /**
     * 产品状态
     * @type {number}
     * @memberof ProductDetailVO
     */
    'status'?: ProductDetailVOStatusEnum;
}

export const ProductDetailVOBuyerRecommendEnum = {
    S: 0,
    A: 1,
    B: 2,
    C: 3
} as const;

export type ProductDetailVOBuyerRecommendEnum = typeof ProductDetailVOBuyerRecommendEnum[keyof typeof ProductDetailVOBuyerRecommendEnum];
export const ProductDetailVOStatusEnum = {
    DRAFT: 0,
    PENDING_REVIEW: 1,
    APPROVED: 2,
    ONLINE: 3,
    OFFLINE: 4,
    DELETED: 5
} as const;

export type ProductDetailVOStatusEnum = typeof ProductDetailVOStatusEnum[keyof typeof ProductDetailVOStatusEnum];


