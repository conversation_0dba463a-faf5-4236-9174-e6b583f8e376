/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ProductDetailVO } from './product-detail-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOProductDetailVO
 */
export interface ResultVOProductDetailVO {
    /**
     * 状态码
     * @type {number}
     * @memberof ResultVOProductDetailVO
     */
    'code'?: number;
    /**
     * 消息
     * @type {string}
     * @memberof ResultVOProductDetailVO
     */
    'message'?: string;
    /**
     * 
     * @type {ProductDetailVO}
     * @memberof ResultVOProductDetailVO
     */
    'data'?: ProductDetailVO;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOProductDetailVO
     */
    'success'?: boolean;
}

