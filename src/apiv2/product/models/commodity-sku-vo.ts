/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ProductSkuPriceVO } from './product-sku-price-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { PropertyValueVO } from './property-value-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { SkuSpecVO } from './sku-spec-vo';

/**
 * 商品Sku
 * @export
 * @interface CommoditySkuVO
 */
export interface CommoditySkuVO {
    /**
     * id
     * @type {number}
     * @memberof CommoditySkuVO
     */
    'id'?: number;
    /**
     * 图片
     * @type {string}
     * @memberof CommoditySkuVO
     */
    'thumbnail'?: string;
    /**
     * 供货价
     * @type {number}
     * @memberof CommoditySkuVO
     */
    'supplyPrice'?: number;
    /**
     * 市场价
     * @type {number}
     * @memberof CommoditySkuVO
     */
    'marketPrice'?: number;
    /**
     * 产品侧sku状态
     * @type {number}
     * @memberof CommoditySkuVO
     */
    'status'?: CommoditySkuVOStatusEnum;
    /**
     * sku code
     * @type {string}
     * @memberof CommoditySkuVO
     */
    'skuCode'?: string;
    /**
     * 折扣
     * @type {number}
     * @memberof CommoditySkuVO
     */
    'discountRate'?: number;
    /**
     * 促销价
     * @type {number}
     * @memberof CommoditySkuVO
     */
    'promoPrice'?: number;
    /**
     * 系数
     * @type {number}
     * @memberof CommoditySkuVO
     */
    'coefficient'?: number;
    /**
     * 属性列表
     * @type {Array<PropertyValueVO>}
     * @memberof CommoditySkuVO
     */
    'properties'?: Array<PropertyValueVO>;
    /**
     * sku 规格
     * @type {Array<SkuSpecVO>}
     * @memberof CommoditySkuVO
     */
    'specs'?: Array<SkuSpecVO>;
    /**
     * 安装费
     * @type {number}
     * @memberof CommoditySkuVO
     */
    'installationFee'?: number;
    /**
     * 市场定价数据
     * @type {Array<ProductSkuPriceVO>}
     * @memberof CommoditySkuVO
     */
    'prices'?: Array<ProductSkuPriceVO>;
}

export const CommoditySkuVOStatusEnum = {
    DRAFT: 0,
    CHECKING: 1,
    ON_SHELF: 2,
    OFF_SHELVES: 3,
    FREEZE: 4,
    DELETED: 5
} as const;

export type CommoditySkuVOStatusEnum = typeof CommoditySkuVOStatusEnum[keyof typeof CommoditySkuVOStatusEnum];


