/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { CommodityListVO } from './commodity-list-vo';

/**
 * 数据
 * @export
 * @interface PageVOCommodityListVO
 */
export interface PageVOCommodityListVO {
    /**
     * 
     * @type {number}
     * @memberof PageVOCommodityListVO
     */
    'pageNum'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOCommodityListVO
     */
    'pageSize'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOCommodityListVO
     */
    'total'?: number;
    /**
     * 
     * @type {number}
     * @memberof PageVOCommodityListVO
     */
    'pages'?: number;
    /**
     * 
     * @type {Array<CommodityListVO>}
     * @memberof PageVOCommodityListVO
     */
    'records'?: Array<CommodityListVO>;
}

