/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { SupplierVO } from './supplier-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOListSupplierVO
 */
export interface ResultVOListSupplierVO {
    /**
     * 状态码
     * @type {number}
     * @memberof ResultVOListSupplierVO
     */
    'code'?: number;
    /**
     * 消息
     * @type {string}
     * @memberof ResultVOListSupplierVO
     */
    'message'?: string;
    /**
     * 数据
     * @type {Array<SupplierVO>}
     * @memberof ResultVOListSupplierVO
     */
    'data'?: Array<SupplierVO>;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOListSupplierVO
     */
    'success'?: boolean;
}

