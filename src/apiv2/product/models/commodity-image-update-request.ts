/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 商品图片更新参数
 * @export
 * @interface CommodityImageUpdateRequest
 */
export interface CommodityImageUpdateRequest {
    /**
     * 图片ID（更新时必填）
     * @type {number}
     * @memberof CommodityImageUpdateRequest
     */
    'imageId'?: number;
    /**
     * skuId
     * @type {number}
     * @memberof CommodityImageUpdateRequest
     */
    'skuId'?: number;
    /**
     * 图片OSS路径
     * @type {string}
     * @memberof CommodityImageUpdateRequest
     */
    'imageKey'?: string;
    /**
     * 图片类型
     * @type {number}
     * @memberof CommodityImageUpdateRequest
     */
    'imageType'?: number;
    /**
     * 文件名
     * @type {string}
     * @memberof CommodityImageUpdateRequest
     */
    'filename'?: string;
    /**
     * 是否为封面图：0=否 1=是
     * @type {boolean}
     * @memberof CommodityImageUpdateRequest
     */
    'isCover'?: boolean;
    /**
     * 排序号
     * @type {number}
     * @memberof CommodityImageUpdateRequest
     */
    'order'?: number;
}

