/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 商品图片信息
 * @export
 * @interface CommodityImageVO
 */
export interface CommodityImageVO {
    /**
     * 图片ID
     * @type {number}
     * @memberof CommodityImageVO
     */
    'id'?: number;
    /**
     * sku code
     * @type {string}
     * @memberof CommodityImageVO
     */
    'skuCode'?: string | null;
    /**
     * 图片key
     * @type {string}
     * @memberof CommodityImageVO
     */
    'imageKey'?: string;
    /**
     * 图片URL
     * @type {string}
     * @memberof CommodityImageVO
     */
    'imageUrl'?: string;
    /**
     * 图片类型
     * @type {number}
     * @memberof CommodityImageVO
     */
    'imageType'?: CommodityImageVOImageTypeEnum;
    /**
     * 是否封面
     * @type {boolean}
     * @memberof CommodityImageVO
     */
    'cover'?: boolean;
    /**
     * 图片名称
     * @type {string}
     * @memberof CommodityImageVO
     */
    'filename'?: string;
    /**
     * 图片尺寸
     * @type {string}
     * @memberof CommodityImageVO
     */
    'imageSize'?: string;
    /**
     * 图片大小
     * @type {number}
     * @memberof CommodityImageVO
     */
    'imageSizeKb'?: number;
    /**
     * 图片排序
     * @type {number}
     * @memberof CommodityImageVO
     */
    'order'?: number;
}

export const CommodityImageVOImageTypeEnum = {
    PRODUCT_MAIN_IMAGE: 0,
    PRODUCT_SKU_IMAGE: 1,
    PRODUCT_DETAIL_IMAGE: 2
} as const;

export type CommodityImageVOImageTypeEnum = typeof CommodityImageVOImageTypeEnum[keyof typeof CommodityImageVOImageTypeEnum];


