/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { ModuleLogVO } from './module-log-vo';

/**
 * 
 * @export
 * @interface AggregatedLogDTO
 */
export interface AggregatedLogDTO {
    /**
     * 
     * @type {string}
     * @memberof AggregatedLogDTO
     */
    'requestId'?: string;
    /**
     * 
     * @type {number}
     * @memberof AggregatedLogDTO
     */
    'userId'?: number;
    /**
     * 
     * @type {string}
     * @memberof AggregatedLogDTO
     */
    'operateTime'?: string;
    /**
     * 
     * @type {string}
     * @memberof AggregatedLogDTO
     */
    'operaType'?: AggregatedLogDTOOperaTypeEnum;
    /**
     * 
     * @type {Array<ModuleLogVO>}
     * @memberof AggregatedLogDTO
     */
    'details'?: Array<ModuleLogVO>;
}

export const AggregatedLogDTOOperaTypeEnum = {
    CREATE: 'CREATE',
    EDIT: 'EDIT',
    DELETE: 'DELETE'
} as const;

export type AggregatedLogDTOOperaTypeEnum = typeof AggregatedLogDTOOperaTypeEnum[keyof typeof AggregatedLogDTOOperaTypeEnum];


