/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVO } from '../models';
// @ts-ignore
import type { ResultVOListTicketTypeVO } from '../models';
// @ts-ignore
import type { ResultVOTicketDefDetailVO } from '../models';
// @ts-ignore
import type { TicketDefUpdateReqVO } from '../models';
/**
 * 工单配置Api - axios parameter creator
 * @export
 */
export const 工单配置ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 获取工单配置
         * @param {number} ticketDefId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDetailById1: async (ticketDefId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'ticketDefId' is not null or undefined
            assertParamExists('getDetailById1', 'ticketDefId', ticketDefId)
            const localVarPath = `/api/ticket-definitions/{ticketDefId}`
                .replace(`{${"ticketDefId"}}`, encodeURIComponent(String(ticketDefId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取工单类型
         * @param {number} [manualCreationAllowed] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryTicketTypeList: async (manualCreationAllowed?: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/ticket-definitions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;

            if (manualCreationAllowed !== undefined) {
                localVarQueryParameter['manualCreationAllowed'] = manualCreationAllowed;
            }


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 更新工单配置
         * @param {TicketDefUpdateReqVO} ticketDefUpdateReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        update: async (ticketDefUpdateReqVO: TicketDefUpdateReqVO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'ticketDefUpdateReqVO' is not null or undefined
            assertParamExists('update', 'ticketDefUpdateReqVO', ticketDefUpdateReqVO)
            const localVarPath = `/api/ticket-definitions`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'PUT', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(ticketDefUpdateReqVO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 工单配置Api - functional programming interface
 * @export
 */
export const 工单配置ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 工单配置ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 获取工单配置
         * @param {number} ticketDefId 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDetailById1(ticketDefId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOTicketDefDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDetailById1(ticketDefId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工单配置Api.getDetailById1']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 获取工单类型
         * @param {number} [manualCreationAllowed] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async queryTicketTypeList(manualCreationAllowed?: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListTicketTypeVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.queryTicketTypeList(manualCreationAllowed, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工单配置Api.queryTicketTypeList']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 更新工单配置
         * @param {TicketDefUpdateReqVO} ticketDefUpdateReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async update(ticketDefUpdateReqVO: TicketDefUpdateReqVO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.update(ticketDefUpdateReqVO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工单配置Api.update']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 工单配置Api - factory interface
 * @export
 */
export const 工单配置ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 工单配置ApiFp(configuration)
    return {
        /**
         * 
         * @summary 获取工单配置
         * @param {工单配置ApiGetDetailById1Request} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDetailById1(requestParameters: 工单配置ApiGetDetailById1Request, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOTicketDefDetailVO> {
            return localVarFp.getDetailById1(requestParameters.ticketDefId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取工单类型
         * @param {工单配置ApiQueryTicketTypeListRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryTicketTypeList(requestParameters: 工单配置ApiQueryTicketTypeListRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListTicketTypeVO> {
            return localVarFp.queryTicketTypeList(requestParameters.manualCreationAllowed, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 更新工单配置
         * @param {工单配置ApiUpdateRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        update(requestParameters: 工单配置ApiUpdateRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVO> {
            return localVarFp.update(requestParameters.ticketDefUpdateReqVO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for getDetailById1 operation in 工单配置Api.
 * @export
 * @interface 工单配置ApiGetDetailById1Request
 */
export interface 工单配置ApiGetDetailById1Request {
    /**
     * 
     * @type {number}
     * @memberof 工单配置ApiGetDetailById1
     */
    readonly ticketDefId: number
}

/**
 * Request parameters for queryTicketTypeList operation in 工单配置Api.
 * @export
 * @interface 工单配置ApiQueryTicketTypeListRequest
 */
export interface 工单配置ApiQueryTicketTypeListRequest {
    /**
     * 
     * @type {number}
     * @memberof 工单配置ApiQueryTicketTypeList
     */
    readonly manualCreationAllowed?: number
}

/**
 * Request parameters for update operation in 工单配置Api.
 * @export
 * @interface 工单配置ApiUpdateRequest
 */
export interface 工单配置ApiUpdateRequest {
    /**
     * 
     * @type {TicketDefUpdateReqVO}
     * @memberof 工单配置ApiUpdate
     */
    readonly ticketDefUpdateReqVO: TicketDefUpdateReqVO
}

/**
 * 工单配置Api - object-oriented interface
 * @export
 * @class 工单配置Api
 * @extends {BaseAPI}
 */
export class 工单配置Api extends BaseAPI {
    /**
     * 
     * @summary 获取工单配置
     * @param {工单配置ApiGetDetailById1Request} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工单配置Api
     */
    public getDetailById1(requestParameters: 工单配置ApiGetDetailById1Request, options?: RawAxiosRequestConfig) {
        return 工单配置ApiFp(this.configuration).getDetailById1(requestParameters.ticketDefId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 获取工单类型
     * @param {工单配置ApiQueryTicketTypeListRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工单配置Api
     */
    public queryTicketTypeList(requestParameters: 工单配置ApiQueryTicketTypeListRequest = {}, options?: RawAxiosRequestConfig) {
        return 工单配置ApiFp(this.configuration).queryTicketTypeList(requestParameters.manualCreationAllowed, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 更新工单配置
     * @param {工单配置ApiUpdateRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工单配置Api
     */
    public update(requestParameters: 工单配置ApiUpdateRequest, options?: RawAxiosRequestConfig) {
        return 工单配置ApiFp(this.configuration).update(requestParameters.ticketDefUpdateReqVO, options).then((request) => request(this.axios, this.basePath));
    }
}

