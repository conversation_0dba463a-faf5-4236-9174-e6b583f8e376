/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { DeployWorkflowDefinitionFileRequest } from '../models';
// @ts-ignore
import type { ResultVOString } from '../models';
/**
 * 工作流定义Api - axios parameter creator
 * @export
 */
export const 工作流定义ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 部署流程定义文件
         * @param {DeployWorkflowDefinitionFileRequest} [deployWorkflowDefinitionFileRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deployWorkflowDefinitionFile: async (deployWorkflowDefinitionFileRequest?: DeployWorkflowDefinitionFileRequest, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            const localVarPath = `/api/workflow-definitions/deploy`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(deployWorkflowDefinitionFileRequest, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 工作流定义Api - functional programming interface
 * @export
 */
export const 工作流定义ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 工作流定义ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 部署流程定义文件
         * @param {DeployWorkflowDefinitionFileRequest} [deployWorkflowDefinitionFileRequest] 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async deployWorkflowDefinitionFile(deployWorkflowDefinitionFileRequest?: DeployWorkflowDefinitionFileRequest, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOString>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.deployWorkflowDefinitionFile(deployWorkflowDefinitionFileRequest, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工作流定义Api.deployWorkflowDefinitionFile']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 工作流定义Api - factory interface
 * @export
 */
export const 工作流定义ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 工作流定义ApiFp(configuration)
    return {
        /**
         * 
         * @summary 部署流程定义文件
         * @param {工作流定义ApiDeployWorkflowDefinitionFileRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        deployWorkflowDefinitionFile(requestParameters: 工作流定义ApiDeployWorkflowDefinitionFileRequest = {}, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOString> {
            return localVarFp.deployWorkflowDefinitionFile(requestParameters.deployWorkflowDefinitionFileRequest, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for deployWorkflowDefinitionFile operation in 工作流定义Api.
 * @export
 * @interface 工作流定义ApiDeployWorkflowDefinitionFileRequest
 */
export interface 工作流定义ApiDeployWorkflowDefinitionFileRequest {
    /**
     * 
     * @type {DeployWorkflowDefinitionFileRequest}
     * @memberof 工作流定义ApiDeployWorkflowDefinitionFile
     */
    readonly deployWorkflowDefinitionFileRequest?: DeployWorkflowDefinitionFileRequest
}

/**
 * 工作流定义Api - object-oriented interface
 * @export
 * @class 工作流定义Api
 * @extends {BaseAPI}
 */
export class 工作流定义Api extends BaseAPI {
    /**
     * 
     * @summary 部署流程定义文件
     * @param {工作流定义ApiDeployWorkflowDefinitionFileRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工作流定义Api
     */
    public deployWorkflowDefinitionFile(requestParameters: 工作流定义ApiDeployWorkflowDefinitionFileRequest = {}, options?: RawAxiosRequestConfig) {
        return 工作流定义ApiFp(this.configuration).deployWorkflowDefinitionFile(requestParameters.deployWorkflowDefinitionFileRequest, options).then((request) => request(this.axios, this.basePath));
    }
}

