/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import type { Configuration } from '../configuration';
import type { AxiosPromise, AxiosInstance, RawAxiosRequestConfig } from 'axios';
import globalAxios from 'axios';
// Some imports not used depending on template conditions
// @ts-ignore
import { DUMMY_BASE_URL, assertParamExists, setApiKeyToObject, setBasicAuthToObject, setBearerAuthToObject, setOAuthToObject, setSearchParams, serializeDataIfNeeded, toPathString, createRequestFunction } from '../common';
// @ts-ignore
import { BASE_PATH, COLLECTION_FORMATS, type RequestArgs, BaseAPI, RequiredError, operationServerMap } from '../base';
// @ts-ignore
import type { ResultVOListTicketInstOperationLogVO } from '../models';
// @ts-ignore
import type { ResultVOListTicketInstStatusCountResultVO } from '../models';
// @ts-ignore
import type { ResultVOLong } from '../models';
// @ts-ignore
import type { ResultVOPageVOTicketInstBoardItemVO } from '../models';
// @ts-ignore
import type { ResultVOPageVOTicketInstListItemVO } from '../models';
// @ts-ignore
import type { ResultVOTicketInstDetailVO } from '../models';
// @ts-ignore
import type { ResultVOVoid } from '../models';
// @ts-ignore
import type { TicketInstBoardQueryReqVO } from '../models';
// @ts-ignore
import type { TicketInstProcessReqVO } from '../models';
// @ts-ignore
import type { TicketInstQueryReqVO } from '../models';
// @ts-ignore
import type { TicketInstStartReqVO } from '../models';
// @ts-ignore
import type { TicketInstStatusCountReqVO } from '../models';
// @ts-ignore
import type { TicketInstTransferReqVO } from '../models';
/**
 * 工单Api - axios parameter creator
 * @export
 */
export const 工单ApiAxiosParamCreator = function (configuration?: Configuration) {
    return {
        /**
         * 
         * @summary 按状态分组统计工单
         * @param {TicketInstStatusCountReqVO} ticketInstStatusCountReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        countByStatus: async (ticketInstStatusCountReqVO: TicketInstStatusCountReqVO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'ticketInstStatusCountReqVO' is not null or undefined
            assertParamExists('countByStatus', 'ticketInstStatusCountReqVO', ticketInstStatusCountReqVO)
            const localVarPath = `/api/ticket-instances/count-by-status`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(ticketInstStatusCountReqVO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 获取工单详情
         * @param {number} ticketInstId 工单实例ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDetailById: async (ticketInstId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'ticketInstId' is not null or undefined
            assertParamExists('getDetailById', 'ticketInstId', ticketInstId)
            const localVarPath = `/api/ticket-instances/{ticketInstId}`
                .replace(`{${"ticketInstId"}}`, encodeURIComponent(String(ticketInstId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 处理工单(接受/提交/审核)
         * @param {number} ticketInstId 
         * @param {TicketInstProcessReqVO} ticketInstProcessReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        process: async (ticketInstId: number, ticketInstProcessReqVO: TicketInstProcessReqVO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'ticketInstId' is not null or undefined
            assertParamExists('process', 'ticketInstId', ticketInstId)
            // verify required parameter 'ticketInstProcessReqVO' is not null or undefined
            assertParamExists('process', 'ticketInstProcessReqVO', ticketInstProcessReqVO)
            const localVarPath = `/api/ticket-instances/{ticketInstId}/process`
                .replace(`{${"ticketInstId"}}`, encodeURIComponent(String(ticketInstId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(ticketInstProcessReqVO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 查询工单列表
         * @param {TicketInstQueryReqVO} ticketInstQueryReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        query: async (ticketInstQueryReqVO: TicketInstQueryReqVO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'ticketInstQueryReqVO' is not null or undefined
            assertParamExists('query', 'ticketInstQueryReqVO', ticketInstQueryReqVO)
            const localVarPath = `/api/ticket-instances/query`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(ticketInstQueryReqVO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 查询工单看板信息
         * @param {TicketInstBoardQueryReqVO} ticketInstBoardQueryReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryBoardData: async (ticketInstBoardQueryReqVO: TicketInstBoardQueryReqVO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'ticketInstBoardQueryReqVO' is not null or undefined
            assertParamExists('queryBoardData', 'ticketInstBoardQueryReqVO', ticketInstBoardQueryReqVO)
            const localVarPath = `/api/ticket-instances/board-query`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(ticketInstBoardQueryReqVO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 查询工单的操作日志
         * @param {number} ticketInstId 工单实例ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryLogs: async (ticketInstId: number, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'ticketInstId' is not null or undefined
            assertParamExists('queryLogs', 'ticketInstId', ticketInstId)
            const localVarPath = `/api/ticket-instances/{ticketInstId}/logs`
                .replace(`{${"ticketInstId"}}`, encodeURIComponent(String(ticketInstId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'GET', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 启动工作流实例
         * @param {TicketInstStartReqVO} ticketInstStartReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        startWorkflowInstance: async (ticketInstStartReqVO: TicketInstStartReqVO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'ticketInstStartReqVO' is not null or undefined
            assertParamExists('startWorkflowInstance', 'ticketInstStartReqVO', ticketInstStartReqVO)
            const localVarPath = `/api/ticket-instances/start`;
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(ticketInstStartReqVO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
        /**
         * 
         * @summary 转移工单
         * @param {number} ticketInstId 
         * @param {TicketInstTransferReqVO} ticketInstTransferReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        transfer: async (ticketInstId: number, ticketInstTransferReqVO: TicketInstTransferReqVO, options: RawAxiosRequestConfig = {}): Promise<RequestArgs> => {
            // verify required parameter 'ticketInstId' is not null or undefined
            assertParamExists('transfer', 'ticketInstId', ticketInstId)
            // verify required parameter 'ticketInstTransferReqVO' is not null or undefined
            assertParamExists('transfer', 'ticketInstTransferReqVO', ticketInstTransferReqVO)
            const localVarPath = `/api/ticket-instances/{ticketInstId}/transfer`
                .replace(`{${"ticketInstId"}}`, encodeURIComponent(String(ticketInstId)));
            // use dummy base URL string because the URL constructor only accepts absolute URLs.
            const localVarUrlObj = new URL(localVarPath, DUMMY_BASE_URL);
            let baseOptions;
            if (configuration) {
                baseOptions = configuration.baseOptions;
            }

            const localVarRequestOptions = { method: 'POST', ...baseOptions, ...options};
            const localVarHeaderParameter = {} as any;
            const localVarQueryParameter = {} as any;


    
            localVarHeaderParameter['Content-Type'] = 'application/json';

            setSearchParams(localVarUrlObj, localVarQueryParameter);
            let headersFromBaseOptions = baseOptions && baseOptions.headers ? baseOptions.headers : {};
            localVarRequestOptions.headers = {...localVarHeaderParameter, ...headersFromBaseOptions, ...options.headers};
            localVarRequestOptions.data = serializeDataIfNeeded(ticketInstTransferReqVO, localVarRequestOptions, configuration)

            return {
                url: toPathString(localVarUrlObj),
                options: localVarRequestOptions,
            };
        },
    }
};

/**
 * 工单Api - functional programming interface
 * @export
 */
export const 工单ApiFp = function(configuration?: Configuration) {
    const localVarAxiosParamCreator = 工单ApiAxiosParamCreator(configuration)
    return {
        /**
         * 
         * @summary 按状态分组统计工单
         * @param {TicketInstStatusCountReqVO} ticketInstStatusCountReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async countByStatus(ticketInstStatusCountReqVO: TicketInstStatusCountReqVO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListTicketInstStatusCountResultVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.countByStatus(ticketInstStatusCountReqVO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工单Api.countByStatus']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 获取工单详情
         * @param {number} ticketInstId 工单实例ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async getDetailById(ticketInstId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOTicketInstDetailVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.getDetailById(ticketInstId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工单Api.getDetailById']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 处理工单(接受/提交/审核)
         * @param {number} ticketInstId 
         * @param {TicketInstProcessReqVO} ticketInstProcessReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async process(ticketInstId: number, ticketInstProcessReqVO: TicketInstProcessReqVO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.process(ticketInstId, ticketInstProcessReqVO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工单Api.process']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 查询工单列表
         * @param {TicketInstQueryReqVO} ticketInstQueryReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async query(ticketInstQueryReqVO: TicketInstQueryReqVO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOTicketInstListItemVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.query(ticketInstQueryReqVO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工单Api.query']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 查询工单看板信息
         * @param {TicketInstBoardQueryReqVO} ticketInstBoardQueryReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async queryBoardData(ticketInstBoardQueryReqVO: TicketInstBoardQueryReqVO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOPageVOTicketInstBoardItemVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.queryBoardData(ticketInstBoardQueryReqVO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工单Api.queryBoardData']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 查询工单的操作日志
         * @param {number} ticketInstId 工单实例ID
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async queryLogs(ticketInstId: number, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOListTicketInstOperationLogVO>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.queryLogs(ticketInstId, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工单Api.queryLogs']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 启动工作流实例
         * @param {TicketInstStartReqVO} ticketInstStartReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async startWorkflowInstance(ticketInstStartReqVO: TicketInstStartReqVO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOLong>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.startWorkflowInstance(ticketInstStartReqVO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工单Api.startWorkflowInstance']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
        /**
         * 
         * @summary 转移工单
         * @param {number} ticketInstId 
         * @param {TicketInstTransferReqVO} ticketInstTransferReqVO 
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        async transfer(ticketInstId: number, ticketInstTransferReqVO: TicketInstTransferReqVO, options?: RawAxiosRequestConfig): Promise<(axios?: AxiosInstance, basePath?: string) => AxiosPromise<ResultVOVoid>> {
            const localVarAxiosArgs = await localVarAxiosParamCreator.transfer(ticketInstId, ticketInstTransferReqVO, options);
            const localVarOperationServerIndex = configuration?.serverIndex ?? 0;
            const localVarOperationServerBasePath = operationServerMap['工单Api.transfer']?.[localVarOperationServerIndex]?.url;
            return (axios, basePath) => createRequestFunction(localVarAxiosArgs, globalAxios, BASE_PATH, configuration)(axios, localVarOperationServerBasePath || basePath);
        },
    }
};

/**
 * 工单Api - factory interface
 * @export
 */
export const 工单ApiFactory = function (configuration?: Configuration, basePath?: string, axios?: AxiosInstance) {
    const localVarFp = 工单ApiFp(configuration)
    return {
        /**
         * 
         * @summary 按状态分组统计工单
         * @param {工单ApiCountByStatusRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        countByStatus(requestParameters: 工单ApiCountByStatusRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListTicketInstStatusCountResultVO> {
            return localVarFp.countByStatus(requestParameters.ticketInstStatusCountReqVO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 获取工单详情
         * @param {工单ApiGetDetailByIdRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        getDetailById(requestParameters: 工单ApiGetDetailByIdRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOTicketInstDetailVO> {
            return localVarFp.getDetailById(requestParameters.ticketInstId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 处理工单(接受/提交/审核)
         * @param {工单ApiProcessRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        process(requestParameters: 工单ApiProcessRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.process(requestParameters.ticketInstId, requestParameters.ticketInstProcessReqVO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 查询工单列表
         * @param {工单ApiQueryRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        query(requestParameters: 工单ApiQueryRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOTicketInstListItemVO> {
            return localVarFp.query(requestParameters.ticketInstQueryReqVO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 查询工单看板信息
         * @param {工单ApiQueryBoardDataRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryBoardData(requestParameters: 工单ApiQueryBoardDataRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOPageVOTicketInstBoardItemVO> {
            return localVarFp.queryBoardData(requestParameters.ticketInstBoardQueryReqVO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 查询工单的操作日志
         * @param {工单ApiQueryLogsRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        queryLogs(requestParameters: 工单ApiQueryLogsRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOListTicketInstOperationLogVO> {
            return localVarFp.queryLogs(requestParameters.ticketInstId, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 启动工作流实例
         * @param {工单ApiStartWorkflowInstanceRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        startWorkflowInstance(requestParameters: 工单ApiStartWorkflowInstanceRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOLong> {
            return localVarFp.startWorkflowInstance(requestParameters.ticketInstStartReqVO, options).then((request) => request(axios, basePath));
        },
        /**
         * 
         * @summary 转移工单
         * @param {工单ApiTransferRequest} requestParameters Request parameters.
         * @param {*} [options] Override http request option.
         * @throws {RequiredError}
         */
        transfer(requestParameters: 工单ApiTransferRequest, options?: RawAxiosRequestConfig): AxiosPromise<ResultVOVoid> {
            return localVarFp.transfer(requestParameters.ticketInstId, requestParameters.ticketInstTransferReqVO, options).then((request) => request(axios, basePath));
        },
    };
};

/**
 * Request parameters for countByStatus operation in 工单Api.
 * @export
 * @interface 工单ApiCountByStatusRequest
 */
export interface 工单ApiCountByStatusRequest {
    /**
     * 
     * @type {TicketInstStatusCountReqVO}
     * @memberof 工单ApiCountByStatus
     */
    readonly ticketInstStatusCountReqVO: TicketInstStatusCountReqVO
}

/**
 * Request parameters for getDetailById operation in 工单Api.
 * @export
 * @interface 工单ApiGetDetailByIdRequest
 */
export interface 工单ApiGetDetailByIdRequest {
    /**
     * 工单实例ID
     * @type {number}
     * @memberof 工单ApiGetDetailById
     */
    readonly ticketInstId: number
}

/**
 * Request parameters for process operation in 工单Api.
 * @export
 * @interface 工单ApiProcessRequest
 */
export interface 工单ApiProcessRequest {
    /**
     * 
     * @type {number}
     * @memberof 工单ApiProcess
     */
    readonly ticketInstId: number

    /**
     * 
     * @type {TicketInstProcessReqVO}
     * @memberof 工单ApiProcess
     */
    readonly ticketInstProcessReqVO: TicketInstProcessReqVO
}

/**
 * Request parameters for query operation in 工单Api.
 * @export
 * @interface 工单ApiQueryRequest
 */
export interface 工单ApiQueryRequest {
    /**
     * 
     * @type {TicketInstQueryReqVO}
     * @memberof 工单ApiQuery
     */
    readonly ticketInstQueryReqVO: TicketInstQueryReqVO
}

/**
 * Request parameters for queryBoardData operation in 工单Api.
 * @export
 * @interface 工单ApiQueryBoardDataRequest
 */
export interface 工单ApiQueryBoardDataRequest {
    /**
     * 
     * @type {TicketInstBoardQueryReqVO}
     * @memberof 工单ApiQueryBoardData
     */
    readonly ticketInstBoardQueryReqVO: TicketInstBoardQueryReqVO
}

/**
 * Request parameters for queryLogs operation in 工单Api.
 * @export
 * @interface 工单ApiQueryLogsRequest
 */
export interface 工单ApiQueryLogsRequest {
    /**
     * 工单实例ID
     * @type {number}
     * @memberof 工单ApiQueryLogs
     */
    readonly ticketInstId: number
}

/**
 * Request parameters for startWorkflowInstance operation in 工单Api.
 * @export
 * @interface 工单ApiStartWorkflowInstanceRequest
 */
export interface 工单ApiStartWorkflowInstanceRequest {
    /**
     * 
     * @type {TicketInstStartReqVO}
     * @memberof 工单ApiStartWorkflowInstance
     */
    readonly ticketInstStartReqVO: TicketInstStartReqVO
}

/**
 * Request parameters for transfer operation in 工单Api.
 * @export
 * @interface 工单ApiTransferRequest
 */
export interface 工单ApiTransferRequest {
    /**
     * 
     * @type {number}
     * @memberof 工单ApiTransfer
     */
    readonly ticketInstId: number

    /**
     * 
     * @type {TicketInstTransferReqVO}
     * @memberof 工单ApiTransfer
     */
    readonly ticketInstTransferReqVO: TicketInstTransferReqVO
}

/**
 * 工单Api - object-oriented interface
 * @export
 * @class 工单Api
 * @extends {BaseAPI}
 */
export class 工单Api extends BaseAPI {
    /**
     * 
     * @summary 按状态分组统计工单
     * @param {工单ApiCountByStatusRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工单Api
     */
    public countByStatus(requestParameters: 工单ApiCountByStatusRequest, options?: RawAxiosRequestConfig) {
        return 工单ApiFp(this.configuration).countByStatus(requestParameters.ticketInstStatusCountReqVO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 获取工单详情
     * @param {工单ApiGetDetailByIdRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工单Api
     */
    public getDetailById(requestParameters: 工单ApiGetDetailByIdRequest, options?: RawAxiosRequestConfig) {
        return 工单ApiFp(this.configuration).getDetailById(requestParameters.ticketInstId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 处理工单(接受/提交/审核)
     * @param {工单ApiProcessRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工单Api
     */
    public process(requestParameters: 工单ApiProcessRequest, options?: RawAxiosRequestConfig) {
        return 工单ApiFp(this.configuration).process(requestParameters.ticketInstId, requestParameters.ticketInstProcessReqVO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 查询工单列表
     * @param {工单ApiQueryRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工单Api
     */
    public query(requestParameters: 工单ApiQueryRequest, options?: RawAxiosRequestConfig) {
        return 工单ApiFp(this.configuration).query(requestParameters.ticketInstQueryReqVO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 查询工单看板信息
     * @param {工单ApiQueryBoardDataRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工单Api
     */
    public queryBoardData(requestParameters: 工单ApiQueryBoardDataRequest, options?: RawAxiosRequestConfig) {
        return 工单ApiFp(this.configuration).queryBoardData(requestParameters.ticketInstBoardQueryReqVO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 查询工单的操作日志
     * @param {工单ApiQueryLogsRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工单Api
     */
    public queryLogs(requestParameters: 工单ApiQueryLogsRequest, options?: RawAxiosRequestConfig) {
        return 工单ApiFp(this.configuration).queryLogs(requestParameters.ticketInstId, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 启动工作流实例
     * @param {工单ApiStartWorkflowInstanceRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工单Api
     */
    public startWorkflowInstance(requestParameters: 工单ApiStartWorkflowInstanceRequest, options?: RawAxiosRequestConfig) {
        return 工单ApiFp(this.configuration).startWorkflowInstance(requestParameters.ticketInstStartReqVO, options).then((request) => request(this.axios, this.basePath));
    }

    /**
     * 
     * @summary 转移工单
     * @param {工单ApiTransferRequest} requestParameters Request parameters.
     * @param {*} [options] Override http request option.
     * @throws {RequiredError}
     * @memberof 工单Api
     */
    public transfer(requestParameters: 工单ApiTransferRequest, options?: RawAxiosRequestConfig) {
        return 工单ApiFp(this.configuration).transfer(requestParameters.ticketInstId, requestParameters.ticketInstTransferReqVO, options).then((request) => request(this.axios, this.basePath));
    }
}

