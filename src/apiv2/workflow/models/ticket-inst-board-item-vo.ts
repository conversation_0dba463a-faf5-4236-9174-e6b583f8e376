/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { TicketInstOperationLogVO } from './ticket-inst-operation-log-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { UserSimpleInfoVO } from './user-simple-info-vo';

/**
 * 工单看板信息
 * @export
 * @interface TicketInstBoardItemVO
 */
export interface TicketInstBoardItemVO {
    /**
     * 工单ID
     * @type {number}
     * @memberof TicketInstBoardItemVO
     */
    'id'?: number;
    /**
     * 工单关联的对象ID
     * @type {string}
     * @memberof TicketInstBoardItemVO
     */
    'relevantObjId'?: string;
    /**
     * 工单类型ID
     * @type {number}
     * @memberof TicketInstBoardItemVO
     */
    'ticketDefId'?: number;
    /**
     * 工单类型code
     * @type {string}
     * @memberof TicketInstBoardItemVO
     */
    'ticketDefCode'?: string;
    /**
     * 工单类型名称
     * @type {string}
     * @memberof TicketInstBoardItemVO
     */
    'ticketDefName'?: string;
    /**
     * 工单关联的工作流实例ID
     * @type {number}
     * @memberof TicketInstBoardItemVO
     */
    'wfInstId'?: number;
    /**
     * 工单关联的表单实例ID
     * @type {number}
     * @memberof TicketInstBoardItemVO
     */
    'formInstId'?: number;
    /**
     * 优先级，值越大优先级越高
     * @type {number}
     * @memberof TicketInstBoardItemVO
     */
    'priority'?: number;
    /**
     * 
     * @type {UserSimpleInfoVO}
     * @memberof TicketInstBoardItemVO
     */
    'creator'?: UserSimpleInfoVO;
    /**
     * 工单状态，0:PROCESSING-进行中，1:COMPLETED-已关闭
     * @type {number}
     * @memberof TicketInstBoardItemVO
     */
    'status'?: TicketInstBoardItemVOStatusEnum;
    /**
     * 是否已接受，已有人认领或者审核过工单就算已接受
     * @type {boolean}
     * @memberof TicketInstBoardItemVO
     */
    'accepted'?: boolean;
    /**
     * 创建时间
     * @type {string}
     * @memberof TicketInstBoardItemVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof TicketInstBoardItemVO
     */
    'updateTime'?: string;
    /**
     * 当前节点ID
     * @type {string}
     * @memberof TicketInstBoardItemVO
     */
    'currentNodeId'?: string;
    /**
     * 当前节点名称
     * @type {string}
     * @memberof TicketInstBoardItemVO
     */
    'currentNodeName'?: string;
    /**
     * 当前节点名称
     * @type {number}
     * @memberof TicketInstBoardItemVO
     */
    'currentNodeStatus'?: TicketInstBoardItemVOCurrentNodeStatusEnum;
    /**
     * 
     * @type {UserSimpleInfoVO}
     * @memberof TicketInstBoardItemVO
     */
    'currentNodeProcessor'?: UserSimpleInfoVO;
    /**
     * 表单字段值
     * @type {{ [key: string]: object; }}
     * @memberof TicketInstBoardItemVO
     */
    'formFieldValues'?: { [key: string]: object; };
    /**
     * 
     * @type {TicketInstOperationLogVO}
     * @memberof TicketInstBoardItemVO
     */
    'lastOperationLog'?: TicketInstOperationLogVO;
}

export const TicketInstBoardItemVOStatusEnum = {
    PROCESSING: 0,
    COMPLETED: 1
} as const;

export type TicketInstBoardItemVOStatusEnum = typeof TicketInstBoardItemVOStatusEnum[keyof typeof TicketInstBoardItemVOStatusEnum];
export const TicketInstBoardItemVOCurrentNodeStatusEnum = {
    TO_BE_ACCEPTED: 0,
    ACCEPTED: 1,
    PROCESSED: 2,
    TO_BE_APPROVED: 3,
    APPROVED: 4,
    REJECTED: 5
} as const;

export type TicketInstBoardItemVOCurrentNodeStatusEnum = typeof TicketInstBoardItemVOCurrentNodeStatusEnum[keyof typeof TicketInstBoardItemVOCurrentNodeStatusEnum];


