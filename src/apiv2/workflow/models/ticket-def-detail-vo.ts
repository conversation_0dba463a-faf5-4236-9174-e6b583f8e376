/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { FormDefVO } from './form-def-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { FormFieldAuthVO } from './form-field-auth-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { WorkflowDefinitionVO } from './workflow-definition-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { WorkflowManualNodeDefinitionVO } from './workflow-manual-node-definition-vo';

/**
 * 工单配置详情
 * @export
 * @interface TicketDefDetailVO
 */
export interface TicketDefDetailVO {
    /**
     * 工单配置ID
     * @type {number}
     * @memberof TicketDefDetailVO
     */
    'id'?: number;
    /**
     * 工单名称
     * @type {string}
     * @memberof TicketDefDetailVO
     */
    'name'?: string;
    /**
     * 工单编码
     * @type {string}
     * @memberof TicketDefDetailVO
     */
    'code'?: string;
    /**
     * 工单描述
     * @type {string}
     * @memberof TicketDefDetailVO
     */
    'desc'?: string;
    /**
     * 
     * @type {FormDefVO}
     * @memberof TicketDefDetailVO
     */
    'ticketRelatedFormDef'?: FormDefVO;
    /**
     * 针对工作流节点设置的表单字段权限
     * @type {Array<FormFieldAuthVO>}
     * @memberof TicketDefDetailVO
     */
    'formFieldAuthList'?: Array<FormFieldAuthVO>;
    /**
     * 
     * @type {WorkflowDefinitionVO}
     * @memberof TicketDefDetailVO
     */
    'wfDef'?: WorkflowDefinitionVO;
    /**
     * 工单关联的工作流人工节点配置
     * @type {Array<WorkflowManualNodeDefinitionVO>}
     * @memberof TicketDefDetailVO
     */
    'wfManualNodeDefList'?: Array<WorkflowManualNodeDefinitionVO>;
}

