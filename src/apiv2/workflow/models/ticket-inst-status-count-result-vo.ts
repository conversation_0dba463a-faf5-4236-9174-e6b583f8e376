/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 工单按状态统计结果
 * @export
 * @interface TicketInstStatusCountResultVO
 */
export interface TicketInstStatusCountResultVO {
    /**
     * 工单相关的用户ID
     * @type {number}
     * @memberof TicketInstStatusCountResultVO
     */
    'userId'?: number;
    /**
     * 用户视角的工单状态分组统计结果，key为状态（TO_BE_PROCESSED-工单等待当前用户处理，PROCESSED_BEFORE-工单曾被当前用户处理过，COMPLETED-工单已关闭），value为计数值
     * @type {{ [key: string]: number; }}
     * @memberof TicketInstStatusCountResultVO
     */
    'count'?: { [key: string]: number; };
}

