/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 工单关联的工作流配置信息
 * @export
 * @interface WorkflowDefinitionVO
 */
export interface WorkflowDefinitionVO {
    /**
     * 工作流配置ID
     * @type {number}
     * @memberof WorkflowDefinitionVO
     */
    'id'?: number;
    /**
     * 工作流名称
     * @type {string}
     * @memberof WorkflowDefinitionVO
     */
    'name'?: string;
    /**
     * 编码
     * @type {string}
     * @memberof WorkflowDefinitionVO
     */
    'code'?: string;
    /**
     * 描述
     * @type {string}
     * @memberof WorkflowDefinitionVO
     */
    'desc'?: string;
    /**
     * 关联的bpmn流程文件key
     * @type {string}
     * @memberof WorkflowDefinitionVO
     */
    'defKey'?: string;
    /**
     * 创建时间
     * @type {string}
     * @memberof WorkflowDefinitionVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof WorkflowDefinitionVO
     */
    'updateTime'?: string;
}

