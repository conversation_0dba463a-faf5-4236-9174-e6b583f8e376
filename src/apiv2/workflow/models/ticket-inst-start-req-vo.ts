/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 创建工作流实例请求
 * @export
 * @interface TicketInstStartReqVO
 */
export interface TicketInstStartReqVO {
    /**
     * 工单编码
     * @type {string}
     * @memberof TicketInstStartReqVO
     */
    'ticketDefCode': string;
    /**
     * 工作流实例名称
     * @type {string}
     * @memberof TicketInstStartReqVO
     */
    'instName'?: string;
    /**
     * 工作流实例的上下文变量
     * @type {{ [key: string]: object; }}
     * @memberof TicketInstStartReqVO
     */
    'variables'?: { [key: string]: object; };
    /**
     * 和工单一起提交的表单数据
     * @type {string}
     * @memberof TicketInstStartReqVO
     */
    'formData': string;
    /**
     * 工单关联的对象ID
     * @type {string}
     * @memberof TicketInstStartReqVO
     */
    'relevantObjId'?: string;
    /**
     * 优先级，数值越大优先级越高
     * @type {number}
     * @memberof TicketInstStartReqVO
     */
    'priority'?: number;
}

