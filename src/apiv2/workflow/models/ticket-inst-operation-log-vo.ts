/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { UserSimpleInfoVO } from './user-simple-info-vo';

/**
 * 工单操作日志
 * @export
 * @interface TicketInstOperationLogVO
 */
export interface TicketInstOperationLogVO {
    /**
     * 日志ID
     * @type {number}
     * @memberof TicketInstOperationLogVO
     */
    'id'?: number;
    /**
     * 操作关联的工作流节点ID
     * @type {string}
     * @memberof TicketInstOperationLogVO
     */
    'nodeId'?: string;
    /**
     * 操作关联的工作流节点名称
     * @type {string}
     * @memberof TicketInstOperationLogVO
     */
    'nodeName'?: string;
    /**
     * 操作类型，CREATE-创建工单，ACCEPT-接受工单，SUBMIT-提交工单，APPROVE-审批工单，TRANSFER-转交工单
     * @type {string}
     * @memberof TicketInstOperationLogVO
     */
    'opType'?: string;
    /**
     * 操作类型，APPROVED-审核通过，REJECTED-审核不通过
     * @type {string}
     * @memberof TicketInstOperationLogVO
     */
    'opResult'?: string;
    /**
     * 操作说明
     * @type {string}
     * @memberof TicketInstOperationLogVO
     */
    'opDesc'?: string;
    /**
     * 
     * @type {UserSimpleInfoVO}
     * @memberof TicketInstOperationLogVO
     */
    'operator'?: UserSimpleInfoVO;
    /**
     * 创建时间
     * @type {string}
     * @memberof TicketInstOperationLogVO
     */
    'createTime'?: string;
}

