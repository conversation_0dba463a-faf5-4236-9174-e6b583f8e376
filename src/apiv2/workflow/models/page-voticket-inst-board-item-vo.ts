/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { TicketInstBoardItemVO } from './ticket-inst-board-item-vo';

/**
 * API分页结果
 * @export
 * @interface PageVOTicketInstBoardItemVO
 */
export interface PageVOTicketInstBoardItemVO {
    /**
     * 当前页码
     * @type {number}
     * @memberof PageVOTicketInstBoardItemVO
     */
    'pageNum'?: number;
    /**
     * 每页大小
     * @type {number}
     * @memberof PageVOTicketInstBoardItemVO
     */
    'pageSize'?: number;
    /**
     * 总记录数
     * @type {number}
     * @memberof PageVOTicketInstBoardItemVO
     */
    'total'?: number;
    /**
     * 总页数
     * @type {number}
     * @memberof PageVOTicketInstBoardItemVO
     */
    'pages'?: number;
    /**
     * 当前页结果集
     * @type {Array<TicketInstBoardItemVO>}
     * @memberof PageVOTicketInstBoardItemVO
     */
    'records'?: Array<TicketInstBoardItemVO>;
}

