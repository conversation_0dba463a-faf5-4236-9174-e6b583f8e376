/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 工单处理请求
 * @export
 * @interface TicketInstProcessReqVO
 */
export interface TicketInstProcessReqVO {
    /**
     * 操作类型，1:ACCEPT-接受工单，2:SUBMIT-提交工单，3:APPROVE-审批工单，4:TRANSFER-转移
     * @type {number}
     * @memberof TicketInstProcessReqVO
     */
    'opType': TicketInstProcessReqVOOpTypeEnum;
    /**
     * 操作结果。类型为APPROVE时需要设置，0:APPROVED-审核通过，1:REJECTED-审核拒绝
     * @type {number}
     * @memberof TicketInstProcessReqVO
     */
    'opResult'?: TicketInstProcessReqVOOpResultEnum;
    /**
     * 操作说明
     * @type {string}
     * @memberof TicketInstProcessReqVO
     */
    'opDesc'?: string;
    /**
     * 提交的表单数据
     * @type {string}
     * @memberof TicketInstProcessReqVO
     */
    'formData'?: string;
}

export const TicketInstProcessReqVOOpTypeEnum = {
    ACCEPT: 1,
    SUBMIT: 2,
    APPROVE: 3,
    TRANSFER: 4
} as const;

export type TicketInstProcessReqVOOpTypeEnum = typeof TicketInstProcessReqVOOpTypeEnum[keyof typeof TicketInstProcessReqVOOpTypeEnum];
export const TicketInstProcessReqVOOpResultEnum = {
    APPROVED: 0,
    REJECTED: 1
} as const;

export type TicketInstProcessReqVOOpResultEnum = typeof TicketInstProcessReqVOOpResultEnum[keyof typeof TicketInstProcessReqVOOpResultEnum];


