/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 工单列表查询请求
 * @export
 * @interface TicketInstQueryReqVO
 */
export interface TicketInstQueryReqVO {
    /**
     * 当前页码
     * @type {number}
     * @memberof TicketInstQueryReqVO
     */
    'pageNum'?: number;
    /**
     * 分页大小
     * @type {number}
     * @memberof TicketInstQueryReqVO
     */
    'pageSize'?: number;
    /**
     * 排序规则
     * @type {string}
     * @memberof TicketInstQueryReqVO
     */
    'orderBy'?: string;
    /**
     * 工单类型ID
     * @type {number}
     * @memberof TicketInstQueryReqVO
     */
    'ticketDefId'?: number;
    /**
     * 工单相关的用户ID
     * @type {number}
     * @memberof TicketInstQueryReqVO
     */
    'userId'?: number;
    /**
     * 工单关联的对象ID
     * @type {Array<string>}
     * @memberof TicketInstQueryReqVO
     */
    'relevantObjIds'?: Array<string>;
    /**
     * 工单ID
     * @type {number}
     * @memberof TicketInstQueryReqVO
     */
    'ticketInstId'?: number;
    /**
     * 优先级，值越大优先级越高
     * @type {number}
     * @memberof TicketInstQueryReqVO
     */
    'priority'?: number;
    /**
     * 待过滤的工单状态列表，过滤出符合此状态的工单，0:PROCESSING-进行中，1:COMPLETED-已完成
     * @type {Array<number>}
     * @memberof TicketInstQueryReqVO
     */
    'includedTicketStatusList'?: Array<TicketInstQueryReqVOIncludedTicketStatusListEnum>;
    /**
     * 待过滤的用户视角工单处理状态。当传入了userId参数时，0:TO_BE_PROCESSED-工单等待当前用户处理，1:PROCESSED_BEFORE-工单曾被当前用户处理过
     * @type {Array<number>}
     * @memberof TicketInstQueryReqVO
     */
    'includedTicketUserStatusList'?: Array<TicketInstQueryReqVOIncludedTicketUserStatusListEnum>;
    /**
     * 搜索关键词
     * @type {string}
     * @memberof TicketInstQueryReqVO
     */
    'keyword'?: string;
}

export const TicketInstQueryReqVOIncludedTicketStatusListEnum = {
    PROCESSING: 0,
    COMPLETED: 1
} as const;

export type TicketInstQueryReqVOIncludedTicketStatusListEnum = typeof TicketInstQueryReqVOIncludedTicketStatusListEnum[keyof typeof TicketInstQueryReqVOIncludedTicketStatusListEnum];
export const TicketInstQueryReqVOIncludedTicketUserStatusListEnum = {
    TO_BE_PROCESSED: 0,
    PROCESSED_BEFORE: 1,
    COMPLETED: 2
} as const;

export type TicketInstQueryReqVOIncludedTicketUserStatusListEnum = typeof TicketInstQueryReqVOIncludedTicketUserStatusListEnum[keyof typeof TicketInstQueryReqVOIncludedTicketUserStatusListEnum];


