/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { TicketInstListItemVO } from './ticket-inst-list-item-vo';

/**
 * API分页结果
 * @export
 * @interface PageVOTicketInstListItemVO
 */
export interface PageVOTicketInstListItemVO {
    /**
     * 当前页码
     * @type {number}
     * @memberof PageVOTicketInstListItemVO
     */
    'pageNum'?: number;
    /**
     * 每页大小
     * @type {number}
     * @memberof PageVOTicketInstListItemVO
     */
    'pageSize'?: number;
    /**
     * 总记录数
     * @type {number}
     * @memberof PageVOTicketInstListItemVO
     */
    'total'?: number;
    /**
     * 总页数
     * @type {number}
     * @memberof PageVOTicketInstListItemVO
     */
    'pages'?: number;
    /**
     * 当前页结果集
     * @type {Array<TicketInstListItemVO>}
     * @memberof PageVOTicketInstListItemVO
     */
    'records'?: Array<TicketInstListItemVO>;
}

