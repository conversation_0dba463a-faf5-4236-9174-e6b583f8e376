/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 工单看板查询请求
 * @export
 * @interface TicketInstBoardQueryReqVO
 */
export interface TicketInstBoardQueryReqVO {
    /**
     * 当前页码
     * @type {number}
     * @memberof TicketInstBoardQueryReqVO
     */
    'pageNum'?: number;
    /**
     * 分页大小
     * @type {number}
     * @memberof TicketInstBoardQueryReqVO
     */
    'pageSize'?: number;
    /**
     * 排序规则
     * @type {string}
     * @memberof TicketInstBoardQueryReqVO
     */
    'orderBy'?: string;
    /**
     * 工单类型ID
     * @type {number}
     * @memberof TicketInstBoardQueryReqVO
     */
    'ticketDefId'?: number;
    /**
     * 工单创建人ID
     * @type {number}
     * @memberof TicketInstBoardQueryReqVO
     */
    'creator'?: number;
    /**
     * 工单关联的对象ID
     * @type {Array<string>}
     * @memberof TicketInstBoardQueryReqVO
     */
    'relevantObjIds'?: Array<string>;
    /**
     * 工单ID
     * @type {number}
     * @memberof TicketInstBoardQueryReqVO
     */
    'ticketInstId'?: number;
    /**
     * 优先级，值越大优先级越高
     * @type {number}
     * @memberof TicketInstBoardQueryReqVO
     */
    'priority'?: number;
    /**
     * 待过滤的工单状态列表，过滤出符合此状态的工单，0:PROCESSING-进行中，1:COMPLETED-已完成
     * @type {Array<number>}
     * @memberof TicketInstBoardQueryReqVO
     */
    'includedTicketStatusList'?: Array<TicketInstBoardQueryReqVOIncludedTicketStatusListEnum>;
    /**
     * 是否已接受，已有人认领或者审核过工单就算已接受
     * @type {boolean}
     * @memberof TicketInstBoardQueryReqVO
     */
    'accepted'?: boolean;
    /**
     * 搜索关键词
     * @type {string}
     * @memberof TicketInstBoardQueryReqVO
     */
    'keyword'?: string;
    /**
     * 按表单字段值过滤条件
     * @type {{ [key: string]: object; }}
     * @memberof TicketInstBoardQueryReqVO
     */
    'formFieldValues'?: { [key: string]: object; };
}

export const TicketInstBoardQueryReqVOIncludedTicketStatusListEnum = {
    PROCESSING: 0,
    COMPLETED: 1
} as const;

export type TicketInstBoardQueryReqVOIncludedTicketStatusListEnum = typeof TicketInstBoardQueryReqVOIncludedTicketStatusListEnum[keyof typeof TicketInstBoardQueryReqVOIncludedTicketStatusListEnum];


