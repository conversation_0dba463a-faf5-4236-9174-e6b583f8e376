/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 表单字段授权配置
 * @export
 * @interface FormFieldAuthVO
 */
export interface FormFieldAuthVO {
    /**
     * 表单字段授权配置ID
     * @type {number}
     * @memberof FormFieldAuthVO
     */
    'id'?: number;
    /**
     * 表单配置ID
     * @type {number}
     * @memberof FormFieldAuthVO
     */
    'formDefId'?: number;
    /**
     * 工作流节点ID
     * @type {string}
     * @memberof FormFieldAuthVO
     */
    'wfNodeId'?: string;
    /**
     * 该节点的字段授权配置
     * @type {string}
     * @memberof FormFieldAuthVO
     */
    'formFieldAuthInfo'?: string;
}

