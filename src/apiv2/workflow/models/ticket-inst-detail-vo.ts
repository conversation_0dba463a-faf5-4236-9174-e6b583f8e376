/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { UserSimpleInfoVO } from './user-simple-info-vo';

/**
 * 工单详情
 * @export
 * @interface TicketInstDetailVO
 */
export interface TicketInstDetailVO {
    /**
     * 工单ID
     * @type {number}
     * @memberof TicketInstDetailVO
     */
    'ticketInstId'?: number;
    /**
     * 工单关联的对象ID
     * @type {string}
     * @memberof TicketInstDetailVO
     */
    'relevantObjId'?: string;
    /**
     * 工单定义ID
     * @type {number}
     * @memberof TicketInstDetailVO
     */
    'ticketDefId'?: number;
    /**
     * 工单关联的工作流实例ID
     * @type {number}
     * @memberof TicketInstDetailVO
     */
    'wfInstId'?: number;
    /**
     * 工单关联的表单实例ID
     * @type {number}
     * @memberof TicketInstDetailVO
     */
    'formInstId'?: number;
    /**
     * 优先级，值越大优先级越高
     * @type {number}
     * @memberof TicketInstDetailVO
     */
    'priority'?: number;
    /**
     * 
     * @type {UserSimpleInfoVO}
     * @memberof TicketInstDetailVO
     */
    'creator'?: UserSimpleInfoVO;
    /**
     * 创建时间
     * @type {string}
     * @memberof TicketInstDetailVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof TicketInstDetailVO
     */
    'updateTime'?: string;
    /**
     * 工单状态，0:PROCESSING-进行中，1:COMPLETED-已完成
     * @type {number}
     * @memberof TicketInstDetailVO
     */
    'ticketStatus'?: TicketInstDetailVOTicketStatusEnum;
    /**
     * 当前节点ID
     * @type {string}
     * @memberof TicketInstDetailVO
     */
    'currentNodeId'?: string;
    /**
     * 当前节点类型，0:SUBMIT-提交，1:APPROVE-审核
     * @type {number}
     * @memberof TicketInstDetailVO
     */
    'currentNodeType'?: TicketInstDetailVOCurrentNodeTypeEnum;
    /**
     * 当前节点名称
     * @type {string}
     * @memberof TicketInstDetailVO
     */
    'currentNodeName'?: string;
    /**
     * 当前节点处理状态
     * @type {number}
     * @memberof TicketInstDetailVO
     */
    'currentNodeStatus'?: TicketInstDetailVOCurrentNodeStatusEnum;
    /**
     * 当前节点处理人
     * @type {Array<UserSimpleInfoVO>}
     * @memberof TicketInstDetailVO
     */
    'currentNodeProcessor'?: Array<UserSimpleInfoVO>;
    /**
     * 填写的表单数据
     * @type {string}
     * @memberof TicketInstDetailVO
     */
    'formData'?: string;
}

export const TicketInstDetailVOTicketStatusEnum = {
    PROCESSING: 0,
    COMPLETED: 1
} as const;

export type TicketInstDetailVOTicketStatusEnum = typeof TicketInstDetailVOTicketStatusEnum[keyof typeof TicketInstDetailVOTicketStatusEnum];
export const TicketInstDetailVOCurrentNodeTypeEnum = {
    SUBMIT: 0,
    APPROVE: 1
} as const;

export type TicketInstDetailVOCurrentNodeTypeEnum = typeof TicketInstDetailVOCurrentNodeTypeEnum[keyof typeof TicketInstDetailVOCurrentNodeTypeEnum];
export const TicketInstDetailVOCurrentNodeStatusEnum = {
    TO_BE_ACCEPTED: 0,
    ACCEPTED: 1,
    PROCESSED: 2,
    TO_BE_APPROVED: 3,
    APPROVED: 4,
    REJECTED: 5
} as const;

export type TicketInstDetailVOCurrentNodeStatusEnum = typeof TicketInstDetailVOCurrentNodeStatusEnum[keyof typeof TicketInstDetailVOCurrentNodeStatusEnum];


