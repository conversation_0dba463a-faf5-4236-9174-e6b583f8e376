/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { UserSimpleInfoVO } from './user-simple-info-vo';

/**
 * 工单查询结果
 * @export
 * @interface TicketInstListItemVO
 */
export interface TicketInstListItemVO {
    /**
     * 工单ID
     * @type {number}
     * @memberof TicketInstListItemVO
     */
    'id'?: number;
    /**
     * 工单关联的对象ID
     * @type {string}
     * @memberof TicketInstListItemVO
     */
    'relevantObjId'?: string;
    /**
     * 工单类型ID
     * @type {number}
     * @memberof TicketInstListItemVO
     */
    'ticketDefId'?: number;
    /**
     * 工单类型code
     * @type {string}
     * @memberof TicketInstListItemVO
     */
    'ticketDefCode'?: string;
    /**
     * 工单类型名称
     * @type {string}
     * @memberof TicketInstListItemVO
     */
    'ticketDefName'?: string;
    /**
     * 工单关联的工作流实例ID
     * @type {number}
     * @memberof TicketInstListItemVO
     */
    'wfInstId'?: number;
    /**
     * 工单关联的表单实例ID
     * @type {number}
     * @memberof TicketInstListItemVO
     */
    'formInstId'?: number;
    /**
     * 优先级，值越大优先级越高
     * @type {number}
     * @memberof TicketInstListItemVO
     */
    'priority'?: number;
    /**
     * 
     * @type {UserSimpleInfoVO}
     * @memberof TicketInstListItemVO
     */
    'creator'?: UserSimpleInfoVO;
    /**
     * 工单状态，0:PROCESSING-进行中，1:COMPLETED-已关闭
     * @type {number}
     * @memberof TicketInstListItemVO
     */
    'status'?: TicketInstListItemVOStatusEnum;
    /**
     * 创建时间
     * @type {string}
     * @memberof TicketInstListItemVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof TicketInstListItemVO
     */
    'updateTime'?: string;
}

export const TicketInstListItemVOStatusEnum = {
    PROCESSING: 0,
    COMPLETED: 1
} as const;

export type TicketInstListItemVOStatusEnum = typeof TicketInstListItemVOStatusEnum[keyof typeof TicketInstListItemVOStatusEnum];


