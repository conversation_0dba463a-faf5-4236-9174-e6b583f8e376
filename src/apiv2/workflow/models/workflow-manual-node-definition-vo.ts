/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */



/**
 * 工单关联的工作流人工节点配置
 * @export
 * @interface WorkflowManualNodeDefinitionVO
 */
export interface WorkflowManualNodeDefinitionVO {
    /**
     * 节点配置ID
     * @type {number}
     * @memberof WorkflowManualNodeDefinitionVO
     */
    'id'?: number;
    /**
     * 关联的工作流配置ID
     * @type {number}
     * @memberof WorkflowManualNodeDefinitionVO
     */
    'wfDefId'?: number;
    /**
     * 节点ID(bpmn流程文件中userTask节点的id)
     * @type {string}
     * @memberof WorkflowManualNodeDefinitionVO
     */
    'nodeId'?: string;
    /**
     * 节点名称
     * @type {string}
     * @memberof WorkflowManualNodeDefinitionVO
     */
    'nodeName'?: string;
    /**
     * 节点类别，0-提交型任务，1-审核型任务
     * @type {number}
     * @memberof WorkflowManualNodeDefinitionVO
     */
    'nodeType'?: WorkflowManualNodeDefinitionVONodeTypeEnum;
    /**
     * 标识节点任务执行结果的流程实例变量名
     * @type {string}
     * @memberof WorkflowManualNodeDefinitionVO
     */
    'resultVarName'?: string;
    /**
     * 创建时间
     * @type {string}
     * @memberof WorkflowManualNodeDefinitionVO
     */
    'createTime'?: string;
    /**
     * 更新时间
     * @type {string}
     * @memberof WorkflowManualNodeDefinitionVO
     */
    'updateTime'?: string;
}

export const WorkflowManualNodeDefinitionVONodeTypeEnum = {
    SUBMIT: 0,
    APPROVE: 1
} as const;

export type WorkflowManualNodeDefinitionVONodeTypeEnum = typeof WorkflowManualNodeDefinitionVONodeTypeEnum[keyof typeof WorkflowManualNodeDefinitionVONodeTypeEnum];


