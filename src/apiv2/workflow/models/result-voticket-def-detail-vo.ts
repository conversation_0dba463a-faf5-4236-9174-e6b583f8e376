/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { TicketDefDetailVO } from './ticket-def-detail-vo';

/**
 * 返回结果
 * @export
 * @interface ResultVOTicketDefDetailVO
 */
export interface ResultVOTicketDefDetailVO {
    /**
     * 错误码
     * @type {number}
     * @memberof ResultVOTicketDefDetailVO
     */
    'code'?: number;
    /**
     * 错误消息
     * @type {string}
     * @memberof ResultVOTicketDefDetailVO
     */
    'message'?: string;
    /**
     * 
     * @type {TicketDefDetailVO}
     * @memberof ResultVOTicketDefDetailVO
     */
    'data'?: TicketDefDetailVO;
    /**
     * 是否成功
     * @type {boolean}
     * @memberof ResultVOTicketDefDetailVO
     */
    'success'?: boolean;
}

