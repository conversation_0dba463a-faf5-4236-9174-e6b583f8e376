/* tslint:disable */
/* eslint-disable */
/**
 * OpenAPI definition
 * No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)
 *
 * The version of the OpenAPI document: v0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


// May contain unused imports in some cases
// @ts-ignore
import type { FormDefUpdateReqVO } from './form-def-update-req-vo';
// May contain unused imports in some cases
// @ts-ignore
import type { FormFieldAuthUpdateReqVO } from './form-field-auth-update-req-vo';

/**
 * 工单配置更新请求
 * @export
 * @interface TicketDefUpdateReqVO
 */
export interface TicketDefUpdateReqVO {
    /**
     * 工单配置ID
     * @type {number}
     * @memberof TicketDefUpdateReqVO
     */
    'id': number;
    /**
     * 工单名称
     * @type {string}
     * @memberof TicketDefUpdateReqVO
     */
    'name'?: string;
    /**
     * 工单描述
     * @type {string}
     * @memberof TicketDefUpdateReqVO
     */
    'desc'?: string;
    /**
     * 
     * @type {FormDefUpdateReqVO}
     * @memberof TicketDefUpdateReqVO
     */
    'ticketRelatedFormDef'?: FormDefUpdateReqVO;
    /**
     * 针对工作流节点设置的表单字段权限
     * @type {Array<FormFieldAuthUpdateReqVO>}
     * @memberof TicketDefUpdateReqVO
     */
    'formFieldAuthList'?: Array<FormFieldAuthUpdateReqVO>;
}

