<script setup lang="ts">
import type { GetOssUploadCredentialAccessTypeEnum, GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'
import { OssUploader } from '@/utils/ossUploader'

defineOptions({
  name: 'CgFileUploader',
})

const props = defineProps({
  modelValue: {
    type: [Object, Array] as PropType<CgFile | CgFile[] | null | undefined>,
    default: () => null,
  },
  multiple: {
    type: Boolean,
    default: false,
  },
  folderName: {
    type: String as () => GetOssUploadCredentialUploadFileTypeEnum,
    required: true,
  },
  accessType: {
    type: String as () => GetOssUploadCredentialAccessTypeEnum,
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  uploadText: {
    type: String,
    default: '附件上传',
  },
  accept: {
    type: String,
    default: '*',
  },
})

const emits = defineEmits<{
  'update:modelValue': [value: CgFile | CgFile[] | null]
  'uploadSuccess': [file: CgFile]
  'uploadError': [error: any]
  'fileRemoved': [file: CgFile]
}>()

export interface CgFile {
  key: string // OSS key 或 唯一标识
  name: string // 文件名，用于显示
  url: string // 实际下载/访问 URL (预签名 URL)
  size?: number
  type?: string
}

const fileLoading = ref(false)
const files = ref<CgFile[]>([])

watch(() => props.modelValue, (newValue) => {
  if (!newValue) {
    files.value = []
  }
  else if (Array.isArray(newValue)) {
    files.value = newValue.filter(f => f && typeof f === 'object' && f.key && f.name && f.url)
  }
  else if (typeof newValue === 'object' && newValue.key && newValue.name && newValue.url) {
    files.value = [newValue]
  }
  else {
    console.warn('CgFileUploader received invalid modelValue:', newValue)
    files.value = []
  }
}, { immediate: true, deep: true })

function createUploadSuccessHandler(file: File) {
  return (downloadUrl: string, key: string) => {
    const newFile: CgFile = {
      key,
      name: file.name,
      url: downloadUrl,
      size: file.size,
      type: file.type,
    }

    if (props.multiple) {
      const updatedFiles = [...files.value, newFile]
      files.value = updatedFiles
      emits('update:modelValue', updatedFiles)
    }
    else {
      files.value = [newFile]
      emits('update:modelValue', newFile)
    }

    emits('uploadSuccess', newFile)
  }
}

function handleUploadError(error: any) {
  emits('uploadError', error)
}

function handleRemoveFile(index: number) {
  if (props.disabled)
    return

  const removedFile = files.value[index]
  files.value.splice(index, 1)

  if (props.multiple) {
    emits('update:modelValue', [...files.value])
  }
  else {
    emits('update:modelValue', null)
  }

  emits('fileRemoved', removedFile)
}

function handleUploadFile() {
  if (props.disabled || fileLoading.value)
    return

  const input = document.createElement('input')
  input.type = 'file'
  input.accept = props.accept
  input.multiple = props.multiple
  input.style.display = 'none'

  input.onchange = async (e: Event) => {
    const target = e.target as HTMLInputElement
    if (target.files && target.files.length > 0) {
      fileLoading.value = true

      try {
        // 如果是单文件模式且已经有文件，则先触发一次移除事件
        if (!props.multiple && files.value.length > 0) {
          handleRemoveFile(0)
        }

        for (let i = 0; i < target.files.length; i++) {
          const file = target.files[i]

          await OssUploader.uploadFile(
            file,
            props.folderName,
            props.accessType,
            undefined,
            createUploadSuccessHandler(file),
            handleUploadError,
          )

          // 如果是单文件模式，只上传第一个文件后中断
          if (!props.multiple)
            break
        }
      }
      finally {
        fileLoading.value = false
        // 清理 input 防止重复选择相同文件不触发 onchange
        input.value = ''
      }
    }
  }

  document.body.appendChild(input)
  input.click()
  document.body.removeChild(input)
}

function truncateFileName(name: string): string {
  if (!name)
    return '未知文件'
  if (name.length <= 10)
    return name
  return `${name.slice(0, 10)}...`
}
</script>

<template>
  <div class="w-full flex items-center gap-2 pl-16px">
    <!-- 文件列表 -->
    <div v-if="files.length > 0" class="flex flex-wrap gap-1.5">
      <div
        v-for="(file, index) in files"
        :key="file.key || index"
        class="h-6 flex items-center gap-1 rounded-full bg-gray-100 px-2 py-0.5 text-xs"
      >
        <font-awesome-icon :icon="['fas', 'paperclip']" class="text-xs text-gray-500" />
        <a
          class="max-w-24 truncate text-gray-700"
          :href="file.url"
          target="_blank"
          :download="file.name"
          :title="file.name"
        >
          {{ truncateFileName(file.name) }}
        </a>
        <font-awesome-icon
          v-if="!disabled"
          :icon="['fas', 'times']"
          class="cursor-pointer text-xs text-gray-500 hover:text-red-500"
          @click.stop="handleRemoveFile(index)"
        />
      </div>
    </div>
    <!-- 上传按钮 -->
    <div
      class="hover:text-primary-dark inline-flex cursor-pointer items-center gap-1.5 text-primary"
      :class="{ 'opacity-50 cursor-not-allowed': disabled || fileLoading }"
      @click="handleUploadFile"
    >
      <font-awesome-icon :icon="['fas', 'upload']" />
      <span>{{ uploadText }}</span>
      <div v-if="fileLoading" class="i-svg-spinners-3-dots-fade h-4 w-4" />
    </div>
  </div>
</template>
