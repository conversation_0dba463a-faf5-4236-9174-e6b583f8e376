import type { ElButton } from 'element-plus'
import type { ExtractPropTypes } from 'vue'

export interface DialogButtonOption {
  text: string
  events?: Record<string, (...args: any[]) => any>
  show?: boolean
  props?: InstanceType<typeof ElButton>['$props']
}
export const dialogProps = {
  confirmButtonText: { type: String, default: '保存' },
  confirmButtonVisible: { type: Boolean, default: true },
  confirmButtonDisabled: { type: Boolean, default: false },
  cancleButtonText: { type: String, default: '关闭' },
  cancleButtonVisible: { type: Boolean, default: true },
  /** 是否显示底部操作栏 */
  footerVisible: { type: Boolean, default: true },
  loading: { type: Boolean, default: false },
  buttons: { type: Array<DialogButtonOption>, default: () => [] },
  fullHeight: { type: Boolean, default: true },
  title: { type: String, default: '提示' },
  modelValue: { type: Boolean },
  /** 内容区域padding */
  padding: { type: [Number, String], default: '20px 20px 0' },
  width: { type: [Number, String], default: '900px' },
  /** 对话框最大高度，支持数字（px）或字符串（如'80vh'） */
  maxHeight: { type: [Number, String] },
  /** 对话框位置，可选值：'center', 'top', 'bottom' */
  position: { type: String, default: 'center' },
  /** 对话框垂直偏移量，正值向下偏移，负值向上偏移 */
  offsetY: { type: [Number, String], default: 0 },
}

export type DialogProps = ExtractPropTypes<typeof dialogProps>
