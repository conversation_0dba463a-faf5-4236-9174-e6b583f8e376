<script setup lang="ts">
import type { DialogButtonOption } from './dialog'
import { useVModel } from '@vueuse/core'
import { ElDialog, useAttrs } from 'element-plus'
import { isNumber } from 'element-plus/es/utils/index'
import { dialogProps } from './dialog'

defineOptions({
  name: 'CgDialog',
  inheritAttrs: false,
})
const props = defineProps(dialogProps)
const emit = defineEmits(['update:modelValue', 'submit'])
const modelValue = useVModel(props, 'modelValue', emit)
const ifRenderComputed = computed(() => {
  return (button: DialogButtonOption) => {
    return button.show === undefined ? true : button.show
  }
})

const buttonsComputed = computed(() => {
  const buttons = [...props.buttons].filter(button => button.show === undefined || button.show)
  buttons.forEach((button: any, index: number) => {
    if (!button.props)
      button.props = {}

    if (index === buttons.length - 1) {
      if (!button.props.type)
        Object.assign(button.props, { type: 'primary' })
    }
    else {
      Object.assign(button.props || {}, { plain: true })
    }
  })
  return buttons
})
const attrsComputed = computed(() => {
  const options: any = {
    closeOnClickModal: false,
    modalAppendToBody: false,
    width: '900px',
  }
  return Object.assign(options, useAttrs())
})

const footerSlotComputed = computed(() => {
  return !!useSlots().footer?.()
})
const propsRenderComputed = computed(() => {
  return (button: any) => {
    return button.props || {}
  }
})
const cssVar = reactive({
  '--el-dialog-padding-primary': isNumber(props.padding) ? `${props.padding}px` : props.padding,
  '--el-dialog-width': isNumber(props.width) ? `${props.width}px` : props.width,
  '--el-dialog-max-height': props.maxHeight ? (isNumber(props.maxHeight) ? `${props.maxHeight}px` : props.maxHeight) : undefined,
  '--el-dialog-margin-top': getDialogMarginTop(),
})

function getDialogMarginTop() {
  if (props.fullHeight) {
    if (props.position === 'center') {
      return '50px'
    }
    else if (props.position === 'top') {
      return isNumber(props.offsetY) ? `${props.offsetY}px` : props.offsetY
    }
    else if (props.position === 'bottom') {
      return 'auto'
    }
    return '20vh'
  }
  else {
    return '0'
  }
}
const dialogRef = ref<InstanceType<typeof ElDialog> | null>(null)
function handleSubmit(args: () => void) {
  emit('submit', args)
}

function addOverlayClass() {
  const parentNode = dialogRef.value?.dialogContentRef?.$el?.parentNode?.parentNode
  if (parentNode) {
    parentNode.classList.add('cg-dialog-overlay')

    // 处理底部位置
    if (props.position === 'bottom') {
      const dialogEl = dialogRef.value?.dialogContentRef?.$el
      if (dialogEl) {
        dialogEl.style.marginBottom = isNumber(props.offsetY) ? `${props.offsetY}px` : props.offsetY
      }
    }
  }
}

watch(modelValue, (newValue) => {
  if (newValue) {
    nextTick(() => {
      addOverlayClass()
    })
  }
}, { immediate: true })

function removeOverlayClass() {
  const parentNode = dialogRef.value?.dialogContentRef?.$el?.parentNode?.parentNode
  if (parentNode) {
    parentNode.classList.remove('cg-dialog-overlay')
  }
}
</script>

<template>
  <ElDialog
    ref="dialogRef" v-model="modelValue" :title="title" class="cg-dialog" :class="{
      'full-height': fullHeight,
      'position-top': position === 'top',
      'position-bottom': position === 'bottom',
      'position-center': position === 'center' || !position,
    }" v-bind="attrsComputed" :style="cssVar" @closed="removeOverlayClass"
  >
    <slot />
    <template v-if="footerSlotComputed && footerVisible" #footer>
      <slot name="footer" />
    </template>
    <template v-else-if="footerVisible" #footer>
      <CgButton v-if="cancleButtonVisible" plain @click="emit('update:modelValue', false)">
        {{ cancleButtonText }}
      </CgButton>
      <CgButton
        v-if="buttons.length === 0 && confirmButtonVisible" type="primary" auto-loading
        :disabled="confirmButtonDisabled" @click="handleSubmit"
      >
        {{ confirmButtonText }}
      </CgButton>
      <template v-for="(button) in buttonsComputed" :key="button.text">
        <CgButton v-if="ifRenderComputed(button)" v-bind="propsRenderComputed(button)" v-on="button.events || {}">
          {{ button.text }}
        </CgButton>
      </template>
    </template>
    <template #header>
      <slot name="header" />
    </template>
  </ElDialog>
</template>

<style lang="scss">
.el-overlay {
  --el-overlay-color-lighter: rgba(0, 0, 0, 0.25);
}

.el-icon svg {
  width: 1rem !important;
  height: 1rem !important;
}

.cg-dialog-overlay {
  display: flex !important;
  align-items: center;
  justify-content: center;

  .el-overlay-dialog {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }
}

.el-dialog {
  &.cg-dialog {
    display: flex;
    flex-direction: column;
    --el-dialog-margin-top: 20vh;
    padding: 0;

    .el-scrollbar__view>* {
      padding: var(--el-dialog-padding-primary);
    }

    // text-align: left;
    .el-dialog__body {
      flex: 1;
      padding: 20px;
      color: #000;
      display: flex;
      flex-direction: column;
      overflow-y: auto;
      width: 100%;
      box-sizing: border-box;
      max-height: var(--el-dialog-max-height);
    }

    // &.full-height >>>  .el-dialog {
    //   margin: 50px auto 0 !important;
    //   height: calc(100% - 100px);
    // }

    &.full-height {
      height: calc(100vh - 100px);
      margin-top: 50px !important;

      @media screen and (max-height: 800px) {
        height: calc(100vh - 120px);
      }
    }

    &:not(.full-height) {
      height: auto;
      max-height: 80vh;
    }

    &.position-top {
      margin-bottom: auto !important;
      align-self: flex-start;
    }

    &.position-bottom {
      margin-top: auto !important;
      align-self: flex-end;
    }

    &.position-center {
      margin-top: var(--el-dialog-margin-top, 20vh);
    }

    .el-dialog__header {
      padding: 0 24px;
      height: 50px;
      align-items: center;
      display: flex;
      position: relative;
      border-bottom: 1px solid #e6e8ed;

      .el-dialog__headerbtn {
        top: 50%;
        transform: translateY(-50%);
        width: 32px;
        height: 32px;
        right: 5px;

        .el-dialog__close {
          font-size: 20px;
          transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
          transform-origin: 100% 50%;
          border-radius: 50%;

          &:hover {
            background-color: var(--el-color-primary-light-9);
          }
        }
      }
    }

    .el-dialog__footer {
      padding: 10px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      border-top: 1px solid #e6e8ed;

      .el-button {
        padding-left: 16px;
        padding-right: 16px;
      }

      .el-button+.el-button {
        margin-left: 10px;
      }
    }

    .el-dialog__title {
      font-weight: bold;
    }

    &.is-hide-footer {
      .el-dialog__body {
        padding-bottom: 20px;
      }
    }
  }
}
</style>

<style>
@keyframes dialog-fade-in {
  0% {
    transform: unset;
    opacity: 0;
  }

  100% {
    transform: unset;
    opacity: 1;
  }
}

@keyframes dialog-fade-out {
  0% {
    transform: unset;
    opacity: 1;
  }

  100% {
    transform: unset;
    opacity: 0;
  }
}
</style>
