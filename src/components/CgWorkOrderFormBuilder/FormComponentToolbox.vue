<script setup lang="ts">
import type { FormColumn } from './types'
import { nanoid } from 'nanoid'
import draggable from 'vuedraggable'

const basicComponents = ref([
  { type: 'input', label: '输入框', icon: 'fa-keyboard' },
  { type: 'selectv2', label: '选择器', icon: 'fa-list-ul' },
  { type: 'datepicker', label: '日期选择', icon: 'fa-calendar-days' },
  { type: 'textarea', label: '文本域', icon: 'fa-paragraph' },
  { type: 'checkbox', label: '复选框', icon: 'fa-check-square' },
  { type: 'radio', label: '单选框', icon: 'fa-dot-circle' },
  { type: 'inputNumber', label: '数字输入', icon: 'fa-hashtag' },
  { type: 'switch', label: '开关', icon: 'fa-toggle-on' },
  // TODO: 添加其他组件类型
])

// vuedraggable 克隆函数
function cloneComponent(original: { type: string, label: string }): FormColumn {
  const newId = nanoid(8)
  let type = original.type
  let props: any = {}

  // 如果从工具箱拖拽的是 'textarea'
  if (original.type === 'textarea') {
    type = 'input'
    props = { type: 'textarea', rows: 3 }
  }
  else {
    if (original.type === 'selectv2' || original.type === 'checkbox' || original.type === 'radio') {
      props.options = []
    }
  }

  return {
    id: newId,
    key: `${original.type}_${newId}`,
    type,
    label: original.label,
    span: 24,
    props,
    rules: [],
    required: false,
  }
}
</script>

<template>
  <div class="component-toolbox mr-4 w-60 overflow-y-auto bg-white">
    <h4 class="mb-3 text-sm text-gray-700 font-medium">
      可用控件
    </h4>
    <draggable
      :list="basicComponents"
      :group="{ name: 'form-components', pull: 'clone', put: false }"
      :sort="false"
      item-key="type"
      :clone="cloneComponent"
      class="space-y-2"
    >
      <template #item="{ element }">
        <div class="toolbox-item flex cursor-grab items-center border rounded bg-gray-50 p-2 transition-colors hover:border-blue-300 hover:bg-blue-50">
          <font-awesome-icon :icon="['fas', element.icon]" class="mr-2 w-4 text-center text-gray-600" size="sm" />
          <span class="text-xs text-gray-800">{{ element.label }}</span>
        </div>
      </template>
    </draggable>
  </div>
</template>

<style scoped>
.toolbox-item {
  cursor: grab;
}
.toolbox-item:active {
  cursor: grabbing;
}
</style>
