<script setup lang="ts">
import type { FormColumn, FormRow } from './types'
import { nanoid } from 'nanoid'
import FormBuilderCanvas from './FormBuilderCanvas.vue'
import FormComponentProperties from './FormComponentProperties.vue'
import FormComponentToolbox from './FormComponentToolbox.vue'

interface Props {
  initialDefinition?: string // 接收初始的 JSON 定义字符串
}
const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'save', definitionJson: string): void
}>()

const formDefinition = ref<FormRow[]>([])
const selectedComponent = ref<FormColumn | null>(null)

function triggerSave() {
  const definitionToSave = formDefinition.value.map(row => ({
    ...row,
    columns: row.columns,
  }))
  const jsonString = JSON.stringify(definitionToSave)
  emit('save', jsonString)
}

function loadDefinition(jsonString: string | undefined) {
  if (!jsonString) {
    formDefinition.value = []
    selectedComponent.value = null
    return
  }
  try {
    const loadedDefinition: FormRow[] = JSON.parse(jsonString)
    // 确保加载的数据有 id, 并处理 options 可能是字符串的情况
    formDefinition.value = loadedDefinition.map((row: any) => ({
      ...row,
      id: row.id || nanoid(8),
      columns: row.columns.map((col: any) => {
        const newCol = {
          ...col,
          id: col.id || nanoid(8),
        }
        if ((newCol.type === 'selectv2' || newCol.type === 'checkbox' || newCol.type === 'radio') && typeof newCol.props?.options === 'string') {
          try {
            if (!newCol.props)
              newCol.props = {}
            newCol.props.options = JSON.parse(newCol.props.options)
          }
          catch (e) {
            console.warn(`加载时解析 ${newCol.key} 的 options 失败: ${e}`)
            if (newCol.props)
              newCol.props.options = []
          }
        }
        return newCol
      }),
    }))
    selectedComponent.value = null
  }
  catch (e) {
    console.error('加载表单定义失败:', e)
    ElMessage.error('加载表单定义失败，请检查数据格式')
    formDefinition.value = []
    selectedComponent.value = null
  }
}

watch(() => props.initialDefinition, (newJson) => {
  loadDefinition(newJson)
}, { immediate: true })
</script>

<template>
  <div class="work-order-form-editor">
    <div class="editor-layout h-full flex">
      <FormComponentToolbox />
      <FormBuilderCanvas v-model:definition="formDefinition" v-model:selected-component="selectedComponent" />
      <FormComponentProperties v-model:selected-component="selectedComponent" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.work-order-form-editor {
  height: 100%;
  background: #fff;

  .editor-layout {
    flex: 1;
    min-height: 500px;
  }
}
</style>
