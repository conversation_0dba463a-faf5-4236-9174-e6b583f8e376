<script setup lang="ts">
import type { FormColumn, FormRow, SimplifiedSortableInstance } from './types'
import ComponentTags from '@/components/CgElementUI/CgForm/ComponentTags'
import { nanoid } from 'nanoid'
import draggable from 'vuedraggable'

const formDefinition = defineModel<FormRow[]>('definition', { required: true })
const selectedComponent = defineModel<FormColumn | null>('selectedComponent')

// 用于 WYSIWYG 视图中组件的 v-model 绑定，避免警告，不存储实际数据
const dummyModel = ref<Record<string, any>>({})

function calculateSpans(row: FormRow) {
  const count = row.columns.length
  if (count === 0)
    return
  const baseSpan = Math.floor(24 / count)
  const extraSpan = 24 % count
  row.columns.forEach((col, index) => {
    col.span = baseSpan + (index < extraSpan ? 1 : 0)
  })
}

watch(formDefinition, (newDefinition, oldDefinition) => {
  newDefinition.forEach((row, rowIndex) => {
    // 只有当这一行的组件数量变化时才重新计算 span
    if (!oldDefinition[rowIndex] || oldDefinition[rowIndex].columns.length !== row.columns.length) {
      calculateSpans(row)
    }

    const selectedExists = row.columns.some(c => c.id === selectedComponent.value?.id)
    if (selectedComponent.value && !selectedExists && !newDefinition.flatMap(r => r.columns).some(c => c.id === selectedComponent.value?.id)) {
      selectedComponent.value = null
    }
  })
}, { deep: true })

function addRow() {
  formDefinition.value.push({ id: nanoid(8), columns: [] })
}

function removeRow(rowIndex: number) {
  if (selectedComponent.value && formDefinition.value[rowIndex]?.columns.find(c => c.id === selectedComponent.value?.id)) {
    selectedComponent.value = null // 如果删除的行包含选中的组件，取消选中
  }
  formDefinition.value.splice(rowIndex, 1)
}

function removeColumn(rowIndex: number, colIndex: number) {
  const col = formDefinition.value[rowIndex].columns[colIndex]
  if (selectedComponent.value && col.id === selectedComponent.value.id) {
    selectedComponent.value = null // 如果删除的是选中的组件，取消选中
  }
  formDefinition.value[rowIndex].columns.splice(colIndex, 1)
  calculateSpans(formDefinition.value[rowIndex])
}

function onRowChange(rowIndex: number) {
  nextTick(() => {
    calculateSpans(formDefinition.value[rowIndex])
  })
}

function selectForEditing(component: FormColumn) {
  selectedComponent.value = component
}

// 获取要传递给 WYSIWYG 组件的 props
function getComponentProps(col: FormColumn) {
  const componentInfo = ComponentTags[col.type || 'input']
  if (!componentInfo)
    return { disabled: true } // 安全检查

  // 合并默认值和用户定义的值
  const props: Record<string, any> = {
    ...(componentInfo.props || {}),
    ...(col.props || {}),
    disabled: true, // !!! 关键：在画布中禁用交互 !!!
    placeholder: (typeof col.props === 'object' ? col.props.placeholder : undefined) || col.label || `[${col.type}]`,
  }

  if ((col.type === 'selectv2' || col.type === 'checkbox' || col.type === 'radio') && typeof props.options === 'string') {
    try {
      props.options = JSON.parse(props.options)
    }
    catch (e) {
      console.warn(`WYSIWYG: Failed to parse options JSON for ${col.key}: ${e}`)
      props.options = []
    }
  }
  else if ((col.type === 'selectv2' || col.type === 'checkbox' || col.type === 'radio') && !Array.isArray(props.options)) {
    props.options = []
  }

  // 处理 input 的 type='textarea'
  if (col.type === 'input' && props.type === 'textarea' && !props.rows) {
    props.rows = 2
  }

  return props
}

const rowGroupOptions = {
  name: 'form-rows',
  pull: true,
  put: (to: SimplifiedSortableInstance, from: SimplifiedSortableInstance): boolean => {
    const fromGroupName = from.options.group?.name
    return fromGroupName === 'form-rows'
  },
}

const columnGroupOptions = {
  name: 'form-components',
  pull: true,
  put: true,
}

const isEmptyCanvas = computed(() => formDefinition.value.length === 0)

function getComponentName(type: string | undefined): string {
  if (!type || !ComponentTags[type]) {
    return ComponentTags.input.component
  }
  return ComponentTags[type].component
}
</script>

<template>
  <div class="form-canvas relative flex-1 overflow-auto rounded bg-gray-100 p-4">
    <draggable
      v-model="formDefinition" item-key="id" handle=".drag-handle-row" :group="rowGroupOptions"
      class="canvas-rows space-y-2" ghost-class="ghost-row" drag-class="dragging-row" :animation="150"
    >
      <template #item="{ element: row, index: rowIndex }">
        <div
          class="form-row group relative border rounded bg-white p-3 shadow-sm transition-shadow hover:shadow-md"
          :data-row-id="row.id"
        >
          <!-- 行操作：拖拽手柄、删除按钮 -->
          <div
            class="row-actions absolute right-1 top-1 z-10 flex gap-1 opacity-0 transition-opacity group-hover:opacity-100"
          >
            <button
              title="拖拽行"
              class="drag-handle-row cursor-move rounded bg-gray-200/50 p-1 text-gray-500 hover:bg-blue-100 hover:text-blue-600"
            >
              <font-awesome-icon :icon="['fas', 'grip-vertical']" size="xs" />
            </button>
            <button
              title="删除行" class="rounded bg-gray-200/50 p-1 text-gray-500 hover:bg-red-100 hover:text-red-500"
              @click.stop="removeRow(rowIndex)"
            >
              <font-awesome-icon :icon="['fas', 'trash-alt']" size="xs" />
            </button>
          </div>

          <draggable
            v-model="row.columns" item-key="id" :group="columnGroupOptions"
            class="row-columns round min-h-[80px] flex flex-wrap items-start -m-1" ghost-class="ghost-col"
            drag-class="dragging-col" :animation="150" @change="() => onRowChange(rowIndex)"
          >
            <template #item="{ element: col, index: colIndex }">
              <div
                :style="{ width: `calc(${(col.span || 0) / 24 * 100}% - 8px)` }"
                class="form-column group/col relative m-1 cursor-pointer border rounded bg-white p-2"
                :class="{ 'border-blue-500 ring-2 ring-blue-300': selectedComponent?.id === col.id }"
                :data-col-id="col.id" @click.stop="selectForEditing(col)"
              >
                <!-- WYSIWYG: 使用 ElFormItem 和实际组件 -->
                <ElFormItem
                  :label="col.label || `未命名 ${col.type}`" :required="col.required"
                  class="wysiwyg-form-item mb-0" label-position="top" size="small" :prop="col.key"
                >
                  <!-- 实际组件渲染 -->
                  <component
                    :is="getComponentName(col.type)" v-if="col.type && ComponentTags[col.type]" :key="col.id"
                    v-model="dummyModel[col.key || col.id]" v-bind="getComponentProps(col)"
                    style="pointer-events: none;"
                  />
                  <div v-else class="border border-red-300 rounded p-2 text-xs text-red-500">
                    未知控件类型: {{ col.type }}
                  </div>
                </ElFormItem>

                <!-- 组件操作：删除按钮 (放在右上角) -->
                <button
                  title="删除控件"
                  class="col-remove-btn absolute right-0.5 top-0.5 z-10 rounded-full bg-white/70 p-0.5 text-gray-400 opacity-0 transition-opacity hover:text-red-500 group-hover/col:opacity-100"
                  @click.stop="removeColumn(rowIndex, colIndex)"
                >
                  <font-awesome-icon :icon="['fas', 'times-circle']" size="sm" />
                </button>
              </div>
            </template>
          </draggable>
        </div>
      </template>

      <template #footer>
        <div v-if="isEmptyCanvas" class="border rounded border-dashed bg-white p-10 text-center text-gray-400">
          从左侧拖拽控件到此处或点击下方按钮添加新行
        </div>
        <button
          class="add-row-btn mt-4 w-full flex items-center justify-center border rounded border-dashed bg-white p-2 text-blue-600 transition-colors hover:border-blue-500 hover:bg-blue-50"
          @click="addRow"
        >
          <font-awesome-icon :icon="['fas', 'plus']" class="mr-1" size="sm" /> 添加新行
        </button>
      </template>
    </draggable>
  </div>
</template>

<style scoped lang="scss">
.ghost-row,
.ghost-col {
  opacity: 0.5;
  background: #c8ebfb;
  border: 1px dashed #409EFF;
}

.dragging-row,
.dragging-col {
  opacity: 0.8;
  cursor: grabbing !important;
}

.form-column {
  transition: all 0.15s ease-in-out;
  min-height: 70px;
  position: relative;
  display: flex;
  flex-direction: column;

  :deep(.wysiwyg-form-item) {
    margin-bottom: 0 !important;

    .el-form-item__label {
      line-height: normal;
      padding-bottom: 2px !important;
      font-size: 12px;
      color: #606266;
    }

    .el-form-item__content {
      line-height: normal;
      font-size: 12px;
    }

    .el-input.is-disabled .el-input__wrapper,
    .el-select-v2__wrapper.is-disabled,
    .el-textarea.is-disabled .el-textarea__inner {
      background-color: #f9fafb;
      cursor: default !important;
    }

    .el-checkbox__input.is-disabled+.el-checkbox__label,
    .el-radio__input.is-disabled+.el-radio__label {
      color: var(--el-text-color-placeholder);
    }

  }
}

.row-columns:empty {
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.row-columns:empty::before {
  content: "将控件拖到此处";
  color: #ccc;
  font-size: 12px;
}

// 防止画布中的控件响应鼠标事件
.form-column :deep(input),
.form-column :deep(textarea),
.form-column :deep(.el-select),
.form-column :deep(.el-radio),
.form-column :deep(.el-checkbox),
.form-column :deep(.el-switch),
.form-column :deep(.el-date-editor) {
  pointer-events: none !important;
}
</style>
