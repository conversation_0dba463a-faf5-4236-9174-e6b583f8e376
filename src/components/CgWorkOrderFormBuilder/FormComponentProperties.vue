<script setup lang="ts">
import type { CgFormOption } from '@/components/CgElementUI/CgForm/Form.vue'
import type { FormColumn } from './types'
import CgForm from '@/components/CgElementUI/CgForm/Form.vue'

const selectedComponent = defineModel<FormColumn | null>('selectedComponent')

const propertyOptions = computed<CgFormOption[][]>(() => {
  if (!selectedComponent.value)
    return []

  // 公共基础属性
  const commonOptions: CgFormOption[] = [
    { type: 'input', key: 'label', label: '标签文本', required: true },
    { type: 'input', key: 'key', label: '字段标识 (key)', required: true, props: { placeholder: '英文或数字, 需唯一' } },
    { type: 'switch', key: 'required', label: '是否必填' },
    { type: 'inputNumber', key: 'span', label: '栅格宽度(1-24)', props: { min: 1, max: 24, controls: false } },
  ]

  const specificOptions: CgFormOption[] = []
  const componentType = selectedComponent.value.type

  if (componentType === 'input') {
    specificOptions.push(
      { type: 'input', key: 'props.placeholder', label: '占位符' },
      {
        type: 'selectv2',
        key: 'props.type',
        label: '输入框类型',
        props: {
          options: [
            { label: '文本(text)', value: 'text' },
            { label: '多行文本(textarea)', value: 'textarea' },
            { label: '数字(number)', value: 'number' },
            { label: '密码(password)', value: 'password' },
          ],
          allowClear: true,
        },
      },
      {
        type: 'inputNumber',
        key: 'props.rows',
        label: '文本域行数',
        props: { min: 1 },
        ifRender: (model: FormColumn) => typeof model.props === 'object' && model.props?.type === 'textarea',
      },
      { type: 'switch', key: 'props.clearable', label: '允许清空' },
    )
  }

  else if (componentType === 'selectv2') {
    specificOptions.push(
      { type: 'input', key: 'props.placeholder', label: '占位符' },
      { type: 'switch', key: 'props.multiple', label: '允许多选' },
      { type: 'switch', key: 'props.collapseTags', label: '多选时折叠', ifRender: model => !!model.props?.multiple },
      { type: 'switch', key: 'props.filterable', label: '允许搜索' },
      { type: 'switch', key: 'props.allowClear', label: '允许清空' },
      // 选项配置: 使用文本域输入 JSON
      // 注意：现在 model.props.options 应该是数组，但为了方便编辑，仍用 JSON 文本域
      // CgForm 在内部绑定时需要能处理对象到字符串，或提供专门的 options 编辑器
      {
        type: 'input',
        key: 'props.options',
        label: '选项 (JSON)',
        props: { type: 'textarea', rows: 4 },
        labelTips: '格式: [{"label":"选项1", "value":1}, ...]',
        // 添加一个转换器，确保输入的是字符串，输出尝试转为对象/数组
        // 这需要 CgForm 支持或自定义组件实现
        // 暂时依赖用户输入正确的 JSON
        // formatter: (value: any) => typeof value === 'object' ? JSON.stringify(value, null, 2) : value, // TODO
        // parser: (value: string) => { // TODO
        //   try {
        //     return JSON.parse(value)
        //   }
        //   catch { return [] }
        // },
      },
    )
  }

  else if (componentType === 'checkbox' || componentType === 'radio') {
    specificOptions.push(
      {
        type: 'input',
        key: 'props.options',
        label: '选项 (JSON)',
        props: { type: 'textarea', rows: 3 },
        labelTips: '格式: [{"label":"选项1", "value":1}, ...]',
        // formatter: (value: any) => typeof value === 'object' ? JSON.stringify(value, null, 2) : value,
        // parser: (value: string) => {
        //   try {
        //     return JSON.parse(value)
        //   }
        //   catch { return [] }
        // },
      },
    )
  }

  else if (componentType === 'datepicker') {
    specificOptions.push(
      { type: 'input', key: 'props.placeholder', label: '占位符' },
      {
        type: 'selectv2',
        key: 'props.type',
        label: '选择器类型',
        props: {
          options: [
            { label: '日期(date)', value: 'date' },
            { label: '日期时间(datetime)', value: 'datetime' },
            { label: '年份(year)', value: 'year' },
            { label: '月份(month)', value: 'month' },
            { label: '日期范围(daterange)', value: 'daterange' },
            { label: '时间范围(datetimerange)', value: 'datetimerange' },
          ],
          allowClear: true,
        },
      },
      { type: 'input', key: 'props.valueFormat', label: '值格式', labelTips: '如: YYYY-MM-DD HH:mm:ss' },
      { type: 'switch', key: 'props.allowClear', label: '允许清空' },
    )
  }
  else if (componentType === 'inputNumber') {
    specificOptions.push(
      { type: 'input', key: 'props.placeholder', label: '占位符' },
      { type: 'inputNumber', key: 'props.min', label: '最小值', props: { controls: false } },
      { type: 'inputNumber', key: 'props.max', label: '最大值', props: { controls: false } },
      { type: 'inputNumber', key: 'props.step', label: '步长', props: { controls: false, min: 0 } },
      { type: 'inputNumber', key: 'props.precision', label: '精度(小数位数)', props: { controls: false, min: 0, step: 1 } },
      { type: 'switch', key: 'props.controls', label: '显示控制按钮' },
    )
  }
  else if (componentType === 'switch') {
    specificOptions.push(
      { type: 'input', key: 'props.activeText', label: '开启时文本' },
      { type: 'input', key: 'props.inactiveText', label: '关闭时文本' },
      // 可以添加 activeValue/inactiveValue，如果需要非布尔值
      // { type: 'input', key: 'props.activeValue', label: '开启值 (默认 true)' },
      // { type: 'input', key: 'props.inactiveValue', label: '关闭值 (默认 false)' },
    )
  }

  // ... 其他组件类型的特定属性配置 ...

  return [[...commonOptions], [...specificOptions]] // 分成两行显示，基础属性一行，特定属性一行
})
</script>

<template>
  <div class="properties-panel ml-4 w-80 overflow-y-auto bg-white">
    <h4 class="mb-3 text-sm text-gray-700 font-medium">
      控件属性
    </h4>
    <div v-if="selectedComponent">
      <CgForm
        v-if="selectedComponent.id" :key="selectedComponent.id" v-model="selectedComponent"
        :options="propertyOptions" :use-grid="false" label-position="top" size="small" class="properties-cg-form"
      />
      <!-- Debug output -->
      <pre
        class="mt-4 overflow-auto rounded bg-gray-100 p-2 text-xs"
      >{{ JSON.stringify(selectedComponent, null, 2) }}</pre>
    </div>
    <div v-else class="p-10 text-center text-xs text-gray-400">
      请在画布中选择一个控件进行编辑
    </div>
  </div>
</template>

<style lang="scss">
// 微调属性面板表单项间距
.properties-cg-form .el-form-item--small.el-form-item {
  margin-bottom: 12px;
}

.properties-cg-form .el-form-item--small .el-form-item__label {
  line-height: normal;
  padding-bottom: 2px;
}
</style>
