import type { CgFormOption } from '../CgElementUI/CgForm/Form.vue'

interface FormColumn extends CgFormOption {
  id: string
}

interface FormRow {
  id: string
  columns: FormColumn[]
}

interface SimplifiedSortableGroupConfig {
  name?: string
}

interface SimplifiedSortableInstance {
  options: {
    group?: SimplifiedSortableGroupConfig
  }
}

function isFormColumn(item: any): item is FormColumn {
  return (
    item
    && typeof item === 'object'
    && typeof item.id === 'string'
    && typeof item.type === 'string'
    && typeof item.key === 'string'
    && item.columns === undefined
  )
}

export type { FormColumn, FormRow, SimplifiedSortableInstance }
export { isFormColumn }
