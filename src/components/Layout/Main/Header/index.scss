@mixin flex($justify: null) {
  display: flex;
  align-items: center;

  @if $justify {
    justify-content: $justify;
  }
}

@mixin hover-border {
  &:hover {
    border: 1px solid #0c7fdb;
  }
}

@mixin active-style {
  border: 1px solid #0c7fdb !important;
  background-color: #dbe1f4;
}

.cg-header {
  padding-right: 25px;
  height: 100%;
  width: 100%;
  background-color: $cg-header-bg-color;
  @include flex(space-between);

  &__left {
    @include flex;
    height: 100%;
    flex: 1;
    overflow: hidden;
  }

  &__right {
    @include flex(flex-end);
    line-height: $cg-header-height;

    .el-button--text {
      color: #fff;
      font-size: 13px;

      &:hover,
      &:focus {
        color: #fff;
      }

      i {
        color: #fff;
        font-weight: bold;
      }
    }

    .el-avatar {
      margin: 0 5px 0 20px;
    }

    .download-center-icon {
      &:before {
        position: absolute;
        display: block;
        content: "";
        width: 1px;
        height: 12px;
        right: -7px;
        background-color: #666b74;
        top: 50%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
      }
    }
  }

  &__logo {
    padding: 0 10px 0 14px;

    img {
      height: 40px;
    }
  }

  &__icon {
    font-size: 20px;
    text-align: center;
    color: #fff;
    cursor: pointer;
    display: flex;
    padding: 5px;
    flex-direction: column;

    &:hover {
      color: $cg-tagview-item-active-color;
      background-color: $cg-tagview-item-active-bg-color;
    }

    &.help i {
      font-size: 20px;
    }

    .icon-wrap {
      height: $cg-header-height;
    }
  }

  &__account .el-button--primary.is-link {
    --el-button-text-color: #fff;
  }
}

.el-drawer.layout-setting {
  .el-drawer__header {
    font-weight: bold;
    color: #666;
    margin-bottom: 10px;
  }

  .el-drawer__title {
    font-size: 14px;
  }

  .el-drawer__body {
    padding: 0;
  }
}

.layout-setting {
  &__wrap {
    padding: 0 10px 0 15px;
  }

  &__title {
    color: #000;
    font-size: 14px;
    padding: 15px 0;
  }

  &__content {
    display: flex;

    &.is-column {
      justify-content: space-between;

      .layout-setting__item {
        width: 130px;
        height: 130px;
        margin-bottom: 10px;
      }
    }
  }

  &__item {
    width: 50px;
    height: 50px;
    margin-right: 15px;
    background: #fff;
    display: flex;
    position: relative;
    overflow: hidden;
    @include hover-border;

    &.is-active {
      cursor: pointer;
      @include active-style;
    }
  }

  &__color {
    width: 40px;
    height: 40px;
    margin: auto;
  }

  .layout-menu {
    flex-direction: column;
    align-items: center;
    border: 1px solid #ccc;
    margin: 0;
    @include hover-border;

    &.is-active {
      @include active-style;
    }

    &__title {
      padding: 10px;
      font-size: 14px;
      font-weight: 600;
    }

    &__wrap {
      width: 100%;
      padding: 0 10px 10px;
      flex: 1;
      display: flex;
      flex-direction: column;

      &.is-vertical {
        flex-direction: row;

        .layout-menu__top {
          width: 10px;
          height: 100%;
          margin-right: 6px;
        }
      }
    }

    &__top {
      width: 100%;
      height: 10px;
      background: #2c4674;
      border-radius: 4px;
      margin-bottom: 6px;
    }

    &__main {
      width: 100%;
      background: #ccc;
      flex: 1;
    }
  }
}