.cg-sideBar {
  width: $cg-sidebar-width;
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background-color: $cg-sidebar-bgcolor;

  &__logo {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 60px;
    width: 100%;
    padding: 10px 0;

    .logo-container {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background-color: $cg-color-primary;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 2px 8px rgba(57, 158, 150, 0.3);

      .logo-text {
        color: #ffffff;
        font-size: 26px;
        font-weight: bold;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
      }
    }
  }

  // 为菜单项设置相对定位，使得子菜单能够相对于它们定位
  .el-menu .el-menu-item {
    position: relative;
  }

  &__popover {
    position: fixed;
    z-index: 9999;
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

    &.is-hidden {
      height: 0 !important;
      min-height: 0 !important;
      overflow: hidden;
      pointer-events: none;
      opacity: 0;
    }

    &.menu-pop {
      padding: 6px 0;
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
      background-color: $cg-sidebar-submenu-bgcolor;
      min-width: unset;
      display: flex;
      min-height: 250px;
      border-radius: 8px;
      border: 0;
      width: auto !important;
      font-size: $cg-sidebar-submenu-font-size;
      backdrop-filter: blur(5px);
      overflow: hidden;
      background-image: linear-gradient(135deg, rgba(57, 158, 150, 0.05) 0%, rgba(255, 255, 255, 0.02) 50%, rgba(57, 158, 150, 0.03) 100%);
      background-size: 200% 200%;
      animation: subtle-pulse 3s infinite ease-in-out, subtle-gradient-shift 15s ease infinite;
    }

    &.is-vertical {
      left: $cg-sidebar-width;
      margin-left: 8px;
      // 使用CSS变量来存储和计算位置，减少与JS的耦合
      --menu-item-height: 60px;
      --menu-item-offset: 0px;
    }
  }

  .el-menu {
    border-right: none;
    background-color: $cg-sidebar-bgcolor;
    height: 100%;
    width: 100%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 8px 0;

    .el-menu-item {
      width: 100%;
      height: 72px;
      border-right: none;
      padding: 0 !important;
      line-height: 16px;
      user-select: none;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      margin-bottom: 8px;
      position: relative;
      overflow: hidden;

      .cg-fba {
        font-size: 24px;
      }

      .cg-caigou {
        font-size: 26px;
      }

      &.is-active {
        background: transparent;
        transform: scale(0.98);

        a {
          font-weight: 500;
          color: $cg-sidebar-current-color;
        }

        .menu-hover {
          height: 72px;
          background: rgba(57, 158, 150, 0.2);
          position: absolute;
          width: 100%;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }

      &:hover,
      &:focus {
        background: rgba(57, 158, 150, 0.08);
        transform: translateY(-2px);

        a {
          color: $cg-sidebar-current-color;
        }
      }

      a {
        width: 100%;
        height: 100%;
        font-size: $cg-sidebar-font-size;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        color: $cg-sidebar-text-color;
        line-height: 1.2;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        i,
        svg {
          font-size: 22px;
          color: inherit;
          margin-bottom: 4px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        span {
          font-size: 12px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }

      &.is-current {
        a {
          background-color: rgba(57, 158, 150, 0.25);
          color: $cg-sidebar-current-color;
          font-size: $cg-sidebar-active-font-size;
        }
      }
    }
  }
}

// 子菜单块样式
.cg-sideBar-submenu__block {
  width: var(--cg-sidebar-submenu-width, 200px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin: 0;
  position: relative;
  padding: 4px;

  &:not(:first-child) {
    border-left: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      top: 15%;
      height: 70%;
      width: 1px;
      background: linear-gradient(to bottom, transparent, rgba(57, 158, 150, 0.15), transparent);
      opacity: 0.7;
      filter: blur(0.5px);
    }
  }
}

@media only screen and (max-height: 900px) {
  .cg-layout .cg-sideBar .el-menu .el-menu-item {
    height: 64px;
  }

  .cg-layout .cg-sideBar .el-menu .el-menu-item.is-active .menu-hover {
    height: 64px;
  }

  .cg-layout .cg-sideBar .el-menu .el-menu-item a {
    top: 0;
  }
}

@media only screen and (max-height: 740px) {
  .cg-layout .cg-sideBar .el-menu .el-menu-item {
    height: 48px;
  }

  .cg-layout .cg-sideBar .el-menu .el-menu-item.is-active .menu-hover {
    height: 48px;
  }

  .cg-layout .cg-sideBar .el-menu .el-menu-item i {
    font-size: 18px;
  }
}

.menu-popover-fade-enter-active,
.menu-popover-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-popover-fade-enter-from,
.menu-popover-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

@keyframes subtle-pulse {
  0% {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }

  50% {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.18);
  }

  100% {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }
}

@keyframes subtle-gradient-shift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}