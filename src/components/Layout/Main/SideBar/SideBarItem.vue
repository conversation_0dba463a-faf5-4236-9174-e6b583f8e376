<script setup lang="ts">
import { useMenuStore } from '@/stores/Menu'

interface MeunItem {
  id?: number
  link?: string
  menu: string
  icon?: string
}

const props = defineProps({
  item: {
    type: Object as () => MeunItem,
    default: () => ({}),
  },
  tabIndex: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['routerLinkClick'])
const menuStore = useMenuStore()
const { pointer } = menuStore

/**
 * 菜单点击跳转路由
 */
function routerLinkClick(event: MouseEvent, navigate: (event: MouseEvent) => void, link?: string) {
  emit('routerLinkClick')
  if (link) {
    sessionStorage.setItem('currentRouterLink', link)
  }
  navigate(event)
}
</script>

<template>
  <div v-if="item.link" class="cg-sideBar-submenu__content">
    <RouterLink v-slot="{ navigate }" :to="item.link" custom>
      <div
        class="menu-item my-0.5 flex items-center rd px-4 py-2 text-sm transition-all duration-300" :class="[{ 'active-link': item.id === pointer.leafId }]"
        @click="event => routerLinkClick(event, navigate, item.link)"
      >
        <div v-if="item.id === pointer.leafId" class="active-indicator" />
        <span class="truncate">{{ item.menu }}</span>
      </div>
    </RouterLink>
  </div>
  <div v-else class="cg-sideBar-submenu__title mb-1 mt-2 flex items-center gap-2 px-4 py-2">
    <CgSvgIcon :icon-name="item.icon || 'menu-product'" class="menu-icon" />
    <span class="truncate font-medium">{{ item.menu }}</span>
  </div>
</template>

<style lang="scss" scoped>
// 子菜单标题样式
.cg-sideBar-submenu__title {
  animation: subtle-gradient-shift 8s ease infinite;
  background: linear-gradient(to right, rgba(57, 158, 150, 0.08), transparent);
  background-size: 200% 100%;
  color: #333;
  font-size: 14px;

  .menu-icon {
    color: rgba(57, 158, 150, 0.9);
  }
}

// 子菜单内容样式
.cg-sideBar-submenu__content {
  .menu-item {
    position: relative;
    color: #666;
    margin-left: 2px;
    cursor: pointer;

    &:hover {
      color: var(--cg-sidebar-submenu-hover-color, #399e96);
      background-color: rgba(57, 158, 150, 0.04);
    }
  }

  .active-link {
    color: var(--cg-sidebar-submenu-active-color, #399e96);
    background-color: rgba(57, 158, 150, 0.06);
    font-weight: 500;

    .active-indicator {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 3px;
      height: 70%;
      background-color: var(--cg-sidebar-submenu-active-color, #399e96);
      border-radius: 0 2px 2px 0;
    }
  }
}

@keyframes subtle-gradient-shift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
</style>
