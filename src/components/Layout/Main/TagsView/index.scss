.el-popover .tag {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 30px;
  padding: 0 8px;
  border-radius: 2px;
  color: #333;
  cursor: pointer;

  &:hover {
    color: $cg-color-primary;

    i {
      display: unset;
      color: $cg-color-primary;
    }
  }

  .el-icon {
    display: none;

    position: relative;
    right: -3px;
    border-radius: 50%;
    text-align: center;

    &:before {
      display: inline-block;
      vertical-align: middle;
    }

    &:hover {
      background-color: #f3f9f9;
    }
  }
}

.cg-tagsview {
  font-size: 12px;
  width: 100%;
  height: 100%;
  padding-top: 6px;
  // background-color: var(--cg-header-bg-color);

  &.cg-tagsview--full {
    background-color: #e1e1e6;
    padding-top: 5px;

    .cg-tagsview__item {
      color: #373742;

      &.is-active {
        color: var(--cg-tagview-item-active-color);
      }

      &:hover {
        color: var(--cg-tagview-item-active-color);
        // .el-icon-close {
        //   visibility: visible;
        // }
      }
    }

    .cg-tagsview__more {
      color: #373742;
    }

    .cg-tagsview__line {
      background-color: #bdbdbd;
      height: 15px;
    }
  }

  &__wrap {
    height: 100%;
  }

  &__more {
    display: inline-block;
    width: 50px;
    text-align: center;
    vertical-align: middle;
    font-size: 14px;
    cursor: pointer;
    color: var(--cg-sidebar-text-color);

    &:hover {
      color: var(--cg-tagview-item-active-color);
    }
  }

  &__line {
    position: absolute;
    width: 1px;
    display: inline-block;
    right: -1px;
    background-color: var(--cg-tagview-line-color);
    height: 16px;
    visibility: visible;
  }

  &__item {
    min-width: 120px;
    display: inline-flex;
    align-items: center;
    justify-content: space-around;
    position: relative;
    padding: 0 10px;
    cursor: pointer;
    height: 100%;
    color: var(--cg-sidebar-text-color);
    user-select: none;
    margin: 0;
    border-radius: 6px 6px 0 0;

    &:hover {
      color: var(--cg-sidebar-submenu-active-color);

      .cg-tagsview__close {
        display: inline-block;
        visibility: visible;
      }
    }

    &:first-of-type {
      margin-left: 6px;
    }

    &.is-active {
      color: var(--cg-tagview-item-active-color);
      background-color: var(--cg-tagview-item-active-bg-color);

      .cg-tagsview__line {
        visibility: hidden;
      }

      .cg-tagsview__close {
        display: inline-block;
        visibility: visible;
      }
    }

    &.home {
      min-width: 70px;
      padding: 0;
    }
  }

  &__close {
    font-weight: bold;
    margin-left: 10px;
    width: 16px;
    height: 16px;
    display: inline-block;
    text-align: center;
    visibility: hidden;

    &:hover {
      background-color: #d4ebe9;
      color: $cg-color-primary;
      border-radius: 50%;
    }

    &:before {
      vertical-align: middle;
    }
  }

  &__contextmenu {
    margin: 0;
    background: #fff;
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: #333;
    box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);

    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;

      &:hover {
        background: #eee;
      }
    }
  }
}