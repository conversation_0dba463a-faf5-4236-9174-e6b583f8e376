<script setup lang="ts">
import type { RouteLocationNormalized } from 'vue-router'
import { addEvent, removeEvent } from '@/common/Events'
import { useTagViewStore } from '@/stores/TagsView'
import { isNavigationFailure, NavigationFailureType } from 'vue-router'

// 引用
const routerLinkRef = ref<any[]>([])
const el = ref<HTMLElement | null>(null)

// 状态定义
const currentRouterLinkIndex = ref(0)
const contextmenuVisible = ref(false)
const top = ref(0)
const left = ref(0)
const selectedTag = ref<any>({})
const affixTags = ref<any[]>([])
const popoverVisible = ref(false)
const visibleLength = ref(0)

// store 和 router
const route = useRoute()
const router = useRouter()
const tagViewStore = useTagViewStore()

// 计算属性
const visitedViews = computed(() => tagViewStore.visitedViews)
const showViews = computed(() => visitedViews.value.slice(0, visibleLength.value))
const hideViews = computed(() => visitedViews.value.slice(visibleLength.value))

// 生命周期钩子
onMounted(() => {
  addEvent(document.documentElement, 'click', closeMenu)
  getVisibleLength()

  nextTick(() => {
    addTags()
    addEvent(window, 'resize', getVisibleLength)
  })
})

onUnmounted(() => {
  removeEvent(document.documentElement, 'click', closeMenu)
  removeEvent(window, 'resize', getVisibleLength)
})

// 监听路由和标签变化
watch([route, visitedViews], () => {
  currentRouterLinkIndex.value = visitedViews.value.findIndex(item => item.path === route.path)
}, { immediate: true, deep: true })

// 监听路由变化
watch(route, () => {
  addTags()
  moveToCurrentTag()
})

// 方法
function routerLinkClick(event: MouseEvent, navigate: (event: MouseEvent) => void) {
  // 延迟加载，解决dom渲染阻塞卡顿
  setTimeout(() => {
    navigate(event)
  }, 20)
}

function isActive(routeToCheck: RouteLocationNormalized) {
  return routeToCheck.path === route.path
}

function isAffix(tag: any) {
  return tag.meta?.affix
}

function getVisibleLength() {
  const width = window.innerWidth > 1300 ? window.innerWidth : 1300
  visibleLength.value = Math.floor((width - 360) / 120)

  tagViewStore.sortVisitedView({
    route,
    visibleLength: visibleLength.value - 1,
  })
}

function addTags() {
  const { name } = route
  if (name && name !== 'Home' && name !== 'Redirect') {
    tagViewStore.addView({
      route,
      visibleLength: visibleLength.value - 1,
    })
  }
  return false
}

function moveToCurrentTag() {
  if (routerLinkRef.value) {
    nextTick(() => {
      for (const tag of routerLinkRef.value) {
        if (tag.to.path === route.path) {
          if (tag.to.fullPath !== route.fullPath)
            tagViewStore.updateVisitedView(route)

          break
        }
      }
    })
  }
}

function refreshSelectedTag(view: RouteLocationNormalized) {
  tagViewStore.delCachedView(route).then(() => {
    const { fullPath } = view
    nextTick(() => {
      router.replace({
        path: `/redirect${fullPath}`,
      })
    })
  })
}

async function closeSelectedTag(viewToClose: RouteLocationNormalized) {
  const initiallyActive = isActive(viewToClose)

  if (initiallyActive) {
    const visited = [...tagViewStore.visitedViews]
    const currentIndex = visited.findIndex(v => v.fullPath === viewToClose.fullPath)
    let navigationTargetRoute: any = null

    if (currentIndex === -1) {
      await tagViewStore.delView(viewToClose)
      return
    }

    if (visited.length === 1) {
      navigationTargetRoute = { path: '/' }
    }
    else if (currentIndex < visited.length - 1) {
      navigationTargetRoute = visited[currentIndex + 1]
    }
    else {
      navigationTargetRoute = visited[currentIndex - 1]
    }

    let navigationAllowed = false
    const failure = await router.push({
      path: navigationTargetRoute.path,
      query: navigationTargetRoute.query,
      params: navigationTargetRoute.params,
    })

    if (!isNavigationFailure(failure)) {
      navigationAllowed = true
    }
    else {
      if (
        failure.type.toString() !== NavigationFailureType.aborted.toString() // aborted: '4'
        && failure.type.toString() !== NavigationFailureType.cancelled.toString() // cancelled: '8'
      ) {
        navigationAllowed = true
      }
    }

    if (navigationAllowed) {
      await tagViewStore.delView(viewToClose)
    }
  }
  else {
    await tagViewStore.delView(viewToClose)
  }
}

function closeOthersTags() {
  tagViewStore.delOthersViews(selectedTag.value).then(() => {
    router.push(selectedTag.value.fullPath)
  })
}

function closeAllTags(view: RouteLocationNormalized) {
  const index = previousViewIndex(view)
  tagViewStore.delAllViews().then((result: any) => {
    if (affixTags.value.some((tag: any) => tag.path === view.path))
      return

    toView(result.visitedViews, index)
  })
}

function previousViewIndex(view: RouteLocationNormalized) {
  return tagViewStore.visitedViews.findIndex(visitedView => visitedView.path === view.path)
}

function toView(visitedViews: any, index: number) {
  let toView: any
  const currentIndex = index - 1
  if (currentIndex >= 0) {
    toView = visitedViews[currentIndex]
    currentRouterLinkIndex.value = currentIndex
  }
  if (toView) {
    setTimeout(() => {
      router.push(toView.fullPath)
    }, 0)
  }
  else {
    currentRouterLinkIndex.value = -1
    router.push('/')
  }
}

/**
 * 右键打开菜单栏
 */
function openMenu(tag: any, e: any) {
  const menuMinWidth = 100
  const offsetLeft = el.value?.getBoundingClientRect().left || 0
  const offsetWidth = el.value?.offsetWidth || 0
  const maxLeft = offsetWidth - menuMinWidth
  const leftPos = e.clientX - offsetLeft
  if (leftPos > maxLeft)
    left.value = maxLeft
  else
    left.value = leftPos

  top.value = e.clientY
  contextmenuVisible.value = true
  selectedTag.value = tag
}

function closeMenu() {
  contextmenuVisible.value = false
}
</script>

<template>
  <div ref="el" class="cg-tagsview">
    <div class="cg-tagsview__wrap">
      <RouterLink
        v-for="(tag, index) in showViews" v-slot="{ navigate }" ref="routerLinkRef" :key="tag.path" custom
        :to="{ path: tag.path, query: tag.query }" @click.middle="!isAffix(tag) ? closeSelectedTag(tag) : ''"
      >
        <span
          class="cg-tagsview__item" :class="currentRouterLinkIndex === index ? 'is-active' : ''"
          @contextmenu.prevent="openMenu(tag, $event)" @click="event => routerLinkClick(event, navigate)"
        >
          <span class="cg-tagsview__title">{{ tag.title }}</span>
          <span v-if="!isAffix(tag)" class="cg-tagsview__close" @click.prevent.stop="closeSelectedTag(tag)">
            <font-awesome-icon icon="fa-solid fa-xmark" />
          </span>
          <span class="cg-tagsview__line" />
        </span>
      </RouterLink>
      <ElPopover
        v-if="hideViews.length" v-model:visible="popoverVisible" popper-class="tag-popover" placement="bottom"
        :show-after="500" trigger="hover"
      >
        <ElScrollbar :max-height="600">
          <RouterLink
            v-for="tag in hideViews" :key="tag.path" :to="{ path: tag.path, query: tag.query }"
            @click="popoverVisible = false"
          >
            <span class="tag">
              <span>{{ tag.title }}</span>
              <span class="cg-tagsview__close" @click.prevent.stop="closeSelectedTag(tag)">
                <font-awesome-icon icon="fa-solid fa-xmark" />
              </span>
            </span>
          </RouterLink>
        </ElScrollbar>
        <!-- 滚动条 -->
        <template #reference>
          <span class="cg-tagsview__more">
            <ElIcon>
              <IEpMoreFilled />
            </ElIcon>
          </span>
        </template>
      </ElPopover>
    </div>
    <ul v-show="contextmenuVisible" :style="{ left: `${left}px`, top: `${top}px` }" class="cg-tagsview__contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">
        刷新当前页面
      </li>
      <li v-if="!isAffix(selectedTag) && !selectedTag.isHome" @click="closeSelectedTag(selectedTag)">
        关闭当前页面
      </li>
      <li v-if="!selectedTag.isHome" @click="closeOthersTags">
        关闭其他页面
      </li>
      <li v-if="!selectedTag.isHome" @click="closeAllTags(selectedTag)">
        关闭全部页面
      </li>
    </ul>
  </div>
</template>

<style lang="scss" scoped src="./index.scss" />

<style lang="scss">
.el-popover.tag-popover {
  padding: 0;
  min-width: 110px;
  font-size: 12px;
  width: unset !important;
}
</style>
