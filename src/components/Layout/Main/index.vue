<script setup lang="ts">
import { useCacheStore } from '@/stores/Cache'
import { useTagViewStore } from '@/stores/TagsView'
import { useRoute } from 'vue-router'
import Header from './Header/index.vue'
import SideBar from './SideBar/index.vue'

defineOptions({
  name: 'MainLayout',
  inheritAttrs: false,
})

const route = useRoute()
const key = computed(() => route.path)
const tagViewStore = useTagViewStore()
const cacheStore = useCacheStore()

onMounted(() => {
  cacheStore.fetchPlatformList()
})
</script>

<template>
  <div class="cg-layout">
    <div class="cg-layout__container">
      <div class="cg-layout__sidebar">
        <SideBar />
      </div>
      <div class="cg-layout__main">
        <div class="cg-layout__top">
          <Header />
        </div>
        <div class="cg-layout__right">
          <RouterView v-slot="{ Component }">
            <KeepAlive :include="tagViewStore.cachedViews">
              <Component :is="Component" :key="key" />
            </KeepAlive>
          </RouterView>
        </div>
      </div>
    </div>
  </div>
</template>

<style src="./index.scss" lang="scss" />
