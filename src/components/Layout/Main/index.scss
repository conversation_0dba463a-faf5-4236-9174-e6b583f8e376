// @import "../../styles//transition.scss";
.cg-layout {
  width: 100%;
  height: 100%;
  min-width: auto;
  position: relative;
  box-sizing: border-box;

  &__container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
  }

  &__sidebar {
    width: $cg-sidebar-width;
    min-width: $cg-sidebar-width;
    background-color: $cg-sidebar-bgcolor;
    height: 100%;
    z-index: 10;

    @media screen and (max-width: 1024px) {
      position: fixed;
      left: 0;
      top: 0;
      bottom: 0;
      z-index: 1000;
    }
  }

  &__main {
    width: 100%;
    height: 100%;
    flex-direction: column;
    overflow-x: hidden;
    display: flex;
    flex: 1;
    z-index: 0;
  }

  &__top {
    height: $cg-header-height;
    width: 100%;
    background: #ffffff;
    position: relative;
    box-sizing: border-box;
    // box-shadow: $cg-header-box-shadow;
    // z-index: 15;
  }

  &__right {
    height: 100%;
    background: $cg-layout-right-bgcolor;
    overflow: auto;
    flex: 1;
    position: relative;
    box-sizing: border-box;

    > div {
      background-color: #ffffff;
    }
  }

  .cg-drawer__overlay {
    margin-left: $cg-sidebar-width;
    background-color: rgba(0, 0, 0, 0.5);

    @media screen and (max-width: 1024px) {
      margin-left: 0;
    }
  }
}

@media screen and (max-width: 1024px) {
  .cg-layout {
    &__main {
      margin-left: $cg-sidebar-width;
    }
  }
}