<script setup lang="ts">
defineOptions({
  name: 'Cg<PERSON>ontainer',
  inheritAttrs: false,
})

const props = withDefaults(defineProps<Props>(), {
  showHeader: true,
  showFooter: false,
  contentBackground: '#fff',
  headerBackground: '#fff',
  footerBackground: '#fff',
  contentMargin: '0 0 0 0',
  contentRadius: '0',
  contentShadow: 'none',
  containerClass: '',
  contentClass: '',
  contentMinHeight: 'auto',
})

const emit = defineEmits(['resize'])

interface Props {
  /** 是否显示头部 */
  showHeader?: boolean
  /** 是否显示底部 */
  showFooter?: boolean
  /** 内容区域背景色 */
  contentBackground?: string
  /** 头部背景色 */
  headerBackground?: string
  /** 底部背景色 */
  footerBackground?: string
  /** 内容区域边距 */
  contentMargin?: string
  /** 内容区域圆角 */
  contentRadius?: string
  /** 内容区域阴影 */
  contentShadow?: string
  /** 自定义容器类名 */
  containerClass?: string
  /** 自定义内容区域类名 */
  contentClass?: string
  /** 内容区域最小高度 */
  contentMinHeight?: string
}

const slots = useSlots()
const containerRef = ref<HTMLElement | null>(null)

const hasHeaderLeft = computed(() => !!slots['header-left'])
const hasHeaderCenter = computed(() => !!slots['header-center'])
const hasHeaderRight = computed(() => !!slots['header-right'])
const hasMainTop = computed(() => !!slots['main-top'])
const hasFooter = computed(() => !!slots.footer && props.showFooter)

const mainStyle = computed(() => {
  let margin = props.contentMargin
  if (!hasFooter.value) {
    const marginParts = margin.split(' ')
    if (marginParts.length === 4) {
      marginParts[2] = '0' // 将底部 margin 设为 0
      margin = marginParts.join(' ')
    }
    else if (marginParts.length === 3) {
      marginParts[2] = '0' // 将底部 margin 设为 0
      margin = marginParts.join(' ')
    }
    else if (marginParts.length === 2) {
      marginParts[0] = '0' // 将上下 margin 设为 0
      margin = marginParts.join(' ')
    }
  }

  return {
    background: props.contentBackground,
    margin,
    borderRadius: props.contentRadius,
    boxShadow: props.contentShadow,
    minHeight: props.contentMinHeight,
  }
})

const headerStyle = computed(() => ({
  background: props.headerBackground,
  marginBottom: '2px',
}))

const footerStyle = computed(() => ({
  background: props.footerBackground,
}))

let resizeObserver: ResizeObserver | null = null
onMounted(() => {
  if (containerRef.value && window.ResizeObserver) {
    resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        emit('resize', entry)
      }
    })
    resizeObserver.observe(containerRef.value)
  }
})

onBeforeUnmount(() => {
  if (resizeObserver && containerRef.value) {
    resizeObserver.unobserve(containerRef.value)
    resizeObserver.disconnect()
  }
})
</script>

<template>
  <div
    ref="containerRef" class="cg-container relative box-border h-full w-full flex flex-col overflow-hidden"
    :class="[props.containerClass]"
  >
    <ElContainer class="h-full min-h-0 w-full flex flex-col">
      <!-- 头部区域 -->
      <ElHeader v-if="props.showHeader" height="auto" :style="headerStyle">
        <div class="cg-page-header w-full flex items-center gap-4">
          <div v-if="hasHeaderLeft" class="cg-page-header__left mr-auto flex items-center">
            <slot name="header-left" />
          </div>
          <div v-if="hasHeaderCenter" class="cg-page-header__center flex items-center justify-center">
            <slot name="header-center" />
          </div>
          <div v-if="hasHeaderRight" class="cg-page-header__right ml-auto flex items-center">
            <slot name="header-right" />
          </div>
        </div>
      </ElHeader>

      <!-- 主内容区域 -->
      <ElMain
        :style="mainStyle"
        class="relative z-1 box-border h-full min-h-0 w-full flex flex-1 flex-col overflow-auto"
        :class="[props.contentClass]"
      >
        <div v-if="hasMainTop" class="mb-4">
          <slot name="main-top" />
        </div>
        <div class="min-h-0 w-full flex flex-1 flex-col">
          <slot name="main" />
        </div>
      </ElMain>

      <!-- 底部区域 -->
      <ElFooter v-if="hasFooter" height="auto" :style="footerStyle">
        <slot name="footer" />
      </ElFooter>
    </ElContainer>
  </div>
</template>

<style lang="scss" scoped>
.cg-container {

  :deep(.el-container) {
    height: 100%;
    min-height: 0;
    /* 防止内容溢出 */
  }

  :deep(.el-header) {
    width: 100%;
    padding: 10px 20px;
    padding-bottom: 7px;
  }

  :deep(.el-main) {
    padding: 0 12px 10px 12px;
    background-color: $cg-layout-right-bgcolor;
  }

  /* 基于主题色的阴影效果 */
  &.cg-container--shadowed .el-main {
    box-shadow: 0 2px 12px 0 rgba(57, 158, 150, 0.1);
  }

  /* 精简模式 */
  &.cg-container--slim {

    .el-header,
    .el-main,
    .el-footer {
      padding: 8px !important;
    }

    .cg-main-top {
      margin-bottom: 8px;
    }
  }
}
</style>
