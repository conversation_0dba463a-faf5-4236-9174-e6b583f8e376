<script setup lang="ts">
import type { CategoryVO as OriginalCategoryVO } from '@/apiv2/product'
import { categoryApi } from '@/api.services/product.service'

interface CategoryVO extends OriginalCategoryVO {
  _childrenIncomplete?: boolean // 标记节点的子节点列表是否不完整
}

defineOptions({
  name: 'CategoryTreeSelect',
  inheritAttrs: false,
})

const props = defineProps({
  modelValue: {
    type: [Number, Array],
    default: undefined,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  showCheckbox: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '请选择上级类目',
  },
  leafOnly: {
    type: Boolean,
    default: false,
  },
  pathSeparator: {
    type: String,
    default: '>',
  },
  isEditing: {
    type: Boolean,
    default: true,
  },
  checkStrictly: {
    type: Boolean,
    default: true,
  },
  lazy: {
    type: Boolean,
    default: true,
  },
})

const emits = defineEmits<{
  'update:modelValue': [value: number | number[] | undefined]
  'change': [id: number | number[] | undefined, name: string | string[] | undefined]
}>()

const modelValue = useVModel(props, 'modelValue', emits)

const categoryTreeData = ref<CategoryVO[]>([])
const isSearching = ref(false)
const originalTreeData = ref<CategoryVO[]>([])
const treeDataLoading = ref(false)
const lastSearchKeyword = ref('')

const isTreeDataLoaded = ref(false)

// 缓存已计算的类目路径，避免 lazy 时闪烁
const categoryPathCache = ref<Record<number, string>>({})
const parentIdCache = ref<Record<number, number | null>>({})

// 将第三级及以下节点标记为非叶子节点，支持懒加载
function markNonLeafNodes(nodes: CategoryVO[]) {
  if (!nodes || !Array.isArray(nodes)) {
    return
  }

  nodes.forEach((node) => {
    if (!node) {
      return
    }

    if (node.level !== undefined && node.level <= 3 && node.isLeaf !== true) {
      node.isLeaf = false
    }

    if (node.children && node.children.length > 0) {
      markNonLeafNodes(node.children)
    }
  })
}

// 查找节点名称的通用函数
function findNodeName(nodes: CategoryVO[], targetId: number): string | null {
  for (const node of nodes) {
    if (node.id === targetId)
      return node.categoryName || `[ID:${targetId}]`
    if (node.children) {
      const foundName = findNodeName(node.children, targetId)
      if (foundName)
        return foundName
    }
  }
  return null
}

// 获取节点名称，先从树中查找，找不到则返回ID占位符
function findNodeNameById(nodeId: number): string {
  const nameInOriginal = findNodeName(originalTreeData.value, nodeId)
  if (nameInOriginal)
    return nameInOriginal

  const nameInCurrent = findNodeName(categoryTreeData.value, nodeId)
  if (nameInCurrent)
    return nameInCurrent

  return `[ID:${nodeId}]`
}

function findNodePathArray(nodes: CategoryVO[] | undefined, id: number, currentPath: string[] = []): string[] | null {
  if (!nodes)
    return null
  for (const node of nodes) {
    const currentSegment = node.categoryName || `[ID:${node.id}]`
    const newPath = [...currentPath, currentSegment]
    if (node.id === id) {
      return newPath
    }
    if (node.children && node.children.length > 0) {
      const foundPath = findNodePathArray(node.children, id, newPath)
      if (foundPath) {
        return foundPath
      }
    }
  }
  return null
}

function getPathString(id: number | undefined): string {
  if (id === undefined || id === null) {
    return ''
  }

  // 缓存命中
  if (categoryPathCache.value[id]) {
    return categoryPathCache.value[id]
  }

  const pathArray = findNodePathArray(originalTreeData.value, id) ?? findNodePathArray(categoryTreeData.value, id)
  if (pathArray) {
    const path = pathArray.join(props.pathSeparator)
    categoryPathCache.value[id] = path
    return path
  }

  const pathArrayCurrent = findNodePathArray(categoryTreeData.value, id)
  if (pathArrayCurrent) {
    const path = pathArrayCurrent.join(props.pathSeparator)
    categoryPathCache.value[id] = path
    return path
  }

  // 尝试使用 parentIdCache 构建临时路径
  if (parentIdCache.value[id] !== undefined) {
    const tempPathSegments: string[] = []
    let currentId: number | null | undefined = id
    const visitedIds = new Set<number>() // 防止循环引用

    // 从当前节点向上构建路径
    while (currentId && !visitedIds.has(currentId)) {
      visitedIds.add(currentId)
      tempPathSegments.unshift(findNodeNameById(currentId))
      currentId = parentIdCache.value[currentId]
    }
    return tempPathSegments.join(props.pathSeparator)
  }

  // 只返回可查到的名称，数据加载由watcher负责
  return findNodeNameById(id)
}

function getMultiplePathString(ids: number[]): string {
  if (!ids || ids.length === 0) {
    return ''
  }

  return ids.map(id => getPathString(id)).join(', ')
}

function findNode(nodes: CategoryVO[] | undefined, id: number): CategoryVO | null {
  if (!nodes)
    return null
  for (const node of nodes) {
    if (node.id === id) {
      return node
    }
    if (node.children && node.children.length > 0) {
      const found = findNode(node.children, id)
      if (found) {
        return found
      }
    }
  }
  return null
}

async function loadCategoryTree() {
  treeDataLoading.value = true
  isTreeDataLoaded.value = false
  try {
    const response = (await categoryApi.getCategoryTree()).data
    if (response.success && response.data) {
      const processNodes = (nodes: OriginalCategoryVO[], parentId: number | null = null): CategoryVO[] => {
        return nodes.map(node => ({
          ...node,
          parentId: node.parentId as number ?? parentId,
          children: node.children ? processNodes(node.children, node.id) : undefined,
        }))
      }

      const processedData = processNodes(response.data)

      if (props.lazy && Array.isArray(processedData)) {
        markNonLeafNodes(processedData)
      }

      categoryTreeData.value = processedData
      originalTreeData.value = JSON.parse(JSON.stringify(processedData))

      // 预填充 parentIdCache
      const buildParentCache = (nodes: CategoryVO[]) => {
        nodes.forEach((node) => {
          if (node.id !== undefined && node.parentId !== undefined) {
            parentIdCache.value[node.id] = node.parentId
          }
          if (node.children) {
            buildParentCache(node.children)
          }
        })
      }
      buildParentCache(originalTreeData.value)
    }
    else {
      categoryTreeData.value = []
      originalTreeData.value = []
    }
  }
  catch (error) {
    console.error('获取类目树数据失败:', error)
    categoryTreeData.value = []
    originalTreeData.value = []
  }
  finally {
    treeDataLoading.value = false
    isTreeDataLoaded.value = true
  }
}

function isNodeLeaf(data: CategoryVO, node: any): boolean {
  if (!node || node.level === undefined) {
    return data.isLeaf === true || (!data.children || data.children.length === 0)
  }

  if (node.level >= 4) {
    return true
  }

  if (data.isLeaf === true) {
    return true
  }

  if (props.lazy && node.level === 3) {
    return false
  }
  return !data.children || data.children.length === 0
}

function isNodeDisabled(data: CategoryVO, node: any): boolean {
  if (props.disabled) {
    return true
  }

  if (!props.leafOnly) {
    return false
  }

  if (data.isLeaf) {
    return !data.isLeaf
  }

  if (props.lazy) {
    if (!node || node.level === undefined) {
      return true
    }
    return node.level < 4
  }

  const isLeaf = !data.children || data.children.length === 0
  return !isLeaf
}

async function lazyLoad(node: any, resolve: (data: CategoryVO[]) => void) {
  if (!node.data || node.data.id === undefined) {
    resolve([])
    return
  }

  const parentId = node.data.id

  const hasIncompleteChildren = node.data._childrenIncomplete === true

  if (node.data.children && node.data.children.length > 0 && !hasIncompleteChildren) {
    resolve(node.data.children)
    return
  }

  try {
    const res = (await categoryApi.getChildCategories({ id: node.data.id })).data
    if (res.success && res.data) {
      const childrenNodes: CategoryVO[] = res.data.map((item: OriginalCategoryVO) => {
        const childNode = {
          ...item,
          parentId: item.parentId ?? parentId,
        }
        if (childNode.id !== undefined && childNode.parentId !== undefined) {
          parentIdCache.value[childNode.id] = childNode.parentId
        }
        return childNode
      })

      // 合并新加载的节点与现有节点
      if (node.data.children && node.data.children.length > 0) {
        const existingIds = new Set(node.data.children.map((child: CategoryVO) => child.id))
        for (const newNode of childrenNodes) {
          if (!existingIds.has(newNode.id)) {
            node.data.children.push(newNode)
          }
        }
      }
      else {
        node.data.children = childrenNodes
      }

      // 移除不完整标记
      if (node.data._childrenIncomplete) {
        node.data._childrenIncomplete = false
      }

      resolve(node.data.children)
    }
    else {
      resolve(node.data.children || [])
    }
  }
  catch (error) {
    console.error('懒加载节点失败:', error)
    ElMessage.error('懒加载节点失败')
    resolve(node.data.children || [])
  }
}

// 只加载详情，避免递归API
async function loadCategoryDetail(categoryId: number): Promise<CategoryVO | null> {
  try {
    const response = (await categoryApi.getCategory({ id: categoryId })).data
    if (response.success && response.data) {
      const categoryDetail: CategoryVO = response.data
      // 确保缓存 parentId
      if (categoryDetail.id !== undefined && categoryDetail.parentId !== undefined) {
        parentIdCache.value[categoryDetail.id] = categoryDetail.parentId
      }
      return categoryDetail
    }
  }
  catch (error) {
    console.error('加载类目详情失败:', error)
  }
  return null
}

/**
 * 缓存节点的父ID关系
 */
function cacheParentId(category: CategoryVO) {
  if (category.id !== undefined && category.parentId !== undefined) {
    parentIdCache.value[category.id] = category.parentId
  }
}

/**
 * 将节点添加到父节点的子节点列表中
 */
function addNodeToParent(parentNode: CategoryVO, category: CategoryVO) {
  if (!parentNode.children) {
    parentNode.children = []
  }

  const existingIndex = parentNode.children.findIndex(child => child.id === category.id)
  if (existingIndex === -1) {
    parentNode.children.push({ ...category, parentId: category.parentId })
  }
  else {
    parentNode.children[existingIndex] = { ...category, parentId: category.parentId }
  }

  // 第3级节点懒加载标记
  if (parentNode.level === 3 && props.lazy && parentNode.isLeaf !== true) {
    parentNode.isLeaf = false
    parentNode._childrenIncomplete = true
  }
}

/**
 * 创建占位父节点并添加到树中
 */
function createPlaceholderParent(category: CategoryVO) {
  if (!category.parentId)
    return null

  const placeholderParent: CategoryVO = {
    id: category.parentId,
    categoryName: `[ID:${category.parentId}]`,
    parentId: undefined, // 暂时不知道上级，后续加载时会更新
    level: category.level ? category.level - 1 : 3, // 假设是上一级
    isLeaf: false,
    _childrenIncomplete: true, // 标记为不完整，需要后续加载
    children: [{ ...category, parentId: category.parentId }], // 直接将当前节点作为子节点
  }

  // 将占位父节点添加到树中
  categoryTreeData.value.push(placeholderParent)
  console.warn(`Created placeholder parent node for category ID:${category.id} with parent ID:${category.parentId}`)

  return placeholderParent
}

/**
 * 递归查找父节点并插入
 */
async function addCategoryToTree(category: CategoryVO) {
  if (!category || category.id === undefined)
    return

  // 缓存父ID关系
  cacheParentId(category)

  // 1-3级节点不需要添加到树中，只需要缓存parentId
  if (category.level !== undefined && category.level < 4) {
    return
  }

  if (category.parentId) {
    const parentNode = findNode(categoryTreeData.value, category.parentId)

    if (parentNode) {
      // 父节点存在，将当前节点添加到父节点的子节点列表中
      addNodeToParent(parentNode, category)
    }
    else {
      // 父节点不存在，创建占位父节点
      createPlaceholderParent(category)
    }
  }
}

/**
 * 确保树中存在 category 及其全部祖先
 */
async function ensureCategoryInTree(category: CategoryVO) {
  if (!category || category.id === undefined)
    return

  // 缓存父ID关系
  cacheParentId(category)

  // 1-3级节点不需要添加到树中，只需要缓存parentId
  if (category.level !== undefined && category.level < 4) {
    return
  }

  // 递归地先处理父节点
  if (category.parentId) {
    const parent = findNode(categoryTreeData.value, category.parentId)
      || findNode(originalTreeData.value, category.parentId)

    if (!parent) {
      // 父节点不存在，需要先加载父节点
      const parentDetail = await loadCategoryDetail(category.parentId)
      if (parentDetail) {
        // 递归确保父节点的祖先链也存在
        await ensureCategoryInTree(parentDetail)
      }
    }
  }

  // 此时父节点应该已经在树中，或者是根节点
  await addCategoryToTree(category)
}

watch(modelValue, async (newVal) => {
  if (newVal === undefined || newVal === null)
    return

  if (!isTreeDataLoaded.value && treeDataLoading.value) {
    await new Promise<void>((resolve) => {
      const unwatch = watch(treeDataLoading, (loading) => {
        if (!loading) {
          unwatch()
          resolve()
        }
      }, { immediate: true })
    })
  }

  // 预加载缺失节点
  const preloadPathData = async (id: number) => {
    let nodeExists = !!findNode(originalTreeData.value, id)
    if (!nodeExists) {
      nodeExists = !!findNode(categoryTreeData.value, id)
    }

    if (!nodeExists) {
      const categoryDetail = await loadCategoryDetail(id)
      if (categoryDetail) {
        // ensureCategoryInTree 方法确保祖先链完整
        await ensureCategoryInTree(categoryDetail)
      }
    }
    else {
      // 节点已存在，确保其 parentId 在缓存中
      if (!parentIdCache.value[id]) {
        const node = findNode(originalTreeData.value, id) ?? findNode(categoryTreeData.value, id)
        if (node?.id !== undefined && node?.parentId !== undefined) {
          parentIdCache.value[node.id] = node.parentId
        }
      }
    }
  }

  // 多选模式
  if (Array.isArray(newVal)) {
    const idsToLoad: number[] = []
    for (const id of newVal) {
      if (typeof id === 'number') {
        let nodeExists = !!findNode(originalTreeData.value, id)
        if (!nodeExists) {
          nodeExists = !!findNode(categoryTreeData.value, id)
        }
        if (!nodeExists) {
          idsToLoad.push(id)
        }
        else {
          // 节点已存在，确保其 parentId 在缓存中
          if (!parentIdCache.value[id]) {
            const node = findNode(originalTreeData.value, id) ?? findNode(categoryTreeData.value, id)
            if (node?.id !== undefined && node?.parentId !== undefined) {
              parentIdCache.value[node.id] = node.parentId
            }
          }
        }
      }
    }

    if (idsToLoad.length > 0) {
      const loadPromises = idsToLoad.map(id => loadCategoryDetail(id))
      const categories = await Promise.all(loadPromises)
      const validCategories = categories.filter(Boolean) as CategoryVO[]

      // 使用 ensureCategoryInTree 确保祖先链完整
      for (const category of validCategories) {
        await ensureCategoryInTree(category)
      }
    }
  }
  else if (typeof newVal === 'number') {
    await preloadPathData(newVal)
  }

  if (typeof newVal === 'number') {
    const pathArr = findNodePathArray(categoryTreeData.value, newVal)
      || findNodePathArray(originalTreeData.value, newVal)
    if (pathArr) {
      categoryPathCache.value[newVal] = pathArr.join(props.pathSeparator)
    }
  }
}, { immediate: true, deep: true })

onMounted(() => {
  loadCategoryTree()
})

// 处理选择变化并过滤非叶子节点
function handleTreeSelectChange(val: number | number[] | undefined) {
  if (props.leafOnly && val !== undefined) {
    const findNode = (nodes: CategoryVO[], id: number): CategoryVO | null => {
      for (const node of nodes) {
        if (node.id === id)
          return node
        if (node.children) {
          const found = findNode(node.children, id)
          if (found)
            return found
        }
      }
      return null
    }

    const checkAndFilter = (id: number): boolean => {
      const nodeDataOriginal = findNode(originalTreeData.value, id)
      if (nodeDataOriginal) {
        if (isNodeDisabled(nodeDataOriginal, { level: nodeDataOriginal.level } as any)) {
          console.warn(`Attempted to select a disabled non-leaf node (ID: ${id}). Selection ignored.`)
          return false
        }
        return true
      }

      const nodeDataCurrent = findNode(categoryTreeData.value, id)
      if (nodeDataCurrent) {
        if (isNodeDisabled(nodeDataCurrent, { level: nodeDataCurrent.level } as any)) {
          console.warn(`Attempted to select a disabled non-leaf node (ID: ${id}, checked in current data). Selection ignored.`)
          return false
        }
        return true
      }

      if (props.lazy) {
        return true
      }

      console.warn(`Selected node (ID: ${id}) not found. Selection ignored.`)
      return false
    }

    if (Array.isArray(val)) {
      const validValues = val.filter(checkAndFilter)
      if (validValues.length !== val.length) {
        modelValue.value = validValues.length > 0 ? validValues : undefined
        if (validValues.length === 0) {
          emits('change', undefined, undefined)
          return
        }
        val = validValues
      }
      else if (validValues.length === 0) {
        emits('change', undefined, undefined)
        return
      }
    }
    else if (typeof val === 'number') {
      if (!checkAndFilter(val)) {
        modelValue.value = undefined
        emits('change', undefined, undefined)
        return
      }
    }
    else {
      emits('change', undefined, undefined)
      return
    }
  }

  if (val === undefined || (Array.isArray(val) && val.length === 0)) {
    emits('change', undefined, undefined)
    return
  }

  /**
   * 查找节点信息（ID、名称、父ID）
   */
  const findNodeInfo = (nodes: CategoryVO[], id: number): { id: number, name: string, parentId: number | null } | null => {
    for (const node of nodes) {
      if (node.id === id) {
        return { id: node.id, name: node.categoryName || `[ID:${id}]`, parentId: node.parentId ?? null }
      }
      if (node.children && node.children.length > 0) {
        const found = findNodeInfo(node.children, id)
        if (found)
          return found
      }
    }
    return null
  }

  /**
   * 获取节点信息，优先从树中查找，找不到则使用缓存的父ID
   */
  const getNodeInfo = (id: number): { id: number, name: string, parentId: number | null } | null => {
    // 先从当前树和原始树中查找
    const nodeInfo = findNodeInfo(categoryTreeData.value, id) || findNodeInfo(originalTreeData.value, id)

    if (nodeInfo) {
      return nodeInfo
    }

    // 找不到节点但有缓存的父ID
    if (parentIdCache.value[id] !== undefined) {
      return {
        id,
        name: findNodeNameById(id),
        parentId: parentIdCache.value[id],
      }
    }

    // 最后兜底
    if (typeof id === 'number' && id > 0) {
      return {
        id,
        name: `[ID:${id}]`,
        parentId: null,
      }
    }

    return null
  }

  if (Array.isArray(val)) {
    // 处理多选模式
    const results: { ids: number[], names: string[] } = { ids: [], names: [] }
    for (const id of val) {
      const nodeInfo = getNodeInfo(id)
      if (nodeInfo) {
        results.ids.push(nodeInfo.id)
        results.names.push(nodeInfo.name)
        // 确保 parentId 被缓存
        if (nodeInfo.id !== undefined && nodeInfo.parentId !== undefined && !parentIdCache.value[nodeInfo.id]) {
          parentIdCache.value[nodeInfo.id] = nodeInfo.parentId
        }
      }
    }
    if (results.ids.length > 0) {
      emits('change', results.ids, results.names)
    }
    else {
      emits('change', undefined, undefined)
    }
  }
  else {
    const nodeInfo = getNodeInfo(val)
    if (nodeInfo) {
      emits('change', nodeInfo.id, nodeInfo.name)
      // 确保 parentId 被缓存
      if (nodeInfo.id !== undefined && nodeInfo.parentId !== undefined && !parentIdCache.value[nodeInfo.id]) {
        parentIdCache.value[nodeInfo.id] = nodeInfo.parentId
      }
    }
    else {
      emits('change', undefined, undefined)
    }
  }
}

// 根据关键词搜索类目
async function performSearch(keyword: string) {
  if (!keyword) {
    if (isSearching.value) {
      categoryTreeData.value = JSON.parse(JSON.stringify(originalTreeData.value))
      isSearching.value = false
    }
    lastSearchKeyword.value = ''
    return
  }
  if (keyword === lastSearchKeyword.value) {
    return
  }

  lastSearchKeyword.value = keyword
  treeDataLoading.value = true
  if (!isSearching.value) {
    isSearching.value = true
  }

  try {
    const response = (await categoryApi.searchCategories({ keyword })).data
    // 确保在异步响应回来时，关键词仍然是用户最后输入的那个
    if (response.success && keyword === lastSearchKeyword.value) {
      // 搜索结果也需要处理 parentId 和懒加载标记
      const processNodes = (nodes: OriginalCategoryVO[], parentId: number | null = null): CategoryVO[] => {
        return nodes.map(node => ({
          ...node,
          parentId: node.parentId as number ?? parentId,
          children: node.children ? processNodes(node.children, node.id) : undefined,
        }))
      }
      const searchResultData = processNodes(response.data || [])

      if (props.lazy && Array.isArray(searchResultData)) {
        markNonLeafNodes(searchResultData)
      }

      // 缓存搜索结果节点的 parentId
      const buildParentCache = (nodes: CategoryVO[]) => {
        nodes.forEach((node) => {
          if (node.id !== undefined && node.parentId !== undefined) {
            parentIdCache.value[node.id] = node.parentId
          }
          if (node.children) {
            buildParentCache(node.children)
          }
        })
      }
      buildParentCache(searchResultData)

      categoryTreeData.value = searchResultData
    }
    else if (keyword === lastSearchKeyword.value) {
      // 搜索失败或无结果
      categoryTreeData.value = [] // 清空当前树
    }
  }
  catch (error) {
    console.error('搜索类目失败:', error)
    if (keyword === lastSearchKeyword.value) {
      categoryTreeData.value = []
    }
  }
  finally {
    if (keyword === lastSearchKeyword.value) {
      treeDataLoading.value = false
    }
  }
}

const currentPathString = computed(() => {
  if (modelValue.value === undefined || modelValue.value === null) {
    return ''
  }

  if (Array.isArray(modelValue.value)) {
    return getMultiplePathString(modelValue.value as number[])
  }
  else {
    return getPathString(modelValue.value as number)
  }
})

// 路径递归查id
function findNodeIdPath(nodes: CategoryVO[] | undefined, id: number, path: number[] = []): number[] | null {
  if (!nodes)
    return null
  for (const node of nodes) {
    if (node.id === undefined)
      continue
    const newPath = [...path, node.id]
    if (node.id === id) {
      return newPath
    }
    if (node.children) {
      const result = findNodeIdPath(node.children, id, newPath)
      if (result)
        return result
    }
  }
  return null
}

const defaultExpandedKeys = computed(() => {
  const val = modelValue.value
  if (val == null)
    return []
  const ids = (Array.isArray(val) ? val : [val]).filter((id): id is number => typeof id === 'number')
  const keysSet = new Set<number>()
  ids.forEach((id) => {
    const path = findNodeIdPath(originalTreeData.value, id)
    if (path) {
      path.slice(0, -1).forEach(key => keysSet.add(key))
    }
    else {
      const parentId = parentIdCache.value[id] ?? null

      if (parentId !== null) {
        const parentPath = findNodeIdPath(originalTreeData.value, parentId)
        if (parentPath) {
          parentPath.forEach(key => keysSet.add(key))
        }
        else {
          const currentPath = findNodeIdPath(categoryTreeData.value, id)
          if (currentPath) {
            currentPath.slice(0, -1).forEach(key => keysSet.add(key))
          }
        }
      }
      else {
        const currentPath = findNodeIdPath(categoryTreeData.value, id)
        if (currentPath) {
          currentPath.slice(0, -1).forEach(key => keysSet.add(key))
        }
        else {
          console.warn(`Cannot determine expansion path for nodeId: ${id}. Not in original tree and parentId not cached.`)
        }
      }
    }
  })
  return Array.from(keysSet)
})

const defaultCheckedKeys = computed(() => {
  const val = modelValue.value
  if (val == null)
    return []
  return Array.isArray(val) ? val : [val]
})

function filterNodeMethod(value: string, data: CategoryVO, node?: any): boolean {
  if (!value || value.trim() === '')
    return true

  const keyword = value.trim().toLowerCase()

  if ((data.categoryName ?? '').toLowerCase().includes(keyword))
    return true

  if (node?.childNodes?.length) {
    return node.childNodes.some((child: any) => child.visible)
  }

  if (props.lazy && data.isLeaf === false && data._childrenIncomplete !== false) {
    return true
  }

  return false
}
</script>

<template>
  <template v-if="isEditing">
    <ElTreeSelect
      v-model="modelValue"
      :disabled="disabled"
      :placeholder="props.placeholder"
      :data="categoryTreeData"
      :props="{
        label: 'categoryName',
        children: 'children',
        value: 'id',
        disabled: isNodeDisabled,
        isLeaf: isNodeLeaf,
      }"
      :check-strictly="props.checkStrictly"
      :default-expand-all="false"
      filterable
      remote
      :remote-method="performSearch"
      :filter-node-method="filterNodeMethod"
      :loading="treeDataLoading"
      :show-checkbox="props.showCheckbox"
      :multiple="props.showCheckbox"
      node-key="id"
      :check-on-click-node="props.showCheckbox && !props.leafOnly"
      :lazy="props.lazy"
      :load="lazyLoad"
      :default-expanded-keys="defaultExpandedKeys"
      :default-checked-keys="defaultCheckedKeys"
      collapse-tags
      collapse-tags-tooltip
      clearable
      @update:model-value="handleTreeSelectChange"
    >
      <template #label="{ value }">
        <span v-if="value" :title="currentPathString">{{ currentPathString }}</span>
      </template>
    </ElTreeSelect>
  </template>
  <template v-else>
    <div class="category-view-mode">
      <template v-if="modelValue !== undefined && modelValue !== null">
        <span :title="currentPathString">{{ currentPathString }}</span>
      </template>
      <template v-else>
        <span class="text-gray-400">{{ placeholder }}</span>
      </template>
    </div>
  </template>
</template>

<style lang="scss" scoped>
.search-form > .el-select {
  max-width: 200px;
}

:deep(.el-select__selected-item) {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.el-select__placeholder {
      color: var(--el-input-text-color, var(--el-text-color-regular)) !important;
    }
}

:deep(.el-select__selection.is-near) {
  flex-wrap: nowrap;
}

.category-view-mode {
  min-height: 32px;
  // padding: 0 12px;
  display: flex;
  align-items: center;
  line-height: 1.5;

  span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
  }

  &:hover {
    border-color: var(--el-border-color-hover);
  }
}
</style>
