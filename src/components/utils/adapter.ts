import type { BaseResponse } from './type'
import { isArray } from 'xe-utils'

/**
 * 处理 HTTP 响应
 * @param response - 后端返回的原始响应
 * @returns 转换后的响应，符合 BaseResponse 格式，并处理分页信息
 */
export function httpResponse(response: any): BaseResponse {
  if (response && response.data && response.success) {
    const { success, message, data } = response
    // 转换返回格式为baseresponse
    const responseData: BaseResponse = {
      Data: [],
      IsSuccess: success,
      Message: message,
    }
    if (isArray(data)) {
      responseData.Data = data
    }
    else if (data && Object.keys(data).length) {
      const current = data.pageNum
      const pageSize = data.pageSize
      const total = data.total
      responseData.Pagination = {
        CurrentPage: current,
        TotalCount: total,
        PageSize: pageSize,
      }
      if (data.records)
        responseData.Data = data.records || []
    }
    return responseData
  }
  else {
    // 错误情况
    return {
      Data: [],
      Pagination: {
        TotalCount: 0,
        CurrentPage: 1,
        PageSize: 10,
      },
      IsSuccess: false,
      Message: response.message || '失败',
    }
  }
}

export const httpRequest = (params: any, postParamKey?: string) => postParamKey ? { [postParamKey]: params } : params
