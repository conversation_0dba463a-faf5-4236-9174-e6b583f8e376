import type { AxiosRequestConfig } from 'axios'

/**
 * 分页信息类型定义
 */
export interface Pagination {
  /** 当前页码 */
  CurrentPage: number

  /** 每页数量 */
  PageSize: number

  /** 总条数 */
  TotalCount: number
}

export interface BaseResponse<T = any> {
  Data?: T
  Pagination?: Pagination
  /** 是否成功 */
  IsSuccess: boolean
  /** 错误消息（当IsSuccess为false时返回） */
  Message: string
}

export interface ProxyOption {
  request: ProxyRequestFunction
  query?: ProxyQueryOption
  postParam?: string
  props?: Record<string, string>
}

export interface ProxyRequestFunction {
  (params: any, options?: AxiosRequestConfig): Promise<any>
  (params: any): Promise<any>
}

export interface ProxyQueryOption {
  [key: string]: any
}

export type Transform = (data: any[], response: BaseResponse) => Array<any>
export interface Adapter {
  httpRequest?: (params: any, key?: string) => Record<string, any>
  httpResponse?: (response: any) => BaseResponse<any>
}
export interface ComponentGlobalConfig {
  // grid?: GridProps
  adapter?: Adapter
  [key: string]: any
}

export type GlobalSetup = (options?: ComponentGlobalConfig) => ComponentGlobalConfig

export interface MoreFieldMoreLineOption {
  field: string
  copy?: boolean
  style?: string
  class?: string
  label?: string
  click: (value: string, row: any, option: MoreFieldMoreLineOption | string) => void
}
