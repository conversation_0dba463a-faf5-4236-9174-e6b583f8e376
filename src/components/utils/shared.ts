import type { DictVO, PropertyVO } from '@/apiv2/product'
import type { BaseResponse } from './type'
import { isArray, isFunction, isNumber, isPlainObject, isString } from 'xe-utils'

export function transform(props: any, data: any[], response: BaseResponse) {
  const transformFn = props.proxyOption?.transform || props.transform

  const transformedData = transformFn && isFunction(transformFn)
    ? transformFn(data, response)
    : data

  return transformedData && transformedData.length
    ? transformedData
    : data
}
export function isStringNumber(val: string): boolean {
  if (!isString(val))
    return false

  return !Number.isNaN(Number(val))
}
export function addUnit(value?: string | number, defaultUnit = 'px') {
  if (!value)
    return ''
  if (isNumber(value) || isStringNumber(value))
    return `${value}${defaultUnit}`

  else if (isString(value))
    return value
}
/**
 * 检查给定的值是否为undefined、null
 * @param value 要检查的值
 * @returns 如果value为undefined、null，那么返回 true，否则返回 false
 */
export const isNullOrUndefined = (value: any) => value === null || value === undefined
/**
 * 检查给定的值是否为undefined、null、''、空对象、空数组
 * @param value 要检查的值
 * @returns 如果value为undefined、null、''、空对象、空数组，那么返回 true，否则返回 false
 */
export const isEmpty = (value: any) => (!value && value !== 0) || (isArray(value) && value.length === 0) || (isPlainObject (value) && !Object.keys(value).length)

/**
 * 获取值，如果值为 undefined、null、''，则返回默认值
 * @param value 获取的值
 * @param defaultValue 默认值  "-"
 * @returns value 或 defaultValue
 */
export const getValue = (value: string | number, defaultValue: string | number = '-') => isEmpty(value) ? defaultValue : value

/**
 * 转换字典数据为下拉选择器可用的格式
 * @param data 字典数据数组
 * @returns 转换后的选项数组，格式为 { label: string, value: number }[]
 */
export function transformDictData(data: DictVO[]) {
  if (!data || data.length === 0)
    return []
  const dictData = data[0]
  return dictData.values?.map(item => ({
    label: item.valueName,
    value: item.id,
  })) || []
}

/**
 * 转换属性数据为下拉选择器的分组格式
 * @param data 属性数据数组
 * @returns 转换后的分组选项数组，格式为 { label: string, children: { label: string, value: number, color?: string }[] }[]
 */
export function transformPropertyData(data: PropertyVO[]) {
  if (!data || data.length === 0)
    return []

  return data.map(property => ({
    label: property.propertyName,
    value: property.propertyCode, // 保留原始属性代码，可能有用
    children: property.values?.map(value => ({
      label: value.valueName,
      value: value.id,
      valueCode: value.valueCode, // 保留原始值代码
      color: (value.attrs as any)?.color, // 保留颜色属性，可以用于特殊显示
    })) || [],
  }))
}

/**
 * 生成UUID（通用唯一识别码）
 * @returns 返回格式为'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'的UUID字符串
 */
export function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}
