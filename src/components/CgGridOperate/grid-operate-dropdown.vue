<script setup lang="ts">
import type { GridOperateOptionItem } from './grid-operate'
import { ElMessageBox } from 'element-plus'
import { computed, ref } from 'vue'
import { gridOperateDropdownProps } from './grid-operate'

const props = defineProps(gridOperateDropdownProps)

const emit = defineEmits(['command'])
const dropdownMeunItems = computed(() => {
  return props.options.items ?? []
})
const visible = ref(false)
function handleCommand(command: GridOperateOptionItem) {
  const commandExec = () => {
    visible.value = false
    props.events?.command ? props.events.command(command) : emit('command', command)
  }
  if (command.confirm) {
    ElMessageBox.confirm(command.confirm.message, command.confirm.title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      closeOnClickModal: false,
      type: command.confirm.type ?? 'warning',
    }).then(() => {
      commandExec()
    }).catch(() => {
      //
    })
  }
  else {
    commandExec()
  }
}
</script>

<template>
  <ElTooltip
    v-model:visible="visible" placement="bottom" popper-class="cg-gird-dropdown" :trigger="trigger"
    effect="light"
  >
    <template #content>
      <ul class="el-dropdown-menu">
        <template v-for="(item, index) in dropdownMeunItems" :key="index">
          <li
            v-if="item.visible === undefined ? true : item.visible" class="el-dropdown-menu__item"
            @click="handleCommand(item)"
          >
            {{ item.text }}
          </li>
        </template>
      </ul>
    </template>
    <div class="el-dropdown">
      <ElButton type="primary" text>
        操作<ElIcon class="el-icon--right">
          <IEpArrowDown />
        </ElIcon>
      </ElButton>
    </div>
  </ElTooltip>
</template>

<style  lang="scss">
.cg-gird-dropdown {
  padding: 0;

  &.el-popper {
    padding: 0;
  }
  .el-dropdown-menu {
    min-width: 80px;
  }
  .el-dropdown-menu__item:not(.is-disabled):hover {
    background-color: var(--el-color-primary-light-9);
    color: var(--el-color-primary);
  }
}
</style>
