import type { TooltipTriggerType } from 'element-plus'
import type { ExtractPropTypes, PropType } from 'vue'
import type { VxeGlobalRendererHandles } from 'vxe-table'

type MessageType = '' | 'success' | 'warning' | 'info' | 'error'
export interface GridOperateOption {
  items: GridOperateOptionItem[]
}
export interface GridOperateOptionItem {
  text: string
  id?: string | number
  items?: GridOperateOptionItem[]
  confirm?: {
    title?: string
    message: string
    type?: MessageType
    callBack: (event: MouseEvent) => void
  }
  visible?: boolean
  ifRender?: (params: VxeGlobalRendererHandles.RenderTableCellParams) => boolean
  textRender?: (params: VxeGlobalRendererHandles.RenderTableCellParams) => string
  disabled?: (params: VxeGlobalRendererHandles.RenderTableCellParams) => boolean | boolean
}

export const gridOperateProps = {
  scope: {
    type: Object as PropType<VxeGlobalRendererHandles.RenderTableCellParams>,
    default: null,
  },
  options: {
    type: Object as PropType<GridOperateOptionItem>,
    default: null,
  },
  events: {
    type: Object as PropType<{ command: (scope: VxeGlobalRendererHandles.RenderTableCellParams, arg: any) => void }>,
    default: () => ({ command: () => {} }),
  },
}
export type GridOperateProps = ExtractPropTypes<typeof gridOperateProps>

export const gridOperateDropdownProps = {
  trigger: {
    type: String as PropType<TooltipTriggerType>,
    default: 'click',
  },
  options: {
    type: Object as PropType<GridOperateOptionItem>,
    default: null,
  },
  events: {
    type: Object as PropType<{ command: (arg: any, scope?: VxeGlobalRendererHandles.RenderTableCellParams) => void }>,
    default: () => ({ command: () => {} }),
  },
}
export type GridOperateDropdownProps = ExtractPropTypes<typeof gridOperateDropdownProps>
