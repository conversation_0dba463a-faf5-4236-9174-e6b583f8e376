<script setup lang="ts">
import type { GridOperateOptionItem } from './grid-operate'
import { computed } from 'vue'
import { clone } from 'xe-utils'
import { gridOperateProps } from './grid-operate'
import DropDown from './grid-operate-dropdown.vue'

defineOptions({
  name: 'CgOperator',
})
const props = defineProps(gridOperateProps)
const commandComputed = computed(() => {
  return {
    command: (cmd: any) => props.events.command(props.scope, cmd),
  }
})

const buttonOptionsComputed = computed(() => {
  const options = clone(props.options.items, true)
  const travser = (items: any) => {
    items.forEach((element: any) => {
      if (element.items?.length)
        travser(element.items)

      if (typeof element.ifRender == 'function') {
        element.visible = element.ifRender(props.scope)
      }
      else {
        if (element.visible === undefined)
          element.visible = true
      }

      if (typeof element.textRender == 'function')
        element.text = element.textRender(props.scope)
    })
  }
  travser(options)
  return options
})

/** 操作列按钮是否禁用 */
function isDisabled(option: GridOperateOptionItem): boolean {
  const { disabled } = option
  if (disabled === undefined)
    return false

  if (typeof disabled === 'boolean')
    return disabled
  else if (typeof disabled === 'function')
    return disabled(props.scope)

  return false
}
function handleClick(option: GridOperateOptionItem) {
  props.events.command(props.scope, option)
}
</script>

<template>
  <div class="cg-grid-operator__wrap">
    <template v-for="(option, index) in buttonOptionsComputed" :key="`op-dropdown${index}`">
      <template v-if="!option.items && option.visible">
        <CgButton
          :key="index"
          type="primary"
          text
          :disabled="isDisabled(option)"
          @click="handleClick(option)"
        >
          {{ option.text }}
        </CgButton>
      </template>
      <template v-else>
        <DropDown
          v-if="option.visible"
          :options="option" :events="commandComputed"
        />
      </template>
    </template>
  </div>
</template>

<style lang="scss" scoped>
  .non-pic {
  display: inline-block;
  background: #f5f5f5;
  -ms-flex-negative: 0;
  flex-shrink: 0;
  color: #d9d9d9;
  font-size: 12px;
  text-align: center;
}
:deep(.package-item .el-badge__content) {
  top: 10px;
  border-radius: 2px;
  right: 0;
}
.cg-grid-operator__wrap {
  display: inline-flex;

  i {
    color: var(--el-color-primary);
    cursor: pointer;
    margin-right: 8px;
    &::before {
      font-size: 18px;
    }
  }
  :deep(.el-button.is-text) {
    padding-left: 10px;
    padding-right: 10px;
    height: 28px;
    &:hover,
    &:focus {
      background-color: rgba(0, 91, 245, 0.06);
    }
  }
  .el-button + .el-button {
    margin-left: 0px;
  }
  :deep(.cg-dropdown-slot) {
    .el-button.is-text {
      i {
        font-size: 14px;
      }
    }
  }
}
</style>

<style>
  .el-dropdown-menu.grid-operator-dropmenu {
  min-width: 100px;
}
</style>
