<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { gridImageProps } from './grid-image'

defineOptions({
  name: 'CgGridImage',
})
const props = defineProps(gridImageProps)
const firstShow = ref(true)
const smallSrc = computed(() => {
  return props.scope?.row[props.scope.column.field] || ''
})

watch(smallSrc, () => {
  firstShow.value = true
})

const loading = ref(true)
function load() {
  loading.value = false
}

const mediumSrc = ref('')
function getMediumSrc() {
  if (firstShow.value) {
    mediumSrc.value = smallSrc.value
    firstShow.value = false
    if (mediumSrc.value !== '') {
      const image = new Image()
      image.src = mediumSrc.value
    }
  }
}
function getImageUrl(name: string) {
  return new URL(`/src/assets/images/${name}${name.endsWith('.svg') ? '' : '.png'}`, import.meta.url).href
}
</script>

<template>
  <div class="cg-image" :style="{ width: `${imageSize}px` }">
    <template
      v-if="
        scope.row[scope.column.field] === undefined
          || scope.row[scope.column.field] === null
          || scope.row[scope.column.field] === ''
      "
    >
      <img
        class="cg-image__table" :src="getImageUrl('empty-svg.svg')" :data-imgid="`${scope.row._X_ROW_KEY}`"
        :width="imageSize" :height="imageSize"
      >
    </template>
    <div v-else class="cg-image__wrap">
      <ElTooltip placement="right" effect="light" :show-after="150" popper-class="image-pop" @show="getMediumSrc">
        <template #content>
          <div v-loading="loading" class="cg-image__content" element-loading-background="rgba(255, 255, 255, 1)">
            <img v-if="mediumSrc !== ''" :src="mediumSrc" @load="load" @error="load">
          </div>
        </template>
        <img
          class="cg-image__table" :src="smallSrc" :data-imgid="`${scope.row._X_ROW_KEY}`" :width="imageSize"
          :height="imageSize"
        >
      </ElTooltip>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.cg-image {
  position: relative;

  &__wrap {
    display: flex;
  }

  &__table {
    object-fit: contain;
    transform: translateZ(0);
  }

  &__content {
    width: 300px;
    height: 300px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    img {
      object-fit: contain;
      width: 100%;
      height: 100%;
    }
  }
}
</style>
