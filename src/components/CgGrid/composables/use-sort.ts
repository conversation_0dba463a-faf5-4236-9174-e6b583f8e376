import type { VxeGridDefines } from 'vxe-table'
import type { GridProps } from '../grid'
import { GlobalConfig } from '@/components/utils'
import { computed } from 'vue'

export function useSort(props: GridProps, requestData: () => Promise<string>) {
  const sortMap = computed(() => {
    return Object.assign({
      orderBy: 'OrderByType',
      orderByColumn: 'OrderBy',
    }, GlobalConfig.grid?.sortMap, props.sortMap!)
  })
  const sortChange = (args: VxeGridDefines.SortChangeEventParams) => {
    if (props.proxyOption) {
      const { query = {} } = props.proxyOption
      if (props.proxyOption && !props.proxyOption.query)
        props.proxyOption.query = {}
      const { orderBy, orderByColumn } = sortMap.value

      if (args.order) {
        query[orderByColumn] = args.property
        query[orderBy] = args.order
      }
      else {
        delete query[orderBy]
        delete query[orderByColumn]
      }
      Object.assign(props.proxyOption, { query })
      requestData()
    }
  }
  return {
    sortChange,
  }
}
