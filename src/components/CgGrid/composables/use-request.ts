/* eslint-disable ts/no-use-before-define */
import type { VxeGridInstance } from 'vxe-table'
import type { Data, GridProps } from '../grid'
import { ref, unref } from 'vue'
import { clone, isFunction } from 'xe-utils'
import { useRequest as useBaseRequest } from '../../../composables/use-request'

function usePagination(props: GridProps, requestData: () => Promise<string>) {
  const currentPage = ref(1)
  const pageSize = ref(props.pageSize)
  const pageSizes = ref(props.pageSizes)
  const total = ref(0)
  const handleSizeChange = (_val: number) => {
    let next = true
    if (isFunction(props.beforePageSizeChange))
      next = props.beforePageSizeChange(1)
    if (next) {
      currentPage.value = 1
      requestData()
    }
  }
  const handleCurrentChange = (_val: number) => {
    let next = true
    if (isFunction(props.beforePageSizeChange))
      next = props.beforePageSizeChange(_val)
    if (next)
      requestData()
  }
  const resetCurrentPage = () => {
    // 分页重置
    currentPage.value = 1
  }
  return {
    currentPage,
    pageSize,
    pageSizes,
    total,
    resetCurrentPage,
    handleSizeChange,
    handleCurrentChange,
  }
}

export function useRequest(props: GridProps) {
  const vxeGridRef = ref<VxeGridInstance>()
  const loading = ref(false)
  const gridData = ref<Data>([])
  const isEmptyData = ref(false)
  const queryModelBackup = ref<Record<string, any>>({})
  const requestData = (queryModel: Record<string, any> = {}, isResetBackupQueryModel = false): Promise<string> => {
    return new Promise((resolve, reject) => {
      loading.value = true
      // 重置滚动条
      if (vxeGridRef) {
        const gridInstance = vxeGridRef.value
        const scrolle = gridInstance?.getScroll()
        gridInstance?.scrollTo(scrolle?.scrollLeft || 0, 0)
      }
      if (!props.proxyOption) {
        if (Array.isArray(props.data))
          processData(props.data, props.data.length)
        requestDone()
        return resolve('ok')
      }
      let finalParams = Object.assign({}, getBackupQueryModel(queryModel, isResetBackupQueryModel), {
        page: unref(currentPage),
        size: unref(pageSize),
      })

      if (isFunction(props.transformRequestParam)) {
        finalParams = props.transformRequestParam(clone(finalParams, true)) || finalParams
      }
      useBaseRequest(props.proxyOption, finalParams).then(({ data, response }) => {
        loading.value = false
        processData(data.value || [], response.value?.Pagination?.TotalCount || 0)
        resolve('ok')
      }).catch((err) => {
        reject(err)
      }).finally(requestDone)
    })
  }
  const { currentPage, pageSize, pageSizes, total, resetCurrentPage, handleSizeChange, handleCurrentChange } = usePagination(props, requestData)
  const processData = (data: any[], totalRecords = 0) => {
    if (props.beforeLoadData instanceof Function) {
      const result = props.beforeLoadData(data)
      if (result)
        data = result
    }

    gridData.value = [...data]
    total.value = totalRecords
  }
  const getBackupQueryModel = (queryModel: Record<string, any>, isResetBackupQueryModel = false) => {
    if (isResetBackupQueryModel || Object.keys(queryModel).length)
      queryModelBackup.value = { ...queryModel }

    return queryModelBackup.value
  }

  const requestDone = () => {
    // 不是系统默认的空数据展示，显示自定义的
    if (!props.useSystemEmptyData) {
      if (gridData.value.length === 0)
        isEmptyData.value = true
    }
    loading.value = false
    expandAllRow()
  }
  const expandAllRow = () => {
    if (props.options?.expandConfig?.expandAll)
      vxeGridRef.value?.setAllRowExpand(true)
  }

  return {
    vxeGridRef,
    loading,
    gridData,
    isEmptyData,
    requestData,
    queryModelBackup,
    currentPage,
    pageSize,
    pageSizes,
    total,
    resetCurrentPage,
    handleSizeChange,
    handleCurrentChange,
  }
}
