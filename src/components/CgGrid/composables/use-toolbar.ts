import type { Ref } from 'vue'
import type { GridProps, GridToolbarButtons, GridToolbarExportFile } from '../grid'
import { GlobalConfig } from '@/components/utils'
import { useThrottleFn } from '@vueuse/core'
import { ElNotification } from 'element-plus'
import { computed, reactive, toRefs, useSlots } from 'vue'
import { isFunction } from 'xe-utils'
import { useRequest } from '../../../composables/use-request'

export function useToolBar(props: GridProps, requestData: () => Promise<string>, queryModel: Ref<Record<string, any>>) {
  const { buttons, tools, refresh, customColumn, slots, exportFile } = toRefs(Object.assign(reactive({}), GlobalConfig.grid?.toolbarConfig, props.toolbarConfig))
  const toolsLeftSlotName = slots?.value?.toolsLeft ?? 'tools-left'
  const toolsRightSlotName = slots?.value?.toolsRight ?? 'tools-right'
  const buttonsSlotName = slots?.value?.buttons ?? 'buttons'
  const toolsSlotName = slots?.value?.tools ?? 'tools'

  const { [buttonsSlotName]: buttonsSlot, [toolsSlotName]: toolsSlot, [toolsLeftSlotName]: toolsLeftSlot, [toolsRightSlotName]: toolsRigthSlot } = useSlots()

  const getButtonType = (index: number, item: GridToolbarButtons) => {
    const type = index === 0 ? 'primary' : (item.props && 'type' in item.props) ? item.props.type : undefined
    return type
  }
  const getButtonProps = (index: number, item: GridToolbarButtons) => {
    const type = index === 0 ? 'primary' : (item.props && 'type' in item.props) ? item.props.type : undefined
    const props = Object.assign({}, item.props || {});
    (props as any).type = type // 按钮类型
    // 添加 plain 属性，当按钮类型不是 primary 时设为 true
    if (type !== 'primary')
      props.plain = true
    return props
  }
  const getButtonComponent = (item: GridToolbarButtons) => {
    return item.component ? item.component : 'CgButton'
  }

  const throttleRefreshFn = useThrottleFn(async () => {
    let proceed = true
    // 检查并执行 beforeRefresh 回调
    if (isFunction(props.beforeRefresh)) {
      try {
        const result = await props.beforeRefresh() // 支持异步
        if (result === false) {
          proceed = false
        }
      }
      catch (error) {
        console.error(error)
      }
    }

    if (proceed) {
      requestData()
    }
  }, 1500) // 保持节流

  const throttleExportFileFn = useThrottleFn(async () => {
    if (!exportFile || !exportFile.value)
      return
    const { exportProxy, permissionProxy } = exportFile.value as GridToolbarExportFile
    if (permissionProxy) {
      // const response = await permissionProxy.request(permissionProxy.query || {})
      const { response } = await useRequest(permissionProxy, permissionProxy.query || {})
      if (!response.value?.IsSuccess)
        return
    }
    if (exportProxy) {
      const { response } = await useRequest(exportProxy, { ...queryModel.value, ...exportProxy.query || {} })
      // const response = await exportProxy.request({ ...queryModel.value, ...exportProxy.query || {} })
      if (response.value?.IsSuccess) {
        ElNotification({
          title: '导出文件准备完成',
          dangerouslyUseHTMLString: true,
          message: response.value?.Message,
          type: 'success',
        })
      }
    }
  }, 1500)
  const buttonsComputed = computed(() => {
    buttons?.value?.forEach((element: any) => {
      if (typeof element.ifRender === 'function') {
        element.visible = element.ifRender()
      }
      else {
        if (element.visible === undefined)
          element.visible = true
      }
    })
    return buttons?.value ?? []
  })

  const toolBarVisible = computed(() => {
    const refreshButton = refresh?.value === true || (refresh?.value && typeof refresh.value === 'object' && 'teleport' in refresh.value)
    const hasButtons = buttons?.value && buttons.value.length > 0
    const hasTools = tools?.value && tools.value.length > 0
    const hasExportFile = !!exportFile?.value
    const hasCustomColumn = !!customColumn?.value
    const hasSlots = !!slots?.value

    return refreshButton || hasButtons || hasExportFile || hasCustomColumn || hasTools || hasSlots
  })

  const hasToolbarContent = computed(() => {
    const refreshButton = refresh?.value === true // 常规刷新
    const buttonsExist = buttonsComputed.value.length > 0
    const toolsExist = tools?.value?.length > 0
    const exportExist = !!exportFile?.value
    const customExist = !!customColumn?.value
    const slotExist = !!slots?.value
    const extraSlotExist = toolsLeftSlot || toolsRigthSlot

    return (
      refreshButton
      || buttonsExist
      || toolsExist
      || exportExist
      || customExist
      || extraSlotExist
      || slotExist
    )
  })
  return {
    buttonsSlot,
    toolsSlot,
    toolsLeftSlot,
    toolsRigthSlot,
    toolsLeftSlotName,
    toolsRightSlotName,
    toolsSlotName,
    buttonsSlotName,
    buttonsComputed,
    tools,
    exportFile,
    refresh,
    customColumn,
    toolBarVisible,
    hasToolbarContent,
    handleExportFile: throttleExportFileFn,
    handleRefresh: throttleRefreshFn,
    getButtonType,
    getButtonProps,
    getButtonComponent,
  }
}
