import type { MaybeRef, UseResizeObserverReturn } from '@vueuse/core'
import type { VxeGridInstance } from 'vxe-table'
import { useDebounceFn, useResizeObserver } from '@vueuse/core'
import { nextTick, onActivated, onDeactivated, ref, unref } from 'vue'

export function useResize(vxeGridRef: MaybeRef<VxeGridInstance | undefined>) {
  const gridWrapperRef = ref(null)
  const debouncedResize = useDebounceFn(() => {
    nextTick(() => {
      const vxeGrid = unref(vxeGridRef)
      const promise = vxeGrid?.recalculate(true)
      if (promise) {
        promise.then(() => {
          vxeGrid?.refreshScroll()
        })
      }
    })
  }, 0)
  let resizeObserverReturn: UseResizeObserverReturn | null = null
  onActivated(() => {
    resizeObserverReturn = useResizeObserver(gridWrapperRef, debouncedResize)
  })
  onDeactivated(() => {
    resizeObserverReturn?.stop()
  })
  return {
    gridWrapperRef,
  }
}
