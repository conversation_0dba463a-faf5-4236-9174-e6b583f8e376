import type { MoreFieldMoreLineOption } from '../utils'
import { ArrowDown } from '@element-plus/icons-vue'
import { ElIcon, ElPopover } from 'element-plus'
import { VxeUI } from 'vxe-table'
import { get, isBoolean, isEmpty, isFunction, isString } from 'xe-utils'
import { isNullOrUndefined } from '../utils'

interface TextTooltipProps {
  content: string
  showAfter: number
  linkButton?: boolean
  style?: string
  class?: string
  twoLine?: boolean
  onClick?: () => void
}

const defaultCopyField = ['msku', 'fnsku', 'sku', 'pAsin', 'pasin', 'spu']
const emptyText = '-'
const openDelay = 200

VxeUI.renderer.add('TableCellText', {
  renderTableDefault(renderOpts, params) {
    const { row, column } = params
    const { copy = false, twoLine = false, click, class: textClass, style } = renderOpts.props || {}
    const isCopy = copy || defaultCopyField.includes(column.field)
    const value = row[column.field]

    if (!isEmpty(value)) {
      const tempValue = `${value}`
      const tooltipProps: TextTooltipProps = {
        content: tempValue,
        showAfter: openDelay,
        linkButton: !!click,
        twoLine,
        onClick: () => { click?.(value, row) },
        class: textClass,
        style,
      }
      const tooltip = <cg-text-tooltip {...tooltipProps} />
      return isCopy
        ? (
            <cg-copy text={tempValue}>
              {tooltip}
            </cg-copy>
          )
        : tooltip
    }
    return emptyText
  },
})

VxeUI.renderer.add('TableCellImage', {
  renderTableDefault(renderOpts, params) {
    const imageSize = renderOpts.props?.imageSize ?? 36
    return <cg-grid-image imageSize={imageSize} scope={params} />
  },
})

VxeUI.renderer.add('TableCellTextOneLine', {
  renderTableDefault(renderOpts, params) {
    const { row, column } = params
    const { copy = false } = renderOpts.props || {}
    const isCopy = copy || defaultCopyField.includes(column.field)
    const value = row[column.field]

    if (!isEmpty(value)) {
      const tempValue = `${value}`
      const tooltip = <cg-text-tooltip show-after={openDelay} content={tempValue} />
      return isCopy
        ? (
            <cg-copy text={tempValue}>
              {tooltip}
            </cg-copy>
          )
        : tooltip
    }
    return emptyText
  },
})

VxeUI.renderer.add('TableCellTextTwoLine', {
  renderTableDefault(renderOpts, params) {
    const { row, column } = params
    const { copy = false } = renderOpts.props || {}
    const isCopy = copy || defaultCopyField.includes(column.field)
    const value = row[column.field]

    if (!isEmpty(value)) {
      const tempValue = `${value}`
      const tooltip = <cg-text-tooltip show-after={openDelay} content={tempValue} two-line />
      return isCopy
        ? (
            <cg-copy text={tempValue}>
              {tooltip}
            </cg-copy>
          )
        : tooltip
    }
    return emptyText
  },
})

VxeUI.renderer.add('TableCellMoreFieldMoreLine', {
  renderTableDefault(renderOpts, params) {
    const { row } = params
    const { options = [], copy = false, emptyText: fieldEmptyText, formatter } = renderOpts.props || {}
    const vNode = []
    const tempEmptyText = fieldEmptyText ?? emptyText

    const getField = (option: MoreFieldMoreLineOption | string) => isString(option) ? option : option.field
    const getValue = (option: MoreFieldMoreLineOption | string) => {
      const value = get(row, getField(option), tempEmptyText)

      return isNullOrUndefined(value) ? tempEmptyText : value
    }
    const getFormattedValue = (option: MoreFieldMoreLineOption | string) => isFunction(formatter) ? formatter(getValue(option), row, option) : getValue(option)
    const renderVNode = (tooltipProps: TextTooltipProps, copy: boolean, label: string) => {
      const { content, twoLine } = tooltipProps
      if (!label && content === tempEmptyText)
        return <div>{emptyText}</div>

      const tooltip = <cg-text-tooltip {...tooltipProps} />
      if (label) {
        const copyParams = { default: () => [<span class="mr-3">{label}</span>, tooltip] }
        return copy
          ? <cg-copy text={content}>{copyParams}</cg-copy>
          : (
              <div class={['flex  gap-3', twoLine ? 'items-start' : 'items-center']}>
                <span>
                  {label}
                  :
                  {' '}
                </span>
                {tooltip}
              </div>
            )
      }
      return copy ? <cg-copy text={content}>{tooltip}</cg-copy> : tooltip
    }
    for (const option of options) {
      if (isEmpty(option)) {
        vNode.push(emptyText)
      }
      else {
        const value = getFormattedValue(option)
        const tooltipProps: TextTooltipProps = {
          content: value,
          showAfter: openDelay,
          linkButton: !!option.click,
          twoLine: option?.twoLine,
          onClick: () => { option?.click?.(value, row, option) },
          class: option?.class,
          style: option?.style,
        }
        const label = option?.label
        const isCopy = isBoolean(option?.copy) ? option?.copy : copy || defaultCopyField.includes(option)
        vNode.push(renderVNode(tooltipProps, isCopy, label))
      }
    }
    return vNode
  },
})

VxeUI.renderer.add('TableCellMoreFieldOneLine', {
  renderTableDefault(renderOpts, params) {
    const { row } = params
    const { field } = renderOpts.props || {}
    const vNode: any = []
    field.forEach((item: any) => {
      vNode.push(<span>{get(row, item, emptyText)}</span>)
    })
    if (vNode.length === 0)
      vNode.push(<span>-</span>)

    return vNode
  },
})

VxeUI.renderer.add('TableCellOperate', {
  renderTableDefault(renderOpts, params) {
    return [
      <cg-operator
        options={renderOpts.props}
        events={renderOpts.events}
        scope={params}
      >
      </cg-operator>,
    ]
  },
})

VxeUI.renderer.add('TableCellStatus', {
  renderTableDefault(renderOpts, params) {
    const { row, column } = params
    const { options, defaultClass: tempDefaultClass } = renderOpts.props || {}
    const defaultClass: string = tempDefaultClass || ''

    const findResult = options.find((item: any) => {
      const field = item.field ? item.field : column.field
      return item.value === get(row, field)
    })
    if (findResult)
      return [<span class={findResult.class}>{findResult.label}</span>]

    else if (defaultClass)
      return [<span class={defaultClass}>{get(row, column.field)}</span>]

    else
      return [<span>{row[column.field]}</span>]
  },
})

VxeUI.renderer.add('TableCellKeyValueList', {
  renderTableDefault(renderOpts, params) {
    interface KeyValue {
      key: string
      value: string
    }
    const { row } = params
    const { keyValues, format }: { keyValues?: KeyValue[], format?: (val: string) => string } = renderOpts.props || {}

    return (
      keyValues?.map((item, index) => {
        let value = get(row, item.value)
        if (isFunction(format))
          value = format(value)

        return (
          <div key={index}>
            <span class="mr-4">
              {item.key}
              :
            </span>
            <span>{value ?? '-'}</span>
          </div>
        )
      }) || []
    )
  },
})

VxeUI.renderer.add('TableCellDropdownGrid', {
  renderTableDefault(renderOpts, params) {
    const { row, column } = params
    const { options: columns = [], key: displayKey } = renderOpts.props || {}
    const listData = Array.isArray(row[column.field]) ? row[column.field] : []
    const firstItem = listData[0] || {}
    const displayValue = firstItem[displayKey] ?? ''

    return (
      <ElPopover
        persistent={false}
        show-after={openDelay}
        trigger="hover"
        width={400}
        v-slots={{
          reference: () => (
            <span class="inline-flex items-center gap-1">
              <span class="truncate text-primary">{displayValue}</span>
              {displayValue && <ElIcon><ArrowDown /></ElIcon>}
            </span>
          ),
        }}
      >
        <cg-grid
          data={listData}
          options={{ columns, paginationVisible: false }}
          border={false}
          height={undefined}
          paginationVisible={false}
        />
      </ElPopover>
    )
  },
})
