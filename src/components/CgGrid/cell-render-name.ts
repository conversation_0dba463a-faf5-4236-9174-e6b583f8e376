/**
 * 导出枚举 GridCellRenderName，定义网格单元格渲染类型。
 * @enum {string}
 */
export const GridCellRenderName = {
  /**
   * 图片列
   */
  ImageUrl: 'TableCellImage',

  /**
   * 文本显示带省略号
   */
  Text: 'TableCellText',

  /**
   * 长文本显示两行带省略号
   */
  TextTwoLine: 'TableCellTextTwoLine',

  /**
   * 长文本显示一行带省略号
   */
  TextOneLine: 'TableCellTextOneLine',

  /**
   * 操作按钮列
   */
  Operate: 'TableCellOperate',

  /**
   * 状态格式化
   */
  Status: 'TableCellStatus',

  /**
   * 多个字段显示一行
   */
  MoreFieldOneLine: 'TableCellMoreFieldOneLine',

  /**
   * 多个字段显示多行
   */
  MoreFieldMoreLine: 'TableCellMoreFieldMoreLine',

  /**
   * 键值对列表
   */
  KeyValueList: 'TableCellKeyValueList',

  /**
   * 下拉表格
   */
  DropdownGrid: 'TableCellDropdownGrid',
}

export default GridCellRenderName
