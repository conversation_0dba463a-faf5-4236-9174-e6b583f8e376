import dayjs from 'dayjs'
import { VxeUI } from 'vxe-pc-ui'
import XEUtils from 'xe-utils'

// 格式化日期，默认 YYYY-MM-DD HH:mm:ss
VxeUI.formats.add('formatDate', {
  cellFormatMethod({ cellValue }, format?: string) {
    if (!cellValue)
      return ''
    // 使用 dayjs 处理日期，可以正确处理带时区的 ISO 格式日期
    return dayjs(cellValue).format(format || 'YYYY-MM-DD HH:mm:ss')
  },
})

// 四舍五入金额，每隔3位逗号分隔，默认2位数
VxeUI.formats.add('formatAmount', {
  cellFormatMethod({ cellValue }, digits = 2) {
    return XEUtils.commafy(XEUtils.toNumber(cellValue), { digits })
  },
})

// 格式化金额，每隔3位逗号分隔，默认2位数
VxeUI.formats.add('formatCurrency', {
  cellFormatMethod({ cellValue }, digits = 2) {
    return XEUtils.commafy(XEUtils.toNumber(cellValue), { digits })
  },
})

// 格式化银行卡，默认每4位空格隔开
VxeUI.formats.add('formatBankcard', {
  cellFormatMethod({ cellValue }) {
    return XEUtils.commafy(XEUtils.toValueString(cellValue), { spaceNumber: 4, separator: ' ' })
  },
})

// 向下舍入,默认两位数
VxeUI.formats.add('formatCutNumber', {
  cellFormatMethod({ cellValue }, digits = 2) {
    return XEUtils.toFixed(XEUtils.floor(cellValue, digits), digits)
  },
})

// 四舍五入,默认两位数
VxeUI.formats.add('formatFixedNumber', {
  cellFormatMethod({ cellValue }, digits = 2) {
    return XEUtils.toFixed(XEUtils.round(cellValue, digits), digits)
  },
})

// 格式化性别
VxeUI.formats.add('formatSex', {
  cellFormatMethod({ cellValue }) {
    return cellValue ? (cellValue === '1' ? '男' : '女') : ''
  },
})
