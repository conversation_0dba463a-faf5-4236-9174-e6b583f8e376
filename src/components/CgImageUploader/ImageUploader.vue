<script setup lang="ts">
import type { GetOssUploadCredentialAccessTypeEnum, GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'
import { OssUploader } from '@/utils/ossUploader'

defineOptions({
  name: 'CgImageUploader',
})

const props = defineProps({
  modelValue: {
    type: String,
    default: '',
  },
  size: {
    type: Object,
    default: () => ({ width: 200, height: 200 }),
  },
  folderName: {
    type: String as () => GetOssUploadCredentialUploadFileTypeEnum,
    required: true,
  },
  accessType: {
    type: String as () => GetOssUploadCredentialAccessTypeEnum,
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  uploadText: {
    type: String,
    default: '上传图片',
  },
  replaceText: {
    type: String,
    default: '更换图片',
  },
  viewOnly: {
    type: Boolean,
    default: false,
  },
  tipText: {
    type: String,
    default: '',
  },
})

const emits = defineEmits<{
  'update:modelValue': [value: string]
  'uploadSuccess': [url: string, key: string]
  'uploadError': [error: any]
}>()

const imageLoading = ref(false)
const imageBlob = ref<string>('')
const uploaderRef = ref<HTMLDivElement | null>(null)

function handleUploadSuccess(url: string, key: string) {
  emits('update:modelValue', key)
  emits('uploadSuccess', url, key)
}

function handleUploadError(error: any) {
  emits('uploadError', error)
}

function handleReplaceImage() {
  if (props.viewOnly || props.disabled || imageLoading.value)
    return

  const input = document.createElement('input')
  input.type = 'file'
  input.accept = 'image/*'
  input.style.display = 'none'

  input.onchange = async (e: Event) => {
    const target = e.target as HTMLInputElement
    if (target.files && target.files.length > 0) {
      const file = target.files[0]

      if (!file.type.startsWith('image/')) {
        ElMessage.warning('请选择图片文件')
        if (input.parentNode) {
          input.parentNode.removeChild(input)
        }
        return
      }

      const localBlobUrl = URL.createObjectURL(file)
      const oldBlobUrl = imageBlob.value
      imageBlob.value = localBlobUrl

      imageLoading.value = true

      try {
        await OssUploader.uploadFile(
          file,
          props.folderName,
          props.accessType,
          undefined,
          handleUploadSuccess,
          handleUploadError,
        )
      }
      catch {
        // 上传失败，恢复之前的 blob 或清空
        imageBlob.value = oldBlobUrl
        URL.revokeObjectURL(localBlobUrl)
      }
      finally {
        imageLoading.value = false
        if (oldBlobUrl && oldBlobUrl !== localBlobUrl && oldBlobUrl.startsWith('blob:')) {
          URL.revokeObjectURL(oldBlobUrl)
        }
      }
    }
    if (input.parentNode) {
      input.parentNode.removeChild(input)
    }
  }

  input.onerror = () => {
    if (input.parentNode) {
      input.parentNode.removeChild(input)
    }
    imageLoading.value = false
  }
  input.onabort = () => {
    if (input.parentNode) {
      input.parentNode.removeChild(input)
    }
    imageLoading.value = false
  }

  uploaderRef.value?.appendChild(input)
  input.click()
}

onBeforeUnmount(() => {
  if (imageBlob.value && imageBlob.value.startsWith('blob:')) {
    URL.revokeObjectURL(imageBlob.value)
  }
})
</script>

<template>
  <div ref="uploaderRef" class="image-uploader" :style="{ width: `${size.width}px` }">
    <div
      v-if="modelValue || imageBlob"
      v-loading="imageLoading"
      class="image-preview"
      :style="{ width: `${size.width}px`, height: `${size.height}px` }"
      element-loading-text="图片上传中..."
    >
      <img :src="imageBlob || modelValue" alt="上传图片">
      <div v-if="!viewOnly" class="image-actions">
        <el-button
          type="primary"
          size="small"
          :disabled="disabled || imageLoading"
          @click="handleReplaceImage"
        >
          {{ replaceText }}
        </el-button>
      </div>
    </div>
    <div
      v-else
      v-loading="imageLoading"
      class="upload-placeholder"
      :style="{ width: `${size.width}px`, height: `${size.height}px` }"
      element-loading-text="图片上传中..."
      :class="{ disabled, 'view-only': viewOnly }"
      @click="handleReplaceImage"
    >
      <font-awesome-icon v-if="!viewOnly && !imageLoading" :icon="['fas', 'plus']" size="2x" />
      <span v-if="!viewOnly && !imageLoading" class="mt-2">{{ uploadText }}</span>
      <template v-if="viewOnly && tipText">
        <div class="tip-text mt-2 text-center" v-html="tipText" />
      </template>
      <template v-if="viewOnly && !tipText && !modelValue && !imageBlob">
        <span>无图片</span>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.image-uploader {
  display: flex;
  justify-content: flex-start;
  position: relative;

  .image-preview {
    position: relative;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    overflow: hidden;
    background-color: #fafafa;

    img {
      display: block;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .image-actions {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: rgba(0, 0, 0, 0.5);
      padding: 8px;
      display: flex;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.3s;
      z-index: 1;
    }

    &:hover .image-actions {
       opacity: 1;
    }
  }

  .upload-placeholder {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: #8c939d;
    transition: border-color 0.3s, color 0.3s;
    background-color: #fafafa;

    &:hover:not(.disabled):not(.view-only) {
      border-color: #399e96;
      color: #399e96;
    }

    &.disabled {
      cursor: not-allowed;
      opacity: 0.6;
       background-color: #f5f7fa;
    }

    &.view-only {
      cursor: default;

      .tip-text {
        font-size: 12px;
        color: #909399;
        text-align: center;
        padding: 0 10px;
      }
       span:not(.tip-text) {
         color: #c0c4cc;
       }
    }
  }

  :deep(.el-loading-mask) {
    z-index: 10;
  }
}
</style>
