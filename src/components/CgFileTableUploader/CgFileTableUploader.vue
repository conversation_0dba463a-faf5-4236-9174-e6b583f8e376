<script setup lang="ts">
import type { GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'
import type { GridProps } from '@/components/CgGrid'
import type { VxeGridPropTypes } from 'vxe-table'
import type { CgFile } from '../CgFileUploader/FileUploader.vue'
import { GetOssUploadCredentialAccessTypeEnum } from '@/apiv2/product'
import CgFileUploader from '@/components/CgFileUploader'

export interface FileRow {
  id?: number
  filename?: string
  filePath?: string
  fileUrl?: string
  extension?: string
  fileSize?: number // bytes
}

interface Props {
  /** v-model */
  modelValue?: FileRow[]
  /** OSS 目录 */
  ossFolder: GetOssUploadCredentialUploadFileTypeEnum
  /** 私有 or 公开 */
  accessType?: GetOssUploadCredentialAccessTypeEnum
  /** input accept */
  accept?: string
  /** 多文件上传 */
  multiple?: boolean
  /** 只读模式 */
  readonly?: boolean
  /** 标题 */
  title?: string
}

defineOptions({
  name: 'CgFileTableUploader',
})

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  accessType: GetOssUploadCredentialAccessTypeEnum.PRIVATE,
  accept: '*',
  multiple: false,
  readonly: false,
})

const emit = defineEmits<{
  'update:modelValue': [value: FileRow[]]
}>()

const rows = computed<FileRow[]>({
  get: () => props.modelValue || [],
  set: v => emit('update:modelValue', v),
})

const columns: VxeGridPropTypes.Columns = [
  {
    title: '附件',
    minWidth: 160,
    slots: {
      footer: 'fileFooter',
      default: 'fileTemp',
    },
  },
  {
    field: 'extension',
    title: '格式',
    minWidth: 100,
  },
  {
    field: 'fileSize',
    title: '大小',
    minWidth: 100,
    slots: { default: 'sizeTemp' },
  },
  {
    title: '操作',
    minWidth: 90,
    slots: { default: 'opTemp' },
  },
]

const gridOption = reactive<GridProps>({
  options: {
    columns,
    border: false,
    minHeight: 240,
    height: 'auto',
    round: true,
    showFooter: true,
    showFooterOverflow: true,
    footerMethod: ({ columns }) => [columns.map(() => null)],
  },
  paginationVisible: false,
})

function addRow() {
  if (props.readonly)
    return
  rows.value = [...rows.value, {}]
}

function deleteRow(row: FileRow) {
  if (props.readonly)
    return
  rows.value = rows.value.filter(r => r !== row)
}

function toCgFile(row: FileRow): CgFile | null {
  if (!row.filePath)
    return null
  return {
    key: row.filePath,
    name: row.filename || '',
    url: row.fileUrl || '',
    size: row.fileSize,
    type: row.extension ? `application/${row.extension.toLowerCase()}` : undefined,
  }
}

function handleFileUpdate(row: FileRow, file: CgFile | null) {
  if (file) {
    row.filePath = file.key
    row.filename = file.name
    row.fileUrl = file.url
    row.fileSize = file.size
    const idx = file.name.lastIndexOf('.')
    row.extension = idx > -1 ? file.name.slice(idx + 1).toUpperCase() : undefined
  }
  else {
    row.filePath = row.filename = row.fileUrl = row.extension = undefined
    row.fileSize = undefined
  }
}

function fmtSize(bytes?: number) {
  if (!bytes)
    return '-'
  const kb = bytes / 1024
  if (kb < 1024)
    return `${Math.round(kb * 100) / 100} KB`
  return `${Math.round((kb / 1024) * 100) / 100} MB`
}
</script>

<template>
  <div v-if="title" class="mb-3 flex items-center justify-between">
    <div class="flex items-center">
      <h3
        class="flex items-center border-l-4 border-#399e96 rounded-r bg-gray-50 py-1 pl-2 text-sm font-medium shadow-sm"
      >
        <span class="pr-1 text-gray-700 transition-colors group-hover:text-#399e96">{{ title }}</span>
      </h3>
    </div>
    <slot name="header-actions" />
  </div>
  <div class="mb-6 h-300px max-h-400px overflow-auto">
    <CgGrid v-bind="gridOption" :data="rows">
      <template #fileFooter>
        <font-awesome-icon
          v-if="!readonly"
          :icon="['fas', 'square-plus']"
          size="2x"
          class="ml-2 cursor-pointer text-gray-500 hover:text-gray-700"
          @click="addRow"
        />
      </template>

      <!-- uploader 列 -->
      <template #fileTemp="{ row }">
        <CgFileUploader
          :model-value="toCgFile(row)"
          :folder-name="props.ossFolder"
          :access-type="props.accessType"
          :multiple="props.multiple"
          :accept="props.accept"
          :disabled="readonly"
          upload-text="上传文件"
          @update:model-value="(f: CgFile | CgFile[] | null) => handleFileUpdate(row, Array.isArray(f) ? f[0] : f)"
        />
      </template>

      <!-- size -->
      <template #sizeTemp="{ row }">
        {{ fmtSize(row.fileSize) }}
      </template>

      <!-- 操作 -->
      <template #opTemp="{ row }">
        <CgButton v-if="!readonly" type="text" @click="deleteRow(row)">
          删除
        </CgButton>
        <span v-else>-</span>
      </template>
    </CgGrid>
  </div>
</template>
