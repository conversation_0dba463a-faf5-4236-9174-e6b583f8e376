<script setup lang="ts">
import type { Slots } from 'vue'
import type { VxeTableDefines } from 'vxe-table'
import { pageListProps } from './page-list'
import BatchSearch from './page-list-batch-search.vue'
import QueryForm from './page-list-form'
import SearchMore from './page-list-search-more.vue'
import { useQuery } from './use-query'

defineOptions({
  name: 'CgPageList',
})
const props = defineProps(pageListProps())
const emit = defineEmits(['search', 'update:queryModel', 'checkboxAll', 'checkboxChange'])
defineSlots<{
  [key: string]: any
}>()
const { gridOption } = toRefs(props)
const {
  handleSearch,
  handleReset,
  resetQueryModel,
  loading,
  queryModel,
  loadData,
  gridRef,
  btnSearchRef,
} = useQuery(props, emit)

function getCheckboxRecords(isFull = false) {
  return gridRef.value?.getCheckboxRecords(isFull)
}
function handleChangeAll(value: { checked: boolean, $event: any }) {
  if (checkMaxSelectCheckboxRow())
    gridRef.value?.vxeGridRef?.clearCheckboxRow()
  emit('checkboxAll', value)
}
function hangleChange(value: any) {
  if (checkMaxSelectCheckboxRow())
    gridRef.value?.vxeGridRef?.toggleCheckboxRow(value.row)
  emit('checkboxChange', value)
}
function checkMaxSelectCheckboxRow() {
  if (props.maxSelectCheckboxRow) {
    const rows = getCheckboxRecords() || []
    if (props.maxSelectCheckboxRow > 0 && rows.length > props.maxSelectCheckboxRow) {
      ElMessageBox.alert(`最多只能选择 ${props.maxSelectCheckboxRow} 行数据.`, '提示信息')
      return true
    }
  }
  return false
}
function hideColumn(columnOptions: VxeTableDefines.ColumnInfo | VxeTableDefines.ColumnInfo[]) {
  gridRef.value?.hideColumn(columnOptions)
}
function showColumn(columnOptions: VxeTableDefines.ColumnInfo | VxeTableDefines.ColumnInfo[]) {
  gridRef.value?.showColumn(columnOptions)
}
function getQueryModel() {
  return queryModel.value
}
defineExpose({
  reload: loadData,
  hideColumn,
  showColumn,
  cgGrid: gridRef,
  getCheckboxRecords,
  getQueryModel,
  resetQueryModel,
})
const searchOption = computed(() => {
  const queryForm = props.queryForm.filter(item => item.visible !== false)
  const maxCount = props.maxQueryFormVisibleCount
  if (queryForm.length > maxCount)
    return queryForm.slice(0, maxCount)
  return props.queryForm
})
const searchMoreOption = computed(() => {
  const queryForm = props.queryForm.filter(item => item.visible !== false)
  const maxCount = props.maxQueryFormVisibleCount
  if (queryForm.length > maxCount)
    return queryForm.slice(maxCount)
  return []
})

const slots: Slots = useSlots()
</script>

<template>
  <CgContainer>
    <template #header-left>
      <form class="search-form" @submit.prevent="handleSearch">
        <QueryForm :form="searchOption" :model="queryModel" />
        <SearchMore
          v-if="searchMoreOption.length"
          :form="searchMoreOption"
          :model="queryModel"
          :display-mode="props.searchMoreDisplayMode"
          @search="handleSearch"
          @reset="handleReset"
        />
        <CgButton ref="btnSearchRef" type="primary" :loading="loading" @click="handleSearch">
          搜索
        </CgButton>
        <BatchSearch
          v-if="batchQueryForm.length" :query-form="batchQueryForm" :model="queryModel"
          @search="handleSearch"
        />
        <CgButton v-if="resetButtonVisible" plain :disabled="loading" @click="handleReset">
          重置
        </CgButton>
      </form>
    </template>
    <template v-if="!!slots['header-right']" #header-right>
      <slot name="header-right" />
    </template>
    <template #main>
      <CgGrid
        v-bind="gridOption" ref="gridRef" :vxe-loading="vxeLoading" :auto-load="false"
        @checkbox-all="handleChangeAll" @checkbox-change="hangleChange"
      >
        <template v-for="(_, slot) in $slots" #[slot]="scope">
          <slot :name="slot" v-bind="scope" />
        </template>
      </CgGrid>
    </template>
  </CgContainer>
</template>

<style lang="scss" scope>
.cg-page-header__left {
  // margin-left: 5px;

  .el-button+.el-button {
    margin-left: 0;
  }
}

.cg-page-header__right {
  flex: 1;
  justify-content: end;
}

.form-item-container {
  display: flex;
  align-items: center;

  .form-item-label {
    width: 66px;
    text-align: right;
    margin-right: 8px;
  }

  .form-item-component {
    flex: 1;
  }
}

.search-form {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
