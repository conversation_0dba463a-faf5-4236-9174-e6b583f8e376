<script setup lang="ts">
import type { PropType } from 'vue'
import type { QueryFormItem } from './page-list'
import { ref } from 'vue'
import QueryForm from './page-list-form'

defineProps({
  model: { type: Object as PropType<Record<string, any>>, default: () => ({}) },
  queryForm: { type: Array as PropType<Array<QueryFormItem>>, default: () => [] },
})
const emit = defineEmits(['search'])
const visible = ref(false)
function handleSearch() {
  visible.value = false
  emit('search')
}
</script>

<template>
  <ElPopover
    v-model:visible="visible"
    :width="380"
    trigger="click"
  >
    <template #reference>
      <ElButton plain>
        批量搜索
      </ElButton>
    </template>
    <div class="flex flex-col gap-8">
      <QueryForm class="w-100%" :form="queryForm" :model="model" />
      <div class="flex justify-end">
        <ElButton type="primary" @click="handleSearch">
          搜索
        </ElButton>
      </div>
    </div>
  </ElPopover>
</template>

<style  lang="scss">

</style>
