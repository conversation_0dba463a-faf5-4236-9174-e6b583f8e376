import type { PropType } from 'vue'
// import ComponentTags from "@/common/component-tag"
import type { QueryFormItem } from './page-list'
import { createBlock, defineComponent, h, mergeProps, openBlock, resolveComponent, toHandlers, toRefs, useAttrs } from 'vue'
import { isPlainObject } from 'xe-utils'

function queryFormProps() {
  return {
    model: { type: Object as PropType<Record<string, any>>, default: () => ({}) },
    form: { type: Array as PropType<Array<QueryFormItem>>, default: () => [] },
  }
}

export default defineComponent({
  props: queryFormProps(),
  setup(props) {
    return () => {
      const { form, model } = toRefs(props)
      return form.value.map((item) => {
        if (item.visible === false)
          return ''

        const { vmodel, component, label } = item

        let vm: Record<string, string> = {}
        if (!isPlainObject(vmodel))
          vm.modelValue = vmodel
        else
          vm = { ...vmodel }

        const vmodelProps: Record<string, any> = {}
        const dynamicProps: string[] = []
        Object.keys(vm).forEach((key) => {
          dynamicProps.push(key)
          const vmValue = vm[key]
          vmodelProps[key] = model.value[vmValue]
          vmodelProps[`onUpdate:${key}`] = (value: string) => model.value[vmValue] = value
        })
        let renderComponent = component
        let defaultComponentProps: Record<string, any> = {}
        if (isPlainObject(component)) {
          const { component: tempComponent, props } = component as any
          if (tempComponent) {
            renderComponent = tempComponent
            if (props)
              defaultComponentProps = props
          }
        }
        const concreteComponent = typeof renderComponent === 'string' ? resolveComponent(renderComponent) : renderComponent
        const componentProps = { ...defaultComponentProps, ...(item.props || {}) }
        // 创建 label 元素
        const labelElement = label ? (openBlock(), createBlock('label', { class: 'form-item-label' }, `${label}`, 1)) : ''
        const componentElement = (openBlock(), createBlock(concreteComponent, mergeProps(useAttrs(), vmodelProps, componentProps, toHandlers(item.on || {}, true), { class: 'form-item-component' }), null, 16, dynamicProps))

        const formItemContainer = h('div', { class: 'form-item-container' }, [labelElement, componentElement])
        return labelElement ? formItemContainer : componentElement
      })
    }
  },
})
