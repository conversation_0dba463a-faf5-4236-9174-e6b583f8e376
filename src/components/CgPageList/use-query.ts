import type CgButton from '@/components/CgElementUI/CgButton'
import type CgGrid from '@/components/CgGrid'
import type { PageListProps } from './page-list'
import { useVModel } from '@vueuse/core'
import { nextTick, onMounted, ref, unref } from 'vue'
import { isFunction } from 'xe-utils'

export function useQuery(props: PageListProps, emit: (name: any, ...args: any[]) => void) {
  const queryModel = useVModel(props, 'queryModel', emit)

  const gridRef = ref<InstanceType<typeof CgGrid> | null>(null)
  // 控制搜索、重置按钮状态
  const loading = ref(false)
  const btnSearchRef = ref<InstanceType<typeof CgButton> | null>(null)
  const queryModelBackup = ref<Record<string, any>>({})
  const isResetCurrentPage = ref(false)

  const getQueryModel = () => {
    props.queryForm?.forEach((option) => {
      switch (option.component?.component) {
        case 'CgDatePicker':{
          const props = option.props || {}
          const type = props.type
          if (!type || type === 'daterange') {
            const key = (option.vmodel as Record<string, string>).modelValue
            const { startDate, endDate } = option.vmodel as Record<string, string>
            let [StartTime, EndTime] = queryModel.value![key]
            if (startDate && endDate) {
              StartTime = `${StartTime} 00:00:00`
              EndTime = `${EndTime} 23:59:59`
              queryModel.value![(option.vmodel as Record<string, string>).startDate] = StartTime
              queryModel.value![(option.vmodel as Record<string, string>).endDate] = EndTime
            }
          }
          break
        }
        default: {
          // const key = option.model
          // const value = queryData[key]
          // if (Array.isArray(value)) {
          //   // 替换掉全选 all 字符串
          //   queryData[key] = value.filter(item => item !== "all")
          // }
          break
        }
      }
    })

    Object.keys(queryModel.value!).forEach((key) => {
      // 去除首尾空格
      if (typeof queryModel.value![key] == 'string')
        queryModel.value![key] = queryModel.value![key].trim()
    })
  }
  const handleSearch = (event: MouseEvent | Event) => {
    let next = true
    if (isFunction(props.beforeSearch))
      next = props.beforeSearch(unref(queryModel))
    if (next) {
      loading.value = true
      getQueryModel()
      if (isResetCurrentPage.value || event !== null)
        gridRef.value?.resetCurrentPage()
      gridRef.value?.requestData(unref(queryModel), true).finally(() => {
        loading.value = false
      })
      isResetCurrentPage.value = false

      emit('search', unref(queryModel))
    }
  }
  const resetQueryModel = () => {
    queryModel.value = { ...unref(queryModelBackup) }
  }
  /**
   * 加载数据
   * @param resetCurrentPage 是否重置当前页为1
   */
  const loadData = (resetCurrentPage = false) => {
    isResetCurrentPage.value = resetCurrentPage
    btnSearchRef.value?.handleClick(null)
  }

  const handleReset = async () => {
    resetQueryModel()
    await nextTick()
    loadData(true)
  }

  onMounted(() => {
    getQueryModel()
    nextTick(() => {
      queryModelBackup.value = { ...unref(queryModel) }
      if (props.autoLoad)
        loadData()
    })
  })
  return {
    handleSearch,
    handleReset,
    loadData,
    resetQueryModel,
    loading,
    queryModel,
    gridRef,
    btnSearchRef,
  }
}
