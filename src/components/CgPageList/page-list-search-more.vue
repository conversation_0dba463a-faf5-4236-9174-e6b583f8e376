<script setup lang="ts">
import type { PropType } from 'vue'
import type { QueryFormItem } from './page-list'

import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { computed, ref } from 'vue'
import QueryForm from './page-list-form'

const props = defineProps({
  model: { type: Object as PropType<Record<string, any>>, default: () => ({}) },
  form: { type: Array as PropType<Array<QueryFormItem>>, default: () => [] },
  displayMode: { type: String as PropType<'button' | 'icon'>, default: 'button' },
})
const emit = defineEmits(['search', 'reset'])

const visible = ref(false)
const iconRef = ref<HTMLElement | null>(null)
const popoverRef = ref()

function handleSearch() {
  emit('search')
  handleClose()
}

function handleReset() {
  emit('reset')
  handleClose()
}

function handleClose() {
  visible.value = false
}

function handleSearchMore(event?: Event) {
  if (event) {
    event.stopPropagation()
  }

  visible.value = true
}

const isIconMode = computed(() => props.displayMode === 'icon')
</script>

<template>
  <!-- 按钮模式 -->
  <template v-if="!isIconMode">
    <ElButton plain @click="handleSearchMore">
      更多搜索
    </ElButton>
    <ElDrawer v-model="visible" size="20%" class="page-list-search-more" title="更多搜索">
      <div class="page-list-search-more__wrapper flex flex-col gap-8">
        <QueryForm :form="form" :model="model" />
        <div class="flex justify-end gap-5">
          <ElButton plain @click="handleClose">
            关闭
          </ElButton>
          <ElButton plain @click="handleReset">
            重置
          </ElButton>
          <ElButton type="primary" @click="handleSearch">
            搜索
          </ElButton>
        </div>
      </div>
    </ElDrawer>
  </template>

  <!-- 图标模式 -->
  <template v-else>
    <div ref="iconRef" class="search-more-icon" @click="handleSearchMore">
      <FontAwesomeIcon :icon="['fas', 'filter']" />
    </div>
    <ElPopover
      ref="popoverRef" :virtual-ref="iconRef" virtual-triggering trigger="click" :visible="visible"
      placement="bottom-start" :width="320" popper-class="search-more-popover"
    >
      <template #default>
        <div class="search-more-popover__wrapper flex flex-col gap-5">
          <div class="search-more-popover__title">
            更多搜索
          </div>
          <QueryForm :form="form" :model="model" />
          <div class="flex justify-end">
            <ElButton plain @click="handleClose">
              关闭
            </ElButton>
            <ElButton type="primary" @click="handleSearch">
              搜索
            </ElButton>
          </div>
        </div>
      </template>
    </ElPopover>
  </template>
</template>

<style lang="scss">
.el-drawer.page-list-search-more {
  .page-list-search-more__wrapper {
    font-size: var(--el-font-size-base);

    >div {
      width: 100%;
    }
  }

  .el-drawer__header {
    padding: 10px;
    margin-bottom: 0;
  }

  .el-drawer__title {
    font-weight: bold;
    font-size: 14px;
    color: #000;
  }

  .el-drawer__body {
    padding: 10px;
  }

  .cg-complex-input__input {
    width: 100%;
  }
}

.search-more-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  border: 1px solid var(--el-border-color);
  cursor: pointer;
  transition: all 0.3s;
  color: var(--el-text-color-regular);

  &:hover {
    color: var(--el-color-primary);
    border-color: var(--el-color-primary);
  }
}

.search-more-popover {
  &__wrapper {
    padding: 5px;
    font-size: var(--el-font-size-base);

    >div {
      width: 100%;
    }
  }

  &__title {
    font-weight: bold;
    font-size: 14px;
    color: #000;
    padding-bottom: 5px;
    border-bottom: 1px solid var(--el-border-color-lighter);
  }

  .cg-complex-input__input {
    width: 100%;
  }

  .el-form-item {
    margin-bottom: 10px;
  }
}
</style>
