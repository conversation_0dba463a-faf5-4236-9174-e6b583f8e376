import type { ExtractPropTypes, PropType } from 'vue'
import type { GridProps } from '../CgGrid'

export interface QueryFormItem {
  component: string | any
  vmodel: string | Record<string, string>
  visible?: boolean
  props?: Record<string, any>
  on?: Record<string, (...args: any[]) => any>
  label?: string
}
export type BeforeSearch = (queryModel?: Record<string, any>) => boolean

export function pageListProps() {
  return {
    queryModel: { type: Object as PropType<Record<string, any>>, default: () => ({}) },
    queryForm: { type: Array as PropType<Array<QueryFormItem>>, default: () => [] },
    /** 使用vxe-table 自带loading，默认为el-loading,表格渲数据多,el-loading状态会卡顿 */
    vxeLoading: {
      type: Boolean,
      default: false,
    },
    autoLoad: {
      type: Boolean,
      default: true,
    },
    resetButtonVisible: {
      type: Boolean,
      default: true,
    },
    /**
     * 批量搜索表单
     */
    batchQueryForm: { type: Array as PropType<Array<QueryFormItem>>, default: () => [] },
    gridOption: { type: Object as PropType<GridProps>, default: () => ({}) },
    maxSelectCheckboxRow: { type: Number, default: 0 },
    maxQueryFormVisibleCount: { type: Number, default: 6 },
    beforeSearch: { type: Function as PropType<BeforeSearch>, default: null },
    /**
     * 更多搜索组件的显示模式
     * button: 按钮模式
     * icon: 图标模式
     */
    searchMoreDisplayMode: { type: String as PropType<'button' | 'icon'>, default: 'button' },
  }
}

export type PageListProps = Partial<ExtractPropTypes<ReturnType<typeof pageListProps>>>
