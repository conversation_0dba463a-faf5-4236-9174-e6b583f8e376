import type { Placement } from 'element-plus'
import type { ExtractPropTypes, PropType } from 'vue'
import { placements } from '@popperjs/core'
import { useTooltipContentProps } from 'element-plus'

export function textTooltipProps() {
  return {
    content: {
      type: String,
      default: '',
    },
    effect: {
      type: String,
      default: 'light',
    },
    class: {
      type: String,
      default: '',
    },
    twoLine: {
      type: Boolean,
      default: false,
    },
    linkButton: {
      type: Boolean,
      default: false,
    },
    emptyText: {
      type: String,
      default: '-',
    },
    maxWidth: {
      type: [String, Number],
      default: 350,
    },
    placement: {
      type: String as PropType<Placement>,
      values: placements,
      default: 'top',
    },
    showAfter: {
      type: Number,
      default: 200,
    },
    popperStyle: useTooltipContentProps.popperStyle,
    popperClass: useTooltipContentProps.popperClass,
  }
}
export type TextTooltipProps = ExtractPropTypes<ReturnType<typeof textTooltipProps>>
