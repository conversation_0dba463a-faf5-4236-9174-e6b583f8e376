<script setup lang="ts">
import { addUnit, getValue } from '@/components/utils'
import { ElTooltip } from 'element-plus'
import { computed, ref } from 'vue'
import { textTooltipProps } from './text-tooltip'

defineOptions({
  name: 'CgTextTooltip',
})
const props = defineProps(textTooltipProps())
const lineClass = computed(() => {
  return [props.twoLine ? 'twoLines' : 'oneLine', 'flex-1', props.class]
})
const style = computed(() => {
  return [
    {
      width: addUnit(props.maxWidth),
    },
    props.popperStyle!,
  ]
})
const kls = computed(() => {
  return ['global-pop', props.popperClass!]
})

const contentText = computed(() => {
  return getValue(props.content, props.emptyText).toString()
})
// 使用isShow来控制tooltip是否显示
const isShow = ref<boolean>(true)
// 在span标签上定义一个ref
const contentRef = ref()
function isShowTooltip() {
  // 计算span标签的offsetWidth与盒子元素的offsetWidth，给isShow赋值
  isShow.value = props.twoLine
    ? contentRef.value.parentNode.offsetHeight >= contentRef.value.offsetHeight
    : contentRef.value.parentNode.offsetWidth >= contentRef.value.offsetWidth
}
</script>

<template>
  <ElTooltip
    :effect="effect" :content="contentText" :show-after="showAfter" :placement="placement" v-bind="$attrs"
    :popper-class="kls" :popper-style="style" :disabled="isShow"
  >
    <div :class="lineClass" @mouseover="isShowTooltip">
      <span ref="contentRef" v-bind="$attrs">

        <slot>
          <template v-if="linkButton">
            <span class="link-button">{{ contentText }}</span>
          </template>
          <template v-else>
            {{ contentText }}
          </template>
        </slot>
      </span>
    </div>
  </ElTooltip>
</template>

<style lang="scss" scoped>
.link-button {
  color: var(--el-color-primary);
  font-size: var(--el-font-size-base);
  font-weight: var(--el-font-weight-primary);
  cursor: pointer;
  &:hover {
    color: var(--el-color-primary-light-3);
  }
}
</style>
