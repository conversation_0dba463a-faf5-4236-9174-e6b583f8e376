import type { SFCWithInstall } from '@/common/Install'
import type { App } from 'vue'

import CgCategoryTree from '@/components/CgCategoryTree/index'
import CgContainer from '@/components/CgContainer/index'
import CgCopy from '@/components/CgCopy/index'
import CgDialog from '@/components/CgDialog/index'
import CgFileTableUploader from '@/components/CgFileTableUploader/index'
import CgFileUploader from '@/components/CgFileUploader/index'
import CgGrid from '@/components/CgGrid/index'
import CgGridImage from '@/components/CgGridImage/index'
import CgGridOperate from '@/components/CgGridOperate/index'
import CgImageManager from '@/components/CgImageManager/index'
import CgImageUploader from '@/components/CgImageUploader/index'
import CgImport from '@/components/CgImport/index'
import CgOperateLog from '@/components/CgOperateLog/index'
import CgPageList from '@/components/CgPageList/index'
import CgSvgIcon from '@/components/CgSvgIcon/index'
import CgTextTooltip from '@/components/CgTextTooltip/index'
import CgTinyMCE from '@/components/CgTinyMCE/index'
import CgWorkOrderFormEditor from '@/components/CgWorkOrderFormBuilder/index'

const components = [
  CgTinyMCE,
  CgSvgIcon,
  CgGrid,
  CgPageList,
  CgWorkOrderFormEditor,
  CgContainer,
  CgTextTooltip,
  CgGridImage,
  CgGridOperate,
  CgImport,
  CgDialog,
  CgImageUploader,
  CgFileUploader,
  CgFileTableUploader,
  CgCategoryTree,
  CgCopy,
  CgOperateLog,
  CgImageManager,
] as SFCWithInstall<any>[]

export function install(app: App) {
  // 每个组件在编写的时候都提供了install方法
  // 有的是组建 有的可能是指令 xxx.install = ()=>{app.directive()}
  components.forEach(component => app.use(component))
}
export default {
  install,
}
