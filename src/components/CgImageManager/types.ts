import type { GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'
import type { FileRow } from '../CgFileTableUploader/CgFileTableUploader.vue'

export interface ImageInfo {
  id?: number
  url: string // 线上 url
  imageKey: string // OSS key
  localUrl?: string // 上传前本地 blob
  size?: number // KB
  dimensions?: { width: number, height: number }
  filename?: string
  skuCode?: string
}

// 内置的图片区域类型
export type BuiltInImageSection = 'main' | 'sku' | 'detail'

// 支持自定义区域类型
export type ImageSection = BuiltInImageSection | string

/**
 * 自定义区域配置
 */
export interface SectionConfig {
  key: string // 区域唯一标识符
  title: string // 区域标题
  display?: 'image' | 'fileTable'
  emptyText?: string // 空状态提示文本
  primaryLabel?: string // 主要标签（用于第一张图片）
  accept?: string // 接受的文件类型，例如 'image/*' 或 '.pdf,.doc'
  useDraggable?: boolean // 是否支持拖拽排序
  ossFolder?: GetOssUploadCredentialUploadFileTypeEnum
}

/**
 * 图片值接口，支持动态区域
 */
export interface ImageValue {
  main: ImageInfo[]
  sku: Record<string, ImageInfo[]> // key = skuCode
  detail: ImageInfo[]
  [key: string]: ImageInfo[] | Record<string, ImageInfo[]> | FileRow[] // 支持自定义区域和文件表格
}

/**
 * 拖拽目标接口
 */
export interface DragTarget {
  /**
   * 图片部分
   */
  section: ImageSection
  /**
   * SKU ID（可选）
   */
  skuId?: string
}
