<script setup lang="ts">
import type { ImageInfo, ImageSection } from '../types'
import draggable from 'vuedraggable'
import ImageCard from './ImageCard.vue'
import ImageDropZone from './ImageDropZone.vue'

const props = defineProps<{
  title: string
  section: ImageSection
  images?: ImageInfo[]
  emptyText: string
  keyPrefix: string
  skuCode?: string
  sectionKey?: string
  isDragOver: boolean
  primaryLabel?: string
  useDraggable?: boolean
  readonly?: boolean
  handleDragOver: () => void
  handleDragLeave: () => void
  handleDrop: (event: DragEvent) => void
  handleClick: () => void
  handleRemove: (index: number) => void
  onUpdate?: (images: ImageInfo[]) => void
}>()

const emit = defineEmits<{
  preview: [index: number]
  updateFilename: [payload: { index: number, newFilename: string, section: ImageSection, skuId?: string }]
}>()

const isDetailView = inject('isDetailView', ref(false))

// 删除多余的、仅用于重新 emit 的函数
// 保留 handleRemove 函数，但修改为调用 props.handleRemove
function handleRemove(index: number) {
  props.handleRemove(index)
}

const orderedImages = ref(props.images || [])

// 监听props.images变化，更新orderedImages
watchEffect(() => {
  if (props.images) {
    orderedImages.value = [...props.images]
  }
})

// 处理拖拽结束后的更新
function handleDragChange() {
  if (props.onUpdate) {
    props.onUpdate(orderedImages.value)
  }
}

// 处理来自 ImageCard 的预览请求
function handlePreviewRequest(index: number) {
  emit('preview', index)
}

// 处理来自 ImageCard 的文件名更新请求
function handleFilenameUpdateRequest(index: number, newFilename: string) {
  emit('updateFilename', { index, newFilename, section: props.section, skuId: props.skuCode })
}
</script>

<template>
  <div class="mb-6">
    <div v-if="title" class="mb-3 flex items-center justify-between">
      <div class="flex items-center">
        <h3
          class="flex items-center border-l-4 border-#399e96 rounded-r bg-gray-50 py-1 pl-2 text-sm font-medium shadow-sm"
        >
          <span class="pr-1 text-gray-700 transition-colors group-hover:text-#399e96">{{ title }}</span>
        </h3>
      </div>
      <slot name="header-actions" />
    </div>

    <slot name="before-dropzone" />
    <ImageDropZone
      :images="images" :section="section" :sku-id="skuCode"
      :is-drag-over="isDragOver"
      @dragover="props.handleDragOver"
      @dragleave="props.handleDragLeave"
      @drop="props.handleDrop"
      @click="props.handleClick"
    >
      <template #empty-text>
        {{ emptyText }}
      </template>

      <div class="grid grid-cols-1 gap-4 lg:grid-cols-4 md:grid-cols-3 sm:grid-cols-2 xl:grid-cols-6">
        <draggable
          v-model="orderedImages"
          item-key="url"
          :animation="150"
          ghost-class="ghost-card"
          :group="{ name: keyPrefix }"
          handle=".drag-handle"
          :disabled="!useDraggable || isDetailView"
          class="contents"
          @change="handleDragChange"
        >
          <template #item="{ element, index }">
            <ImageCard
              :key="`${keyPrefix}-${index}`"
              :image="element"
              :index="index"
              :primary-label="index === 0 && primaryLabel ? primaryLabel : undefined"
              :class="{ 'drag-handle': !isDetailView }"
              @remove="handleRemove"
              @preview="() => handlePreviewRequest(index)"
              @update:filename="(idx, newName) => handleFilenameUpdateRequest(idx, newName)"
            />
          </template>
        </draggable>

        <!-- 添加更多按钮 -->
        <div
          v-if="!isDetailView && !readonly"
          class="h-full min-h-[200px] flex flex-col cursor-pointer items-center justify-center border-2 border-gray-300 rounded border-dashed transition-colors hover:border-#399e96 hover:bg-#399e96/5" @click.stop="props.handleClick"
        >
          <font-awesome-icon :icon="['fas', 'plus']" class="mb-2 text-xl text-gray-400" />
          <div class="text-xs text-gray-500">
            添加更多
          </div>
        </div>
      </div>

      <slot />
    </ImageDropZone>

    <slot name="after-dropzone" />
  </div>
</template>

<style scoped>
.ghost-card {
  opacity: 0.5;
  background: #c8ebfb;
  border: 1px dashed #399e96;
}

.drag-handle {
  cursor: move;
}

/* 预览模式下移除拖拽相关样式 */
:deep(.draggable-disabled .sortable-chosen) {
  background: transparent !important;
  box-shadow: none !important;
}
</style>
