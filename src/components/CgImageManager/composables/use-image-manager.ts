import type { GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'
import type { DragTarget, ImageInfo, ImageSection, ImageValue, SectionConfig } from '../types'
import { GetOssUploadCredentialAccessTypeEnum } from '@/apiv2/product'
import { OssUploader } from '@/utils/ossUploader'
import { imageSize } from 'image-size'

/**
 * 上传策略接口，用于配置不同模块的上传文件类型
 */
export interface UploadStrategy {
  /**
   * 获取上传文件类型
   * @param section 图片区域类型（main/sku/detail或自定义类型）
   * @param sku 可选的SKU编码，当section为sku时使用
   * @returns 上传文件类型枚举值
   */
  getUploadFileType: (section: ImageSection, sku?: string) => GetOssUploadCredentialUploadFileTypeEnum

  /**
   * 获取文件接受类型
   * @param section 图片区域类型
   * @returns 文件接受类型，例如 'image/*' 或 '.pdf,.doc'
   */
  getAcceptType?: (section: ImageSection) => string

  /**
   * 检查文件类型是否有效
   * @param file 要检查的文件
   * @param section 区域类型
   * @returns 如果文件类型有效返回 true，否则返回 false
   */
  isValidFileType?: (file: File, section: ImageSection) => boolean
}

/**
 * 通用图片管理的composable
 * @param props 组件props
 * @param props.modelValue 图片数据的响应式引用
 * @param props.skuList SKU列表
 * @param props.readonly 是否只读模式
 * @param props.uploadStrategy 上传策略
 * @param props.customSections 自定义区域配置
 * @param emit 组件emit函数
 */
export function useImageManager(
  propsRef: Ref<{
    modelValue?: ImageValue
    skuField?: string
    skuList?: string[]
    readonly?: boolean
    uploadStrategy: UploadStrategy
    customSections?: SectionConfig[]
  }>,
  emit: {
    (e: 'update:modelValue', value: ImageValue): void
    (e: 'upload-success', url: string, key: string): void
    (e: 'upload-error', err: any): void
  },
) {
  const { readonly, uploadStrategy, customSections } = toRefs(propsRef.value)

  const fileInputRefs = ref<Record<string, HTMLInputElement | null>>({})
  const dragTarget = ref<DragTarget | null>(null)

  /**
   * 设置文件输入引用
   */
  function setFileInputRef(el: Element | ComponentPublicInstance | null, key: string) {
    if (el instanceof HTMLInputElement) {
      fileInputRefs.value[key] = el
    }
    else {
      fileInputRefs.value[key] = null
    }
  }

  // 创建响应式引用
  const modelValue = computed({
    get: () => propsRef.value.modelValue || { main: [], sku: {} as Record<string, ImageInfo[]>, detail: [] },
    set: val => emit('update:modelValue', val),
  })

  /**
   * 上传图片
   */
  function uploadImage(file: File, section: ImageSection, sku?: string): Promise<string[]> {
    return new Promise((resolve, reject) => {
      const progress = (_percent: number) => {
        // 进度回调，可以在这里实现进度条等功能
      }

      const uploadFileType = uploadStrategy.value.getUploadFileType(section, sku)

      OssUploader.uploadFile(
        file,
        uploadFileType,
        GetOssUploadCredentialAccessTypeEnum.PRIVATE,
        progress,
        (url, key) => {
          resolve([url, key])
        },
        (error) => {
          reject(new Error(`图片上传失败: ${error}`))
          emit('upload-error', error)
        },
      )
    })
  }

  /**
   * 移除图片
   */
  function removeImage(section: ImageSection, index: number, skuId?: string) {
    const newValue = modelValue.value
    let imageToRemove: ImageInfo | undefined

    if (section === 'main' && newValue.main && newValue.main[index]) {
      const isFirstMainImage = index === 0
      imageToRemove = newValue.main[index]
      newValue.main.splice(index, 1)

      // 如果删除的是第一张主图，需要更新主图
      if (isFirstMainImage && newValue.main.length > 0) {
        const newFirstImage = newValue.main[0]
        emit('upload-success', newFirstImage.url, newFirstImage.imageKey || '')
      }
      else if (isFirstMainImage) {
        // 如果删除后没有主图了，发送空字符串
        emit('upload-success', '', '')
      }
    }
    else if (section === 'detail' && newValue.detail && newValue.detail[index]) {
      imageToRemove = newValue.detail[index]
      newValue.detail.splice(index, 1)
    }
    else if (section === 'sku' && skuId && newValue.sku && newValue.sku[skuId] && newValue.sku[skuId][index]) {
      imageToRemove = newValue.sku[skuId][index]
      newValue.sku[skuId].splice(index, 1)
    }
    // 处理自定义区域
    else if (section !== 'main' && section !== 'detail' && section !== 'sku' && newValue[section] && Array.isArray(newValue[section])) {
      const customImages = newValue[section] as ImageInfo[]
      if (customImages[index]) {
        imageToRemove = customImages[index]
        customImages.splice(index, 1)
      }
    }

    // 释放本地Blob URL资源
    if (imageToRemove?.localUrl) {
      try {
        URL.revokeObjectURL(imageToRemove.localUrl)
      }
      catch (error) {
        console.error(`释放本地Blob URL失败:`, error)
      }
    }

    // 将修改后的副本赋值回 ref, 触发更新
    modelValue.value = newValue
  }

  /**
   * 移除图片
   */
  function removeImageByLocalUrl(localUrl: string) {
    const newValue = modelValue.value
    let found = false
    let isFirstMainImageRemoved = false

    if (newValue.main) {
      const index = newValue.main.findIndex((img: ImageInfo) => img.localUrl === localUrl)
      if (index !== -1) {
        isFirstMainImageRemoved = index === 0
        newValue.main.splice(index, 1)
        found = true
      }
    }

    if (newValue.detail) {
      const index = newValue.detail.findIndex((img: ImageInfo) => img.localUrl === localUrl)
      if (index !== -1) {
        newValue.detail.splice(index, 1)
        found = true
      }
    }

    if (newValue.sku) {
      Object.keys(newValue.sku).forEach((key) => {
        if (newValue.sku[key]) {
          const index = newValue.sku[key].findIndex((img: ImageInfo) => img.localUrl === localUrl)
          if (index !== -1) {
            newValue.sku[key].splice(index, 1)
            found = true
          }
        }
      })
    }

    // 处理自定义区域
    // 遍历所有属性，查找自定义区域
    Object.keys(newValue).forEach((key) => {
      if (key !== 'main' && key !== 'detail' && key !== 'sku' && Array.isArray(newValue[key])) {
        const customImages = newValue[key] as ImageInfo[]
        const index = customImages.findIndex((img: ImageInfo) => img.localUrl === localUrl)
        if (index !== -1) {
          customImages.splice(index, 1)
          found = true
        }
      }
    })

    if (found) {
      try {
        URL.revokeObjectURL(localUrl)
      }
      catch (e) { console.error(e) }

      // 如果删除的是第一张主图，需要更新主图
      if (isFirstMainImageRemoved) {
        if (newValue.main && newValue.main.length > 0) {
          emit('upload-success', newValue.main[0].url, newValue.main[0].imageKey || '')
        }
        else {
          emit('upload-success', '', '')
        }
      }

      // 赋值副本回 ref
      modelValue.value = newValue
    }
  }

  /**
   * 处理拖拽区域拖拽开始
   */
  function handleDropZoneDragOver(section: ImageSection, skuId?: string, event?: DragEvent) {
    if (event?.dataTransfer && event.dataTransfer.types.includes('application/internal-image-card')) {
      return
    }

    dragTarget.value = { section, skuId }
  }

  /**
   * 处理拖拽区域拖拽离开
   */
  function handleDropZoneDragLeave(event?: DragEvent) {
    if (event?.dataTransfer && event.dataTransfer.types.includes('application/internal-image-card')) {
      return
    }

    dragTarget.value = null
  }

  /**
   * 处理拖拽区域放置
   */
  function handleDropZoneDrop(event: DragEvent, section: ImageSection, skuId?: string) {
    if (event.dataTransfer && event.dataTransfer.types.includes('application/internal-image-card')) {
      return
    }

    dragTarget.value = null
    const files = event.dataTransfer?.files
    if (files && files.length > 0) {
      // 批量处理所有文件
      const imageFiles = Array.from(files).filter(file => file.type.startsWith('image/'))

      if (imageFiles.length === 0) {
        ElMessage.error('请拖拽图片文件')
        return
      }

      // 批量创建 ImageInfo 对象
      const imageInfos = imageFiles.map(file => buildImageInfo(file))

      // 一次性合并所有图片
      mergeImages(imageInfos, section, skuId)
    }
  }

  /**
   * 创建图片信息对象
   */
  function buildImageInfo(file: File): ImageInfo {
    const localBlobUrl = URL.createObjectURL(file)

    const img: ImageInfo = {
      url: localBlobUrl,
      imageKey: '',
      filename: file.name,
      size: Math.round(file.size / 1024),
      dimensions: undefined,
    }

    // 让 localUrl 不参与 JSON.stringify
    Object.defineProperty(img, 'localUrl', {
      value: localBlobUrl,
      writable: true,
      enumerable: false,
    })

    return img
  }

  /**
   * 合并多个图片到当前状态
   */
  function mergeImages(images: ImageInfo[], section: ImageSection, skuId?: string) {
    const newValue = modelValue.value

    if (section === 'main') {
      if (!newValue.main)
        newValue.main = []
      const isFirstMainImage = newValue.main.length === 0 && images.length > 0
      newValue.main = [...newValue.main, ...images]

      // 如果是第一张主图，立即触发主图更新事件
      if (isFirstMainImage) {
        const firstImage = images[0]
        if (firstImage && firstImage.url) {
          // 在上传完成后会更新 imageKey，这里先使用本地URL更新显示
          emit('upload-success', firstImage.url, firstImage.imageKey || '')
        }
      }
    }
    else if (section === 'detail') {
      if (!newValue.detail)
        newValue.detail = []
      newValue.detail = [...newValue.detail, ...images]
    }
    else if (section === 'sku' && skuId) {
      if (!newValue.sku)
        newValue.sku = {}
      if (!newValue.sku[skuId])
        newValue.sku[skuId] = []
      newValue.sku[skuId] = [...newValue.sku[skuId], ...images]
    }
    // 处理自定义区域
    else if (section !== 'main' && section !== 'detail' && section !== 'sku') {
      if (!newValue[section]) {
        newValue[section] = []
      }

      if (Array.isArray(newValue[section])) {
        (newValue[section] as ImageInfo[]) = [...(newValue[section] as ImageInfo[]), ...images]
      }
    }

    modelValue.value = newValue

    // 异步加载图片尺寸和上传到OSS
    images.forEach(async (imageInfo) => {
      // 从 localUrl 获取原始 File 对象
      const fileUrl = imageInfo.localUrl
      if (!fileUrl)
        return

      // 加载图片尺寸
      const img = new Image()
      img.onload = async () => {
        try {
          // 使用 fetch 获取 Blob
          const response = await fetch(fileUrl)
          const blob = await response.blob()
          const arrayBuffer = await blob.arrayBuffer()
          const uint8Array = new Uint8Array(arrayBuffer)

          // 更新尺寸信息
          const dimensions = imageSize(uint8Array) as { width: number, height: number, type?: string }

          // 更新当前图片的尺寸
          updateImageDimensions(imageInfo, dimensions, section, skuId)

          // 上传到OSS
          uploadImageByLocalUrl(imageInfo.localUrl!, section, skuId)
        }
        catch (err) {
          console.error('获取图片尺寸失败:', err)
        }
      }
      img.onerror = () => {
        console.error('Image failed to load for dimension check:', fileUrl)
      }
      img.src = fileUrl
    })
  }

  /**
   * 更新图片尺寸
   */
  function updateImageDimensions(imageInfo: ImageInfo, dimensions: { width: number, height: number }, section: ImageSection, skuId?: string) {
    const newValue = modelValue.value
    let updated = false

    if (section === 'main' && newValue.main) {
      const index = newValue.main.findIndex((img: ImageInfo) => img.localUrl === imageInfo.localUrl)
      if (index !== -1) {
        newValue.main[index].dimensions = dimensions
        updated = true
      }
    }
    else if (section === 'detail' && newValue.detail) {
      const index = newValue.detail.findIndex((img: ImageInfo) => img.localUrl === imageInfo.localUrl)
      if (index !== -1) {
        newValue.detail[index].dimensions = dimensions
        updated = true
      }
    }
    else if (section === 'sku' && skuId && newValue.sku && newValue.sku[skuId]) {
      const index = newValue.sku[skuId].findIndex((img: ImageInfo) => img.localUrl === imageInfo.localUrl)
      if (index !== -1) {
        newValue.sku[skuId][index].dimensions = dimensions
        updated = true
      }
    }
    // 处理自定义区域
    else if (section !== 'main' && section !== 'detail' && section !== 'sku' && newValue[section] && Array.isArray(newValue[section])) {
      const customImages = newValue[section] as ImageInfo[]
      const index = customImages.findIndex((img: ImageInfo) => img.localUrl === imageInfo.localUrl)
      if (index !== -1) {
        customImages[index].dimensions = dimensions
        updated = true
      }
    }

    if (updated) {
      modelValue.value = newValue
    }
  }

  /**
   * 根据本地URL上传图片
   */
  async function uploadImageByLocalUrl(localUrl: string, section: ImageSection, skuId?: string) {
    try {
      // 使用 fetch 获取 Blob
      const response = await fetch(localUrl)
      const blob = await response.blob()

      // 上传到OSS
      const [ossUrl, imageKey] = await uploadImage(new File([blob], 'image.jpg', { type: blob.type }), section, skuId)

      // 更新图片信息
      updateImageUrl(localUrl, ossUrl, imageKey, section, skuId)

      // 触发上传成功事件
      emit('upload-success', ossUrl, imageKey)
    }
    catch (error) {
      console.error('上传失败:', error)
      removeImageByLocalUrl(localUrl)
      emit('upload-error', error)
    }
  }

  /**
   * 更新图片URL和键
   */
  function updateImageUrl(localUrl: string, ossUrl: string, imageKey: string, section: ImageSection, skuId?: string) {
    const newValue = modelValue.value
    let img: ImageInfo | undefined

    if (section === 'main')
      img = newValue.main.find(i => i.localUrl === localUrl)
    else if (section === 'detail')
      img = newValue.detail.find(i => i.localUrl === localUrl)
    else if (section === 'sku' && skuId)
      img = (newValue.sku as any)?.[skuId]?.find((i: { localUrl: string }) => i.localUrl === localUrl)
    else if (section !== 'main' && section !== 'detail' && section !== 'sku')
      img = (newValue as any)[section]?.find?.((i: ImageInfo) => i.localUrl === localUrl)

    if (img) {
      img.url = ossUrl
      img.imageKey = imageKey
      ElMessage.success({ message: '文件上传成功', grouping: true, type: 'success' })
    }
  }

  /**
   * 处理文件选择
   */
  function handleFileSelect(event: Event, section: ImageSection, skuId?: string) {
    const input = event.target as HTMLInputElement
    if (input.files && input.files.length > 0) {
      // 批量处理所有文件
      const files = Array.from(input.files)
      let validFiles: File[] = []

      // 使用自定义的文件类型验证器（如果提供）
      if (uploadStrategy.value.isValidFileType) {
        validFiles = files.filter(file => uploadStrategy.value.isValidFileType!(file, section))
      }
      else {
        validFiles = files.filter(file => file.type.startsWith('image/'))
      }

      if (validFiles.length === 0) {
        ElMessage.error('请上传有效的文件')
        return
      }

      // 批量创建 ImageInfo 对象
      const imageInfos = validFiles.map(file => buildImageInfo(file))

      // 一次性合并所有文件
      mergeImages(imageInfos, section, skuId)

      // 清空输入框
      input.value = ''
    }
  }

  /**
   * 打开文件输入
   */
  function openFileInput(key: string) {
    const input = fileInputRefs.value[key]
    if (input) {
      input.click()
    }
  }

  /**
   * SKU ID列表
   */
  const skuIds = computed(() => {
    try {
      const { skuList, skuField } = propsRef.value

      if (skuList && skuList.length) {
        return skuList
      }

      if (!skuField) {
        return []
      }

      const fieldInModel = propsRef.value.modelValue?.[skuField]

      if (fieldInModel && typeof fieldInModel === 'object') {
        const keys = Object.keys(fieldInModel as Record<string, ImageInfo[]>)
        return keys
      }

      return []
    }
    catch (error) {
      console.error('计算 skuIds 时发生错误:', error)
      return []
    }
  })

  /**
   * 判断是否为拖拽目标
   */
  function isDragTarget(section: ImageSection, skuId?: string): boolean {
    if (!dragTarget.value)
      return false

    if (dragTarget.value.section !== section)
      return false

    if (section === 'sku') {
      return dragTarget.value.skuId === skuId
    }

    return true
  }

  /**
   * 获取所有输入键
   */
  const allInputKeys = computed(() => {
    const keys = ['main', 'detail']
    // 添加 SKU 输入键
    skuIds.value.forEach((skuId) => {
      keys.push(`sku-${skuId}`)
    })

    // 添加自定义区域输入键
    if (customSections?.value?.length) {
      customSections.value
        .filter(s => s.display !== 'fileTable') // 只为 image 区域生成隐藏 input
        .forEach(s => keys.push(s.key))
    }

    return keys
  })

  /**
   * 创建图片区域组件的props
   */
  function sectionProps(section: ImageSection, skuCode?: string) {
    const sectionKey = skuCode ? `sku-${skuCode}` : section
    let images: ImageInfo[] = []

    if (section === 'main') {
      images = modelValue.value.main || []
    }
    else if (section === 'detail') {
      images = modelValue.value.detail || []
    }
    else if (section === 'sku' && skuCode) {
      // Ensure sku is a valid Record before accessing with string index
      const skuRecord = modelValue.value.sku as Record<string, ImageInfo[]>
      images = (skuRecord && typeof skuRecord === 'object') ? (skuRecord[skuCode] || []) : []
    }
    // 处理自定义区域
    else if (section !== 'main' && section !== 'detail' && section !== 'sku') {
      // 使用类型断言处理自定义区域
      const customValue = modelValue.value as Record<string, any>
      if (customValue[section] && Array.isArray(customValue[section])) {
        images = customValue[section] as ImageInfo[]
      }
      else {
        images = []
      }
    }

    return {
      section,
      images,
      skuCode,
      sectionKey,
      readonly: readonly?.value,
      isDragOver: isDragTarget(section, skuCode),
      handleDragOver: () => handleDropZoneDragOver(section, skuCode),
      handleDragLeave: () => handleDropZoneDragLeave(),
      handleDrop: (event: DragEvent) => handleDropZoneDrop(event, section, skuCode),
      handleClick: () => openFileInput(sectionKey),
      handleRemove: (index: number) => removeImage(section, index, skuCode),
      onUpdate: (images: ImageInfo[]) => {
        const newValue = { ...modelValue.value }
        const isMainSection = section === 'main'
        const hadMainImages = isMainSection && newValue.main && newValue.main.length > 0
        const firstMainImageChanged = isMainSection && hadMainImages
          && (newValue.main[0]?.url !== images[0]?.url || newValue.main[0]?.imageKey !== images[0]?.imageKey)

        if (isMainSection) {
          newValue.main = images
          // 如果主图的第一张图片变化了，需要更新主图
          if (firstMainImageChanged && images.length > 0) {
            emit('upload-success', images[0].url, images[0].imageKey || '')
          }
        }
        else if (section === 'detail') {
          newValue.detail = images
        }
        else if (section === 'sku' && skuCode) {
          if (!newValue.sku)
            newValue.sku = {}
          const skuRecord = newValue.sku as Record<string, ImageInfo[]>
          skuRecord[skuCode] = images
        }
        // 处理自定义区域
        else if (section !== 'main' && section !== 'detail' && section !== 'sku') {
          const customValue = newValue as Record<string, any>
          customValue[section] = images
        }

        modelValue.value = newValue
      },
    }
  }

  return {
    fileInputRefs,
    dragTarget,
    setFileInputRef,
    handleFileSelect,
    skuIds,
    isDragTarget,
    allInputKeys,
    sectionProps,
    removeImage,
    uploadImage,
  }
}
