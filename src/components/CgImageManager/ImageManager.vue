<script setup lang="ts">
import type { UploadStrategyName } from '@/utils/imageUploadStrategyRegistry'
import type { FileRow } from '../CgFileTableUploader/CgFileTableUploader.vue'
import type { UploadStrategy } from './composables/use-image-manager'
import type { ImageInfo, ImageSection, ImageValue, SectionConfig } from './types'
import { GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'
import { getUploadStrategy } from '@/utils/imageUploadStrategyRegistry'
import { useImageManager } from './composables/use-image-manager'
import ImageSectionComponent from './internal/ImageSection.vue'

interface Props {
  modelValue?: ImageValue
  skuField?: string
  skuList?: string[] // 没有 sku 功能就传空
  readonly?: boolean
  uploadStrategyName: UploadStrategyName
  viewEmptyText?: string
  // 自定义区域配置
  customSections?: SectionConfig[]
}

defineOptions({
  name: 'CgImageManager',
})

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => ({ main: [], sku: {}, detail: [] }),
  skuField: 'sku',
  skuList: () => [],
  readonly: false,
  uploadStrategyName: 'product',
  viewEmptyText: '-',
  customSections: () => [],
})

const emit = defineEmits<{
  'update:modelValue': [ImageValue]
  'upload-success': [url: string, key: string]
  'upload-error': [err: any]
}>()

// 图片预览相关状态
provide('isDetailView', computed(() => props.readonly))
const showPreview = ref(false)
const previewImages = ref<string[]>([])
const previewIndex = ref(0)

// 处理图片预览
function handlePreview(images: ImageInfo[], index: number) {
  previewImages.value = images.map(img => img.localUrl || img.url).filter(Boolean) // 过滤掉无效url
  if (previewImages.value.length > 0 && index >= 0 && index < previewImages.value.length) {
    previewIndex.value = index
    showPreview.value = true
  }
}

const resolvedUploadStrategy = computed(() => {
  const strategy = getUploadStrategy(props.uploadStrategyName)
  if (!strategy) {
    console.error(`CgImageManager: 未找到名为 "${props.uploadStrategyName}" 的上传策略。将使用默认策略。`)
    return {
      getUploadFileType: (_section: ImageSection, _sku?: string) => GetOssUploadCredentialUploadFileTypeEnum.PRODUCT_FILES,
    } as UploadStrategy
  }
  return strategy
})

const managerProps = computed(() => ({
  modelValue: props.modelValue,
  skuField: props.skuField,
  skuList: props.skuList,
  readonly: props.readonly,
  uploadStrategy: resolvedUploadStrategy.value,
  customSections: props.customSections,
}))

// 调用 composable
const {
  setFileInputRef,
  handleFileSelect,
  skuIds,
  allInputKeys,
  sectionProps,
} = useImageManager(managerProps, emit)

// 处理文件名更新
function handleFilenameUpdate(payload: { index: number, newFilename: string, section: ImageSection, skuId?: string }) {
  const { index, newFilename, section, skuId } = payload
  const newValue = props.modelValue

  try {
    if (section === 'main' && newValue.main && newValue.main[index]) {
      newValue.main[index].filename = newFilename
    }
    else if (section === 'detail' && newValue.detail && newValue.detail[index]) {
      newValue.detail[index].filename = newFilename
    }
    else if (section === 'sku' && skuId && newValue.sku && newValue.sku[skuId] && newValue.sku[skuId][index]) {
      newValue.sku[skuId][index].filename = newFilename
    }
    // 处理自定义区域
    else if (section !== 'main' && section !== 'detail' && section !== 'sku') {
      // 检查自定义区域是否存在
      const customSection = props.customSections?.find(s => s.key === section)
      if (customSection && newValue[section] && Array.isArray(newValue[section])) {
        const images = newValue[section] as ImageInfo[]
        if (images[index]) {
          images[index].filename = newFilename
        }
      }
    }

    emit('update:modelValue', newValue)
  }
  catch (error) {
    console.error('Failed to update filename:', error)
  }
}
</script>

<template>
  <div class="h-full pr-4">
    <!-- 隐藏的文件输入框 -->
    <template v-if="!readonly">
      <input
        v-for="key in allInputKeys" :key="`input-${key}`"
        :ref="el => setFileInputRef(el, key)" type="file"
        :accept="resolvedUploadStrategy.getAcceptType ? resolvedUploadStrategy.getAcceptType(key.startsWith('sku-') ? 'sku' : key as ImageSection) : 'image/*'"
        class="hidden" multiple
        @change="(e) => {
          const section = key.startsWith('sku-') ? 'sku' : key as ImageSection
          const skuId = key.startsWith('sku-') ? key.substring(4) : undefined
          handleFileSelect(e, section, skuId)
        }"
      >
    </template>

    <!-- 主图 -->
    <ImageSectionComponent
      v-bind="sectionProps('main')"
      title="主图"
      empty-text="拖拽图片到此区域或点击上传主图"
      key-prefix="main"
      :use-draggable="true"
      primary-label="主图"
      @preview="(index: number) => handlePreview(props.modelValue.main, index)"
      @update-filename="handleFilenameUpdate"
    />

    <!-- SKU 图 -->
    <template v-for="sku in skuIds" :key="sku">
      <ImageSectionComponent
        v-bind="sectionProps('sku', sku)"
        :title="`SKU图片 - ${sku}`"
        empty-text="拖拽图片到此区域或点击上传SKU图片"
        :key-prefix="`sku-${sku}`"
        :use-draggable="true"
        @preview="(index: number) => handlePreview((props.modelValue[props.skuField] as any)[sku] || [], index)"
        @update-filename="handleFilenameUpdate"
      />
    </template>

    <!-- 详情图 -->
    <ImageSectionComponent
      v-bind="sectionProps('detail')"
      title="详情图"
      empty-text="拖拽图片到此区域或点击上传详情图"
      key-prefix="detail"
      :use-draggable="true"
      @preview="(index: number) => handlePreview(props.modelValue.detail, index)"
      @update-filename="handleFilenameUpdate"
    />

    <!-- 自定义区域 -->
    <template v-for="section in props.customSections" :key="section.key">
      <!-- 图片模式 -->
      <ImageSectionComponent
        v-if="section.display !== 'fileTable'"
        v-bind="sectionProps(section.key)"
        :title="section.title"
        :empty-text="section.emptyText || `拖拽文件到此区域或点击上传${section.title}`"
        :key-prefix="section.key"
        :use-draggable="section.useDraggable !== false"
        :primary-label="section.primaryLabel"
        @preview="idx => handlePreview(props.modelValue[section.key] as ImageInfo[] || [], idx)"
        @update-filename="handleFilenameUpdate"
      />

      <!-- fileTable 模式 -->
      <CgFileTableUploader
        v-else
        :model-value="(props.modelValue[section.key] as any[]) || []"
        :title="section.title"
        :oss-folder="section.ossFolder!"
        :accept="section.accept || '*'"
        :multiple="false"
        :readonly="props.readonly"
        class="mb-6"
        @update:model-value="(val: FileRow[]) => emit('update:modelValue', { ...props.modelValue, [section.key]: val })"
      />
    </template>

    <!-- 全屏预览 -->
    <el-image-viewer
      v-if="showPreview"
      :url-list="previewImages"
      :initial-index="previewIndex"
      :hide-on-click-modal="true"
      @close="showPreview = false"
    />
  </div>
</template>
