<script setup lang="ts">
import type { AuditLogPageReqModuleEnum } from '@/apiv2/product'
import type { GridProps } from '../CgGrid'
import { logApi } from '@/api.services/product.service'
import { GridCellFormatName } from '../CgGrid'

defineOptions({
  name: 'CgOperateLog',
})

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: undefined,
  },
  id: {
    type: Number as PropType<number | null>,
    default: null,
  },
  module: {
    type: String as PropType<AuditLogPageReqModuleEnum | null>,
    default: null,
  },
})
const emit = defineEmits(['update:modelValue'])
const modelValue = useVModel(props, 'modelValue', emit)
const gridOption = ref<GridProps>({
  options: {
    border: false,
    height: '500',
    columns: [
      {
        field: 'operateTime',
        title: '时间',
        width: 160,
        formatter: [GridCellFormatName.FormatDate, 'YYYY-MM-DD HH:mm:ss'],
      },
      {
        field: 'operaType',
        title: '操作',
        width: 80,
        formatter: ({ cellValue }) => {
          switch (cellValue) {
            case 'CREATE':
              return '创建'
            case 'EDIT':
              return '修改'
            case 'DELETE':
              return '删除'
            default:
              return cellValue
          }
        },
      },
      {
        field: 'details',
        title: '详情',
        showOverflow: false,
        slots: {
          default: 'detailsTemp',
        },
      },
    ],
    showOverflow: false,
  },
  proxyOption: {
    request: logApi.queryLog,
    query: {
      auditLogPageReq: {
        id: props.id,
        module: props.module,
      },
    },
  },
  paginationSmall: true,
})
</script>

<template>
  <CgDialog v-model="modelValue" destroy-on-close title="操作日志" :confirm-button-visible="false" :full-height="false">
    <CgGrid v-bind="gridOption">
      <template #detailsTemp="{ row }">
        <div class="w-full">
          <div
            v-for="(detail, index) in row.details" :key="index"
            class="flex items-start border-b border-gray-200 border-dashed py-1.5 text-gray-700 last:border-0"
          >
            <p class="break-words text-sm leading-normal">
              {{ detail.operateContent }}
            </p>
          </div>
        </div>
      </template>
    </CgGrid>
  </CgDialog>
</template>

<style lang="scss" scoped></style>
