<script setup lang="ts">
interface CopyProps {
  text?: string
  inlineFlex?: boolean
}
defineOptions({
  name: 'CgC<PERSON>',
  inheritAttrs: false,
})
const props = withDefaults(defineProps<CopyProps>(), {
  text: '',
})
const { text, copy, isSupported } = useClipboard()
function handleCopy() {
  if (isSupported) {
    copy(props.text)
    ElMessage.success('复制成功')
  }

  else { ElMessage.error('Your browser or device does not support the copy event') }
}
</script>

<template>
  <div class="cg-copy" :class="{ 'is-inline-flex': inlineFlex }">
    <slot />
    <ElTooltip v-if="text !== '-'" effect="dark" content="复制" placement="top">
      <ElIcon class="cg-copy__icon" @click="handleCopy">
        <IEpDocumentCopy />
      </ElIcon>
    </ElTooltip>
  </div>
</template>

<style lang="scss">
  .cg-copy {
    // width: 100%;
    display: inline-flex;
    align-items: center;
    &.is-inline-flex {
      display: inline-flex;
    }

    &__icon {
      opacity: 0;
      //color: #888;
      margin-left: 3px;
      cursor: pointer;
      &:hover {
        color: var(--el-color-primary);
      }
    }
    &:hover {
      i {
        opacity: 1;
      }
    }
  }
</style>
