<script setup lang="ts">
interface SvgIconProps {
  iconName: string
  className?: string
  type?: string
}
defineOptions({
  name: 'CgSvgIcon',
  inheritAttrs: false,
})
const props = defineProps<SvgIconProps>()
const svgName = computed(() => `#icon-${props.iconName}`)

const svgClass = computed(() => {
  return props.className ? `cg-svg-icon ${props.className}` : 'cg-svg-icon'
})
const attrs = computed(() => useAttrs())
</script>

<template>
  <i v-if="type === 'iconfont'" v-bind="attrs" class="iconfont" :class="iconName" />
  <svg v-else v-bind="attrs" :class="svgClass">
    <use :xlink:href="svgName" />
  </svg>
</template>

<style>
  .cg-svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
</style>
