import type { RawEditorOptions, Editor as TinyMCEEditor } from 'tinymce'

export interface EditorEvents {
  onInit?: (editor: TinyMCEEditor) => void
  onActivate?: (editor: TinyMCEEditor) => void
  onDeactivate?: (editor: TinyMCEEditor) => void
  onFocus?: (event: FocusEvent) => void
  onBlur?: (event: FocusEvent) => void
  onBeforeSetContent?: (e: { content: string, format?: string }) => void
  onSetContent?: (e: { content: string, format?: string }) => void
  onGetContent?: (e: { content: string, format?: string }) => void
  onChange?: (e: { level: { content: string, format?: string } }) => void
  onUndo?: (level: { content: string, format?: string }) => void
  onRedo?: (level: { content: string, format?: string }) => void
  onBeforeAddUndo?: (e: { level: { content: string, format?: string } }) => void
  onAddUndo?: (e: { level: { content: string, format?: string } }) => void
  onKeyUp?: (e: KeyboardEvent) => void
  onKeyDown?: (e: KeyboardEvent) => void
  onKeyPress?: (e: KeyboardEvent) => void
  onClick?: (e: MouseEvent) => void
  onMouseUp?: (e: MouseEvent) => void
  onMouseDown?: (e: MouseEvent) => void
  onPaste?: (e: ClipboardEvent) => void
  onCut?: (e: ClipboardEvent) => void
  onCopy?: (e: ClipboardEvent) => void
}

export interface EditorConfig extends RawEditorOptions {
  // 如果需要扩展额外的配置，可以在这里添加
  // 但要确保不覆盖原有的类型
}

export type EditorAttributes = Partial<{
  class: string | string[] | Record<string, boolean>
  style: string | Record<string, string>
}> & EditorEvents
