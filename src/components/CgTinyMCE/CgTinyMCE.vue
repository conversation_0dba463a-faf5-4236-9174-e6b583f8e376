<script setup lang="ts">
import type { Editor as TinyMCEEditor } from 'tinymce'
import type { EditorAttributes, EditorConfig, EditorEvents } from './types'
import { GetOssUploadCredentialAccessTypeEnum, GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'
import { OssUploader } from '@/utils/ossUploader'
import Editor from '@tinymce/tinymce-vue'
import tinymce from 'tinymce/tinymce'
import 'tinymce/themes/silver'
import 'tinymce/models/dom'
// 编辑器插件plugins
// 更多插件参考：https://www.tiny.cloud/docs/plugins/
import 'tinymce/plugins/image' // 插入上传图片插件
import 'tinymce/plugins/media' // 插入视频插件
import 'tinymce/plugins/table' // 插入表格插件
import 'tinymce/plugins/lists' // 列表插件
import 'tinymce/plugins/wordcount' // 字数统计插件
import 'tinymce/icons/default/icons'

// 定义组件名称
defineOptions({
  name: 'CgTinyMCE',
  inheritAttrs: false,
})

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  simple: false,
  plugins: () => ['lists', 'image', 'media', 'table', 'wordcount'],
  toolbar: () => ['undo redo | | blocks fontfamily fontsize | bold italic forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | table | image | removeformat | wordcount'],
  baseUrl: '.',
  init: () => ({}),
  events: () => ({}),
  imageFileType: GetOssUploadCredentialUploadFileTypeEnum.CATEGORY_ICON, // TODO 接口增加枚举
  accessType: GetOssUploadCredentialAccessTypeEnum.PUBLIC,
})

// 定义插槽类型
defineSlots<{
  default?: (props: { editor: any }) => any
}>()

// 使用 defineModel 替代 modelValue prop
const content = defineModel<string>('modelValue', {
  default: '',
})

// 定义组件 props
interface Props {
  disabled?: boolean
  simple?: boolean
  plugins?: string | string[]
  toolbar?: string | string[]
  baseUrl?: string
  init?: Partial<EditorConfig>
  events?: EditorEvents
  imageFileType?: GetOssUploadCredentialUploadFileTypeEnum
  accessType?: GetOssUploadCredentialAccessTypeEnum
}

const editorId = ref(`tinymce_${Date.now()}`)
const vEditor = ref<TinyMCEEditor>()
const uploadingRef = ref(false)

// 合并 attrs 和 events
const mergedAttrs = computed<EditorAttributes>(() => ({
  ...useAttrs(),
  ...props.events,
}))

function handleImageUpload(blobInfo: any, progress: (percent: number) => void) {
  return new Promise<string>((resolve, reject) => {
    const file = blobInfo.blob()

    if (!file) {
      reject(new Error('文件不存在'))
      return
    }

    uploadingRef.value = true

    OssUploader.uploadFile(
      file,
      props.imageFileType,
      props.accessType,
      progress,
      (url) => {
        uploadingRef.value = false
        resolve(url)
        ElMessage.success('图片上传成功')
      },
      (error) => {
        uploadingRef.value = false
        reject(new Error(`图片上传失败: ${error}`))
        ElMessage.error('图片上传失败')
      },
    )
  })
}

const toolbarConfig = computed(() => {
  if (props.simple) {
    // 精简版工具栏配置
    return ['bold italic | forecolor | alignleft aligncenter alignright | bullist numlist | image | removeformat']
  }
  return props.toolbar
})

// 使用计算属性处理配置
const defaultInit = computed(() => ({
  language_url: `${props.baseUrl}/tinymce/langs/zh_CN.js`,
  language: 'zh_CN',
  skin_url: `${props.baseUrl}/tinymce/skins/ui/oxide`,
  content_css: `${props.baseUrl}/tinymce/skins/content/default/content.css`,
  height: '100%',
  plugins: props.plugins,
  toolbar: toolbarConfig.value,
  branding: false,
  menubar: false,
  content_style: 'p { margin: 0; }',
  statusbar: false,
  font_size_formats: '8px 10px 12px 14px 16px 18px 24px 36px',
  font_family_formats: '微软雅黑=Microsoft YaHei,微软雅黑;苹方=PingFang SC,苹方-简;黑体=SimHei,黑体;宋体=SimSun,宋体;楷体=KaiTi,楷体;仿宋=FangSong,仿宋;Arial=arial,helvetica,sans-serif;Times=times new roman,times;思源黑体=Source Han Sans SC,Noto Sans SC;',
  // 图片上传相关配置
  images_upload_handler: handleImageUpload,
  automatic_uploads: true,
  file_picker_types: 'image',
  image_title: true,
  image_description: false,
  image_dimensions: false,
  paste_data_images: true,
}))

const initData = computed(() => ({
  ...defaultInit.value,
  ...props.init,
  license_key: 'gpl',
}))

onMounted(() => {
  tinymce.init(initData.value)
})

function getEditor() {
  return tinymce.get(editorId.value)
}

// 暴露组件方法
defineExpose({
  getEditor,
  editor: vEditor,
  isUploading: uploadingRef,
})
</script>

<template>
  <Editor :id="editorId" ref="vEditor" v-model="content" :init="initData" :disabled="disabled" v-bind="mergedAttrs" />
</template>
