import type { InjectionKey } from 'vue'
import type { OptionProps } from './select'
import type { useSelectStates } from './use-select'

export interface SelectV2Context {
  props: {
    multiple?: boolean
    props: OptionProps
    modelValue?: string | number | unknown | unknown[]
    popperClass?: string
    fitInputWidth?: boolean
    filterMethod: (val: string, data: any, node: any) => boolean
  }
  selectWrapper: HTMLElement
  filterTextChange: string
  filteredOptionsCount: number
  cachedOptions: Map<any, any>
  optionsCount: number
  propsMap: OptionProps
  onOptionCreate: (option: Record<string, any>, data: Record<string, any>) => void
  onOptionDestroy: (option: Record<string, any>) => void
  handleOptionSelect: (option: Record<string, any>) => void
}
export const selectV2Key: InjectionKey<SelectV2Context> = Symbol('CgSelectV2')
export type States = ReturnType<typeof useSelectStates>
