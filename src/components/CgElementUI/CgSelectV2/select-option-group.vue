<script setup lang="ts">
import type { VNode } from 'vue'
import type { OptionData } from './use-option'
import { get, isFunction } from 'xe-utils'
import { selectV2Key } from './common'

defineOptions({
  name: 'CgSelectV2OptionGroup',
})
const props = defineProps({
  label: {
    type: [String, Number],
    default: '',
  },
  optionsData: {
    type: Array as PropType<OptionData[]>,
    default: () => [],
  },
})

const select = inject(selectV2Key)!
const visible = ref(true)
const children = ref<any>([])

watch(
  () => select.filterTextChange,
  () => {
    visible.value = children.value.some((option: any) => option.visible === true)
  },
  { flush: 'post' },
)

const instance = getCurrentInstance()
onMounted(() => {
  if (instance)
    children.value = flattedChildren(instance.subTree)
})

function flattedChildren(node: VNode) {
  const children: any[] = []
  if (Array.isArray(node.children)) {
    node.children.forEach((child: any) => {
      if (child) {
        if (
          child.type
          && child.type.name === 'CgSelectV2Option'
          && child.component
          && child.component.proxy
        ) {
          children.push(child.component.proxy)
        }

        else if (child.children?.length) {
          children.push(...flattedChildren(child))
        }
      }
    })
  }
  return children
}

const groupVisible = computed(() => {
  const filterValue = select.filterTextChange
  if (!filterValue)
    return true

  if (!props.optionsData || props.optionsData.length === 0)
    return false

  return props.optionsData.some((optionData) => {
    const propsMap = select.propsMap
    const node = {
      label: get(optionData, propsMap.label!),
      value: get(optionData, propsMap.value!),
      disabled: get(optionData, propsMap.disabled!),
    }

    if (isFunction(select.props.filterMethod))
      return select.props.filterMethod(filterValue, optionData, node)

    const label = node.label || node.value
    return label ? String(label).toLocaleLowerCase().includes(filterValue.toLocaleLowerCase()) : false
  })
})
</script>

<template>
  <ul v-show="groupVisible" class="el-select-group__wrap">
    <li class="el-select-group__title">
      {{ label }}
    </li>
    <li>
      <ul class="el-select-group">
        <slot />
      </ul>
    </li>
  </ul>
</template>
