import { get, isEqual, isFunction } from 'xe-utils'
import { selectV2Key } from './common'

export interface OptionData {
  [key: string]: any
}
export function useOption(props: { data: OptionData }) {
  const visible = ref(true)
  const select = inject(selectV2Key)!
  const getValByProp: any = (
    prop: 'value' | 'label' | 'disabled',
  ) => {
    const propVal = select!.propsMap[prop]!
    return get(props.data, propVal)
  }
  const optionNode = computed(() => {
    return {
      label: getValByProp('label'),
      value: getValByProp('value'),
      disabled: getValByProp('disabled'),
    }
  })
  const currentLabel = computed(() => {
    return optionNode.value.label || optionNode.value.value
  })

  const itemSelected = computed(() => {
    const node = unref(optionNode)
    if (!select.props.multiple)
      return isEqual(node.value, select.props.modelValue)
    else
      return Array.isArray(select.props.modelValue) ? select.props.modelValue.includes(node.value) : false
  })
  const filterMethod = (value: string) => {
    if (isFunction(select.props.filterMethod))
      return select.props.filterMethod(value, props.data, unref(optionNode))

    return value ? optionNode.value.label.toLocaleLowerCase().includes(value.toLocaleLowerCase()) : true
  }

  watch(
    () => select.filterTextChange,
    (val) => {
      visible.value = filterMethod(val)
    },
  )
  return {
    visible,
    currentLabel,
    itemSelected,
    select,
    optionNode,
  }
}
