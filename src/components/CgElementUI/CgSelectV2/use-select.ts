import type { ElInput, ElTooltip } from 'element-plus'
import type { States } from './common'
import type { OptionProps, SelectV2Props } from './select'
import { until, useDebounceFn, useResizeObserver } from '@vueuse/core'
import { useFormItem } from 'element-plus'
import { isBoolean, isEqual, isFunction, isNumber } from 'xe-utils'
import { isEmpty, isNullOrUndefined } from '../../utils'

export function useSelectStates(props: SelectV2Props) {
  return reactive({
    reverse: false,
    focused: false,
    inputHovering: false,
    dropMenuVisible: false,
    filteredOptionsCount: 0,
    optionsCount: 0,
    filterText: '',
    cachedOptions: new Map(),
    cachedDatas: new Map(),
    selected: [] as { value: any, label: any }[],
    currentPlaceholder: props.placeholder || '请选择',
  })
}
export function useSelect(props: SelectV2Props, {
  modelValue,
  selectData,
  states,
  emit,
}: {
  modelValue: Ref<typeof props.modelValue>
  selectData: Ref<any[]>
  states: States
  emit: (name: any, ...args: any[]) => void
}) {
  // const states = reactive<any>({
  //   reverse: false,
  //   focused: false,
  //   inputHovering: false,
  //   dropMenuVisible: false,
  //   filteredOptionsCount: 0,
  //   filterText: "",
  //   cachedOptions: new Map(),
  //   cachedDatas: new Map(),
  //   selected: [],
  //   currentPlaceholder: props.placeholder || "请选择"
  // })
  // const reverse = ref(false)
  // const focused = ref(false)
  // const inputHovering = ref(false)
  // const dropMenuVisible = ref(false)
  // const filteredOptionsCount = ref(0)
  // const filterText = ref("")
  // const cachedOptions = ref(new Map())
  // const cachedDatas = ref(new Map())
  // const selected = ref<any[]>([])
  const filterTextChange = ref('')
  const inputRef = ref<InstanceType<typeof ElInput> | null>(null)
  const scrollbar = ref<{ handleScroll: () => void } | null>(null)
  const selectWrapper = ref<HTMLElement | null>(null)
  const { form, formItem } = useFormItem()
  const selectDisabled = computed(() => props.disabled || form?.disabled || props.readonly)
  // const currentPlaceholder = ref(props.placeholder || "请选择")
  const tooltipRef = ref<InstanceType<typeof ElTooltip> | null>(null)
  const popperPaneRef = computed(() => {
    return tooltipRef.value?.popperRef?.contentRef
  })
  const key = computed(() => props.props.value || props.valueKey || 'value')
  const propsMap = computed(() => ({
    label: 'label',
    children: 'children',
    disabled: 'disabled',
    ...props.props,
    value: key.value,

  }))

  const labelMaxWidth = computed(() => {
    if (isNumber(props.labelMaxWidth))
      return `${props.labelMaxWidth}px`
    if (isBoolean(props.labelMaxWidth))
      return props.labelMaxWidth ? '200px' : 'unset'
    return props.labelMaxWidth
  })
  const showClose = computed(() => {
    const hasValue = props.multiple
      ? Array.isArray(props.modelValue) && props.modelValue.length > 0
      : props.modelValue !== undefined
        && props.modelValue !== null
        && props.modelValue !== ''

    const criteria
      = props.clearable
        && !selectDisabled.value
        && states.inputHovering
        && hasValue
    return criteria
  })
  const selectedLabel = computed(() =>
    states.selected.length ? (states.selected as any[])[0].label : '',
  )

  const emptyText = computed(() => {
    if (states.cachedOptions.size === 0 || states.filteredOptionsCount === 0)
      return props.emptyText || '暂无数据'

    return null
  })

  const setDefalutSelectFirst = () => {
    // modelValue无值的时候才进行默认设置选中第一项
    if (isEmpty(modelValue.value)) {
      if (props.defaultSelectFirst && selectData.value.length) {
        const value = selectData.value[0][propsMap.value.value]
        modelValue.value = props.multiple ? [value] : value
      }
    }
  }

  watch(
    () => selectData.value?.length,
    (_val) => {
      setDefalutSelectFirst()
    },
    {
      flush: 'post',
    },
  )

  const getOption = (value: any) => {
    const option = states.cachedOptions.get(value)
    if (option)
      return option
    const label = value ?? ''
    const newOption = {
      value,
      label,
    }
    return newOption
  }
  const setSelected = () => {
    if (!props.multiple) {
      if (isNullOrUndefined(props.modelValue)) {
        states.selected = []
      }
      else {
        const option = getOption(props.modelValue);
        (states.selected as any[]) = [option]
      }
      return
    }
    const result: any[] = []
    if (Array.isArray(props.modelValue)) {
      props.modelValue.forEach((value) => {
        result.push(getOption(value))
      })
    }
    (states.selected as any[]) = result
  }
  // 监听 selectData 变化，确保 cachedOptions 被正确填充，解决静态 data 属性时的选中值显示问题
  watch(
    () => selectData.value,
    () => {
      if (selectData.value && selectData.value.length > 0) {
        selectData.value.forEach((item) => {
          const option = {
            value: item[propsMap.value.value],
            label: item[propsMap.value.label],
            disabled: item[propsMap.value.disabled] || false,
          }
          if (!states.cachedOptions.has(option.value)) {
            states.cachedOptions.set(option.value, option)
            states.cachedDatas.set(option.value, item)
            states.optionsCount++
            states.filteredOptionsCount++
          }
        })
        setSelected()
      }
    },
    { immediate: true },
  )

  const getData = (value: any) => {
    const option = states.cachedDatas.get(value)
    if (option)
      return option
    return null
  }
  const emitChange = async (val: any) => {
    await until(selectData).toMatch(v => v.length > 0)
    setSelected()
    if (props.validateEvent)
      // eslint-disable-next-line no-console
      formItem?.validate('change').catch(err => console.log(err))
    if (!props.multiple) {
      const data = getData(val)
      emit('change', val, data)
    }
    else {
      emit('change', val)
    }
  }
  watch(
    () => props.modelValue,
    async (val) => {
      await emitChange(val)
    },
    {
      flush: 'post',
    },
  )

  const updatePopper = () => {
    tooltipRef.value?.updatePopper?.()
  }

  const filterMethod = (value: string, data: any, node: any) => {
    if (isFunction(props.filterMethod)) {
      return props.filterMethod(value, data, node)
    }
    return value
      ? String(node.label).toLocaleLowerCase().includes(value.toLocaleLowerCase())
      : true
  }

  const handleQueryChange = (val: string) => {
    // 仅更新过滤文本
    filterTextChange.value = val
    updatePopper()

    nextTick(() => {
      // 重新计算可见选项的数量
      let count = 0
      states.cachedOptions.forEach((option) => {
        const node = option.data ? option : option
        if (filterMethod(val, node, node)) {
          count++
        }
      })
      states.filteredOptionsCount = count
    })
  }

  const debouncedQueryChange = useDebounceFn(handleQueryChange, 300)
  const onOptionCreate = (option: Record<string, any>, data: Record<string, any>) => {
    if (!states.cachedOptions.get(option.value)) {
      states.filteredOptionsCount++
      states.optionsCount++
      states.cachedOptions.set(option.value, option)
      states.cachedDatas.set(option.value, data)
    }
  }

  const handleClick = (_e?: MouseEvent) => {
    if (!selectDisabled.value) {
      states.reverse = !states.reverse
      states.focused = true
    }
  }

  const closePopper = () => {
    states.dropMenuVisible = false
    if (states.reverse)
      states.reverse = !states.reverse
  }
  const scrollIntoView = (container: HTMLElement, selected: HTMLElement) => {
    if (!selected) {
      container.scrollTop = 0
      return
    }
    const offsetParents: HTMLElement[] = []
    let pointer = selected as HTMLElement
    while (pointer && pointer.offsetParent && container !== pointer && container.contains(pointer)) {
      offsetParents.push(pointer.offsetParent as HTMLElement)
      pointer = pointer.offsetParent as HTMLElement
    }
    const top = selected.offsetTop + offsetParents.reduce((prev, curr) => prev + curr.offsetTop, 0)
    const bottom = top + selected.offsetHeight
    const viewRectTop = container.scrollTop
    const viewRectBottom = viewRectTop + container.clientHeight
    if (top < viewRectTop) {
      container.scrollTop = top
    }
    else if (bottom > viewRectBottom) {
      container.scrollTop = bottom - container.clientHeight
    }
  }
  const scrollToOption = () => {
    const target = tooltipRef.value?.popperRef?.contentRef?.querySelector?.('.el-select-dropdown__item.selected')
    if (tooltipRef.value && target) {
      const menu = tooltipRef.value?.popperRef?.contentRef?.querySelector?.('.el-select-dropdown__wrap')
      if (menu)
        scrollIntoView(menu as HTMLElement, target as HTMLElement)
    }
    scrollbar.value?.handleScroll()
  }
  const handleOptionSelect = (node: Omit<OptionProps, 'children'>) => {
    if (props.multiple) {
      const value = ((props.modelValue || []) as []).slice() as any[]
      const optionIndex = value.indexOf(node.value)
      if (optionIndex > -1)
        value.splice(optionIndex, 1)
      else
        value.push(node.value)

      modelValue.value = value
      if (props.filterable)
        inputRef.value?.focus()
    }
    else {
      modelValue.value = node.value
      closePopper()
    }
    if (states.dropMenuVisible)
      return
    nextTick(() => {
      scrollToOption()
    })
  }
  const handleClose = () => {
    states.focused = false
    if (states.reverse)
      states.reverse = !states.reverse
  }

  const handleResize = () => {
    updatePopper()
  }

  const onShow = () => {
    handleQueryChange('')
    nextTick(() => {
      inputRef.value?.focus()
      scrollToOption()
    })
    emit('visible-change', true)
  }
  const onHide = () => {
    states.filterText = ''
    emit('visible-change', false)
  }
  const handleClear = () => {
    modelValue.value = props.multiple ? [] : undefined
    emit('clear')
  }

  const viewModeText = computed(() => {
    if (!states.selected.length) {
      return props.viewEmptyText
    }

    if (props.multiple) {
      return (states.selected as any[]).map(item => item.label).join('、')
    }
    else {
      return selectedLabel.value
    }
  })

  const removeTag = (valueToRemove: any) => {
    if (!props.multiple || selectDisabled.value)
      return

    const currentValues = (modelValue.value || []) as any[]
    const newValues = currentValues.filter(val => !isEqual(val, valueToRemove))

    if (newValues.length !== currentValues.length) {
      modelValue.value = newValues
      emit('remove-tag', valueToRemove)
    }
  }

  onMounted(() => {
    useResizeObserver(selectWrapper, handleResize)
    useResizeObserver(popperPaneRef, handleResize)
  })

  return {
    selectDisabled,
    popperPaneRef,
    tooltipRef,
    inputRef,
    showClose,
    labelMaxWidth,
    selectWrapper,
    propsMap,
    emptyText,
    selectedLabel,
    viewModeText,
    filterTextChange,
    handleOptionSelect,
    handleClick,
    handleClose,
    onShow,
    onHide,
    handleClear,
    closePopper,
    onOptionCreate,
    setSelected,
    getData,
    setDefalutSelectFirst,
    emitChange,
    debouncedQueryChange,
    removeTag,
  }
}
