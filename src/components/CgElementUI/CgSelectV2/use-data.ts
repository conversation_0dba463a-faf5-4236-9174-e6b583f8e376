import type { States } from './common'
import type { SelectV2Props } from './select'
import { useRequest } from '@/composables/use-request'
import { transform } from '../../utils'

export function useData(props: SelectV2Props, states: States) {
  const selectData = ref<any[]>([])
  const requestData = () => {
    if (!props.proxyOption) {
      selectData.value = transform(props, props.data, { Data: props.data, IsSuccess: true, Message: '' })
      return
    }
    useRequest(props.proxyOption).then(({ data, response }) => {
      selectData.value = transform(props, data.value, response.value!)
    })
  }
  const optionDestroy = () => {
    states.optionsCount = 0
    states.filteredOptionsCount = 0
    states.cachedDatas.clear()
    states.cachedOptions.clear()
    states.selected = []
  }
  watch(() => [...props.data], () => {
    optionDestroy()
    requestData()
  }, {
    immediate: true,
  })
  return { selectData, requestData }
}
