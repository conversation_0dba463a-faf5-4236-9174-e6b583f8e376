<script setup lang="ts">
import type { PropType } from 'vue'
import type { OptionData } from './use-option'
import { useOption } from './use-option'

defineOptions({
  name: 'CgSelectV2Option',
})

const props = defineProps({
  data: {
    type: Object as PropType<OptionData>,
    default: () => { },
  },
})

const { currentLabel, itemSelected, visible, select, optionNode } = useOption(props)
select.onOptionCreate(unref(optionNode), props.data)
onBeforeUpdate(() => {
  select.onOptionCreate(unref(optionNode), props.data)
})

function selectOptionClick() {
  if (optionNode.value.disabled !== true)
    select.handleOptionSelect(unref(optionNode))
}

const containerKls = computed(() => [
  'el-select-dropdown__item',
  {
    'selected': itemSelected.value,
    'is-disabled': optionNode.value.disabled,
  },
])

// 判断是否显示复选框
const showCheckbox = computed(() => select.props.multiple)
</script>

<template>
  <li v-show="visible" :class="containerKls" @click.stop="selectOptionClick">
    <div class="w-full flex items-center">
      <!-- 添加复选框 -->
      <div v-if="showCheckbox" class="mr-3 flex-none">
        <ElCheckbox
          :model-value="itemSelected"
          :disabled="optionNode.disabled"
          @click.prevent
        />
      </div>
      <div class="flex-1">
        <slot :option="optionNode" :data="data">
          <span>{{ currentLabel }}</span>
        </slot>
      </div>
    </div>
  </li>
</template>

<style lang="scss" scoped>
.el-select-dropdown__item.is-disabled {
  color: #a8abb2;
}
.el-select-dropdown__item.selected {
  color: var(--el-color-primary);
}

/* 调整复选框和文本的对齐 */
.el-select-dropdown__item {
  padding: 0 20px;
  display: flex;
  align-items: center;
}

/* 调整复选框的样式 */
:deep(.el-checkbox) {
  margin-right: 0;
  height: 100%;
  display: flex;
  align-items: center;
}
</style>
