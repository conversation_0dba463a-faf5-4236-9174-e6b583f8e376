<script setup lang="ts">
import { useResizeObserver } from '@vueuse/core'
import { useNamespace } from 'element-plus'
import { selectV2Key } from './common'

const ns = useNamespace('select')
const treeSelect = inject(selectV2Key)!
// computed
const popperClass = computed(() => treeSelect.props.popperClass)
const isFitInputWidth = computed(() => treeSelect.props.fitInputWidth)
const isMultiple = computed(() => treeSelect.props.multiple)
const minWidth = ref('')
function updateMinWidth() {
  const width = treeSelect.selectWrapper?.offsetWidth
  if (!width)
    return
  minWidth.value = `${width}px`
}
onMounted(() => {
  updateMinWidth()
  useResizeObserver(treeSelect.selectWrapper, updateMinWidth)
})
</script>

<template>
  <div
    :class="[ns.b('dropdown'), ns.is('multiple', isMultiple), popperClass]"
    :style="{ [isFitInputWidth ? 'width' : 'minWidth']: minWidth }"
  >
    <slot />
  </div>
</template>
