<script setup lang="ts">
import type { SelectV2Context } from './common'
import { ArrowDown, CircleClose } from '@element-plus/icons-vue'
import { isArray } from 'xe-utils'
import { isNullOrUndefined } from '../../utils'
import { selectV2Key } from './common'
import SelectDropdown from './selec-dropdown.vue'
import { selectV2Props } from './select'
import OptionGroup from './select-option-group.vue'
import Option from './select-option.vue'
import { useData } from './use-data'
import { useSelect, useSelectStates } from './use-select'

defineOptions({
  name: 'CgSelectV2',
})
const props = defineProps(selectV2Props())
const emit = defineEmits(['change', 'update:modelValue', 'visible-change', 'clear'])
const states = useSelectStates(props)
const modelValue = useVModel(props, 'modelValue', emit)
const { selectData } = useData(props, states)

const selectWrapperRef = ref<HTMLElement>()

const {
  selectDisabled,
  selectedLabel,
  popperPaneRef,
  tooltipRef,
  inputRef,
  showClose,
  propsMap,
  emptyText,
  filterTextChange,
  labelMaxWidth,
  handleOptionSelect,
  handleClick,
  handleClose,
  onShow,
  onHide,
  handleClear,
  onOptionCreate,
  // onOptionDestroy,
  setDefalutSelectFirst,
  emitChange,
  debouncedQueryChange,
  removeTag,
  viewModeText,
} = useSelect(props, { modelValue, selectData, states, emit })
const {
  reverse,
  focused,
  inputHovering,
  currentPlaceholder,
  filterText,
  optionsCount,
  dropMenuVisible,
  cachedOptions,
  filteredOptionsCount,
  selected,
} = toRefs(states)

provide(
  selectV2Key,
  reactive({
    props,
    propsMap,
    selectWrapper: selectWrapperRef,
    filterTextChange,
    cachedOptions,
    filteredOptionsCount,
    optionsCount,
    handleOptionSelect,
    onOptionCreate,
    // onOptionDestroy
  }) as unknown as SelectV2Context,
)

const containerKls = computed(() => [
  'cg-select-v2',
  {
    'is-focus': focused.value,
    'is-disabled': selectDisabled.value,
  },
])

onClickOutside(selectWrapperRef, (event) => {
  const target = event.target as Node
  if (!popperPaneRef?.value?.contains?.(target)) {
    handleClose()
  }
})

onMounted(async () => {
  setDefalutSelectFirst()
  if (props.multiple) {
    if (isArray(props.modelValue) && props.modelValue.length)
      await emitChange(props.modelValue)
  }
  else if (!isNullOrUndefined(props.modelValue) && props.modelValue !== '') {
    await emitChange(props.modelValue)
  }
})
</script>

<template>
  <!-- 查看模式 -->
  <div v-if="!props.isEditing" class="cg-select-v2-view-mode">
    <span v-if="selected.length">{{ viewModeText }}</span>
    <span v-else class="cg-select-v2-empty-text">{{ props.viewEmptyText }}</span>
  </div>
  <!-- 编辑模式 -->
  <div v-else ref="selectWrapperRef" :class="containerKls" @click.stop="handleClick">
    <ElTooltip
      ref="tooltipRef" v-model:visible="dropMenuVisible" :effect="effect" :persistent="true" trigger="click"
      transition="el-zoom-in-top" :disabled="selectDisabled" :teleported="teleported" :stop-popper-mouse-event="false"
      :gpu-acceleration="false" :hide-after="0" :placement="placement" pure
      :popper-class="['cg-select-v2__popper', popperClass]" @hide="onHide" @show="onShow"
    >
      <template #default>
        <div
          class="h-full flex items-center justify-between gap-3 px-4" @mouseenter="inputHovering = true"
          @mouseleave="inputHovering = false"
        >
          <div class="inline-flex flex-1 items-center gap-3 overflow-hidden">
            <template v-if="selected.length">
              <ElTooltip
                v-if="props.multiple && selected.length > 1"
                :disabled="dropMenuVisible || !props.multiple || selected.length <= 1" placement="bottom" effect="light"
                trigger="hover" :teleported="true" popper-class="cg-select-v2__tags-tooltip"
              >
                <div class="flex flex-shrink-0 items-center gap-1 overflow-hidden">
                  <div class="oneLine min-w-10 flex-shrink-1" :style="{ 'max-width': labelMaxWidth }">
                    {{ selectedLabel }}
                  </div>
                  <ElTag
                    v-if="selected.length > 1" disable-transitions type="info"
                    class="cg-select-v2__tag-text flex-shrink-0 px-2 tabular-nums"
                  >
                    {{ `+${selected.length - 1}` }}
                  </ElTag>
                </div>

                <template #content>
                  <div class="cg-select-v2__tags-list">
                    <ElTag
                      v-for="item in selected" :key="item.value" class="m-1" :closable="!props.isEditing" disable-transitions
                      type="info" @close.stop="removeTag(item.value)"
                    >
                      {{ item.label ?? item.value }}
                    </ElTag>
                  </div>
                </template>
              </ElTooltip>
              <!-- 多选但只有一个选项的情况 -->
              <div
                v-else-if="props.multiple && selected.length === 1"
                class="flex flex-shrink-0 items-center gap-1 overflow-hidden"
              >
                <div class="oneLine min-w-10" :style="{ 'max-width': labelMaxWidth }">
                  {{ selectedLabel }}
                </div>
              </div>
              <!-- 单选模式的情况 -->
              <div v-else-if="!props.multiple" class="oneLine min-w-10" :style="{ 'max-width': labelMaxWidth }">
                {{ selectedLabel }}
              </div>
            </template>
            <template v-else>
              <div style="color:var(--el-text-color-placeholder)" class="flex-1 truncate">
                {{ currentPlaceholder }}
              </div>
            </template>
          </div>
          <ElIcon v-if="!showClose" class="cg-select-v2__caret" :class="{ 'is-reverse': reverse }">
            <ArrowDown />
          </ElIcon>
          <ElIcon v-else-if="!readonly" class="cg-select-v2__caret" @click.stop="handleClear">
            <CircleClose />
          </ElIcon>
        </div>
      </template>
      <template #content>
        <SelectDropdown>
          <div v-if="filterable" class="z-1 h-12 w-full px-3 lh-12">
            <ElInput
              ref="inputRef" v-model="filterText" :validate-event="false" class="cg-select-v2__search-input"
              placeholder="搜索内容" clearable @input="debouncedQueryChange"
            >
              <template #prefix>
                <ElIcon><i class="i-ep-search" /></ElIcon>
              </template>
            </ElInput>
          </div>
          <ElScrollbar
            v-show="selectData.length && !emptyText" tag="ul" wrap-class="el-select-dropdown__wrap"
            view-class="el-select-dropdown__list"
          >
            <template v-if="groupable">
              <OptionGroup
                v-for="item in selectData" :key="`group${item[propsMap.label]}`"
                :label="item[propsMap.label]" :options-data="item[propsMap.children]"
              >
                <Option v-for="option in item[propsMap.children]" :key="option[propsMap.value]" :data="option">
                  <template #default="scope">
                    <slot v-bind="scope" />
                  </template>
                </Option>
              </OptionGroup>
            </template>
            <Option v-for="item in selectData" v-else :key="item.value" :data="item">
              <template #default="scope">
                <slot v-bind="scope" />
              </template>
            </Option>
          </ElScrollbar>
          <template v-if="emptyText">
            <p class="el-select-dropdown__empty">
              <slot name="empty">
                {{ emptyText }}
              </slot>
            </p>
          </template>
        </SelectDropdown>
      </template>
    </ElTooltip>
  </div>
</template>

<style lang="scss">
.cg-select-v2 {
  background-color: var(--el-input-bg-color, var(--el-fill-color-blank));
  background-image: none;
  border-radius: var(--el-input-border-radius, var(--el-border-radius-base));
  transition: var(--el-transition-box-shadow);
  transform: translateZ(0px);
  box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
  line-height: var(--el-component-size);
  height: var(--el-component-size);
  font-size: var(--el-font-size-base);
  cursor: pointer;

  &:hover {
    box-shadow: 0 0 0 1px var(--el-text-color-disabled) inset;
  }

  &.is-focus {
    box-shadow: 0 0 0 1px var(--el-color-primary) inset !important;
  }

  &.is-disabled {
    cursor: not-allowed;
    background-color: var(--el-disabled-bg-color);
    box-shadow: 0 0 0 1px var(--el-disabled-border-color) inset;
    user-select: none;

    .cg-select-v2__caret {
      cursor: not-allowed;
    }

    .cg-select-v2__tag-text {
      color: var(--el-disabled-text-color);
    }
  }

  &__caret {
    color: var(--el-text-color-placeholder);
    font-size: 14px;
    transition: transform var(--el-transition-duration);
    transform: rotate(0deg);
    cursor: pointer;

    &.is-reverse {
      transform: rotate(-180deg);
    }

    &.is-disabled {
      cursor: not-allowed;
    }
  }

  &__tag-text {
    color: var(--cg-select-tag-text-color);
  }
}

.cg-select-v2__popper {
  .el-tree-node__expand-icon {
    margin-left: 8px;
  }

  .cg-select-v2__search-input {
    .el-input__wrapper {
      border-bottom: 1px solid var(--el-border-color);
      border-radius: 0;
      box-shadow: none;

      &.is-focus,
      &:hover {
        border-bottom: 1px solid var(--el-input-focus-border-color);
      }
    }
  }

  .el-select-dropdown__item:hover {
    background-color: var(--el-fill-color-light) !important;
  }
}

.el-form-item.is-error {
  .cg-select-v2 {
    box-shadow: 0 0 0 1px var(--el-color-danger) inset;
  }
}

.el-select-dropdown__empty {
  min-width: 80px;
}

/* 查看模式样式 */
.cg-select-v2-view-mode {
  min-height: 32px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  word-break: break-word;
}

.cg-select-v2-empty-text {
  color: #909399;
}

.cg-select-v2 .el-tooltip__trigger {
  display: inline-flex;
  align-items: center;
  width: 100%;
  overflow: hidden;
}

.cg-select-v2__tags-tooltip {
  padding: 6px 12px !important;
}

.cg-select-v2__tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  max-width: 400px;
}

.cg-select-v2__tags-list .el-tag {
  margin: 2px;
}

.cg-select-v2 .inline-flex>div:not(.el-tooltip__trigger) {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
