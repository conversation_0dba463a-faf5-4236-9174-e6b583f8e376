import type { Placement } from '@popperjs/core'
import type { ExtractPropTypes, PropType } from 'vue'
import type { ProxyOption, Transform } from '../../utils'
import { placements } from '@popperjs/core'
import { useTooltipContentProps } from 'element-plus'

export interface OptionProps {
  label?: string
  value?: string
  disabled?: string
  children?: string
}
export type FilterMethod = (value: string, data: Record<string, any>, option: { label: string, value: any, disabled: boolean }) => boolean
export function selectV2Props() {
  return {
    modelValue: {
      type: [Array, String, Number, Boolean, Object],
      default: undefined,
    },
    proxyOption: {
      type: Object as PropType<ProxyOption>,
      default: null,
    },
    filterable: { type: Boolean, default: false },
    fitInputWidth: {
      type: Boolean,
      default: false,
    },
    teleported: useTooltipContentProps.teleported,
    placeholder: {
      type: String,
    },
    placement: {
      type: String as PropType<Placement>,
      values: placements,
      default: 'bottom-start',
    },
    popperClass: {
      type: String,
      default: '',
    },
    data: {
      type: Array,
      default: [],
    },
    valueKey: {
      type: String,
      default: 'value',
    },
    labelMaxWidth: {
      type: [Number, String, Boolean],
      default: '200px',
    },
    emptyText: {
      type: String,
      default: '暂无数据',
    },
    effect: {
      type: String as PropType<'light' | 'dark' | string>,
      default: 'light',
    },
    disabled: { type: Boolean, default: false },
    clearable: { type: Boolean, default: true },
    multiple: { type: Boolean, default: false },
    groupable: { type: Boolean, default: false },
    props: {
      type: Object as PropType<OptionProps>,
      default: () => ({
        children: 'children',
        label: 'label',
        value: 'value',
        disabled: 'disabled',
      }),
    },
    defaultSelectFirst: { type: Boolean, default: false },
    readonly: { type: Boolean, default: false },
    /** 初始化 modelValue 有值时，是否触发 change事件 */
    // initValueTriggerChange: { type: Boolean, default: true },
    /** 是否触发表单验证 */
    validateEvent: { type: Boolean, default: true },
    transform: { type: Function as PropType<Transform>, default: null },
    filterMethod: Function as PropType<FilterMethod>,
    isEditing: {
      type: Boolean,
      default: true,
    },
    viewEmptyText: {
      type: String,
      default: '-',
    },
  }
}
export type SelectV2Props = ExtractPropTypes<ReturnType<typeof selectV2Props>>
