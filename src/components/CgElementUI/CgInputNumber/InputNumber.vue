<script setup lang="ts">
import type { PropType } from 'vue'
import { isNullOrUndefined } from '@/components/utils'
import XEUtils from 'xe-utils'

defineOptions({
  name: 'CgInputNumber',
  inheritAttrs: false,
})

const props = defineProps({
  modelValue: {
    type: Number,
    default: undefined,
  },
  prefix: {
    type: String,
    default: '',
  },
  suffix: {
    type: String,
    default: '',
  },
  isEditing: {
    type: Boolean,
    default: true,
  },
  /** 查看模式下，当值为空时展示的占位符 */
  viewEmptyText: {
    type: String,
    default: '-',
  },
  /** 预设格式化类型，内置: number(千分位) / currency(货币) / percent(百分比) */
  formatType: {
    type: String as PropType<'number' | 'currency' | 'percent' | ''>,
    default: '',
  },
  /** 透传给格式化器的额外选项，如 { digits:0 } */
  formatOptions: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({}),
  },
  /** 自定义格式化函数，优先级最高 */
  formatter: {
    type: Function as PropType<(val: number, opts?: any) => string>,
    default: null,
  },
})

const emit = defineEmits(['update:modelValue'])

const $slots = defineSlots<Slots>()

interface Slots {
  prefix?: () => any
  suffix?: () => any
  [key: string]: any
}

const vModel = useVModel(props, 'modelValue', emit)

const attrs = computed(() => {
  const attrs = useAttrs()
  return {
    placeholder: '请输入',
    ...attrs,
    controls: attrs.controls === undefined ? false : !!attrs.controls,
  }
})

/** 内部格式化器 */
function innerFormat(val: number) {
  const { formatType, formatOptions } = props
  if (!formatType)
    return String(val)

  // 数字千分位（使用 XEUtils）
  if (formatType === 'number')
    return XEUtils.commafy(val, { digits: formatOptions.digits ?? 0 })

  // 货币格式化
  if (formatType === 'currency') {
    const digits = formatOptions.digits ?? 0
    return XEUtils.commafy(val, { digits })
  }

  // 百分比格式化
  if (formatType === 'percent') {
    const digits = formatOptions.digits ?? 0
    return `${XEUtils.toFixed(val * 100, digits)}%`
  }

  return String(val)
}

const displayValue = computed(() => {
  if (isNullOrUndefined(props.modelValue))
    return props.viewEmptyText

  const num = Number(props.modelValue)

  /* 自定义 formatter 优先 */
  if (typeof props.formatter === 'function')
    return props.formatter(num, props.formatOptions)

  return innerFormat(num)
})
</script>

<template>
  <div v-if="!props.isEditing" class="cg-input-number-view-mode">
    <span v-if="props.prefix || $slots.prefix" class="cg-view-prefix">
      <slot v-if="$slots.prefix" name="prefix" />
      <template v-else>{{ props.prefix }}</template>
    </span>
    <span class="cg-view-value">
      {{ displayValue }}
    </span>
    <span v-if="props.suffix || $slots.suffix" class="cg-view-suffix">
      <slot v-if="$slots.suffix" name="suffix" />
      <template v-else>{{ props.suffix }}</template>
    </span>
  </div>

  <!-- 编辑模式 -->
  <ElInputNumber
    v-else
    v-model="vModel"
    class="cg-input-number"
    v-bind="attrs"
  >
    <!-- 处理 prefix 属性和插槽 -->
    <template #prefix>
      <slot v-if="$slots.prefix" name="prefix" />
      <template v-else-if="props.prefix">
        {{ props.prefix }}
      </template>
    </template>

    <!-- 处理 suffix 属性和插槽 -->
    <template #suffix>
      <slot v-if="$slots.suffix" name="suffix" />
      <template v-else-if="props.suffix">
        {{ props.suffix }}
      </template>
    </template>

    <!-- 透传其他可能存在的插槽 -->
    <template v-for="(_, key) in $slots" #[key]="slotProps">
      <slot v-if="String(key) !== 'prefix' && String(key) !== 'suffix'" :name="key" v-bind="slotProps" />
    </template>
  </ElInputNumber>
</template>

<style lang="scss" scoped>
.cg-input-number-view-mode {
  min-height: 32px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  word-break: break-word;

  .cg-view-prefix {
    margin-right: 5px;
    color: #909399;
  }
  .cg-view-suffix {
    margin-left: 5px;
    color: #909399;
  }
  .cg-view-value {
    &.is-empty {
      color: #c0c4cc;
    }
  }
}

.cg-input-number {
  width: 100%;

  :deep(.el-input__inner) {
    text-align: start;
  }
}
</style>
