<script setup lang="ts">
import type { PropType } from 'vue'
import { useVModel } from '@vueuse/core'
import { ElRadio, ElRadioButton, ElRadioGroup } from 'element-plus'
import { computed, useAttrs } from 'vue'
import XEUtils from 'xe-utils'

defineOptions({
  name: 'CgRadio',
})

const props = defineProps({
  modelValue: {
    type: [Boolean, String, Number] as PropType<ModelValueType>,
    default: undefined,
  },
  options: {
    type: [Array, Object] as PropType<any>,
    default: null,
  },
  optionsAttr: {
    type: String,
    default: 'value',
  },
  radioButton: Boolean,
  disabledArray: {
    type: Array as PropType<number[]>,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  events: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue'])

type ModelValueType = string | number | boolean

const model = useVModel(props, 'modelValue', emit)

const attrs = computed(() => ({
  ...useAttrs(),
  ...props.events,
}))

const optionsList = computed(() => {
  if (XEUtils.isArray(props.options)) {
    const [label, value] = props.optionsAttr.split(',')
    const list: any[] = []
    props.options.forEach((item: { [key: string]: string }) => {
      const isPlainObject = XEUtils.isPlainObject(item)
      if (isPlainObject) {
        list.push({
          label: item[label],
          value: item[value],
        })
      }
      else {
        list.push({
          label: item,
          value: item,
        })
      }
    })
    return list
  }
  if (XEUtils.isPlainObject(props.options)) {
    const obj: any = {}
    Object.keys(props.options).forEach((key) => {
      obj[key] = props.options[key]
    })
    return obj
  }
  return props.options
})

const isPluralValues = computed(() => {
  if (typeof optionsList.value === 'object') {
    return !!optionsList.value?.length
  }
  return false
})

const normalizedOptions = computed(() => {
  if (!optionsList.value) return null

  if (isPluralValues.value) {
    return optionsList.value.map((item: any) => ({
      value: item.value ?? item.label, // 如果没有 value，使用 label 作为 value
      label: item.label
    }))
  }

  return {
    value: optionsList.value.value ?? optionsList.value.label,
    label: optionsList.value.label
  }
})
</script>

<template>
  <ElRadioGroup v-if="isPluralValues" v-model="model" v-bind="attrs">
    <template v-for="(item, index) in normalizedOptions" :key="item.value">
      <ElRadioButton v-if="radioButton" :value="item.value">
        {{ item.label }}
      </ElRadioButton>
      <ElRadio v-else :value="item.value" :disabled="disabledArray.includes(index)">
        {{ item.label }}
      </ElRadio>
    </template>
  </ElRadioGroup>
  <div v-else>
    <ElRadioButton
      v-if="optionsList && radioButton"
      v-model="model"
      :value="normalizedOptions.value"
      :disabled="disabled"
    >
      {{ normalizedOptions.label }}
    </ElRadioButton>
    <ElRadio
      v-else-if="optionsList"
      v-model="model"
      :value="normalizedOptions.value"
      :disabled="disabled"
      v-bind="attrs"
    >
      {{ normalizedOptions.label }}
    </ElRadio>
  </div>
</template>
