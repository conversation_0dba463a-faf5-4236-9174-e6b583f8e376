<script setup lang="ts">
import type { PropType } from 'vue'
import XEUtils from 'xe-utils'

type CheckboxGroupValue = (string | number)[]
type SingleCheckboxValue = string | number | boolean | undefined

interface CheckboxOption {
  label: string
  value: string | number  // 限制为 string | number，因为这是 Element Plus 期望的类型
  disabled?: boolean
  [key: string]: any
}

defineOptions({
  name: 'CgCheckbox',
  inheritAttrs: false,
})

const props = defineProps({
  modelValue: {
    type: Array as PropType<(string | number | boolean)[]>,
    default: () => [],
  },
  options: {
    type: [Array, Object] as PropType<any>,
    default: null,
  },
  optionsAttr: {
    type: String,
    default: 'label,value',
  },
  clickButton: Boolean,
  indeterminate: {
    type: Boolean,
    default: false,
  },
  events: {
    type: Object,
    default: () => ({}),
  },
})

const emit = defineEmits(['update:modelValue'])

const modelValue = useVModel(props, 'modelValue', emit)

// 用于 CheckboxGroup 的模型值，确保类型为 (string | number)[]
const groupModelValue = computed<CheckboxGroupValue>({
  get: () => Array.isArray(modelValue.value)
    ? modelValue.value.map(v => typeof v === 'boolean' ? String(v) : v)
    : [],
  set: (val) => {
    modelValue.value = val
  }
})

// 用于单个 Checkbox 的模型值，类型为 string | number | boolean | undefined
const singleModelValue = computed<SingleCheckboxValue>({
  get: () => Array.isArray(modelValue.value) ? modelValue.value[0] : modelValue.value,
  set: (val) => {
    modelValue.value = val !== undefined ? [val] : []
  }
})

const eventList = computed(() => ({
  ...props.events,
}))

const optionsList = computed(() => {
  const [label, value, ...other] = props.optionsAttr.split(',')
  let option: CheckboxOption

  const list: CheckboxOption[] = []
  if (XEUtils.isArray(props.options)) {
    props.options.forEach((item: { [key: string]: string }) => {
      const isPlainObject = XEUtils.isPlainObject(item)
      if (isPlainObject) {
        option = {
          label: item[label],
          value: item[value],
        }
        if (other.length) {
          option = {
            ...option,
            ...Object.assign({}, ...other.map(o => ({ [o]: item[o] }))),
          }
        }
        list.push(option)
      }
      else {
        list.push({
          label: String(item),
          value: item,
        })
      }
    })
    return list
  }

  const options = { ...props.options as Record<string, any> }
  if (XEUtils.isPlainObject(options)) {
    const obj: Record<string, any> = {}
    Object.keys(options).forEach((key) => {
      obj[key] = options[key]
    })
    return obj
  }
  return props.options
})

const isPluralValues = computed(() => {
  if (typeof optionsList.value === 'object') {
    return Array.isArray(optionsList.value) || !!optionsList.value?.length
  }
  return false
})

const normalizedOptions = computed(() => {
  if (!optionsList.value) return null

  if (isPluralValues.value) {
    return optionsList.value.map((item: CheckboxOption) => ({
      value: item.value ?? item.label,
      label: item.label,
      disabled: item.disabled
    }))
  }

  if (optionsList.value.label) {
    return {
      value: optionsList.value.value ?? optionsList.value.label,
      label: optionsList.value.label,
      disabled: optionsList.value.disabled
    }
  }

  return optionsList.value
})
</script>

<template>
  <ElCheckboxGroup v-if="isPluralValues" v-model="groupModelValue" v-bind="eventList">
    <template v-for="item in normalizedOptions">
      <ElCheckboxButton v-if="clickButton" :key="item.value" :value="item.value"
        :disabled="item.disabled">
        {{ item.label }}
      </ElCheckboxButton>
      <ElCheckbox v-else :key="`${item.value}x`" :value="item.value" :disabled="item.disabled">
        {{ item.label }}
      </ElCheckbox>
    </template>
  </ElCheckboxGroup>
  <!-- 单个button框 -->
  <ElCheckboxButton v-else-if="!isPluralValues && clickButton && normalizedOptions" :key="normalizedOptions.value"
    v-model="singleModelValue" :value="normalizedOptions.value"
    :disabled="normalizedOptions.disabled" v-bind="eventList">
    {{ normalizedOptions.label }}
  </ElCheckboxButton>
  <div v-else>
    <!-- 单个框 -->
    <ElCheckbox v-if="normalizedOptions && normalizedOptions.label" :key="normalizedOptions.value" v-model="singleModelValue"
      :value="normalizedOptions.value" :disabled="normalizedOptions.disabled"
      v-bind="eventList">
      {{ normalizedOptions.label }}
      <slot />
    </ElCheckbox>
    <!-- 全选框 -->
    <ElCheckbox v-else v-model="singleModelValue" :disabled="normalizedOptions?.disabled" :indeterminate="indeterminate"
      v-bind="eventList">
      <slot />
    </ElCheckbox>
  </div>
</template>
