<script setup lang="ts">
interface Props {
  modelValue?: boolean
  hasText?: boolean
  events?: Record<string, any>
  disabled?: boolean
  roles?: string[]
}

defineOptions({
  name: 'CgSwitch',
  inheritAttrs: false,
})

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  hasText: false,
  events: () => ({}),
  disabled: false,
  roles: () => [],
})

const emit = defineEmits(['update:modelValue', 'change'])

const interVal = ref(props.modelValue)

// Watch for modelValue changes from parent
watch(() => props.modelValue, (newVal) => {
  interVal.value = newVal
})

// Watch for internal value changes
watch(interVal, () => {
  nextTick(() => {
    // No need for $forceUpdate in Vue 3
  })
})

function handleStatusChange(status: string | number | boolean) {
  const boolStatus = Boolean(status)
  interVal.value = boolStatus
  emit('update:modelValue', boolStatus)
  emit('change', boolStatus)
}
</script>

<template>
  <el-switch
    v-model="interVal"
    v-permission:[props.roles]
    :class="props.hasText ? 'switchCls' : ''"
    :active-text="props.hasText ? '启用' : ''"
    :inactive-text="props.hasText ? '禁用' : ''"
    :disabled="props.disabled"
    @change="handleStatusChange"
  />
</template>

<style lang="scss" scoped>
.switchCls {
  :deep(.el-switch__label) {
    position: absolute;
    display: none;
    color: #fff;

    .el-switch__label--right {
      z-index: 1;
      right: 21px;
    }

    .el-switch__label--left {
      z-index: 1;
      left: 21px;
    }

    &.is-active {
      display: block;
    }
  }
}

.switchCls.el-switch {
  :deep(.el-switch__core, .el-switch__label) {
    width: 40px !important;
    /*开关按钮的宽度大小*/
  }
}

:deep(.el-switch__core) {
  min-width: 30px !important;
  width: 30px !important;
  height: 16px;

  /*设置圆*/
  .el-switch__action {
    width: 12px;
    height: 12px;
  }
}

.el-switch.is-checked {
  :deep(.el-switch__core .el-switch__action) {
    left: calc(100% - 13px) !important;
  }
}
</style>
