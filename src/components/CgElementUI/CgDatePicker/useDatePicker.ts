import type { Placement } from 'element-plus'
import type { Ref } from 'vue'
import type { DatePickerProps, DatePickerType, SelectRangeOption } from './date-picker.props'
import { ElMessage } from 'element-plus'
import { computed, unref, watch } from 'vue'
import { getWhatDay, getWhatMonth, getWhatWeek, getWhatYear, toDateString } from 'xe-utils'
import { DateType } from './date-picker.props'

interface UseDatePickerContext {
  props: DatePickerProps
  modelValue: Ref<string | [string, string] | null>
  startDateModelValue: Ref<string | null>
  endDateModelValue: Ref<string | null>
  type: Ref<string>
}

/**
 * @description 组合式函数：管理 shortcuts
 * @param {UseDatePickerContext} ctx - 上下文对象
 * @returns {{ shortcuts: Ref<any[]> }} - 快捷选项
 */
export function useShortcuts(ctx: UseDatePickerContext) {
  const { props, type } = ctx

  const shortcuts = computed(() => {
    if (!type.value.includes('range') || !props.showShortcuts) {
      return []
    }

    const day = 3600 * 1000 * 24
    if (type.value === 'monthrange') {
      return [
        {
          text: '今年至今',
          value: () => {
            const endDate = new Date()
            const startDate = getWhatYear(endDate, 0, 'first')
            return [startDate, endDate]
          },
        },
        {
          text: '最近六个月',
          value: () => {
            const endDate = new Date()
            const startDate = getWhatMonth(endDate, -5)
            return [startDate, endDate]
          },
        },
      ]
    }

    return [
      {
        text: '今天',
        value: () => {
          const startDate = new Date()
          return [startDate, startDate]
        },
      },
      {
        text: '昨天',
        value: () => {
          const startDate = new Date()
          startDate.setTime(startDate.getTime() - day)
          return [startDate, startDate]
        },
      },
      {
        text: '前天',
        value: () => {
          const startDate = new Date()
          startDate.setTime(startDate.getTime() - day * 2)
          return [startDate, startDate]
        },
      },
      {
        text: '最近7天',
        value: () => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - day * 6)
          return [start, end]
        },
      },
      {
        text: '最近30天',
        value: () => {
          const end = new Date()
          const start = new Date()
          start.setTime(start.getTime() - day * 29)
          return [start, end]
        },
      },
      {
        text: '本月',
        value: () => {
          const end = new Date()
          const start = getWhatMonth(end, 0, 'first')
          return [start, end]
        },
      },
      {
        text: '上月',
        value: () => {
          const date = new Date()
          const start = getWhatMonth(date, -1, 'first')
          const end = getWhatMonth(date, -1, 'last')
          return [start, end]
        },
      },
    ]
  })

  return { shortcuts }
}

/**
 * @description 组合式函数：管理 disabledDate
 * @param {UseDatePickerContext} _ctx - 上下文对象
 * @returns {{ disabledDate: (time: Date) => boolean }} - 禁用日期函数
 */
export function useDisabledDate(_ctx: UseDatePickerContext) {
  const disabledDate = (time: Date) => time.getTime() > Date.now()
  return { disabledDate }
}

/**
 * @description 组合式函数：管理 change 事件
 * @param {UseDatePickerContext} ctx - 上下文对象
 * @returns {{ handleChange: (dateValue: any) => void }} - change 事件处理函数
 */
export function useChangeHandler(ctx: UseDatePickerContext) {
  const { props, modelValue, type } = ctx

  function handleChange(dateValue: any) {
    if (!props.maxSelectRange || !dateValue)
      return

    const [minDate, maxDate] = unref(dateValue) as string[]
    if (!maxDate)
      return

    let maxRange = 0 - props.maxSelectRange
    if (type.value === 'monthrange') {
      maxRange += 1
    }

    const dateDiff = getWhatMonth(maxDate, maxRange)
    if (new Date(minDate) < dateDiff) {
      ElMessage.error(`日期最大选择范围不能超过[ ${props.maxSelectRange} ]个月,已自动选择最大范围日期！`)
      modelValue.value = [toDateString(dateDiff, 'yyyy-MM-dd'), maxDate]
    }
  }

  return { handleChange }
}

/**
 * @description 组合式函数：管理默认属性 (defaultAttrs)
 * @param {UseDatePickerContext} ctx - 上下文对象
 * @returns {{ defaultAttrs: Ref<any> }} - 默认属性
 */
export function useDefaultAttrs(ctx: UseDatePickerContext) {
  const { props, type } = ctx

  const defaultAttrs = computed(() => {
    const attrs: {
      type: DatePickerType
      startPlaceholder: string
      endPlaceholder: string
      placeholder: string
      placement: Placement
      unlinkPanels: boolean
      editable: boolean
      valueFormat?: string
      disabledDate?: (time: Date) => boolean
    } = {
      type: DateType.dateRange as DatePickerType,
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      placeholder: '选择日期',
      placement: 'bottom-start',
      unlinkPanels: true,
      editable: false,
    }
    const mergedType = type.value || attrs.type

    const dateFormatMap: Record<string, string> = {
      daterange: 'yyyy-MM-dd',
      date: 'yyyy-MM-dd',
      weekrange: 'yyyy-MM-dd',
      month: 'yyyy-MM',
      monthrange: 'yyyy-MM',
      year: 'yyyy',
    }

    const format = dateFormatMap[mergedType] || 'yyyy-MM-dd'
    attrs.valueFormat = format.toUpperCase()

    if (props.defaultDisabledDate) {
      attrs.disabledDate = useDisabledDate(ctx).disabledDate
    }

    return attrs
  })

  return { defaultAttrs }
}

/**
 * @description 组合式函数：管理默认选择范围
 * @param {UseDatePickerContext} ctx - 上下文对象
 * @returns {{ setDefaultSelectRange: (format: string) => void }} - 设置默认选择范围函数
 */
export function useDefaultSelectRange(ctx: UseDatePickerContext) {
  const { props, modelValue, type } = ctx

  type GetWhatFn = (date: Date, offset: number, mode?: 'first' | 'last') => Date

  // Wrapper function to adapt getWhatWeek to match GetWhatFn signature
  const getWhatWeekWrapper: GetWhatFn = (date: Date, offset: number) => {
    return getWhatWeek(date, offset)
  }

  const getDefaultSelectRange = (
    fn: GetWhatFn,
    rang?: number,
    offset?: number,
    mode?: 'first' | 'last',
  ) => {
    let startDate = null
    let endDate = new Date()
    if (offset) {
      endDate = mode ? fn(endDate, offset, mode) : fn(endDate, offset)
    }
    const tempRange = rang || 0
    if (tempRange === 0) {
      startDate = getWhatMonth(endDate, 0, 'first')
    }
    else {
      startDate = fn(endDate, tempRange)
    }
    return { startDate, endDate }
  }

  const setDefaultSelectRange = (format: string) => {
    if (Array.isArray(modelValue.value) && modelValue.value.length)
      return
    if (typeof modelValue.value === 'string' && modelValue.value.length)
      return

    const option = props.defaultSelectRange
    if (!option?.type)
      return

    const { type: rangeType, rang, offset, mode } = option
    const selectRangeTypeMap: Record<SelectRangeOption['type'], GetWhatFn> = {
      day: getWhatDay,
      month: getWhatMonth,
      week: getWhatWeekWrapper,
      year: getWhatYear,
    }

    const fn = selectRangeTypeMap[rangeType] ?? getWhatDay
    const { startDate, endDate } = getDefaultSelectRange(fn, rang, offset, mode)

    if (['daterange', 'monthrange'].includes(type.value)) {
      modelValue.value = [
        toDateString(startDate, format),
        toDateString(endDate, format),
      ]
    }
    else {
      modelValue.value = toDateString(startDate, format)
    }
  }

  watch(
    () => props.defaultSelectRange,
    () => {
      setDefaultSelectRange((useDefaultAttrs(ctx).defaultAttrs.value as any).valueFormat ?? 'yyyy-MM-dd')
    },
  )

  return { setDefaultSelectRange }
}
