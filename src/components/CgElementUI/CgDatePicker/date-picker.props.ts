import type { TimePickerDefaultProps } from 'element-plus'
import type { ExtractPropTypes, PropType } from 'vue'

/**
 * @description 定义日期选择范围的选项类型
 */
export interface SelectRangeOption {
  type: 'day' | 'month' | 'year' | 'week'
  /** 选择的日期范围 */
  rang?: number
  /** 天偏移量(默认0)、前几天、后几天 */
  offset?: number
  /** 获取哪天：（日、月、年）初(first)、（日、月、年）末(last) */
  mode?: 'first' | 'last'
}

/**
 * @description 定义 DatePicker 的类型
 */
export type DatePickerType =
  | 'year'
  | 'month'
  | 'date'
  | 'dates'
  | 'week'
  | 'datetime'
  | 'datetimerange'
  | 'daterange'
  | 'monthrange'

/**
 * @description 定义 DatePicker 的类型枚举
 */
export enum DateType {
  year = 'year',
  month = 'month',
  date = 'date',
  dates = 'dates',
  datetime = 'datetime',
  week = 'week',
  datetimeRange = 'datetimerange',
  dateRange = 'daterange',
  monthRange = 'monthrange',
}

/**
 * @description 定义 DatePicker 的 props
 */
export const datePickerProps = {
  defaultSelectRange: {
    type: Object as PropType<SelectRangeOption>,
    default: null,
  },
  maxSelectRange: {
    type: Number,
    default: 0,
  },
  modelValue: {
    type: [String, Array] as PropType<TimePickerDefaultProps['modelValue']>,
    default: null,
  },
  startDate: {
    type: String,
    default: null,
  },
  endDate: {
    type: String,
    default: null,
  },
  showIcon: {
    type: Boolean,
    default: true,
  },
  showConfirmButton: {
    type: Boolean,
    default: false,
  },
  showShortcuts: {
    type: Boolean,
    default: true,
  },
  defaultDisabledDate: {
    type: Boolean,
    default: true,
  },
}

/**
 * @description 定义 DatePicker 的 props 类型
 */
export type DatePickerProps = ExtractPropTypes<typeof datePickerProps>
