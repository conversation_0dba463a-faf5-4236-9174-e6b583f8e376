<script setup lang="ts">
import type { Ref } from 'vue'
import { until, useVModels } from '@vueuse/core'
import { computed, onMounted, watch } from 'vue'

import { datePickerProps, DateType } from './date-picker.props'
import { useChangeHand<PERSON>, useDefaultAttrs, useDefaultSelectRange, useShortcuts } from './useDatePicker'

defineOptions({ name: 'CgDatePicker', inheritAttrs: false })

const props = defineProps(datePickerProps)
const emit = defineEmits(['update:modelValue', 'update:startDate', 'update:endDate'])

const {
  modelValue: mv,
  startDate: startDateModelValue,
  endDate: endDateModelValue,
} = useVModels(props, emit)

const type = computed(() => {
  return (props as any).type ?? DateType.dateRange
})

const context = {
  props,
  modelValue: mv as Ref<string | [string, string] | null>,
  startDateModelValue,
  endDateModelValue,
  type,
}

const { shortcuts } = useShortcuts(context)
const { handleChange } = useChangeHandler(context)
const { defaultAttrs } = useDefaultAttrs(context)
const { setDefaultSelectRange } = useDefaultSelectRange(context)

const popperClassName = computed(() =>
  props.showConfirmButton ? 'cg-date-picker-popper--with-confirm' : 'cg-date-picker-popper',
)

// 监听 mv 的变化，并更新 startDateModelValue 和 endDateModelValue
watch(mv, (value) => {
  if (['daterange', 'monthrange'].includes(type.value)) {
    if (value) {
      const [start, end] = value as [string, string]
      startDateModelValue.value = start
      endDateModelValue.value = end
    }
    else {
      startDateModelValue.value = ''
      endDateModelValue.value = ''
    }
  }
})

onMounted(async () => {
  if (
    !mv.value
    && ['daterange', 'monthrange'].includes(type.value)
  ) {
    await until(startDateModelValue).toMatch(v => v !== '' && v !== null)
    await until(endDateModelValue).toMatch(v => v !== '' && v !== null)
    mv.value = [startDateModelValue.value, endDateModelValue.value]
  }
  setDefaultSelectRange((defaultAttrs.value as any).valueFormat ?? 'yyyy-MM-dd')
})
</script>

<template>
  <ElDatePicker
    v-model="mv"
    class="cg-date-picker"
    :popper-class="popperClassName"
    :class="{ 'no-icon': !props.showIcon }"
    :shortcuts="shortcuts"
    v-bind="defaultAttrs"
    @change="handleChange"
  />
</template>

<style lang="scss" scoped>
.cg-date-picker {
  display: inline-flex;
}

.cg-date-picker-popper {
  .el-month-table td.current:not(.disabled) .cell {
    color: #fff;
    background-color: var(--el-color-primary);
  }
  .el-picker-panel__body-wrapper {
    .el-picker-panel__sidebar {
      .el-picker-panel__shortcut {
        font-size: 12px;
        &:hover {
          background-color: var(--el-color-primary-light-9);
        }
      }
    }
    .el-picker-panel__body {
      .el-picker-panel__content {
        padding: 12px;
        border-right: 0;

        .el-date-range-picker__header {
          .el-picker-panel__icon-btn {
            font-weight: bold;
          }
          div {
            font-weight: 700;
            font-size: 14px;
            color: #333;
          }
        }
      }
    }
  }
}
</style>
