<script setup lang="ts">
import { useVModels } from '@vueuse/core'
import { useAttrs } from 'vue'
import { complexInputProps } from './complex-input'

defineOptions({
  name: 'CgComplexInput',
  inheritAttrs: false,
})
const props = defineProps(complexInputProps)
const emit = defineEmits<EmitType>()

interface EmitType {
  (e: 'update:modelValue', value: any): void
  (e: 'update:modelSelect', value: string | number | boolean): void
  (e: 'update:start', value: string | number): void
  (e: 'update:end', value: string | number): void
  (e: `update:${string}`, value: any[]): void
}

const { modelValue, modelSelect, start, end } = useVModels(props, emit)

const batchInputVisible = ref(false)
const batchInputText = ref('')
const batchInputTooltipRef = ref(null)
const complexInputRef = ref<HTMLElement | null>(null)
const componentWidth = ref(0)

const attrs = useAttrs() as Record<string, any>

const batchItemsCount = ref(0)
const showBatchFeedback = ref(false)
const originalValue = ref<string | number | null>(null)
const canResetFeedback = ref(true)

const selectOptions = computed(() => {
  if (typeof props.selectOptions === 'function')
    return props.selectOptions()

  return props.selectOptions
})

const defaultBatchFieldsMap = computed(() => {
  const map: Record<string, string> = {}
  props.batchSearchFields.forEach((field: string) => {
    map[field] = `${field}s` // 默认将字段名加上's'作为事件名
  })
  return map
})

const batchFieldsMap = computed(() => {
  return props.batchFieldsMap || defaultBatchFieldsMap.value
})

const showBatchSearchIcon = computed(() => {
  return props.batchSearchEnabled && props.batchSearchFields.includes(modelSelect.value as string)
})

// 计算当前应该显示的placeholder
const currentPlaceholder = computed(() => {
  if (showBatchFeedback.value && batchItemsCount.value > 0) {
    return `\u2714 ${props.batchFeedbackTemplate.replace('{count}', String(batchItemsCount.value))}`
  }
  return attrs.placeholder || '请输入'
})

const inputAttrs = computed(() => {
  const baseAttrs: Record<string, any> = { ...attrs }

  if (showBatchFeedback.value && batchItemsCount.value > 0) {
    baseAttrs.placeholder = currentPlaceholder.value
  }

  return baseAttrs
})

function toggleBatchInput() {
  batchInputVisible.value = !batchInputVisible.value
  if (batchInputVisible.value) {
    // 显示tooltip时，获取组件宽度
    nextTick(() => {
      if (complexInputRef.value) {
        componentWidth.value = complexInputRef.value.offsetWidth
      }
    })
  }
  else if (batchInputText.value) {
    processBatchInput()
  }
}

function processBatchInput() {
  if (!batchInputText.value)
    return

  const items = batchInputText.value
    .split('\n')
    .map(item => item.trim())
    .filter(item => item)

  if (items.length === 0)
    return

  originalValue.value = modelValue.value
  batchItemsCount.value = items.length
  showBatchFeedback.value = true
  modelValue.value = ''

  // 使用配置的映射关系来确定要触发的事件
  const currentField = modelSelect.value as string
  const emitEvent = batchFieldsMap.value[currentField]

  if (emitEvent) {
    emit(`update:${emitEvent}`, items)
  }
}

function handleBatchCancel() {
  batchInputVisible.value = false
  batchInputText.value = ''

  if (batchItemsCount.value > 0) {
    clearBatchData()
  }
}

function handleBatchConfirm() {
  if (batchInputText.value) {
    // 暂时禁用重置反馈状态，防止因为点击确认按钮触发的focus事件重置状态
    canResetFeedback.value = false

    processBatchInput()

    // 添加高亮动画效果
    const inputEl = complexInputRef.value?.querySelector('.cg-complex-input__input')
    if (inputEl) {
      inputEl.classList.add('highlight-animation')
      setTimeout(() => {
        inputEl.classList.remove('highlight-animation')
      }, 800)
    }

    setTimeout(() => {
      canResetFeedback.value = true
    }, 1000)
  }
  batchInputVisible.value = false
}

function clearBatchData() {
  batchItemsCount.value = 0
  showBatchFeedback.value = false

  // 触发清除事件，将批量数据设置为空数组
  const currentField = modelSelect.value as string
  const emitEvent = batchFieldsMap.value[currentField]

  if (emitEvent) {
    emit(`update:${emitEvent}`, [])
  }
}

function handleInputFocus() {
  if (!canResetFeedback.value) {
    return
  }

  if (showBatchFeedback.value) {
    clearBatchData()
  }
}

function updateComponentWidth() {
  if (complexInputRef.value) {
    componentWidth.value = complexInputRef.value.offsetWidth
  }
}

watch(modelValue, (newValue) => {
  if (newValue === '' && batchItemsCount.value > 0 && !showBatchFeedback.value) {
    clearBatchData()
  }
})

watch(modelSelect, () => {
  if (batchItemsCount.value > 0) {
    clearBatchData()
  }
})

onMounted(() => {
  if (props.selectFirstOption)
    modelSelect.value = selectOptions.value?.[0].value

  nextTick(updateComponentWidth)
  window.addEventListener('resize', updateComponentWidth)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateComponentWidth)
})
</script>

<template>
  <div ref="complexInputRef" class="cg-complex-input" :class="{ 'is-disabled': $attrs.disabled }">
    <CgSelectV2
      v-model="modelSelect" :data="selectOptions" :clearable="false" :placeholder="selectPlaceholder"
      class="cg-complex-input__select"
    />
    <CgInput
      v-if="type === 'input'" v-bind="inputAttrs" v-model="modelValue"
      class="cg-complex-input__input" :class="{ 'batch-feedback': showBatchFeedback }"
      :clearable="true" @focus="handleInputFocus"
    >
      <template #suffix>
        <div v-if="showBatchSearchIcon" class="cg-complex-input__batch-trigger" title="批量搜索" @click="toggleBatchInput">
          <font-awesome-icon :icon="['fas', 'ellipsis-v']" size="sm" />
        </div>
      </template>
    </CgInput>
    <CgDatePicker
      v-else v-model="modelValue" v-model:start-date="start" v-model:end-date="end" type="daterange"
      :unlink-panels="true" v-bind="$attrs" start-placeholder="开始日期" end-placeholder="结束日期" :teleported="true"
    />

    <el-tooltip
      v-if="showBatchSearchIcon" ref="batchInputTooltipRef" :visible="batchInputVisible" transition="el-zoom-in-top"
      placement="bottom-start" :show-after="0" :hide-after="0" :teleported="true" trigger="click"
      popper-class="cg-complex-input__batch-tooltip" effect="light" :offset="8" :show-arrow="false"
      :virtual-ref="complexInputRef!" virtual-triggering
      :popper-style="{ width: `${componentWidth}px`, minWidth: '220px' }"
    >
      <template #content>
        <div class="cg-complex-input__batch-container">
          <div class="cg-complex-input__batch-textarea-container">
            <CgInput
              v-model="batchInputText" type="textarea" :rows="5" :placeholder="batchSearchPlaceholder"
              class="cg-complex-input__batch-textarea"
            />
          </div>
          <div class="cg-complex-input__batch-footer">
            <span v-if="batchInputText" class="cg-complex-input__batch-count">
              {{ batchInputText.split('\n').filter(item => item.trim()).length }} 个条目
            </span>
            <CgButton size="small" @click="handleBatchCancel">
              取消
            </CgButton>
            <CgButton size="small" type="primary" @click="handleBatchConfirm">
              确认
            </CgButton>
          </div>
        </div>
      </template>
    </el-tooltip>
  </div>
</template>

<style lang="scss">
.cg-complex-input {
  display: flex;
  position: relative;
  transition: all 0.25s ease;

  &__input {
    flex: 1;
    transition: all 0.3s ease;

    .el-input__wrapper {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      transition: box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .el-input__inner {
      min-width: 100px;
    }
  }

  &__select {
    transition: all 0.3s ease;

    &.cg-select-v2 {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      margin-right: -1px;
    }

    &.is-focus {
      z-index: 3;
    }

    :deep(.el-input__wrapper) {
      transition: box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    :deep(.el-input__wrapper.is-focus) {
      box-shadow: 0 0 0 1px #399e96 inset;
    }
  }

  &__other {
    .el-input__wrapper {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      transition: box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  .el-date-editor.el-input__wrapper {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    transition: box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .el-date-editor {
    width: 214px !important;

    :deep(.el-range__icon) {
      color: #909399;
    }

    :deep(.el-range-separator) {
      color: #909399;
    }

    :deep(.el-input__wrapper.is-focus) {
      box-shadow: 0 0 0 1px #399e96 inset;
    }
  }

  &__batch-tooltip {
    padding: 0 !important;
    border: none !important;

    :deep(.el-popper__arrow) {
      display: none;
    }

    :deep(.el-popper__content) {
      border-radius: 8px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      overflow: hidden;
      border: 1px solid #ebeef5;
      transition: transform 0.2s ease, opacity 0.2s ease;
    }
  }

  &__batch-container {
    width: 100%;
    overflow: hidden;
  }

  &__batch-count {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
  }

  &__batch-textarea-container {
    border-bottom: 1px solid #ebeef5;
  }

  &__batch-textarea {
    margin: 0;

    .el-textarea__inner {
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      resize: none;
      transition: background-color 0.2s ease;
      font-size: 12px;
      padding: 12px;

      &:focus {
        background-color: #f9fafc;
      }
    }

    :deep(.el-textarea__wrapper) {
      padding: 0;
      box-shadow: none !important;
      border: none !important;
    }
  }

  &__batch-footer {
    padding: 6px 12px;
    text-align: right;
    background-color: #f0f2f5;
    border-radius: 0 0 var(--el-border-radius-base) var(--el-border-radius-base);
  }

  &__batch-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;
    height: 100%;
    color: #606266;
    cursor: pointer;
    transition: all 0.25s ease;
    position: relative;

    &:hover {
      color: #399e96;
      background-color: rgba(57, 158, 150, 0.05);
    }

    &:active {
      color: #318880;
    }

    svg {
      transition: transform 0.3s ease;
    }

    &:hover svg {
      transform: scale(1.2);
    }
  }

  &__input.batch-feedback {
    :deep(.el-input__wrapper) {
      background-color: rgba(57, 158, 150, 0.05);
      transition: background-color 0.3s ease;
    }

    :deep(.el-input__inner) {
      color: #399e96;
      font-weight: 500;
      transition: color 0.3s ease, font-weight 0.3s ease;
    }

    :deep(.el-input__placeholder) {
      color: #399e96;
      font-weight: 500;
      opacity: 1;
    }
  }

  &__input.highlight-animation {
    :deep(.el-input__wrapper) {
      animation: highlight-pulse 0.8s ease;
    }
  }

  @keyframes highlight-pulse {
    0% {
      box-shadow: 0 0 0 1px rgba(57, 158, 150, 0.2) inset;
      background-color: rgba(57, 158, 150, 0.05);
    }
    50% {
      box-shadow: 0 0 0 2px rgba(57, 158, 150, 0.6) inset;
      background-color: rgba(57, 158, 150, 0.15);
    }
    100% {
      box-shadow: 0 0 0 1px rgba(57, 158, 150, 0.2) inset;
      background-color: rgba(57, 158, 150, 0.05);
    }
  }
}
</style>
