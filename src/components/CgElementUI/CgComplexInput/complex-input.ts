import type { ExtractPropTypes, PropType } from 'vue'

export type FocusEventHandler = () => Array<{ value: string | number, label: string }>
export const complexInputProps = {
  modelValue: {
    type: [String, Number, Array] as PropType<any>,
    default: null,
  },
  modelSelect: {
    type: [String, Number, Boolean],
    default: null,
  },
  start: {
    type: [String, Number],
    default: null,
  },
  end: {
    type: [String, Number],
    default: null,
  },
  selectOptions: {
    type: [Object, Function] as PropType<FocusEventHandler>,
    default: [],
  },
  selectFirstOption: { type: Boolean, default: true },
  selectPlaceholder: { type: String, default: '请选择' },
  type: { type: String, default: 'input' },
  batchSearchEnabled: { type: Boolean, default: true },
  batchSearchPlaceholder: { type: String, default: '请输入多个编码，每行一个' },
  batchFeedbackTemplate: { type: String, default: '已选择 {count} 个项目' },
  /**
   * 支持批量搜索的字段列表
   * 当 modelSelect 的值在这个列表中时，将显示批量搜索图标
   */
  batchSearchFields: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
  /**
   * 字段名到事件名的映射关系
   * 例如：{ productCode: 'productCodes' } 表示当选择 productCode 字段并进行批量搜索时，
   * 将触发 'update:productCodes' 事件
   *
   * 默认情况下，字段名加上's'作为事件名，例如 productCode -> productCodes
   */
  batchFieldsMap: {
    type: Object as PropType<Record<string, string>>,
    default: undefined, // 使用运行时计算的默认值
  },
}
export type ComplexInputProps = ExtractPropTypes<typeof complexInputProps>
