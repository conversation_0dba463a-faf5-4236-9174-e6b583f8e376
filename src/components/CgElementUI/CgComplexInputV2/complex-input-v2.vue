<script setup lang="ts">
import { toRefs, useVModel } from '@vueuse/core'
import { computed, reactive } from 'vue'

defineOptions({
  name: 'ComplexInputV2',
  inheritAttrs: false,
})

const props = defineProps({
  components: {
    type: Array<any>,
    default: () => [],
  },
  modelValue: {
    type: Object,
    default: () => { },
  },
  append: {
    type: String,
    default: '',
  },
  isEditing: {
    type: Boolean,
    default: true,
  },
  viewEmptyText: {
    type: String,
    default: '-',
  },
})

const emit = defineEmits(['update:modelValue', 'submit', 'closed'])
const modelValue = useVModel(props, 'modelValue', emit)
const { components } = toRefs(props)

const leftComp = computed(() => {
  const comp = reactive(components.value?.[0])
  if (!comp)
    return undefined
  if (!comp.span)
    comp.span = 24 - (components.value.slice(1)?.reduce((acc, cur) => acc + (cur.span || 0), 0) || 0) || 0
  return comp
})

const centerComps = computed(() => {
  if (components.value.length <= 2)
    return []
  const centers = reactive(components.value.slice(1, components.value.length - 1))
  centers[0].position = 'centerLeft'
  centers[centers.length - 1].position = 'centerRight'
  const spans = centers.reduce((acc, cur) => acc + (cur.span || 0), 0)
  centers.forEach((comp, index) => {
    if (index > 0 && index < centers.length - 1) {
      comp.position = 'center'
      if (!comp.span)
        comp.span = Math.round((24 - spans) / (centers.filter(c => !c.span).length))
    }
  })
  return centers
})

const rightComp = computed(() => {
  if (components.value.length === 1)
    return undefined
  const comp = reactive(components.value[components.value.length - 1])
  if (!comp)
    return undefined
  // 确保最后一个组件的 span 值保持不变
  return comp
})

const viewModeText = computed(() => {
  const values: string[] = []
  props.components.forEach((comp) => {
    if (comp.key && props.modelValue) {
      const value = props.modelValue[comp.key]
      // 检查值是否为 null, undefined 或空字符串
      if (value !== null && value !== undefined && value !== '') {
        values.push(String(value))
      }
    }
  })

  if (values.length === 0) {
    return props.viewEmptyText
  }

  // 检查所有值是否都是数字，如果是则使用 * 分隔，否则使用空格
  const allNumeric = values.every(val => !Number.isNaN(Number(val)))
  return values.join(allNumeric ? ' * ' : ' ')
})

function onfocus(comp: any) {
  comp.active = true
  const index = components.value.findIndex(c => c === comp)
  if (index >= 0) {
    components.value.forEach((c, i) => {
      if (i !== index)
        c.active = false
    })
  }
}

function onhover(comp: any) {
  comp.hover = true
  const index = components.value.findIndex(c => c === comp)
  if (index >= 0) {
    components.value.forEach((c, i) => {
      if (i !== index)
        c.hover = false
    })
  }
}

function validateComp(comp: any) {
  if (comp.customValidate)
    return comp.customValidate()

  else
    return true
}
</script>

<template>
  <!-- 查看模式 -->
  <div v-if="!props.isEditing" class="cg-complex-input-v2-view-mode">
    <span>{{ viewModeText }}</span>
    <span v-if="append && viewModeText !== props.viewEmptyText" class="cg-complex-input-v2-view-append">
      {{ append }}
    </span>
  </div>

  <!-- 编辑模式 -->
  <div v-else class="mixed-container">
    <div
      v-if="(!!leftComp && leftComp.type)" class="left-comp"
      :class="[`width-${leftComp.span}`, leftComp.active ? 'active' : '', leftComp.hover ? 'hover' : '', validateComp(leftComp) ? '' : 'alert']"
    >
      <Component
        :is="leftComp.component" v-model="modelValue[leftComp.key]" v-bind="leftComp.props"
        @focus="onfocus(leftComp!)" @blur="(leftComp!.active = false)" @mouseenter="onhover(leftComp!)"
        @mouseleave="(leftComp!.hover = false)"
      />
    </div>
    <div
      v-for="(comp, index) in centerComps" :key="index" class="center-comp"
      :class="[`width-${comp.span}`, comp.active ? 'active' : '', comp.hover ? 'hover' : '', validateComp(comp) ? '' : 'alert']"
    >
      <Component
        :is="comp.component" v-if="comp.type" v-model="modelValue[comp.key]"
        v-bind="comp.props" @focus="onfocus(comp)" @blur="(comp!.active = false)" @mouseenter="onhover(comp)"
        @mouseleave="(comp!.hover = false)"
      />
    </div>
    <div
      v-if="(!!rightComp && rightComp.type)" class="right-comp"
      :class="[`width-${rightComp.span}`, rightComp.active ? 'active' : '', rightComp.hover ? 'hover' : '', validateComp(rightComp) ? '' : 'alert']"
    >
      <Component
        :is="rightComp.component" v-model="modelValue[rightComp.key]"
        v-bind="rightComp.props" @focus="onfocus(rightComp!)"
        @blur="(rightComp!.active = false)" @mouseenter="onhover(rightComp!)"
        @mouseleave="(rightComp!.hover = false)"
      />
    </div>
    <div v-if="append" class="append-container">
      <span class="append-text">{{ append }}</span>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.cg-complex-input-v2-view-mode {
  min-height: 22px;
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
  word-break: break-word;
}

.cg-complex-input-v2-view-append {
  margin-left: 5px;
  color: #909399;
  background-color: #f5f7fa;
  padding: 0 5px;
  border-radius: 3px;
  white-space: nowrap;
}

.mixed-container {
  display: flex;
  align-items: center;
  width: 100%;

  .left-comp {
    margin-right: -1px;
    z-index: 0;
    transition: z-index 0.5s;

    :deep(.el-input__wrapper) {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    > div {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }

    &:hover {
      z-index: 1;
    }

    &:active {
      z-index: 2;
    }
  }

  .center-comp {
    margin-right: -1px;
    z-index: 0;
    transition: z-index 0.5s;

    :deep(.el-input__wrapper) {
      border-radius: 0;
    }

    &:hover {
      z-index: 1;
    }

    &:active {
      z-index: 2;
    }
  }

  .right-comp {
    margin-right: -1px;
    z-index: 0;
    transition: z-index 0.5s;

    :deep(.el-input__wrapper) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }

    &:hover {
      z-index: 1;
    }

    &:active {
      z-index: 2;
    }
  }

  .hover {
    z-index: 1;
  }

  .alert {
    z-index: 7;

    :deep(.el-input__wrapper) {
      box-shadow: 0 0 0 1px $cg-color-danger inset;
    }
  }

  .active {
    z-index: 5;
  }

  .hover.active {
    z-index: 6;
  }

  .append-container {
    display: flex;
    align-items: center;
    padding: 0 11px;
    color: var(--el-text-color-regular);
    background-color: var(--el-fill-color-light);
    border: 1px solid var(--el-border-color);
    border-left: 0; // 修正，确保左边框不重复
    border-radius: 0 4px 4px 0;
    height: 32px;
    z-index: 0;
    white-space: nowrap;
    flex-shrink: 0; // 防止被压缩
  }

  .append-text {
    font-size: 14px;
    white-space: nowrap;
  }
}

@for $i from 1 to 24 {
  .width-#{$i} {
    width: calc(100% / 24 * #{$i});
  }
}
  // 确保最后一个组件（如果有append）的右边框与append的左边框重合
  .right-comp:has(+ .append-container) {
      :deep(.el-input__wrapper) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
  }
  // 只有一个组件且有 append
  .left-comp:only-child:has(+ .append-container) {
      :deep(.el-input__wrapper) {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }
  }
</style>
