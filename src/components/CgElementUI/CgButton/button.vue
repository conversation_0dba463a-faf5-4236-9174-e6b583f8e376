<script setup lang="ts">
import { ElButton } from 'element-plus'
import { buttonProps } from './button'

defineOptions({
  name: 'CgButton',
  inheritAttrs: false,
})
const props = defineProps(buttonProps)
const emit = defineEmits(['click'])
const loadingStatus = ref(false)
function handleClick($event: any) {
  if (props.autoLoading) {
    loadingStatus.value = true
    emit('click', () => {
      loadingStatus.value = false
    }, $event)
  }
  else {
    emit('click', $event)
  }
}

const nonDefaultSlotNames = computed(() =>
  Object.keys(useSlots()).filter(name => name !== 'default'),
)

defineExpose({
  handleClick,
  loadingStatus,
})
</script>

<template>
  <ElButton
    :loading="loadingStatus"
    :size="props.size"
    class="cg-button"
    v-bind="$attrs"
    @click.stop="handleClick"
  >
    <!-- 使用默认插槽 -->
    <slot v-if="$slots.default" />
    <!-- 如果没有默认插槽但有text属性 -->
    <template v-else-if="props.btnText">
      {{ props.btnText }}
    </template>
    <!-- 其他命名插槽 -->
    <template v-for="name in nonDefaultSlotNames" #[name]>
      <slot :name="name" />
    </template>
  </ElButton>
</template>

<style lang="scss">
.cg-button.is-circle {
  span {
    display: none;
  }
}

.el-button {
  min-width: unset !important;
}
</style>
