import type { App } from 'vue'
import CgButton from './CgButton/index'
import CgCheckbox from './CgCheckbox/index'
import CgComplexInput from './CgComplexInput/index'
import CgComplexInputV2 from './CgComplexInputV2/index'
import CgDatePicker from './CgDatePicker/index'
import CgDropdown from './CgDropdown/index'
import { CgForm, CgFormItem } from './CgForm/index'
import CgImage from './CgImage/index'
import CgInput from './CgInput/index'
import CgInputNumber from './CgInputNumber/index'
import CgLinkButton from './CgLinkButton/index'
import CgRadio from './CgRadio/index'
import CgSelectV2 from './CgSelectV2/index'
import CgSwitch from './CgSwitch/index'
import CgTreeSelect from './CgTreeSelect/index'

const components = [
  CgButton,
  CgCheckbox,
  CgRadio,
  CgInputNumber,
  CgInput,
  CgForm,
  CgFormItem,
  CgDropdown,
  CgDatePicker,
  CgSwitch,
  CgSelectV2,
  CgTreeSelect,
  CgComplexInput,
  CgComplexInputV2,
  CgLinkButton,
  CgImage,
]
export function install(app: App) {
  // 每个组件在编写的时候都提供了install方法
  // 有的是组件 有的可能是指令 xxx.install = ()=>{app.directive()}
  components.forEach(component => app.use(component))
}
export default {
  install,
}
