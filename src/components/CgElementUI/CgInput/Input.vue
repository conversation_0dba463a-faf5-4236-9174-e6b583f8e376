<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { computed, useAttrs } from 'vue'

defineOptions({
  name: 'CgInput',
})

const props = withDefaults(defineProps<InputProps>(), {
  modelValue: '',
  isError: false,
  append: '',
  prepend: '',
  prefix: '',
  suffix: '',
  isEditing: true,
  emptyText: '-',
  size: 'default',
})

const emit = defineEmits(['update:modelValue'])

interface InputProps {
  modelValue: string | number | null
  keyupEnterHandler?: (arg: any) => void
  isError?: boolean
  append?: string
  prepend?: string
  prefix?: string
  suffix?: string
  isEditing?: boolean
  emptyText?: string
  formatter?: (value: string | number) => string
  size?: 'default' | 'small' | 'large'
}

const attrs = computed(() => {
  const options: Record<string, any> = {
    placeholder: '请输入',
    clearable: true,
  }
  return Object.assign(options, useAttrs())
})

const errorClass = computed(() => props.isError ? 'error' : '')

const inputValue = useVModel(props, 'modelValue', emit)
// 创建类型化的插槽名称数组
const availableSlots = computed(() => Object.keys(useSlots()))

// 格式化显示值
function formatValue(value: string | number | null) {
  if (value === null || value === undefined || value === '') {
    return props.emptyText
  }

  if (props.formatter) {
    return props.formatter(value)
  }

  return value
}
</script>

<template>
  <div v-if="!isEditing" class="cg-input-view-mode" :class="[errorClass]">
    <span v-if="prepend || $slots.prepend" class="cg-view-prepend">
      <slot v-if="$slots.prepend" name="prepend" />
      <template v-else>{{ prepend }}</template>
    </span>

    <span v-if="prefix || $slots.prefix" class="cg-view-prefix">
      <slot v-if="$slots.prefix" name="prefix" />
      <template v-else>{{ prefix }}</template>
    </span>

    <span class="cg-view-value">
      <slot name="view" :value="modelValue">
        {{ formatValue(modelValue) }}
      </slot>
    </span>

    <span v-if="suffix || $slots.suffix" class="cg-view-suffix">
      <slot v-if="$slots.suffix" name="suffix" />
      <template v-else>{{ suffix }}</template>
    </span>

    <span v-if="append || $slots.append" class="cg-view-append">
      <slot v-if="$slots.append" name="append" />
      <template v-else>{{ append }}</template>
    </span>
  </div>

  <ElInput
    v-else v-bind="attrs" v-model="inputValue" :class="[errorClass]" :size="size"
    v-on="keyupEnterHandler ? { 'keyup.enter': keyupEnterHandler } : {}"
  >
    <!-- 处理插槽 -->
    <template v-for="slot in availableSlots" #[slot]="slotProps">
      <slot :name="slot" v-bind="slotProps" />
    </template>

    <!-- 处理append属性 -->
    <template v-if="props.append && !$slots.append" #append>
      {{ props.append }}
    </template>

    <!-- 处理prepend属性 -->
    <template v-if="props.prepend && !$slots.prepend" #prepend>
      {{ props.prepend }}
    </template>

    <!-- 处理prefix属性 -->
    <template v-if="props.prefix && !$slots.prefix" #prefix>
      {{ props.prefix }}
    </template>

    <!-- 处理suffix属性 -->
    <template v-if="props.suffix && !$slots.suffix" #suffix>
      {{ props.suffix }}
    </template>
  </ElInput>
</template>

<style lang="scss" scoped>
.cg-input-view-mode {
  display: flex;
  align-items: center;
  min-height: 32px;
  font-size: 14px;
  color: #606266;

  .cg-view-prepend,
  .cg-view-append {
    color: #909399;
    padding: 0 5px;
    background-color: #f5f7fa;
    border-radius: 3px;
    margin: 0 5px;
  }

  .cg-view-prefix,
  .cg-view-suffix {
    color: #909399;
  }

  .cg-view-prefix {
    margin-right: 5px;
  }

  .cg-view-suffix {
    margin-left: 5px;
  }

  .cg-view-value {
    word-break: break-word;
  }

  &.error .cg-view-value {
    color: #f5222d;
  }
}

// 原有样式保持不变
.error :deep(.el-input__inner) {
  border-color: #f5222d;
}
</style>
