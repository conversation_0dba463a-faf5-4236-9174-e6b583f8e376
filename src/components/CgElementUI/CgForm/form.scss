$form-columns: 24;
$form-column-gap: 30px;

.cg-form,
.cg-form-grid {
  // 使所有动态组件宽度为100%
  :deep(.el-form-item__content > *) {
    width: 100%;
  }

  .question-icon {
    font-size: 14px;
    color: #909399;
    cursor: pointer;
  }

  &.is-detail {
    :deep(.info) {
      font-size: 12px;
      color: #000;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .el-form-item {
      margin-bottom: 0;

      :deep(.el-form-item__label) {
        font-size: 12px;
      }
    }

    :deep(.cg-grid) {
      color: #000;
      font-weight: normal;
    }

    :deep(.el-form-item__content) {
      line-height: 32px;
    }
  }

  &__col {
    padding-right: 30px;
  }

  &__column {
    display: flex;
    flex-direction: column;
  }

  :deep(.el-radio-group, .el-checkbox-group) {
    height: 32px;
    display: flex;
    align-items: center;
  }
}

.cg-form-grid {
  .el-row {
    clear: both;

    &::before,
    &::after {
      display: none !important;
    }
  }

  &__row {
    display: grid !important;
    column-gap: min(calc(100% / #{$form-columns}), #{$form-column-gap});
    grid-template-columns: repeat(#{$form-columns}, 1fr);

    @for $i from 1 through $form-columns {
      .el-col-#{$i} {
        width: 100% !important;
        max-width: 100% !important;
        grid-column: span #{$i};
      }
    }
  }
}

:deep(.el-form-item__content) {
  line-height: unset;
  font-size: 12px;
}

:deep(.el-form-item--small .el-form-item__label) {
  height: 32px;
}

.el-form-item {

  .el-select,
  .el-input-number {
    width: 100%;
  }

  .cg-date-picker {
    width: 100% !important;
  }
}

:deep(.el-input-number .el-input__inner) {
  text-align: left;
}

.form-split__title,
.form-grid-split__title {
  color: rgb(51, 51, 51);
  font-weight: 600;
  margin: 16px 0;

  :deep(.el-form-item) {
    padding-left: 15px;
  }
}

.form-grid-split__title {
  width: 100%;
  grid-column: span $form-columns;
}

.form-split__line {
  width: 2px;
  height: 14px;
  margin-right: 8px;
  display: inline-block;
  transform: translateY(2px);
}

.cg-form__wrap,
.cg-form-grid__wrap {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  & .cg-form__top {
    width: 100%;
    height: auto;
    display: flex;
  }

  & .cg-form__center {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: column;
  }

  & .cg-form__bottom {
    width: 100%;
    display: flex;
    flex: 1;
    min-height: 100px;
    flex-direction: column;
  }
}

.cg-form-grid__wrap {
  .cg-form__center .el-row {
    .cg-form__col {
      padding: 0;
    }

    clear: both;

    &::before,
    &::after {
      display: none !important;
    }

    display: grid;
    column-gap: min(calc(100% / #{$form-columns}), #{$form-column-gap});
    grid-template-columns: repeat(#{$form-columns}, 1fr);

    @for $i from 1 through $form-columns {
      .el-col-#{$i} {
        width: 100% !important;
        grid-column: span #{$i};
      }
    }
  }
}

// 类 table 布局样式
.cg-form--table-style {
  border: 1px solid #EBEEF5;
  border-radius: 4px;

  .cg-form-grid__row {
    column-gap: unset;
  }

  :deep(.el-form-item) {
    margin-bottom: 0;
    border-bottom: 1px solid #EBEEF5;

    &:last-child {
      border-bottom: none;
    }

    .el-form-item__label {
      border-right: 1px solid #EBEEF5 !important;
      background-color: #fafbfc;
      margin-bottom: 0;
    }

    .el-form-item__content {
      &:hover {
        outline: 1px solid $cg-color-primary-light-3;
      }

      &:active,
      &:focus {
        outline: 1px solid $cg-color-primary-light-1;
      }

      &:focus-within {
        outline: 1px solid $cg-color-primary-light-1;
      }

      .cg-select-v2 {
        border-radius: 0;
      }
    }

    .el-input__wrapper,
    .el-select__wrapper,
    .cg-select-v2 {
      box-shadow: none !important;
    }

    .el-form-item__error {
      display: none;
    }

    // 当表单项验证失败时，添加红色边框
    &.is-error .el-form-item__content {
      outline: 1px solid $cg-color-danger !important;
    }
  }

  // 行样式
  .el-row {
    margin: 0 !important;

    &:not(:last-child) {
      border-bottom: 1px solid #EBEEF5;
    }

    // 第一行第一列的表单项标签左上角圆角
    &:first-child .el-col:first-child :deep(.el-form-item) .el-form-item__label {
      border-top-left-radius: 4px;
    }

    // 最后一行第一列的表单项标签左下角圆角
    &:last-child .el-col:first-child :deep(.el-form-item) .el-form-item__label {
      border-bottom-left-radius: 4px;
    }
  }

  // 列样式
  .el-col {
    border-right: 1px solid #EBEEF5;
    margin-bottom: 0;
    padding: 0;

    &:last-child {
      border-right: none;
    }
  }

  // 分隔标题样式
  .form-split__title,
  .form-grid-split__title {
    background-color: #F5F7FA;
    padding: 10px 12px;
    border-bottom: 1px solid #EBEEF5;
    margin: 0;
  }
}

.cg-form {

  /** 重置el-row  el-col样式 */
  .el-row {
    position: relative;
    box-sizing: border-box;
    display: block;
    clear: both;

    &::after,
    &::before {
      display: table;
      content: "";
    }

    &::after {
      clear: both;
    }

    &--flex {
      display: flex;
    }

    &--flex:after,
    &--flex:before {
      display: none;
    }

    &--flex.is-justify-center {
      justify-content: center;
    }

    &--flex.is-justify-end {
      justify-content: flex-end;
    }

    &--flex.is-justify-space-between {
      justify-content: space-between;
    }

    &--flex.is-justify-space-around {
      justify-content: space-around;
    }

    &--flex.is-align-top {
      align-items: flex-start;
    }

    &--flex.is-align-middle {
      align-items: center;
    }

    &--flex.is-align-bottom {
      align-items: flex-end;
    }
  }

  [class*="el-col-"] {
    float: left;
    box-sizing: border-box;
  }

  .el-col-0 {
    display: none;
    width: 0;
  }

  .el-col-offset-0 {
    margin-left: 0;
  }

  .el-col-pull-0 {
    position: relative;
    right: 0;
  }

  .el-col-push-0 {
    position: relative;
    left: 0;
  }

  @for $i from 1 through 24 {
    .el-col-#{$i} {
      width: calc(100% / 24) * $i;
    }

    .el-col-offset-#{$i} {
      margin-left: calc(100% / 24) * $i;
    }

    .el-col-pull-#{$i} {
      position: relative;
      right: calc(100% / 24) * $i;
    }

    .el-col-push-#{$i} {
      position: relative;
      left: calc(100% / 24) * $i;
    }
  }
}