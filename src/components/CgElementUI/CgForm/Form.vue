<script setup lang="ts">
import type { ElForm, FormItemProp, FormItemRule, FormValidateCallback } from 'element-plus'
import type { Arrayable } from 'element-plus/es/utils'
import type { Slot } from 'vue'
import { getApiFunction, getTransformFunction } from '@/utils/formDataSourceRegistry'
import { resolveDynamicValue } from '@/utils/resolveDynamicValue'
import ComponentTags from './ComponentTags'

export interface CgFormOption {
  // 布局配置
  layout?: {
    direction: 'column' | 'row' // 布局方向：列布局或行布局
    span: number // 布局跨度
  }
  type?: string // 表单项类型
  key?: string // 表单项键名
  label?: string // 表单项标签文本
  span?: number // 表单项占据的列数
  className?: string // 自定义CSS类名
  formItemClassName?: string // 表单项的自定义CSS类名
  gutter?: number // 栅格间隔
  required?: boolean // 是否必填
  rules?: FormItemRule[] // 表单验证规则
  props?: Record<string, any> | ((model: any, context: any) => Record<string, any>) // 组件属性配置
  events?: Record<string, any> // 事件处理配置
  slotName?: string // 插槽名称
  labelTips?: string // 标签提示信息
  style?: any // 自定义样式
  labelWidth?: string // 标签宽度
  ifRender?: (model: any) => boolean // 条件渲染函数
  dataSource?: { // 数据源配置
    type: 'api' | 'static' // 数据源类型
    apiIdentifier?: string // API 标识符，用于在注册表中查找对应的 API 函数
    queryParams?: Record<string, any> | string // API 查询参数
    transformIdentifier?: string // 转换函数标识符，用于在注册表中查找对应的转换函数
    valueField?: string // 选项值字段
    labelField?: string // 选项标签字段
    staticData?: any[] // 静态数据源
  }
}

interface Props {
  options?: CgFormOption[][]
  colSpan?: number
  readonly?: boolean
  useGrid?: boolean
  layout?: boolean
  detail?: boolean
  labelPosition?: string
  modelValue?: any
  tableStyle?: boolean // 新增：是否使用类 table 布局样式
  context?: Record<string, any>
}

defineOptions({
  name: 'CgForm',
  inheritAttrs: false,
})

const props = withDefaults(defineProps<Props>(), {
  options: () => [],
  colSpan: 24,
  readonly: false,
  useGrid: false,
  layout: false,
  detail: false,
  labelPosition: 'right',
  tableStyle: false, // 默认不启用类 table 布局
  context: () => ({}),
})

defineEmits<{
  (e: 'update:modelValue', value: any): void
}>()
const elForm = ref<InstanceType<typeof ElForm>>()
const formModel = useModel(props, 'modelValue')
const bottomSlotNode = ref<Slot | undefined>()

const attrs = computed(() => {
  const options: any = {
    'label-width': '110px',
  }
  return Object.assign(options, useAttrs())
})

const gridSlot = computed(() => !!useSlots().grid)

const bottomSlot = computed(() => !!bottomSlotNode.value)

watchEffect(() => {
  bottomSlotNode.value = useSlots().bottom
})

const formOptions = computed(() => {
  if (props.options.length)
    return props.options

  return []
})

const ifRender = computed(() => (colOption: any) => {
  if (typeof colOption.ifRender === 'function')
    return colOption.ifRender(formModel.value)

  return true
})

function getComponentProps(colOption: CgFormOption): Record<string, any> {
  const defaultProps = ComponentTags[colOption.type!]?.props || {}

  // 获取colOption.props（静态对象或函数）
  let configProps: any = {}
  if (typeof colOption.props === 'function') {
    try {
      configProps = colOption.props(formModel.value, props.context)
      if (configProps instanceof Promise) {
        // 不支持异步props函数，仅警告
        console.warn('[CgForm] props函数返回Promise，暂不支持异步。')
      }
    }
    catch {
      console.error(`[CgForm] props函数执行异常: ${colOption.key}`)
      configProps = {}
    }
  }
  else if (colOption.props && typeof colOption.props === 'object') {
    configProps = colOption.props
  }

  const mergedProps = { ...defaultProps, ...configProps }
  // 动态值解析（如$context.key/$model.key）
  const resolvedProps = resolveDynamicValue(mergedProps, formModel.value, props.context)

  // 占位符逻辑
  let placeholder = `请输入${colOption.label || ''}`
  if ([
    'select',
    'date',
    'selectv2',
    'treeSelect',
    'datepicker',
  ].includes(colOption.type!)) {
    placeholder = `请选择${colOption.label}`
  }
  if (resolvedProps.placeholder) {
    placeholder = resolvedProps.placeholder
  }
  const defaultPlaceholder: any = {}
  if (colOption.type !== 'table' && placeholder) {
    defaultPlaceholder.placeholder = placeholder
  }

  // 最终合并顺序：默认占位符 -> 组件默认props -> colOption.props
  const finalProps: Record<string, any> = {}
  let basePlaceholder = `请输入${colOption.label || ''}`
  if ([
    'select',
    'date',
    'selectv2',
    'treeSelect',
    'datepicker',
  ].includes(colOption.type!)) {
    basePlaceholder = `请选择${colOption.label}`
  }
  if (colOption.type !== 'table' && basePlaceholder) {
    finalProps.placeholder = basePlaceholder
  }
  Object.assign(finalProps, defaultProps)
  let optionSpecificProps: any = {}
  if (typeof colOption.props === 'function') {
    try {
      optionSpecificProps = colOption.props(formModel.value, props.context)
      if (optionSpecificProps instanceof Promise) {
        console.warn('[CgForm] props函数返回Promise，暂不支持异步。')
      }
    }
    catch {
      console.error(`[CgForm] props函数执行异常: ${colOption.key}`)
    }
  }
  else if (colOption.props && typeof colOption.props === 'object') {
    optionSpecificProps = colOption.props
  }
  Object.assign(finalProps, optionSpecificProps)

  // 最终动态解析
  const resolvedFinalProps = resolveDynamicValue(finalProps, formModel.value, props.context)
  return resolvedFinalProps
}

function constructProxyOption(colOption: CgFormOption): object | undefined {
  const propsObj: Record<string, any> = typeof colOption.props === 'function'
    ? colOption.props(formModel.value, props.context)
    : (colOption.props || {})

  if (propsObj.proxyOption) {
    if (typeof propsObj.proxyOption === 'object') {
      const proxyOptionCopy = { ...propsObj.proxyOption }
      if (proxyOptionCopy.query && (typeof proxyOptionCopy.query === 'object' || typeof proxyOptionCopy.query === 'string')) {
        proxyOptionCopy.query = resolveDynamicValue(proxyOptionCopy.query, formModel.value, props.context)
      }
      return proxyOptionCopy
    }
    return propsObj.proxyOption
  }

  // 处理API数据源
  if (colOption.dataSource?.type === 'api' && colOption.dataSource.apiIdentifier) {
    const apiFunc = getApiFunction(colOption.dataSource.apiIdentifier)
    const transformFunc = getTransformFunction(colOption.dataSource.transformIdentifier)

    if (apiFunc) {
      const proxyOption: Record<string, any> = {
        request: apiFunc,
        // queryParams支持对象、字符串或函数
        query: typeof colOption.dataSource.queryParams === 'function'
          ? resolveDynamicValue(colOption.dataSource.queryParams(formModel.value, props.context), formModel.value, props.context)
          : resolveDynamicValue(colOption.dataSource.queryParams || {}, formModel.value, props.context),
      }
      if (transformFunc) {
        proxyOption.transform = transformFunc
      }
      return proxyOption
    }
    else {
      console.warn(`[CgForm] 未找到API函数: ${colOption.dataSource.apiIdentifier}`)
    }
  }
  // 静态数据源无需proxyOption
  else if (colOption.dataSource?.type === 'static') {
    return undefined
  }

  return undefined
}

/**
 * 获取组件 data 属性的值：
 * - 若 dataSource.type 为 'static'，解析并返回静态数据
 * - 否则从 props 中获取 data 字段，props 可为对象或函数
 * @param colOption 表单项配置对象
 * @returns 用于组件的 data 值
 */
function getDataProp(colOption: CgFormOption): any {
  if (colOption.dataSource?.type === 'static') {
    return resolveDynamicValue(colOption.dataSource.staticData, formModel.value, props.context)
  }
  let propsObj: Record<string, any> = {}
  if (typeof colOption.props === 'function') {
    try {
      propsObj = colOption.props(formModel.value, props.context)
    }
    catch { propsObj = {} }
  }
  else if (colOption.props && typeof colOption.props === 'object') {
    propsObj = colOption.props
  }
  return propsObj.data
}

function validate(callback?: FormValidateCallback) {
  return elForm.value?.validate(callback)
}

function validateField(props?: Arrayable<FormItemProp>, callback?: FormValidateCallback) {
  return elForm.value?.validateField(props, callback)
}

function resetFields(props?: Arrayable<FormItemProp>) {
  return elForm.value?.resetFields(props)
}

function clearValidate(props?: Arrayable<FormItemProp>) {
  return elForm.value?.clearValidate(props)
}

function getDetailValue(key?: string) {
  const defaultValue = '-'
  if (!key)
    return defaultValue

  const value = formModel.value?.[key]
  if (value === null || value === undefined || value === '') {
    return defaultValue
  }
  return Array.isArray(value)
    ? value.join(',') || defaultValue
    : value.toString() || defaultValue
}

function getSpan(colOption: any, defaultSpan = 0) {
  return colOption.span ? colOption.span : defaultSpan === 0 ? props.colSpan : defaultSpan
}

function getFormItemProp(colOption: CgFormOption) {
  const { required, rules, key, type, label, labelTips, style, labelWidth: optionLabelWidth } = colOption
  const nullToZero = (colOption as any).nullToZero as boolean
  const itemProps: any = {}
  const rulesTemp = [...(rules || [])]

  if (required) {
    const requiredRule = rulesTemp.find(rule => rule.required)
    if (!requiredRule) {
      let message = label ? '请输入' : getComponentProps(colOption).placeholder || '请输入'
      const trigger = ['blur', 'change']
      if (['select', 'date', 'selectv2', 'treeSelect', 'datepicker'].includes(type!)) {
        message = '请选择'
      }

      message += label || ''
      const rule = {
        required: !props.detail,
        message,
        trigger,
      }
      rulesTemp.push(rule)

      if (nullToZero) {
        const validate = (_rule: any, value: any, callback: (error?: Error) => void) => {
          if (value === 0) {
            callback(new Error(message))
          }
          else {
            callback()
          }
        }
        const newRule = {
          validator: validate,
          trigger: 'blur',
        }
        rulesTemp.push(newRule)
      }
    }
  }

  itemProps.prop = key
  itemProps.rules = rulesTemp
  if (style) {
    itemProps.style = style
  }

  if (!label && !labelTips) {
    itemProps.labelWidth = '0'
  }
  else if (optionLabelWidth) {
    // 如果选项中明确设置了 labelWidth，则使用它
    itemProps.labelWidth = optionLabelWidth
  }

  return itemProps
}

defineExpose({
  validate,
  validateField,
  resetFields,
  clearValidate,
  getDetailValue,
  elForm,
})
</script>

<template>
  <!-- 布局1: 无选项时的简单表单渲染 -->
  <ElForm
    v-if="formOptions.length === 0" ref="elForm" :model="formModel" :label-position="labelPosition"
    class="cg-form" v-bind="attrs" :class="{ 'cg-form--table-style': tableStyle }"
  >
    <slot />
  </ElForm>
  <!-- 布局2: 标准表单渲染(非layout模式) -->
  <ElForm
    v-else-if="!layout" ref="elForm" :class="[
      useGrid ? 'cg-form-grid' : 'cg-form',
      { 'is-detail': detail },
      { 'cg-form--table-style': tableStyle },
    ]" v-bind="attrs" :model="formModel" :label-position="labelPosition"
  >
    <slot name="top" />
    <template v-for="(rowOptions, index) in formOptions" :key="index">
      <!-- 列布局模式: 垂直方向排列表单项 -->
      <ElCol
        v-if="rowOptions.length > 0 && rowOptions[0].layout?.direction === 'column' && ifRender(rowOptions[0])"
        :key="`col-layout-${index}`" class="cg-form__col" :span="rowOptions[0].layout.span"
      >
        <template v-for="(colOption, index1) in rowOptions" :key="`col-item-${index}-${index1}`">
          <div
            v-if="colOption.type === 'split' && ifRender(colOption)"
            :class="[colOption.className, useGrid ? 'form-grid-split__title' : 'form-split__title']"
          >
            <span v-if="colOption.label" class="form-split__line" />
            {{ colOption.label }}
            <template v-if="colOption.slotName">
              <slot :name="colOption.slotName" />
            </template>
          </div>
          <template v-else-if="ifRender(colOption)">
            <ElCol
              v-if="colOption.type !== 'grid'" :key="colOption.key" :class="colOption.className"
              :style="{ 'padding-right': `${colOption.gutter || 0}px` }" :span="getSpan(colOption, 24)"
            >
              <ElFormItem
                :label="colOption.label" v-bind="getFormItemProp(colOption)"
                :class="colOption.formItemClassName"
              >
                <template v-if="colOption.type === 'slot'">
                  <slot :name="colOption.slotName" :model="formModel" :context="props.context" />
                </template>
                <!-- 详情模式: 只读显示数据 -->
                <template v-else-if="detail">
                  <div :title="getDetailValue(colOption.key)" class="info">
                    {{ getDetailValue(colOption.key) }}
                  </div>
                </template>
                <Component
                  :is="ComponentTags[colOption.type!]?.component" v-else v-model="formModel[colOption.key!]"
                  v-bind="getComponentProps(colOption)" :proxy-option="constructProxyOption(colOption)"
                  :data="getDataProp(colOption)" :context="props.context" :model="formModel" v-on="colOption.events || {}"
                />
                <template v-if="colOption.labelTips" #label>
                  <span>
                    {{ colOption.label }}
                    <ElTooltip class="item" effect="dark" placement="top">
                      <template #default>
                        <font-awesome-icon icon="question-circle" class="question-icon" />
                      </template>
                      <template #content>
                        <div v-html="colOption.labelTips" />
                      </template>
                    </ElTooltip>
                  </span>
                </template>
              </ElFormItem>
            </ElCol>
            <template v-else-if="colOption.type === 'grid'">
              <Component
                :is="ComponentTags[colOption.type!]?.component" :key="colOption.type + index"
                v-model="formModel[colOption.key!]" v-bind="getComponentProps(colOption)"
                :proxy-option="constructProxyOption(colOption)" :data="getDataProp(colOption)" :context="props.context"
                :model="formModel" v-on="colOption.events || {}"
              />
            </template>
          </template>
        </template>
      </ElCol>

      <!-- 行布局模式: 水平方向排列表单项 -->
      <ElRow v-else-if="rowOptions.length > 0" :key="`layout${index}`" :class="[{ 'cg-form-grid__row': useGrid }]">
        <template v-for="(colOption, index1) in rowOptions" :key="`row-item-${index}-${index1}`">
          <div
            v-if="colOption.type === 'split' && ifRender(colOption)"
            :class="[colOption.className, useGrid ? 'form-grid-split__title' : 'form-split__title']"
          >
            <span v-if="colOption.label" class="form-split__line" />
            {{ colOption.label }}
            <template v-if="colOption.slotName">
              <slot :name="colOption.slotName" :model="formModel" :context="props.context" />
            </template>
          </div>
          <template v-else-if="ifRender(colOption)">
            <ElCol
              v-if="colOption.type !== 'grid'" :key="colOption.key"
              :class="[colOption.className, { 'cg-form-grid__row__col': useGrid }]" :span="getSpan(colOption)"
            >
              <ElFormItem
                :label="colOption.label" :class="colOption.formItemClassName"
                v-bind="getFormItemProp(colOption)"
              >
                <template v-if="colOption.type === 'slot'">
                  <slot :name="colOption.slotName" :model="formModel" :context="props.context" />
                </template>
                <!-- 详情模式: 只读显示数据 -->
                <template v-else-if="detail">
                  <div :title="getDetailValue(colOption.key)" class="info">
                    {{ getDetailValue(colOption.key) }}
                  </div>
                </template>
                <Component
                  :is="ComponentTags[colOption.type!]?.component" v-else v-model="formModel[colOption.key!]"
                  v-bind="getComponentProps(colOption)" :proxy-option="constructProxyOption(colOption)"
                  :data="getDataProp(colOption)" :context="props.context" :model="formModel" v-on="colOption.events || {}"
                />
                <template v-if="colOption.labelTips" #label>
                  <span>
                    {{ colOption.label }}
                    <ElTooltip class="item" effect="dark" placement="top">
                      <template #default>
                        <font-awesome-icon icon="question-circle" class="question-icon" />
                      </template>
                      <template #content>
                        <div v-html="colOption.labelTips" />
                      </template>
                    </ElTooltip>
                  </span>
                </template>
              </ElFormItem>
            </ElCol>
            <template v-else-if="colOption.type === 'grid'">
              <Component
                :is="ComponentTags[colOption.type!]?.component" :key="colOption.type + index"
                v-model="formModel[colOption.key!]" v-bind="getComponentProps(colOption)"
                :proxy-option="constructProxyOption(colOption)" :data="getDataProp(colOption)" :context="props.context"
                :model="formModel" v-on="colOption.events || {}"
              />
            </template>
          </template>
        </template>
      </ElRow>
    </template>

    <slot v-if="gridSlot" name="grid" :model="formModel" :context="props.context" />
    <slot :model="formModel" :context="props.context" />
  </ElForm>
  <!-- 布局3: 自定义布局模式 -->
  <div v-else class="cg-form" :class="[useGrid ? 'cg-form-grid__wrap' : 'cg-form__wrap']">
    <div class="cg-form__top">
      <slot name="top" :model="formModel" :context="props.context" />
    </div>
    <div class="cg-form__center">
      <ElForm
        ref="elForm" class="cg-form" :model="formModel" :label-position="labelPosition"
        :class="{ 'is-detail': detail, 'cg-form--table-style': tableStyle }" v-bind="attrs"
      >
        <ElRow v-for="(rowOptions, index) in formOptions" :key="index">
          <template v-for="(colOption, index1) in rowOptions" :key="`wrap-item-${index}-${index1}`">
            <div v-if="colOption.type === 'split' && ifRender(colOption)" class="form-split__title">
              <span v-if="colOption.label" class="form-split__line" />
              {{ colOption.label }}
              <template v-if="colOption.slotName">
                <slot :name="colOption.slotName" :model="formModel" :context="props.context" />
              </template>
            </div>
            <template v-else-if="ifRender(colOption)">
              <ElCol
                v-if="colOption.type !== 'grid'" :key="colOption.key"
                :class="[colOption.className, { 'cg-form__col': getSpan(colOption) !== 24 }]"
                :span="getSpan(colOption)"
              >
                <ElFormItem
                  :label="colOption.label" v-bind="getFormItemProp(colOption)"
                  :class="colOption.formItemClassName"
                >
                  <template v-if="colOption.type === 'slot'">
                    <slot :name="colOption.slotName" :model="formModel" :context="props.context" />
                  </template>
                  <template v-else-if="detail">
                    <div :title="getDetailValue(colOption.key)" class="info">
                      {{ getDetailValue(colOption.key) }}
                    </div>
                  </template>
                  <Component
                    :is="ComponentTags[colOption.type!]?.component" v-else
                    v-model="formModel[colOption.key || '']" v-bind="getComponentProps(colOption)"
                    :proxy-option="constructProxyOption(colOption)" :data="getDataProp(colOption)"
                    :context="props.context" :model="formModel" v-on="colOption.events || {}"
                  />
                  <template v-if="colOption.labelTips" #label>
                    <span>
                      {{ colOption.label }}
                      <ElTooltip class="item" effect="dark" placement="top">
                        <i class="iconfont cg-question" />
                        <template #content>
                          <div v-html="colOption.labelTips" />
                        </template>
                      </ElTooltip>
                    </span>
                  </template>
                </ElFormItem>
              </ElCol>
              <template v-else-if="colOption.type === 'grid'">
                <Component
                  :is="ComponentTags[colOption.type!]?.component" :key="colOption.type + index"
                  v-model="formModel[colOption.key || '']" v-bind="getComponentProps(colOption)"
                  :proxy-option="constructProxyOption(colOption)" :data="getDataProp(colOption)"
                  :context="props.context" :model="formModel" v-on="colOption.events || {}"
                />
              </template>
            </template>
          </template>
        </ElRow>
        <slot v-if="gridSlot" name="grid" :model="formModel" :context="props.context" />
      </ElForm>
    </div>
    <div v-if="bottomSlot" class="cg-form__bottom">
      <slot name="bottom" :model="formModel" :context="props.context" />
    </div>
    <slot :model="formModel" :context="props.context" />
  </div>
</template>

<style scoped lang="scss" src="./form.scss" />
