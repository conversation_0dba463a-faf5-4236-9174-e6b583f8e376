enum ComponentTagsEnum {
  /** 文本框 */
  Input = 'input',
  /** 下拉框 */
  SelectV2 = 'selectv2',
  /** 日期框 */
  DatePicker = 'datepicker',
  /** 组合文本框 */
  ComplexInput = 'complexInput',
  /** 组合文本框V2 */
  ComplexInputV2 = 'complexInputV2',
  /** radio */
  Radio = 'radio',
  /** 按钮 */
  Button = 'button',
  /** 数值文本框 */
  InputNumber = 'inputNumber',
  /** checkbox */
  Checkbox = 'checkbox',
  /** 表格 */
  Grid = 'grid',
  /** 滑块 */
  Switch = 'switch',
  /** 附件上传 */
  FileUploader = 'fileUploader',
  /** 类目树选择 */
  CategoryTree = 'categoryTree',
  /** 图片 */
  Image = 'image',
  /** 链接按钮 */
  LinkButton = 'linkButton',
  /** 图片管理器 */
  ImageManager = 'imageManager',
  /** 富文本 */
  RichText = 'richText',
}
export default ComponentTagsEnum
