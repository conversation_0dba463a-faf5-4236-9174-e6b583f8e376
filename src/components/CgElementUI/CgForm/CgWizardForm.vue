<script setup lang="ts">
import type { Slots } from 'vue'
import type { CgFormOption } from './Form.vue'

import CgForm from './Form.vue'

const props = withDefaults(
  defineProps<{
    steps: WizardStep[]
    /** v-model */
    modelValue: Record<string, any>
    /** 传给 CgForm 的 context */
    context?: Record<string, any>
    /** label-position 透传 */
    labelPosition?: 'left' | 'right' | 'top'
    /** 是否显示步骤条 */
    showStepper?: boolean
    /** 下一步 / 完成 按钮文案 */
    nextText?: string
    finishText?: string
    /** 透传给 CgForm 的其他 props （如 detail/useGrid/tableStyle 等） */
    formProps?: Record<string, any>
  }>(),
  {
    context: () => ({}),
    labelPosition: 'right',
    showStepper: true,
    nextText: '下一步',
    finishText: '提交',
    formProps: () => ({}),
  },
)
const emit = defineEmits<{
  (e: 'update:modelValue', v: any): void
  (e: 'submit', v: any): void
  (e: 'change', current: number): void
}>()

interface WizardStep {
  title: string
  /** 可选：图标、描述等额外信息 */
  icon?: string | any
  description?: string
  options: CgFormOption[][]
  /** 切步前置钩子：返回 false/Promise<false> 阻止前进 */
  beforeNext?: (model: any) => boolean | Promise<boolean>
  /** 可选：动态条件渲染 */
  ifRender?: (model: any) => boolean
}

const current = ref(0)
const formModel = useModel(props, 'modelValue')
const formRef = ref<InstanceType<typeof CgForm>>()
const slots: Slots = useSlots()
const slotNames = computed(() => Object.keys(slots))

const visibleSteps = computed(() =>
  props.steps.filter(s =>
    typeof s.ifRender === 'function' ? s.ifRender(formModel.value) : true,
  ),
)

async function handleNext() {
  const pass = await formRef.value?.validate()
  if (!pass)
    return

  const step = visibleSteps.value[current.value]
  if (step?.beforeNext) {
    const ok = await step.beforeNext(formModel.value)
    if (!ok)
      return
  }

  if (current.value >= visibleSteps.value.length - 1) {
    emit('submit', formModel.value)
    return
  }

  current.value += 1
  emit('change', current.value)
}

function handlePrev() {
  if (current.value > 0) {
    current.value -= 1
    emit('change', current.value)
  }
}

watch(
  () => visibleSteps.value.length,
  (len) => {
    if (current.value >= len)
      current.value = len - 1
  },
)
</script>

<template>
  <div class="cg-wizard-form">
    <ElSteps v-if="showStepper" :active="current" class="mb-4" align-center>
      <ElStep v-for="(s, i) in steps" :key="i" :title="s.title" :description="s.description" :icon="s.icon" />
    </ElSteps>

    <Transition name="fade" mode="out-in">
      <CgForm
        key="form-{{current}}" ref="formRef" v-model="formModel" :options="steps[current].options"
        :context="context" :label-position="labelPosition" v-bind="formProps"
      >
        <!-- 向内层转发全部具名插槽 -->
        <template v-for="name in slotNames" :key="name" #[name]="slotProps">
          <slot :name="name" v-bind="slotProps" />
        </template>
      </CgForm>
    </Transition>

    <template v-if="!slots.nav">
      <div class="cg-wizard-form__nav">
        <ElButton :disabled="current === 0" @click="handlePrev">
          上一步
        </ElButton>
        <ElButton type="primary" @click="handleNext">
          {{ current === steps.length - 1 ? finishText : nextText }}
        </ElButton>
      </div>
    </template>
    <slot
      v-else name="nav" :current="current" :steps="steps" :next="handleNext" :prev="handlePrev"
      :is-last="current === steps.length - 1"
    />
  </div>
</template>

<style scoped lang="scss">
.cg-wizard-form {
  width: 100%;

  &__nav {
    margin-top: 24px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
