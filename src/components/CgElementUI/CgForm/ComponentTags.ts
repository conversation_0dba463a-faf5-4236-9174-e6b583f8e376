export default <any>{
  // 富文本编辑器
  richText: {
    component: 'CgTinyMCE',
    props: {
      init: {
        height: 300, // 设置编辑器高度
      },
    },
  },
  // 文本框
  input: {
    // 对应的组件 tag
    component: 'cg-input',
    // 传递给组件的默认 props
    props: {
      clearable: true,
    },
  },
  // 组合型文本下拉组合框
  complexInput: {
    // 对应的组件 tag
    component: 'cg-complex-input',
    // 传递给组件的默认 props
    props: {
      clearable: true,
      type: 'input',
    },
  },
  complexInputV2: {
    component: 'cg-complex-input-v2',
    // 传递给组件的默认 props
    props: {
      clearable: true,
    },
  },
  selectv2: {
    component: 'CgSelectV2',
    props: {
      allowClear: true,
      class: 'cg-width-150',
      collapseTags: true,
    },
  },
  // 树形选择框
  treeSelect: {
    component: 'cg-tree-select',
    props: {
      clearable: true,
      checkStrictly: true,
      nodeKey: 'id',
    },
  },
  // 日期框
  datepicker: {
    component: 'cg-date-picker',
    props: {
      clearable: true,
    },
  },
  // radio选择框
  radio: {
    component: 'cg-radio',
    props: {},
  },
  // button
  button: {
    component: 'cg-button',
    props: {},
  },
  // checkbox
  checkbox: {
    component: 'cg-checkbox',
    props: {},
  },
  // 数字框
  inputNumber: {
    component: 'cg-input-number',
    props: {},
  },
  switch: {
    component: 'cg-switch',
    props: {},
  },
  grid: {
    component: 'cg-grid',
    props: {
      height: 'auto',
    },
  },
  fileUploader: {
    component: 'CgFileUploader',
    props: {},
  },
  // 类目树选择
  categoryTree: {
    component: 'CategoryTreeSelect',
    props: {
      clearable: true,
    },
  },
  // 图片
  image: {
    component: 'CgImage',
    props: {},
  },
  linkButton: {
    component: 'CgLinkButton',
    props: {},
  },
  // 图片管理器
  imageManager: {
    component: 'CgImageManager',
    props: {
      uploadStrategy: null, // 需要在使用时提供
    },
  },
  // // vxe表格
  // vxeGrid: {
  //   component: "vxe-grid",
  //   props: {
  //     height: "auto",
  //   },
  // },
}
