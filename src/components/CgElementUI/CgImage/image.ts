import type { ExtractPropTypes, PropType } from 'vue'

export const imageProps = {
  /**
   * v-model value for image source
   */
  modelValue: { type: String, default: '' },
  /**
   * 图片源，同原生
   */
  src: { type: String, default: '' },
  /**
   * 图片适应容器的方式，同 object-fit
   */
  fit: {
    type: String as PropType<'fill' | 'contain' | 'cover' | 'none' | 'scale-down'>,
    default: 'cover',
  },
  /**
   * 是否使用懒加载
   */
  lazy: { type: Boolean, default: false },
  /**
   * 允许大图预览的图片列表
   */
  previewSrcList: { type: Array as PropType<string[]>, default: () => [] },
  /**
   * 初始预览图片的索引，小于等于 previewSrcList 的长度
   */
  initialIndex: { type: Number, default: 0 },
  /**
   * 是否显示图片预览进度条
   */
  showProgress: { type: Boolean, default: true },
  /**
   * 图片宽度
   */
  width: { type: [String, Number], default: 'auto' },
  /**
   * 图片高度
   */
  height: { type: [String, Number], default: 'auto' },
  /**
   * 图片圆角
   */
  borderRadius: { type: String, default: '0' },
  /**
   * 加载失败时显示的文本
   */
  errorText: { type: String, default: '加载失败' },
  /**
   * 加载中显示的文本
   */
  loadingText: { type: String, default: '加载中...' },
  /**
   * 图片源为空时显示的文本
   */
  emptySrcText: { type: String, default: '暂无图片' },
}

export type CgImageProps = ExtractPropTypes<typeof imageProps>
