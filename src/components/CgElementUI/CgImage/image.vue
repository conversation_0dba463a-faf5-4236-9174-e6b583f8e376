<script setup lang="ts">
import { ElImage } from 'element-plus'
import { imageProps } from './image'

defineOptions({
  name: 'CgImage',
  inheritAttrs: false,
})

const props = defineProps(imageProps)
const computedSrc = computed(() => props.modelValue || props.src)
const emit = defineEmits(['load', 'error', 'switch', 'close', 'show'])

// 图片加载状态
const isLoading = ref(false)
const isError = ref(false)
const imageRef = ref<InstanceType<typeof ElImage> | null>(null)

// 计算样式
const imageStyle = computed(() => {
  const style: Record<string, string> = {}

  if (props.width !== 'auto') {
    style.width = typeof props.width === 'number' ? `${props.width}px` : props.width
  }

  if (props.height !== 'auto') {
    style.height = typeof props.height === 'number' ? `${props.height}px` : props.height
  }

  if (props.borderRadius !== '0') {
    style.borderRadius = props.borderRadius
  }

  return style
})

// 处理图片加载事件
function handleLoad(e: Event) {
  isLoading.value = false
  isError.value = false
  emit('load', e)
}

// 处理图片加载失败事件
function handleError(e: Event) {
  isLoading.value = false
  isError.value = true
  emit('error', e)
}

// 处理图片切换事件
function handleSwitch(index: number) {
  emit('switch', index)
}

// 处理图片预览关闭事件
function handleClose() {
  emit('close')
}

// 处理图片预览显示事件
function handleShow() {
  emit('show')
}

// 暴露方法
function showPreview() {
  if (imageRef.value) {
    imageRef.value.showPreview()
  }
}

defineExpose({
  showPreview,
})
</script>

<template>
  <div class="cg-image-container">
    <div v-if="!computedSrc" class="cg-image cg-image-empty" :style="imageStyle">
      <slot name="empty">
        <div class="cg-image-empty-content">
          <i class="el-icon-picture-outline" />
          <p>{{ props.emptySrcText }}</p>
        </div>
      </slot>
    </div>
    <ElImage
      v-else
      ref="imageRef"
      :src="computedSrc"
      :fit="props.fit"
      :lazy="props.lazy"
      :preview-src-list="props.previewSrcList"
      :initial-index="props.initialIndex"
      :show-progress="props.showProgress"
      class="cg-image"
      :style="imageStyle"
      v-bind="$attrs"
      @load="handleLoad"
      @error="handleError"
      @switch="handleSwitch"
      @close="handleClose"
      @show="handleShow"
    >
      <!-- 加载中插槽 -->
      <template #placeholder>
        <slot name="placeholder">
          <div class="cg-image-placeholder">
            <div class="cg-image-loading">
              <i class="el-icon-loading" />
              <p>{{ props.loadingText }}</p>
            </div>
          </div>
        </slot>
      </template>

      <!-- 加载失败插槽 -->
      <template #error>
        <slot name="error">
          <div class="cg-image-error">
            <i class="el-icon-picture-outline" />
            <p>{{ props.errorText }}</p>
          </div>
        </slot>
      </template>

      <!-- 工具栏插槽 -->
      <template v-if="$slots.toolbar" #toolbar="slotProps">
        <slot name="toolbar" v-bind="slotProps" />
      </template>

      <!-- 进度条插槽 -->
      <template v-if="$slots.progress" #progress="slotProps">
        <slot name="progress" v-bind="slotProps" />
      </template>

      <!-- 查看器插槽 -->
      <template v-if="$slots.viewer" #viewer="slotProps">
        <slot name="viewer" v-bind="slotProps" />
      </template>
    </ElImage>
  </div>
</template>

<style lang="scss">
.cg-image-container {
  display: inline-block;
  vertical-align: middle;

  .cg-image {
    display: block;
  }

  .cg-image-placeholder,
  .cg-image-error,
  .cg-image-empty-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background-color: #f5f7fa;
    color: #909399;

    i {
      font-size: 24px;
      margin-bottom: 8px;
    }

    p {
      margin: 0;
      font-size: 12px;
    }
  }
}
</style>
