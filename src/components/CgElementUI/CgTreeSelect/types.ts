// CgTreeSelect 组件的类型定义

export interface TreeNodeData {
  id?: number | string
  label?: string
  children?: TreeNodeData[]
  disabled?: boolean
  isLeaf?: boolean
  [key: string]: any
}

export interface TreeProps {
  label?: string
  children?: string
  disabled?: string
  isLeaf?: string
  value?: string
  [key: string]: any
}

export interface CgTreeSelectProps {
  modelValue?: any
  clearable?: boolean
  checkStrictly?: boolean
  nodeKey?: string
  data?: TreeNodeData[]
  props?: TreeProps
  defaultExpandAll?: boolean
  placeholder?: string
  disabled?: boolean
  filterable?: boolean
  filterMethod?: (value: string, data: TreeNodeData) => boolean
}

export interface CgTreeSelectEmits {
  (e: 'update:modelValue', value: any): void
  (e: 'change', value: any): void
}
