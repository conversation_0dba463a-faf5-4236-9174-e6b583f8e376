<script setup lang="ts">
import { useVModel } from '@vueuse/core'
import { ElTreeSelect } from 'element-plus'
import { computed } from 'vue'

defineOptions({
  name: 'CgTreeSelect',
  inheritAttrs: false,
})

// 定义组件属性
const props = withDefaults(defineProps<{
  modelValue?: any
  clearable?: boolean
  checkStrictly?: boolean
  nodeKey?: string
  data?: any[]
  props?: any
  defaultExpandAll?: boolean
  placeholder?: string
  disabled?: boolean
  filterable?: boolean
  filterMethod?: (value: string, data: any) => boolean
}>(), {
  modelValue: undefined,
  clearable: true,
  checkStrictly: true,
  nodeKey: 'id',
  data: () => [],
  props: undefined,
  defaultExpandAll: false,
  placeholder: '请选择',
  disabled: false,
  filterable: false,
  filterMethod: undefined,
})

// 定义事件
const emits = defineEmits<{
  'update:modelValue': [value: any]
  'change': [value: any]
}>()

// 使用 useVModel 处理双向绑定
const innerValue = useVModel(props, 'modelValue', emits)

// 计算属性：树形组件配置
const treeProps = computed(() => {
  return {
    label: 'label',
    children: 'children',
    value: 'id',
    ...props.props,
  }
})

// 处理值变化
function handleChange(value: any) {
  innerValue.value = value
  emits('change', value)
}

// 暴露方法
defineExpose({
  // 可以在这里添加需要暴露给父组件的方法
})
</script>

<template>
  <ElTreeSelect
    :model-value="innerValue" :clearable="clearable" :check-strictly="checkStrictly" :node-key="nodeKey"
    :data="data" :props="treeProps" :default-expand-all="defaultExpandAll" :placeholder="placeholder"
    :disabled="disabled" :filterable="filterable" :filter-method="filterMethod" class="cg-tree-select"
    @update:model-value="handleChange"
  >
    <template v-if="$slots.default" #default="scope">
      <slot v-bind="scope" />
    </template>
  </ElTreeSelect>
</template>

<style lang="scss" scoped>
.cg-tree-select {
  width: 100%;

  :deep(.el-select-dropdown__wrap) {
    max-height: 300px;
  }

  :deep(.el-tree-node__content) {
    height: 32px;
  }

  :deep(.el-tree-node__label) {
    font-size: 14px;
  }

  &.is-disabled {
    cursor: not-allowed;
  }
}
</style>
