import type { ButtonProps } from 'element-plus'
import type { Component, ExtractPropTypes, PropType } from 'vue'

export interface DropdownItem {
  id: string | number
  text: string
  disabled?: boolean
  divided?: boolean
  icon?: string | Component
  roles?: string[]
  _if?: boolean
  confirm?: {
    title: string
    message: string
    type?: 'success' | 'warning' | 'info' | 'error'
  }
}

export interface DropdownOptions {
  text: string
  icon?: string
  items: DropdownItem[]
  _if?: boolean
}

export const dropdownProps = {
  options: { type: Object as PropType<DropdownOptions>, default: () => ({}) },
  buttonType: { type: String as PropType<ButtonProps['type']>, default: 'primary' },
  events: { type: Object, default: () => ({}) },
  plain: { type: Boolean, default: false },
  dropdownMenuClass: { type: String, default: '' },
}

export type DropdownProps = ExtractPropTypes<typeof dropdownProps>
