<script setup lang="ts">
import { usePermissionStore } from '@/stores/Permission'
import { ElMessageBox } from 'element-plus'
import { dropdownProps } from './dropdown'

defineOptions({
  name: 'CgDropdown',
  inheritAttrs: false,
})

const props = defineProps(dropdownProps)

const emit = defineEmits(['command'])

const spiltButton = ref(false)

// 设置下拉菜单项
const dropdownMeunItems = computed(() => {
  return props.options?.items ?? []
})

// 是否渲染组件
const ifRender = computed(() => {
  return props.options && props.options._if === undefined ? true : props.options._if
})

// 处理属性
const attrs = useAttrs()
const attrsCompute = computed(() => {
  const options: Record<string, any> = {
    trigger: 'click',
  }

  return Object.assign(options, attrs)
})

// 监听splitButton属性变化
watchEffect(() => {
  if (attrs.splitButton === '')
    spiltButton.value = true
  else
    spiltButton.value = Boolean(attrs.splitButton || attrs['split-button'])
})

function handleCommand(command: any) {
  if (command.confirm) {
    ElMessageBox.confirm(command.confirm.message, command.confirm.title, {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      closeOnClickModal: false,
      type: command.confirm.type ?? 'warning',
    })
      .then(() => {
        props.events?.command ? props.events.command(command) : emit('command', command)
      })
      .catch(() => {
        //
      })
  }
  else {
    props.events?.command ? props.events.command(command) : emit('command', command)
  }
}

const rolesCompute = computed(() => {
  const roles: any[] = []
  const travser = (items: any) => {
    items?.forEach((element: any) => {
      if (element.items) {
        travser(element.items)
      }
      else {
        // 操作按钮添加权限控制
        if (element.roles?.length)
          roles.push(...element.roles)
        else
          roles.push('*')
      }
    })
  }
  travser(props.options.items)
  return roles
})

function disabled(item: any) {
  if (item.disabled !== undefined)
    return item.disabled

  if (item.roles && Array.isArray(item.roles)) {
    return !usePermissionStore().roles.some((role: string) => {
      return item.roles?.includes(role)
    })
  }

  return false
}
</script>

<template>
  <ElDropdown
    class="cg-dropdown"
    v-bind="attrsCompute"
    :type="buttonType ? buttonType : undefined"
    @command="handleCommand"
  >
    <template v-if="ifRender" #dropdown>
      <ElDropdownMenu :class="dropdownMenuClass">
        <template v-for="dropdownMeunItem in dropdownMeunItems" :key="dropdownMeunItem.id">
          <ElDropdownItem
            v-if="dropdownMeunItem._if === undefined ? true : dropdownMeunItem._if"
            :disabled="disabled(dropdownMeunItem)"
            :command="dropdownMeunItem"
          >
            {{ dropdownMeunItem.text }}
          </ElDropdownItem>
        </template>
      </ElDropdownMenu>
    </template>
    <template v-if="ifRender">
      <template v-if="!options.icon">
        <span v-if="spiltButton" data-type="splitButton">
          {{ options.text }}
        </span>
        <ElButton v-else v-permission:[rolesCompute] link :type="buttonType ? buttonType : undefined">
          {{ options.text }}
          <ElIcon class="el-icon--right">
            <IEpArrowDown />
          </ElIcon>
        </ElButton>
      </template>
      <template v-else>
        <i v-permission:[rolesCompute] :class="options.icon" class="icon-operator" :title="options.text" />
      </template>
    </template>
    <slot v-else />
  </ElDropdown>
</template>

<style scoped lang="scss">
.cg-dropdown {
  .icon-operator {
    color: $cg-color-primary;
    cursor: pointer;
    &::before {
      font-size: 18px;
    }
  }
}
</style>
