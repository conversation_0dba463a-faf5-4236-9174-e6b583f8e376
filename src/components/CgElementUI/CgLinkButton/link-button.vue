<script setup lang="ts">
import type { RouteLocationRaw } from 'vue-router'
import { resolveDynamicValue } from '@/utils/resolveDynamicValue'
import { useRouter } from 'vue-router'
import CgButton from '../CgButton'
import { linkButtonProps } from './link-button'

defineOptions({
  name: 'CgLinkButton',
  inheritAttrs: false,
})

const props = defineProps(linkButtonProps)
const modelValue = useVModel(props, 'modelValue')
const router = useRouter()

function handleClick(_event: MouseEvent) {
  const context = props.context || {}
  const model = props.model
  const rawTo = props.to as string | RouteLocationRaw
  const defaultId = typeof modelValue.value === 'number' ? String(modelValue.value) : modelValue.value

  // 外部链接检测并解析占位符，直接打开
  if (typeof rawTo === 'string' && /^https?:\/\//.test(rawTo)) {
    const url = rawTo.replace(/\$context\.[\w.]+|\$model\.[\w.]+/g, (placeholder) => {
      const val = resolveDynamicValue(placeholder, model, context)
      return val != null ? String(val) : ''
    })
    window.open(url, props.target)
    return
  }

  let to = rawTo as RouteLocationRaw

  // 将 modelValue 作为默认路由 id
  if (defaultId != null && typeof to === 'object' && to !== null) {
    const toObj = to as any
    const params = (typeof toObj.params === 'object' ? { ...toObj.params } : {}) as Record<string, any>
    if (!('id' in params) || params.id == null || params.id === '') {
      params.id = defaultId
      to = { ...toObj, params }
    }
  }

  // 动态解析 props.to.params 中的 $model/$context 引用
  if (typeof to === 'object' && to !== null && 'params' in to && to.params) {
    const resolvedParams = resolveDynamicValue((to as any).params, model, context)
    to = { ...to, params: resolvedParams }
  }

  const finalQuery = { ...props.query }

  if (Object.keys(finalQuery).length > 0) {
    if (typeof to === 'string') {
      try {
        const resolved = router.resolve({ path: to, query: finalQuery })
        to = resolved
      }
      catch (e) {
        console.error(e)
      }
    }
    else if (typeof to === 'object' && to !== null) {
      to = {
        ...to,
        query: { ...(to.query || {}), ...finalQuery },
      }
    }
  }

  // 如果设置了在新窗口打开
  if (props.target === '_blank') {
    const routeLocation = router.resolve(to)
    window.open(routeLocation.href, '_blank')
    return
  }

  if (props.replace)
    router.replace(to)
  else
    router.push(to)
}
</script>

<template>
  <CgButton
    v-bind="$attrs" :size="props.size" :auto-loading="props.autoLoading" class="cg-link-button"
    @click="handleClick"
  >
    <slot>{{ props.text }}</slot>
  </CgButton>
</template>

<style lang="scss">
.cg-link-button {
  cursor: pointer;
}
</style>
