import type { ExtractPropTypes, PropType } from 'vue'
import type { RouteLocationRaw } from 'vue-router'
import { buttonProps } from '../CgButton/button'

export const linkButtonProps = {
  ...buttonProps,
  /** 路由目标，可以是路由名称、路径或完整的路由对象 */
  to: {
    type: [String, Object] as PropType<RouteLocationRaw>,
    required: true,
  },
  /** 是否在新窗口打开 */
  target: {
    type: String as PropType<'_self' | '_blank' | '_parent' | '_top'>,
    default: '_self',
  },
  /** 是否替换当前路由（使用 router.replace 而不是 router.push） */
  replace: {
    type: Boolean,
    default: false,
  },
  /** 按钮显示的文本 */
  text: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [Number, String],
    default: '',
  },
  /**
   * 查询参数，将合并到路由的 query 对象中
   */
  query: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({}),
  },
  context: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({}),
  },
  model: {
    type: Object as PropType<Record<string, any>>,
    default: () => ({}),
  },
}

export type LinkButtonProps = ExtractPropTypes<typeof linkButtonProps>
