# CgLinkButton 组件

CgLinkButton 是一个通用的链接按钮组件，用于在点击时导航到指定的路由。

## 基本用法

```vue
<template>
  <!-- 基本用法 - 使用路径 -->
  <CgLinkButton to="/product/list" type="primary">
    跳转到产品列表
  </CgLinkButton>

  <!-- 使用路由名称 -->
  <CgLinkButton :to="{ name: 'ProductList' }" type="success">
    跳转到产品列表
  </CgLinkButton>

  <!-- 带参数的路由 -->
  <CgLinkButton :to="{ name: 'ProductDetail', params: { id: 123 } }" type="info">
    查看产品详情
  </CgLinkButton>

  <!-- 带查询参数的路由 -->
  <CgLinkButton :to="{ path: '/product/list', query: { category: 'electronics' } }">
    查看电子产品
  </CgLinkButton>

  <!-- 替换当前路由 -->
  <CgLinkButton :to="'/product/list'" replace>
    替换当前路由
  </CgLinkButton>

  <!-- 在新窗口打开 -->
  <CgLinkButton :to="'/product/list'" target="_blank">
    在新窗口打开
  </CgLinkButton>
</template>
```

## 属性

| 属性名      | 类型                      | 默认值   | 说明                                     |
|------------|--------------------------|---------|----------------------------------------|
| to         | string / RouteLocationRaw | -       | 必填，要导航到的路由路径或对象              |
| replace    | boolean                  | false   | 是否替换当前路由（使用 router.replace）     |
| target     | string                   | '_self' | 链接的打开方式，可选值：'_self', '_blank', '_parent', '_top' |
| autoLoading | boolean                 | false   | 是否自动显示加载状态                       |
| size       | string                   | 'small' | 按钮大小，可选值：'', 'default', 'small', 'large' |

此外，组件还支持所有 ElButton 的属性，如 `type`, `plain`, `round` 等。
