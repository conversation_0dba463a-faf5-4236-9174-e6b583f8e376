import type { GetOssUploadCredentialAccessTypeEnum, GetOssUploadCredentialUploadFileTypeEnum } from '@/apiv2/product'
import type { AxiosProgressEvent } from 'axios'
import { fileApi } from '@/api.services/product.service'
import axios from 'axios'

/**
 * 阿里云OSS文件上传工具类
 */
export class OssUploader {
  /**
   * 上传文件到阿里云OSS
   * @param file 要上传的文件
   * @param uploadFileType 用途，用于确定存储路径,可用值:category-icon,image/main,image/sku,image/detail,instrument
   * @param accessType 访问权限类型,可用值:public,private,示例值(private)
   * @param onProgress 上传进度回调函数
   * @param onSuccess 上传成功回调函数
   * @param onError 上传失败回调函数
   */
  static async uploadFile(
    file: File,
    uploadFileType: GetOssUploadCredentialUploadFileTypeEnum,
    accessType: GetOssUploadCredentialAccessTypeEnum,
    onProgress?: (percent: number) => void,
    onSuccess?: (url: string, key: string) => void,
    onError?: (error: any) => void,
  ): Promise<string | undefined> {
    const response = (await fileApi.getOssUploadCredential({
      fileName: file.name,
      uploadFileType,
      accessType,
    })).data

    if (!response.success || !response.data) {
      return
    }

    const credential = response.data

    const formData = new FormData()
    formData.append('key', credential.key!)
    formData.append('policy', credential.policy!)
    formData.append('signature', credential.signature!)
    formData.append('OSSAccessKeyId', credential.accessKeyId!)
    formData.append('file', file, file.name)

    const fileUrl = `${credential.host}/${credential.key}`

    try {
      const config = {
        method: 'post',
        url: credential.host,
        data: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent: AxiosProgressEvent) => {
          if (onProgress && progressEvent.total) {
            const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total)
            onProgress(percentCompleted)
          }
        },
      }

      await axios(config)

      if (onSuccess) {
        onSuccess(fileUrl, credential.key || '')
      }

      return fileUrl
    }
    catch (error) {
      if (onError) {
        onError(error)
      }
    }
  }

  /**
   * 从OSS URL中提取文件名
   * @param url OSS文件URL
   * @returns 文件名
   */
  static getFileNameFromUrl(url: string): string {
    if (!url)
      return ''
    const parts = url.split('/')
    return parts[parts.length - 1]
  }

  /**
   * 检查URL是否是OSS URL
   * @param url 要检查的URL
   * @returns 是否是OSS URL
   */
  static isOssUrl(url: string): boolean {
    if (!url)
      return false
    return url.includes('.aliyuncs.com/')
  }

  /**
   * 根据文件类型和文件名生成唯一文件名
   * @param fileName 原始文件名
   * @returns 生成的唯一文件名
   */
  static generateUniqueFileName(fileName: string): string {
    const ext = fileName.substring(fileName.lastIndexOf('.'))
    const randomStr = Math.random().toString(36).substring(2, 10)
    const timestamp = new Date().getTime()
    return `${randomStr}_${timestamp}${ext}`
  }
}
