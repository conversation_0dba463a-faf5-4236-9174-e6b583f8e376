import type { UploadStrategy } from '@/components/CgImageManager'
import { extendedProductUploadStrategy } from '@/views/Product/Product/utils/extendedProductUploadStrategy'
import { productUploadStrategy } from '@/views/Product/Product/utils/productUploadStrategy'

export type UploadStrategyName = 'product' | 'extendedProduct'

// 定义注册表类型
export const uploadStrategyRegistry: Record<UploadStrategyName, UploadStrategy> = {
  product: productUploadStrategy,
  extendedProduct: extendedProductUploadStrategy,
  // 'anotherStrategy': anotherUploadStrategy,
}

/**
 * 根据标识符获取上传策略
 * @param identifier 策略的字符串标识符
 * @returns 对应的 UploadStrategy 对象或 undefined
 */
export function getUploadStrategy(identifier: UploadStrategyName): UploadStrategy | undefined {
  const strategy = uploadStrategyRegistry[identifier]
  if (!strategy) {
    console.warn(`[UploadStrategyRegistry] 未找到标识符为 "${identifier}" 的上传策略。`)
  }
  return strategy
}
