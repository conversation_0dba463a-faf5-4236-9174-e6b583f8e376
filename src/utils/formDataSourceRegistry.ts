import { dictApi, supplierApi } from '@/api.services/product.service'
import userApi from '@/api.services/user.service'
import { transformDictData, transformPropertyData } from '@/components/utils/shared'

// API 函数注册表
export const apiFunctionRegistry: Record<string, (...args: any[]) => Promise<any>> = {
  listDicts: dictApi.listDicts,
  listSuppliers: supplierApi.listSuppliers,
  listUsers: userApi.search,
}

// 数据转换函数注册表
export const transformFunctionRegistry: Record<string, (...args: any[]) => any> = {
  transformDictData,
  transformPropertyData,
}

// 获取 API 函数
export function getApiFunction(identifier: string): ((...args: any[]) => Promise<any>) | undefined {
  return apiFunctionRegistry[identifier]
}

// 获取转换函数
export function getTransformFunction(identifier?: string): ((...args: any[]) => any) | undefined {
  return identifier ? transformFunctionRegistry[identifier] : undefined
}
