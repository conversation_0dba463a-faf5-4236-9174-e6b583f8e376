import { describe, expect, it } from 'vitest'
import { RsaEncryption } from '../encryption'

describe('rsaEncryption', () => {
  it('should encrypt a string', () => {
    const plainText = 'test-password'
    const encrypted = RsaEncryption.encrypt(plainText)

    // 加密后的字符串应该不等于原始字符串
    expect(encrypted).not.toBe(plainText)

    // 加密后的字符串应该是非空的
    expect(encrypted).toBeTruthy()

    // 加密后的字符串应该是一个字符串
    expect(typeof encrypted).toBe('string')
  })
})
