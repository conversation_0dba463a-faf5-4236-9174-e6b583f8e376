import JSEncrypt from 'jsencrypt'

/**
 * RSA加密工具类
 */
export class RsaEncryption {
  private static publicKey = `
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhRmd55IcfgcPXAtW9l7lHuTqfgn8FmQtT3yi4bHFJD8SbV/6l5ZeS64K5L5O1cfRjIURDKsOgEb6/PmchwHiKpXbJxidFkyjnF/xc1iQPyNfQk7+Prcyqaev5eZm6rUor1ofdBoBEWFOz5LAFKcJ3hBh3jxoAwxaPvjhD6wQ+qHpXleZpiD9Af17lsr4Xq4xOJr2Uu1Zm8wWRhMdaSIPmvBAhaMdQuHJzOaouJsvhiMqsxTiT5Pyn+UuT2ptqEYMMYSuy9Kc0MSjTFtFaB5W1YHugH17qOsfic2YaqbJY/RySrex0ZpADCpt4kXVO1/tf+JRTlAPNBNKuml7pmoSyQIDAQAB
`

  /**
   * 使用RSA公钥加密数据
   * @param data 要加密的数据
   * @returns 加密后的字符串
   */
  static encrypt(data: string): string {
    const encryptor = new JSEncrypt()
    encryptor.setPublicKey(this.publicKey)
    const encrypted = encryptor.encrypt(data)
    return encrypted || ''
  }
}
