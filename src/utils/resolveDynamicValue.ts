/**
 * 解析动态属性值：处理 $context.key 和 $model.key 引用
 * @param value 待解析的值
 * @param model 表单模型 (可选, CgLinkButton 可能不需要)
 * @param context 上下文数据
 * @returns 解析后的值
 */
export function resolveDynamicValue(value: any, model: any, context: any): any {
  if (value === null || value === undefined) {
    return value
  }

  // 处理字符串引用，如 $context.key 或 $model.key
  if (typeof value === 'string') {
    if (value.startsWith('$context.')) {
      const key = value.substring('$context.'.length)
      // 支持简单的点路径访问，例如 $context.user.id
      const keys = key.split('.')
      let result = context
      for (const k of keys) {
        if (result && typeof result === 'object' && k in result) {
          result = result[k]
        }
        else {
          console.warn(`[resolveDynamicValue] Context key not found: ${key}`)
          return undefined // Or return original value, depending on desired behavior
        }
      }
      return result
    }
    if (value.startsWith('$model.')) {
      const key = value.substring('$model.'.length)
      // 支持简单的点路径访问
      const keys = key.split('.')
      let result = model
      for (const k of keys) {
        if (result && typeof result === 'object' && k in result) {
          result = result[k]
        }
        else {
          console.warn(`[resolveDynamicValue] Model key not found: ${key}`)
          return undefined
        }
      }
      return result
    }
    return value
  }

  // 递归处理数组
  if (Array.isArray(value)) {
    return value.map(item => resolveDynamicValue(item, model, context))
  }

  // 递归处理普通对象（避免处理非普通对象如Date等）
  if (typeof value === 'object' && value.constructor === Object) {
    const resolvedObject: Record<string, any> = {}
    for (const key in value) {
      if (Object.prototype.hasOwnProperty.call(value, key)) {
        resolvedObject[key] = resolveDynamicValue(value[key], model, context)
      }
    }
    return resolvedObject
  }

  return value
}
