/**
 * 格式化数字，每三位用逗号分隔，并能指定小数精度。
 *
 * @param value 需要格式化的数字或数字字符串。
 * @param precision 小数位数，默认为 0。
 * @returns 格式化后的字符串。如果输入无法转换为有效数字，则返回原始输入的字符串形式。
 */
import { floor, isNaN, toNumber, toString } from 'lodash-es'

export function formatNumber(value: number | string, precision: number = 0): string {
  const num = toNumber(value)

  if (isNaN(num)) {
    console.warn(`[formatNumber] 输入 "${value}" 不是一个有效的数字。`)
    return toString(value)
  }

  const safePrecision = Math.max(0, floor(precision))
  const fixedNum = num.toFixed(safePrecision)
  const [integerPart, decimalPart] = fixedNum.split('.')
  const formattedIntegerPart = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',')

  if (safePrecision > 0 && decimalPart) {
    return `${formattedIntegerPart}.${decimalPart}`
  }

  return formattedIntegerPart
}
